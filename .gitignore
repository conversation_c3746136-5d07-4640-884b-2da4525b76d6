# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
<<<<<<< HEAD
pnpm-debug.log*

# Next.js
.next/
out/
build/
dist/
=======

# Production builds
dist/
build/
>>>>>>> 52af1f050aa2dea8a741d2d9a618c392237d65cf

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

<<<<<<< HEAD
# IDE and editor files
=======
# IDE files
>>>>>>> 52af1f050aa2dea8a741d2d9a618c392237d65cf
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
<<<<<<< HEAD
*.lcov
=======
>>>>>>> 52af1f050aa2dea8a741d2d9a618c392237d65cf

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

<<<<<<< HEAD
# Optional eslint cache
.eslintcache

=======
>>>>>>> 52af1f050aa2dea8a741d2d9a618c392237d65cf
# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

<<<<<<< HEAD
# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Temporary folders
tmp/
temp/

# Local Netlify folder
.netlify

# Vercel
.vercel

# Turbo
.turbo

# TypeScript
*.tsbuildinfo
next-env.d.ts
=======
# Temporary files
*.tmp
*.temp

# Vite/Build files (not needed for static HTML app)
vite.config.js
tailwind.config.js
postcss.config.js
eslint.config.js
.eslintrc.cjs

# Test files (keep only the main application)
test.html
demo.html
advanced.html
complete-advanced.html
components.js
app-components.js
README-new.md

# Source directory (not needed for static app)
src/

# Build outputs
dist/
build/
>>>>>>> 52af1f050aa2dea8a741d2d9a618c392237d65cf
