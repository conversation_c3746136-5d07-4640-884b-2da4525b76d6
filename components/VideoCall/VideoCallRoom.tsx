'use client'

import { useEffect, useState, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { useVideoCallStoreContext } from '@/providers/StoreProvider'
import { socketManager } from '@/lib/socket'
import { rtcManager } from '@/lib/rtc'
import { VideoGrid } from './VideoGrid'
import { VideoControls } from './VideoControls'
import { Chat } from './Chat'
import { ParticipantsList } from './ParticipantsList'
import { SettingsModal } from './SettingsModal'
import {
  Users,
  Settings,
  PhoneOff,
  Copy,
  Share,
  Maximize2,
  Minimize2
} from 'lucide-react'

interface VideoCallRoomProps {
  roomId: string
}

export function VideoCallRoom({ roomId }: VideoCallRoomProps) {
  const router = useRouter()
  const [isInitialized, setIsInitialized] = useState(false)
  const [showSettings, setShowSettings] = useState(false)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [notification, setNotification] = useState<string | null>(null)

  const currentUser = useVideoCallStoreContext((state) => state.currentUser)
  const participants = useVideoCallStoreContext((state) => state.participants)
  const setConnected = useVideoCallStoreContext((state) => state.setConnected)
  const addParticipant = useVideoCallStoreContext((state) => state.addParticipant)
  const removeParticipant = useVideoCallStoreContext((state) => state.removeParticipant)
  const updateParticipant = useVideoCallStoreContext((state) => state.updateParticipant)
  const setLocalStream = useVideoCallStoreContext((state) => state.setLocalStream)
  const addMessage = useVideoCallStoreContext((state) => state.addMessage)
  const reset = useVideoCallStoreContext((state) => state.reset)

  // Show notification
  const showNotification = (message: string) => {
    setNotification(message)
    setTimeout(() => setNotification(null), 3000)
  }

  // Initialize WebRTC and Socket.IO
  useEffect(() => {
    const initializeCall = async () => {
      try {
        // Connect to Socket.IO server
        const socket = socketManager.connect()

        if (!socket) {
          throw new Error('Failed to connect to server')
        }

        // Get user media
        const stream = await rtcManager.getUserMedia()
        setLocalStream(stream)

        // Set up RTC signal callback
        rtcManager.setSendSignalCallback((userId, signal) => {
          socketManager.sendSignal(userId, signal)
        })

        // Set up RTC stream callback
        rtcManager.onStream((userId, stream) => {
          updateParticipant(userId, { stream })
        })

        // Set up RTC disconnect callback
        rtcManager.onUserDisconnected((userId) => {
          removeParticipant(userId)
          showNotification('A participant has left the meeting')
        })

        // Join the room
        if (currentUser) {
          socketManager.joinRoom(roomId, currentUser.id, currentUser.name)
        }

        setIsInitialized(true)
        setConnected(true)
        showNotification('Successfully connected to meeting')

      } catch (error) {
        console.error('Failed to initialize call:', error)
        showNotification('Failed to join meeting. Please check your permissions.')
      }
    }

    if (currentUser && !isInitialized) {
      initializeCall()
    }

    return () => {
      // Cleanup on unmount
      if (isInitialized) {
        socketManager.leaveRoom()
        rtcManager.cleanup()
        reset()
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentUser, roomId, isInitialized])

  // Set up Socket.IO event listeners
  useEffect(() => {
    if (!isInitialized) return

    const socket = socketManager.getSocket()
    if (!socket) return

    // Handle new user joined
    const handleUserJoined = async (data: { userId: string, userName: string }) => {
      console.log('User joined:', data)

      addParticipant({
        id: data.userId,
        name: data.userName,
        isAudioMuted: false,
        isVideoMuted: false,
        isScreenSharing: false,
        role: 'participant',
        isHandRaised: false,
        joinedAt: new Date(),
        lastActivity: new Date()
      })

      // Create offer for new user
      try {
        const offer = await rtcManager.createOffer(data.userId)
        socketManager.sendSignal(data.userId, {
          type: 'offer',
          offer
        })
      } catch (error) {
        console.error('Error creating offer:', error)
      }

      showNotification(`${data.userName} joined the meeting`)
    }

    // Handle user left
    const handleUserLeft = (data: { userId: string, userName: string }) => {
      console.log('User left:', data)
      removeParticipant(data.userId)
      rtcManager.removePeerConnection(data.userId)
      showNotification(`${data.userName} left the meeting`)
    }

    // Handle WebRTC signals
    const handleSignal = async (data: { fromUserId: string, signal: any }) => {
      console.log('Received signal:', data)

      try {
        const { fromUserId, signal } = data

        switch (signal.type) {
          case 'offer':
            const answer = await rtcManager.createAnswer(fromUserId, signal.offer)
            socketManager.sendSignal(fromUserId, {
              type: 'answer',
              answer
            })
            break

          case 'answer':
            await rtcManager.handleAnswer(fromUserId, signal.answer)
            break

          case 'ice-candidate':
            await rtcManager.handleIceCandidate(fromUserId, signal.candidate)
            break
        }
      } catch (error) {
        console.error('Error handling signal:', error)
      }
    }

    // Handle chat messages
    const handleChatMessage = (data: { userId: string, userName: string, message: string, timestamp: string }) => {
      addMessage({
        id: Math.random().toString(36).substring(2, 15),
        userId: data.userId,
        userName: data.userName,
        message: data.message,
        timestamp: new Date(data.timestamp)
      })
    }

    // Register event listeners
    socket.on('user-joined', handleUserJoined)
    socket.on('user-left', handleUserLeft)
    socket.on('signal', handleSignal)
    socket.on('chat-message', handleChatMessage)

    return () => {
      socket.off('user-joined', handleUserJoined)
      socket.off('user-left', handleUserLeft)
      socket.off('signal', handleSignal)
      socket.off('chat-message', handleChatMessage)
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isInitialized])

  const handleLeaveCall = useCallback(() => {
    if (confirm('Are you sure you want to leave the meeting?')) {
      socketManager.leaveRoom()
      rtcManager.cleanup()
      reset()
      router.push('/')
    }
  }, [router, reset])

  const copyMeetingLink = useCallback(() => {
    const meetingLink = `${window.location.origin}/room/${roomId}`
    navigator.clipboard.writeText(meetingLink)
    showNotification('Meeting link copied to clipboard!')
  }, [roomId])

  const shareMeetingId = useCallback(() => {
    navigator.clipboard.writeText(roomId)
    showNotification('Meeting ID copied to clipboard!')
  }, [roomId])

  const toggleFullscreen = useCallback(() => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen()
      setIsFullscreen(true)
    } else {
      document.exitFullscreen()
      setIsFullscreen(false)
    }
  }, [])

  if (!isInitialized || !currentUser) {
    return (
      <>
        <div className="animated-bg"></div>
        <div className="min-h-screen flex items-center justify-center relative z-10">
          <div className="glass p-8 text-center">
            <div className="animate-spin rounded-full h-16 w-16 border-4 border-white/30 border-t-white mx-auto mb-6"></div>
            <h2 className="text-2xl font-semibold text-white mb-2">Connecting to Meeting</h2>
            <p className="text-white/70">Please wait while we set up your video call...</p>
          </div>
        </div>
      </>
    )
  }

  return (
    <>
      {/* Animated Background */}
      <div className="animated-bg"></div>

      <div className="min-h-screen flex flex-col relative z-10">
        {/* Header */}
        <div className="glass-dark p-4 m-4 mb-0">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h1 className="text-white text-xl font-semibold">
                StreamIt Pro Meeting
              </h1>
              <div className="flex items-center space-x-2 text-white/70">
                <Users className="h-4 w-4" />
                <span>{participants.size + 1} participants</span>
              </div>
              <div className="text-white/50 text-sm">
                ID: {roomId}
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <button
                onClick={copyMeetingLink}
                className="glass-button p-2 text-white hover:text-purple-300"
                title="Copy meeting link"
              >
                <Share className="h-5 w-5" />
              </button>

              <button
                onClick={shareMeetingId}
                className="glass-button p-2 text-white hover:text-purple-300"
                title="Copy meeting ID"
              >
                <Copy className="h-5 w-5" />
              </button>

              <button
                onClick={() => setShowSettings(!showSettings)}
                className={`glass-button p-2 text-white hover:text-purple-300 ${showSettings ? 'bg-purple-500/30' : ''}`}
                title="Settings"
              >
                <Settings className="h-5 w-5" />
              </button>

              <button
                onClick={toggleFullscreen}
                className="glass-button p-2 text-white hover:text-purple-300"
                title="Toggle fullscreen"
              >
                {isFullscreen ? <Minimize2 className="h-5 w-5" /> : <Maximize2 className="h-5 w-5" />}
              </button>

              <button
                onClick={handleLeaveCall}
                className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors"
                title="Leave meeting"
              >
                <PhoneOff className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>

        {/* Main content */}
        <div className="flex-1 flex p-4 pt-0 gap-4">
          {/* Video area */}
          <div className="w-2/3 flex flex-col">
            <VideoGrid />
            <VideoControls />
          </div>

          {/* Right Sidebar */}
          <div className="w-1/3 flex flex-col gap-4">
            {/* Participants Panel */}
            <div className="h-1/2">
              <ParticipantsList />
            </div>

            {/* Chat Panel */}
            <div className="h-1/2">
              <Chat />
            </div>
          </div>
        </div>

        {/* Settings Modal */}
        <SettingsModal
          isOpen={showSettings}
          onClose={() => setShowSettings(false)}
        />

        {/* Notification */}
        {notification && (
          <div className="fixed top-4 right-4 glass p-4 text-white z-50 fade-in">
            {notification}
          </div>
        )}
      </div>
    </>
  )
}