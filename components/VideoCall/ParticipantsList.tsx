'use client'

import { useState } from 'react'
import { useVideoCallStore } from '@/lib/store'
import {
  Mic,
  MicOff,
  Video,
  VideoOff,
  Monitor,
  Crown,
  MoreVertical,
  Users,
  UserMinus,
  UserCheck,
  Shield,
  ShieldOff,
  Hand,
  Lock,
  Unlock
} from 'lucide-react'

export function ParticipantsList() {
  const [showAdminMenu, setShowAdminMenu] = useState<string | null>(null)

  const {
    currentUser,
    participants,
    roomLocked,
    muteParticipant,
    muteAllParticipants,
    removeParticipant,
    promoteToCoHost,
    demoteFromCoHost,
    lockRoom,
    unlockRoom,
    blockUser
  } = useVideoCallStore()

  const participantsList = Array.from(participants.values())
  const allParticipants = currentUser ? [currentUser, ...participantsList] : participantsList

  const getStatusIcon = (participant: any) => {
    const icons = []

    if (participant.isAudioMuted) {
      icons.push(<MicOff key="mic" className="h-3 w-3 text-red-400" />)
    } else {
      icons.push(<Mic key="mic" className="h-3 w-3 text-green-400" />)
    }

    if (participant.isVideoMuted) {
      icons.push(<VideoOff key="video" className="h-3 w-3 text-red-400" />)
    } else {
      icons.push(<Video key="video" className="h-3 w-3 text-green-400" />)
    }

    if (participant.isScreenSharing) {
      icons.push(<Monitor key="screen" className="h-3 w-3 text-blue-400" />)
    }

    return icons
  }

  const isAdmin = currentUser?.role === 'host' || currentUser?.role === 'co-host'
  const isHost = currentUser?.role === 'host'

  const handleAdminAction = (action: string, participantId: string) => {
    switch (action) {
      case 'mute':
        muteParticipant(participantId)
        break
      case 'remove':
        removeParticipant(participantId)
        break
      case 'promote':
        promoteToCoHost(participantId)
        break
      case 'demote':
        demoteFromCoHost(participantId)
        break
      case 'block':
        blockUser(participantId)
        break
    }
    setShowAdminMenu(null)
  }

  return (
    <div className="chat-container flex flex-col h-full">
      {/* Header */}
      <div className="p-3 border-b border-white/10">
        <div className="flex items-center justify-between">
          <h3 className="text-white font-semibold text-sm flex items-center gap-2">
            <Users className="h-4 w-4" />
            Participants ({allParticipants.length})
            {roomLocked && <Lock className="h-3 w-3 text-yellow-400" />}
          </h3>

          {isAdmin && (
            <div className="flex items-center gap-1">
              <button
                onClick={muteAllParticipants}
                className="glass-button p-1 text-white/60 hover:text-white"
                title="Mute all participants"
              >
                <MicOff className="h-3 w-3" />
              </button>

              {isHost && (
                <button
                  onClick={() => roomLocked ? unlockRoom() : lockRoom()}
                  className="glass-button p-1 text-white/60 hover:text-white"
                  title={roomLocked ? 'Unlock room' : 'Lock room'}
                >
                  {roomLocked ? <Unlock className="h-3 w-3" /> : <Lock className="h-3 w-3" />}
                </button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Participants list */}
      <div className="flex-1 overflow-y-auto p-2 space-y-2">
        {allParticipants.map((participant) => {
          const isCurrentUser = participant.id === currentUser?.id

          return (
            <div
              key={participant.id}
              className="glass-button p-2 hover:bg-white/20 transition-all relative"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  {/* Avatar */}
                  <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center relative">
                    <span className="text-white text-xs font-bold">
                      {participant.name.charAt(0).toUpperCase()}
                    </span>
                    {participant.isHandRaised && (
                      <div className="absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 rounded-full flex items-center justify-center">
                        <Hand className="h-2 w-2 text-black" />
                      </div>
                    )}
                  </div>

                  {/* Name and status */}
                  <div className="flex flex-col flex-1 min-w-0">
                    <div className="flex items-center space-x-1">
                      <span className="text-white text-xs font-medium truncate">
                        {participant.name}
                        {isCurrentUser && ' (You)'}
                      </span>

                      {/* Role indicators */}
                      {participant.role === 'host' && (
                        <Crown className="h-3 w-3 text-yellow-400 flex-shrink-0" />
                      )}
                      {participant.role === 'co-host' && (
                        <Shield className="h-3 w-3 text-blue-400 flex-shrink-0" />
                      )}
                    </div>
                    <div className="flex items-center space-x-1">
                      {getStatusIcon(participant)}
                    </div>
                  </div>
                </div>

                {/* Actions */}
                {!isCurrentUser && isAdmin && (
                  <div className="relative">
                    <button
                      onClick={() => setShowAdminMenu(showAdminMenu === participant.id ? null : participant.id)}
                      className="glass-button p-1 text-white/60 hover:text-white"
                    >
                      <MoreVertical className="h-3 w-3" />
                    </button>

                    {/* Admin Menu */}
                    {showAdminMenu === participant.id && (
                      <div className="absolute right-0 top-full mt-1 glass-dark rounded-lg border border-white/20 min-w-32 z-10">
                        <div className="p-1">
                          <button
                            onClick={() => handleAdminAction('mute', participant.id)}
                            className="w-full p-2 text-left text-white text-xs hover:bg-white/10 rounded flex items-center gap-2"
                          >
                            <MicOff className="h-3 w-3" />
                            Mute
                          </button>

                          {isHost && participant.role === 'participant' && (
                            <button
                              onClick={() => handleAdminAction('promote', participant.id)}
                              className="w-full p-2 text-left text-white text-xs hover:bg-white/10 rounded flex items-center gap-2"
                            >
                              <Shield className="h-3 w-3" />
                              Make Co-Host
                            </button>
                          )}

                          {isHost && participant.role === 'co-host' && (
                            <button
                              onClick={() => handleAdminAction('demote', participant.id)}
                              className="w-full p-2 text-left text-white text-xs hover:bg-white/10 rounded flex items-center gap-2"
                            >
                              <ShieldOff className="h-3 w-3" />
                              Remove Co-Host
                            </button>
                          )}

                          <button
                            onClick={() => handleAdminAction('block', participant.id)}
                            className="w-full p-2 text-left text-red-400 text-xs hover:bg-white/10 rounded flex items-center gap-2"
                          >
                            <UserMinus className="h-3 w-3" />
                            Block User
                          </button>

                          <button
                            onClick={() => handleAdminAction('remove', participant.id)}
                            className="w-full p-2 text-left text-red-400 text-xs hover:bg-white/10 rounded flex items-center gap-2"
                          >
                            <UserMinus className="h-3 w-3" />
                            Remove
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}
