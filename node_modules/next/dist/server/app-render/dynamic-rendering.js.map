{"version": 3, "sources": ["../../../src/server/app-render/dynamic-rendering.ts"], "names": ["Postpone", "createPostponedAbortSignal", "createPrerenderState", "formatDynamicAPIAccesses", "markCurrentScopeAsDynamic", "trackDynamicDataAccessed", "trackDynamicFetch", "usedDynamicAPIs", "hasPostpone", "React", "unstable_postpone", "isDebugSkeleton", "dynamicAccesses", "store", "expression", "pathname", "getPathname", "urlPathname", "isUnstableCacheCallback", "dynamicShouldError", "StaticGenBailoutError", "prerenderState", "postponeWithTracking", "revalidate", "isStaticGeneration", "err", "DynamicServerError", "dynamicUsageDescription", "dynamicUsageStack", "stack", "Error", "reason", "assertPostpone", "push", "undefined", "length", "filter", "access", "map", "split", "slice", "line", "includes", "join", "controller", "AbortController", "x", "abort", "signal"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;CAoBC,GAED,wFAAwF;;;;;;;;;;;;;;;;;;;;;;IAgJxEA,QAAQ;eAARA;;IAgGAC,0BAA0B;eAA1BA;;IA3MAC,oBAAoB;eAApBA;;IAyJAC,wBAAwB;eAAxBA;;IA1IAC,yBAAyB;eAAzBA;;IA+CAC,wBAAwB;eAAxBA;;IAyDAC,iBAAiB;eAAjBA;;IA8BAC,eAAe;eAAfA;;;8DAzLE;oCAGiB;yCACG;qBACV;;;;;;AAE5B,MAAMC,cAAc,OAAOC,cAAK,CAACC,iBAAiB,KAAK;AA6BhD,SAASR,qBACdS,eAAoC;IAEpC,OAAO;QACLA;QACAC,iBAAiB,EAAE;IACrB;AACF;AAQO,SAASR,0BACdS,KAA4B,EAC5BC,UAAkB;IAElB,MAAMC,WAAWC,IAAAA,gBAAW,EAACH,MAAMI,WAAW;IAC9C,IAAIJ,MAAMK,uBAAuB,EAAE;QACjC,6FAA6F;QAC7F,iGAAiG;QACjG,kCAAkC;QAClC;IACF,OAAO,IAAIL,MAAMM,kBAAkB,EAAE;QACnC,MAAM,IAAIC,8CAAqB,CAC7B,CAAC,MAAM,EAAEL,SAAS,8EAA8E,EAAED,WAAW,4HAA4H,CAAC;IAE9O,OAAO,IACL,oDAAoD;IACpDD,MAAMQ,cAAc,EACpB;QACA,uDAAuD;QACvD,sDAAsD;QACtD,kCAAkC;QAClCC,qBAAqBT,MAAMQ,cAAc,EAAEP,YAAYC;IACzD,OAAO;QACLF,MAAMU,UAAU,GAAG;QAEnB,IAAIV,MAAMW,kBAAkB,EAAE;YAC5B,uGAAuG;YACvG,MAAMC,MAAM,IAAIC,sCAAkB,CAChC,CAAC,MAAM,EAAEX,SAAS,iDAAiD,EAAED,WAAW,2EAA2E,CAAC;YAE9JD,MAAMc,uBAAuB,GAAGb;YAChCD,MAAMe,iBAAiB,GAAGH,IAAII,KAAK;YAEnC,MAAMJ;QACR;IACF;AACF;AAWO,SAASpB,yBACdQ,KAA4B,EAC5BC,UAAkB;IAElB,MAAMC,WAAWC,IAAAA,gBAAW,EAACH,MAAMI,WAAW;IAC9C,IAAIJ,MAAMK,uBAAuB,EAAE;QACjC,MAAM,IAAIY,MACR,CAAC,MAAM,EAAEf,SAAS,OAAO,EAAED,WAAW,iLAAiL,EAAEA,WAAW,6KAA6K,CAAC;IAEtZ,OAAO,IAAID,MAAMM,kBAAkB,EAAE;QACnC,MAAM,IAAIC,8CAAqB,CAC7B,CAAC,MAAM,EAAEL,SAAS,8EAA8E,EAAED,WAAW,4HAA4H,CAAC;IAE9O,OAAO,IACL,oDAAoD;IACpDD,MAAMQ,cAAc,EACpB;QACA,uDAAuD;QACvD,sDAAsD;QACtD,kCAAkC;QAClCC,qBAAqBT,MAAMQ,cAAc,EAAEP,YAAYC;IACzD,OAAO;QACLF,MAAMU,UAAU,GAAG;QAEnB,IAAIV,MAAMW,kBAAkB,EAAE;YAC5B,uGAAuG;YACvG,MAAMC,MAAM,IAAIC,sCAAkB,CAChC,CAAC,MAAM,EAAEX,SAAS,iDAAiD,EAAED,WAAW,2EAA2E,CAAC;YAE9JD,MAAMc,uBAAuB,GAAGb;YAChCD,MAAMe,iBAAiB,GAAGH,IAAII,KAAK;YAEnC,MAAMJ;QACR;IACF;AACF;AAUO,SAASzB,SAAS,EACvB+B,MAAM,EACNV,cAAc,EACdN,QAAQ,EACM;IACdO,qBAAqBD,gBAAgBU,QAAQhB;AAC/C;AAMO,SAAST,kBACdO,KAA4B,EAC5BC,UAAkB;IAElB,IAAID,MAAMQ,cAAc,EAAE;QACxBC,qBAAqBT,MAAMQ,cAAc,EAAEP,YAAYD,MAAMI,WAAW;IAC1E;AACF;AAEA,SAASK,qBACPD,cAA8B,EAC9BP,UAAkB,EAClBC,QAAgB;IAEhBiB;IACA,MAAMD,SACJ,CAAC,MAAM,EAAEhB,SAAS,iEAAiE,EAAED,WAAW,EAAE,CAAC,GACnG,CAAC,+EAA+E,CAAC,GACjF,CAAC,iFAAiF,CAAC;IAErFO,eAAeT,eAAe,CAACqB,IAAI,CAAC;QAClC,0EAA0E;QAC1E,eAAe;QACfJ,OAAOR,eAAeV,eAAe,GAAG,IAAImB,QAAQD,KAAK,GAAGK;QAC5DpB;IACF;IAEAL,cAAK,CAACC,iBAAiB,CAACqB;AAC1B;AAEO,SAASxB,gBAAgBc,cAA8B;IAC5D,OAAOA,eAAeT,eAAe,CAACuB,MAAM,GAAG;AACjD;AAEO,SAAShC,yBACdkB,cAA8B;IAE9B,OAAOA,eAAeT,eAAe,CAClCwB,MAAM,CACL,CAACC,SACC,OAAOA,OAAOR,KAAK,KAAK,YAAYQ,OAAOR,KAAK,CAACM,MAAM,GAAG,GAE7DG,GAAG,CAAC,CAAC,EAAExB,UAAU,EAAEe,KAAK,EAAE;QACzBA,QAAQA,MACLU,KAAK,CAAC,KACP,wEAAwE;QACxE,qEAAqE;QACrE,uDAAuD;SACtDC,KAAK,CAAC,GACNJ,MAAM,CAAC,CAACK;YACP,kDAAkD;YAClD,IAAIA,KAAKC,QAAQ,CAAC,uBAAuB;gBACvC,OAAO;YACT;YAEA,oDAAoD;YACpD,IAAID,KAAKC,QAAQ,CAAC,mBAAmB;gBACnC,OAAO;YACT;YAEA,kDAAkD;YAClD,IAAID,KAAKC,QAAQ,CAAC,YAAY;gBAC5B,OAAO;YACT;YAEA,OAAO;QACT,GACCC,IAAI,CAAC;QACR,OAAO,CAAC,0BAA0B,EAAE7B,WAAW,GAAG,EAAEe,MAAM,CAAC;IAC7D;AACJ;AAEA,SAASG;IACP,IAAI,CAACxB,aAAa;QAChB,MAAM,IAAIsB,MACR,CAAC,gIAAgI,CAAC;IAEtI;AACF;AAMO,SAAS7B,2BAA2B8B,MAAc;IACvDC;IACA,MAAMY,aAAa,IAAIC;IACvB,qFAAqF;IACrF,IAAI;QACFpC,cAAK,CAACC,iBAAiB,CAACqB;IAC1B,EAAE,OAAOe,GAAY;QACnBF,WAAWG,KAAK,CAACD;IACnB;IACA,OAAOF,WAAWI,MAAM;AAC1B"}