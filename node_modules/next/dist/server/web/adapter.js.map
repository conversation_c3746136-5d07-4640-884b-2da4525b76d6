{"version": 3, "sources": ["../../../src/server/web/adapter.ts"], "names": ["NextRequestHint", "adapter", "NextRequest", "constructor", "params", "input", "init", "sourcePage", "page", "request", "PageSignatureError", "respondWith", "waitUntil", "headersGetter", "keys", "headers", "Array", "from", "get", "key", "undefined", "propagator", "fn", "tracer", "getTracer", "withPropagatedContext", "testApisIntercepted", "ensureTestApisIntercepted", "process", "env", "NEXT_PRIVATE_TEST_PROXY", "interceptTestApis", "wrapRequestHandler", "require", "ensureInstrumentationRegistered", "isEdgeRendering", "self", "__BUILD_MANIFEST", "prerenderManifest", "__PRERENDER_MANIFEST", "JSON", "parse", "url", "normalizeRscURL", "requestUrl", "NextURL", "nextConfig", "searchParams", "value", "getAll", "NEXT_QUERY_PARAM_PREFIX", "startsWith", "normalizedKey", "substring", "length", "delete", "val", "append", "buildId", "isDataReq", "pathname", "requestHeaders", "fromNodeOutgoingHttpHeaders", "flightHeaders", "Map", "param", "FLIGHT_PARAMETERS", "toString", "toLowerCase", "set", "normalizeUrl", "__NEXT_NO_MIDDLEWARE_URL_NORMALIZE", "URL", "stripInternalSearchParams", "body", "geo", "ip", "method", "signal", "Object", "defineProperty", "enumerable", "globalThis", "__incrementalCache", "IncrementalCache", "appDir", "fetchCache", "minimalMode", "NODE_ENV", "fetchCacheKeyPrefix", "__NEXT_FETCH_CACHE_KEY_PREFIX", "dev", "requestProtocol", "getPrerenderManifest", "version", "routes", "dynamicRoutes", "notFoundRoutes", "preview", "previewModeId", "event", "NextFetchEvent", "response", "cookiesFromResponse", "isMiddleware", "trace", "MiddlewareSpan", "execute", "spanName", "nextUrl", "attributes", "RequestAsyncStorageWrapper", "wrap", "requestAsyncStorage", "req", "renderOpts", "onUpdateCookies", "cookies", "previewProps", "previewModeEncryptionKey", "previewModeSigningKey", "handler", "Response", "TypeError", "rewrite", "rewriteUrl", "forceLocale", "host", "String", "relativizedRewrite", "relativizeURL", "__NEXT_EXTERNAL_MIDDLEWARE_REWRITE_RESOLVE", "match", "redirect", "redirectURL", "finalResponse", "NextResponse", "next", "middlewareOverrideHeaders", "overwrittenHeaders", "push", "join", "Promise", "all", "waitUntilSymbol", "fetchMetrics"], "mappings": ";;;;;;;;;;;;;;;IAsBaA,eAAe;eAAfA;;IA8DSC,OAAO;eAAPA;;;uBAjFa;uBACS;4BACb;yBACH;0BACC;+BACC;yBAEN;+BACkB;0BACV;kCACE;2BACM;yBACQ;4CACL;6CACP;wBACV;4BAEK;AAExB,MAAMD,wBAAwBE,oBAAW;IAI9CC,YAAYC,MAIX,CAAE;QACD,KAAK,CAACA,OAAOC,KAAK,EAAED,OAAOE,IAAI;QAC/B,IAAI,CAACC,UAAU,GAAGH,OAAOI,IAAI;IAC/B;IAEA,IAAIC,UAAU;QACZ,MAAM,IAAIC,yBAAkB,CAAC;YAAEF,MAAM,IAAI,CAACD,UAAU;QAAC;IACvD;IAEAI,cAAc;QACZ,MAAM,IAAID,yBAAkB,CAAC;YAAEF,MAAM,IAAI,CAACD,UAAU;QAAC;IACvD;IAEAK,YAAY;QACV,MAAM,IAAIF,yBAAkB,CAAC;YAAEF,MAAM,IAAI,CAACD,UAAU;QAAC;IACvD;AACF;AAEA,MAAMM,gBAAwC;IAC5CC,MAAM,CAACC,UAAYC,MAAMC,IAAI,CAACF,QAAQD,IAAI;IAC1CI,KAAK,CAACH,SAASI,MAAQJ,QAAQG,GAAG,CAACC,QAAQC;AAC7C;AASA,IAAIC,aAA8D,CAChEZ,SACAa;IAEA,MAAMC,SAASC,IAAAA,iBAAS;IACxB,OAAOD,OAAOE,qBAAqB,CAAChB,QAAQM,OAAO,EAAEO,IAAIT;AAC3D;AAEA,IAAIa,sBAAsB;AAE1B,SAASC;IACP,IAAI,CAACD,qBAAqB;QACxBA,sBAAsB;QACtB,IAAIE,QAAQC,GAAG,CAACC,uBAAuB,KAAK,QAAQ;YAClD,MAAM,EACJC,iBAAiB,EACjBC,kBAAkB,EACnB,GAAGC,QAAQ;YACZF;YACAV,aAAaW,mBAAmBX;QAClC;IACF;AACF;AAEO,eAAepB,QACpBG,MAAsB;IAEtBuB;IACA,MAAMO,IAAAA,wCAA+B;IAErC,yCAAyC;IACzC,MAAMC,kBAAkB,OAAOC,KAAKC,gBAAgB,KAAK;IACzD,MAAMC,oBACJ,OAAOF,KAAKG,oBAAoB,KAAK,WACjCC,KAAKC,KAAK,CAACL,KAAKG,oBAAoB,IACpCnB;IAENhB,OAAOK,OAAO,CAACiC,GAAG,GAAGC,IAAAA,yBAAe,EAACvC,OAAOK,OAAO,CAACiC,GAAG;IAEvD,MAAME,aAAa,IAAIC,gBAAO,CAACzC,OAAOK,OAAO,CAACiC,GAAG,EAAE;QACjD3B,SAASX,OAAOK,OAAO,CAACM,OAAO;QAC/B+B,YAAY1C,OAAOK,OAAO,CAACqC,UAAU;IACvC;IAEA,yIAAyI;IACzI,4CAA4C;IAC5C,MAAMhC,OAAO;WAAI8B,WAAWG,YAAY,CAACjC,IAAI;KAAG;IAChD,KAAK,MAAMK,OAAOL,KAAM;QACtB,MAAMkC,QAAQJ,WAAWG,YAAY,CAACE,MAAM,CAAC9B;QAE7C,IACEA,QAAQ+B,kCAAuB,IAC/B/B,IAAIgC,UAAU,CAACD,kCAAuB,GACtC;YACA,MAAME,gBAAgBjC,IAAIkC,SAAS,CAACH,kCAAuB,CAACI,MAAM;YAClEV,WAAWG,YAAY,CAACQ,MAAM,CAACH;YAE/B,KAAK,MAAMI,OAAOR,MAAO;gBACvBJ,WAAWG,YAAY,CAACU,MAAM,CAACL,eAAeI;YAChD;YACAZ,WAAWG,YAAY,CAACQ,MAAM,CAACpC;QACjC;IACF;IAEA,4DAA4D;IAC5D,MAAMuC,UAAUd,WAAWc,OAAO;IAClCd,WAAWc,OAAO,GAAG;IAErB,MAAMC,YAAYvD,OAAOK,OAAO,CAACM,OAAO,CAAC,gBAAgB;IAEzD,IAAI4C,aAAaf,WAAWgB,QAAQ,KAAK,UAAU;QACjDhB,WAAWgB,QAAQ,GAAG;IACxB;IAEA,MAAMC,iBAAiBC,IAAAA,kCAA2B,EAAC1D,OAAOK,OAAO,CAACM,OAAO;IACzE,MAAMgD,gBAAgB,IAAIC;IAC1B,oDAAoD;IACpD,IAAI,CAAC7B,iBAAiB;QACpB,KAAK,MAAM8B,SAASC,mCAAiB,CAAE;YACrC,MAAM/C,MAAM8C,MAAME,QAAQ,GAAGC,WAAW;YACxC,MAAMpB,QAAQa,eAAe3C,GAAG,CAACC;YACjC,IAAI6B,OAAO;gBACTe,cAAcM,GAAG,CAAClD,KAAK0C,eAAe3C,GAAG,CAACC;gBAC1C0C,eAAeN,MAAM,CAACpC;YACxB;QACF;IACF;IAEA,MAAMmD,eAAe1C,QAAQC,GAAG,CAAC0C,kCAAkC,GAC/D,IAAIC,IAAIpE,OAAOK,OAAO,CAACiC,GAAG,IAC1BE;IAEJ,MAAMnC,UAAU,IAAIT,gBAAgB;QAClCQ,MAAMJ,OAAOI,IAAI;QACjB,mDAAmD;QACnDH,OAAOoE,IAAAA,wCAAyB,EAACH,cAAc,MAAMH,QAAQ;QAC7D7D,MAAM;YACJoE,MAAMtE,OAAOK,OAAO,CAACiE,IAAI;YACzBC,KAAKvE,OAAOK,OAAO,CAACkE,GAAG;YACvB5D,SAAS8C;YACTe,IAAIxE,OAAOK,OAAO,CAACmE,EAAE;YACrBC,QAAQzE,OAAOK,OAAO,CAACoE,MAAM;YAC7B/B,YAAY1C,OAAOK,OAAO,CAACqC,UAAU;YACrCgC,QAAQ1E,OAAOK,OAAO,CAACqE,MAAM;QAC/B;IACF;IAEA;;;;GAIC,GACD,IAAInB,WAAW;QACboB,OAAOC,cAAc,CAACvE,SAAS,YAAY;YACzCwE,YAAY;YACZjC,OAAO;QACT;IACF;IAEA,IACE,CAAC,AAACkC,WAAmBC,kBAAkB,IACvC,AAAC/E,OAAegF,gBAAgB,EAChC;QACEF,WAAmBC,kBAAkB,GAAG,IAAI,AAC5C/E,OACAgF,gBAAgB,CAAC;YACjBC,QAAQ;YACRC,YAAY;YACZC,aAAa3D,QAAQC,GAAG,CAAC2D,QAAQ,KAAK;YACtCC,qBAAqB7D,QAAQC,GAAG,CAAC6D,6BAA6B;YAC9DC,KAAK/D,QAAQC,GAAG,CAAC2D,QAAQ,KAAK;YAC9B3B,gBAAgBzD,OAAOK,OAAO,CAACM,OAAO;YACtC6E,iBAAiB;YACjBC,sBAAsB;gBACpB,OAAO;oBACLC,SAAS,CAAC;oBACVC,QAAQ,CAAC;oBACTC,eAAe,CAAC;oBAChBC,gBAAgB,EAAE;oBAClBC,SAAS;wBACPC,eAAe;oBACjB;gBACF;YACF;QACF;IACF;IAEA,MAAMC,QAAQ,IAAIC,0BAAc,CAAC;QAAE5F;QAASD,MAAMJ,OAAOI,IAAI;IAAC;IAC9D,IAAI8F;IACJ,IAAIC;IAEJD,WAAW,MAAMjF,WAAWZ,SAAS;QACnC,8DAA8D;QAC9D,MAAM+F,eACJpG,OAAOI,IAAI,KAAK,iBAAiBJ,OAAOI,IAAI,KAAK;QACnD,IAAIgG,cAAc;YAChB,OAAOhF,IAAAA,iBAAS,IAAGiF,KAAK,CACtBC,0BAAc,CAACC,OAAO,EACtB;gBACEC,UAAU,CAAC,WAAW,EAAEnG,QAAQoE,MAAM,CAAC,CAAC,EAAEpE,QAAQoG,OAAO,CAACjD,QAAQ,CAAC,CAAC;gBACpEkD,YAAY;oBACV,eAAerG,QAAQoG,OAAO,CAACjD,QAAQ;oBACvC,eAAenD,QAAQoE,MAAM;gBAC/B;YACF,GACA,IACEkC,sDAA0B,CAACC,IAAI,CAC7BC,gDAAmB,EACnB;oBACEC,KAAKzG;oBACL0G,YAAY;wBACVC,iBAAiB,CAACC;4BAChBd,sBAAsBc;wBACxB;wBACA,2EAA2E;wBAC3EC,cAAchF,CAAAA,qCAAAA,kBAAmB4D,OAAO,KAAI;4BAC1CC,eAAe;4BACfoB,0BAA0B;4BAC1BC,uBAAuB;wBACzB;oBACF;gBACF,GACA,IAAMpH,OAAOqH,OAAO,CAAChH,SAAS2F;QAGtC;QACA,OAAOhG,OAAOqH,OAAO,CAAChH,SAAS2F;IACjC;IAEA,yCAAyC;IACzC,IAAIE,YAAY,CAAEA,CAAAA,oBAAoBoB,QAAO,GAAI;QAC/C,MAAM,IAAIC,UAAU;IACtB;IAEA,IAAIrB,YAAYC,qBAAqB;QACnCD,SAASvF,OAAO,CAACsD,GAAG,CAAC,cAAckC;IACrC;IAEA;;;;;GAKC,GACD,MAAMqB,UAAUtB,4BAAAA,SAAUvF,OAAO,CAACG,GAAG,CAAC;IACtC,IAAIoF,YAAYsB,SAAS;QACvB,MAAMC,aAAa,IAAIhF,gBAAO,CAAC+E,SAAS;YACtCE,aAAa;YACb/G,SAASX,OAAOK,OAAO,CAACM,OAAO;YAC/B+B,YAAY1C,OAAOK,OAAO,CAACqC,UAAU;QACvC;QAEA,IAAI,CAAClB,QAAQC,GAAG,CAAC0C,kCAAkC,EAAE;YACnD,IAAIsD,WAAWE,IAAI,KAAKtH,QAAQoG,OAAO,CAACkB,IAAI,EAAE;gBAC5CF,WAAWnE,OAAO,GAAGA,WAAWmE,WAAWnE,OAAO;gBAClD4C,SAASvF,OAAO,CAACsD,GAAG,CAAC,wBAAwB2D,OAAOH;YACtD;QACF;QAEA;;;;KAIC,GACD,MAAMI,qBAAqBC,IAAAA,4BAAa,EACtCF,OAAOH,aACPG,OAAOpF;QAGT,IACEe,aACA,kDAAkD;QAClD,oDAAoD;QACpD,yCAAyC;QACzC,CACE/B,CAAAA,QAAQC,GAAG,CAACsG,0CAA0C,IACtDF,mBAAmBG,KAAK,CAAC,gBAAe,GAE1C;YACA9B,SAASvF,OAAO,CAACsD,GAAG,CAAC,oBAAoB4D;QAC3C;IACF;IAEA;;;;GAIC,GACD,MAAMI,WAAW/B,4BAAAA,SAAUvF,OAAO,CAACG,GAAG,CAAC;IACvC,IAAIoF,YAAY+B,YAAY,CAAClG,iBAAiB;QAC5C,MAAMmG,cAAc,IAAIzF,gBAAO,CAACwF,UAAU;YACxCP,aAAa;YACb/G,SAASX,OAAOK,OAAO,CAACM,OAAO;YAC/B+B,YAAY1C,OAAOK,OAAO,CAACqC,UAAU;QACvC;QAEA;;;KAGC,GACDwD,WAAW,IAAIoB,SAASpB,SAAS5B,IAAI,EAAE4B;QAEvC,IAAI,CAAC1E,QAAQC,GAAG,CAAC0C,kCAAkC,EAAE;YACnD,IAAI+D,YAAYP,IAAI,KAAKtH,QAAQoG,OAAO,CAACkB,IAAI,EAAE;gBAC7CO,YAAY5E,OAAO,GAAGA,WAAW4E,YAAY5E,OAAO;gBACpD4C,SAASvF,OAAO,CAACsD,GAAG,CAAC,YAAY2D,OAAOM;YAC1C;QACF;QAEA;;;;KAIC,GACD,IAAI3E,WAAW;YACb2C,SAASvF,OAAO,CAACwC,MAAM,CAAC;YACxB+C,SAASvF,OAAO,CAACsD,GAAG,CAClB,qBACA6D,IAAAA,4BAAa,EAACF,OAAOM,cAAcN,OAAOpF;QAE9C;IACF;IAEA,MAAM2F,gBAAgBjC,WAAWA,WAAWkC,sBAAY,CAACC,IAAI;IAE7D,iFAAiF;IACjF,MAAMC,4BAA4BH,cAAcxH,OAAO,CAACG,GAAG,CACzD;IAEF,MAAMyH,qBAA+B,EAAE;IACvC,IAAID,2BAA2B;QAC7B,KAAK,MAAM,CAACvH,KAAK6B,MAAM,IAAIe,cAAe;YACxCwE,cAAcxH,OAAO,CAACsD,GAAG,CAAC,CAAC,qBAAqB,EAAElD,IAAI,CAAC,EAAE6B;YACzD2F,mBAAmBC,IAAI,CAACzH;QAC1B;QAEA,IAAIwH,mBAAmBrF,MAAM,GAAG,GAAG;YACjCiF,cAAcxH,OAAO,CAACsD,GAAG,CACvB,iCACAqE,4BAA4B,MAAMC,mBAAmBE,IAAI,CAAC;QAE9D;IACF;IAEA,OAAO;QACLvC,UAAUiC;QACV3H,WAAWkI,QAAQC,GAAG,CAAC3C,KAAK,CAAC4C,2BAAe,CAAC;QAC7CC,cAAcxI,QAAQwI,YAAY;IACpC;AACF"}