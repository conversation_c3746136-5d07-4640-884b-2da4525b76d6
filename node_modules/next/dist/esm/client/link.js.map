{"version": 3, "sources": ["../../src/client/link.tsx"], "names": ["React", "resolveHref", "isLocalURL", "formatUrl", "isAbsoluteUrl", "addLocale", "RouterContext", "AppRouterContext", "useIntersection", "getDomainLocale", "addBasePath", "PrefetchKind", "prefetched", "Set", "prefetch", "router", "href", "as", "options", "appOptions", "isAppRouter", "window", "bypassPrefetchedCheck", "locale", "undefined", "prefetched<PERSON><PERSON>", "has", "add", "prefetchPromise", "Promise", "resolve", "catch", "err", "process", "env", "NODE_ENV", "isModifiedEvent", "event", "eventTarget", "currentTarget", "target", "getAttribute", "metaKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "nativeEvent", "which", "linkClicked", "e", "replace", "shallow", "scroll", "nodeName", "isAnchorNodeName", "toUpperCase", "preventDefault", "navigate", "routerScroll", "startTransition", "formatStringOrUrl", "urlObjOrString", "Link", "forwardRef", "LinkComponent", "props", "forwardedRef", "children", "hrefProp", "asProp", "childrenProp", "prefetchProp", "passHref", "onClick", "onMouseEnter", "onMouseEnterProp", "onTouchStart", "onTouchStartProp", "legacyBeh<PERSON>or", "restProps", "a", "pagesRouter", "useContext", "appRouter", "prefetchEnabled", "appPrefetchKind", "AUTO", "FULL", "createPropError", "args", "Error", "key", "expected", "actual", "requiredPropsGuard", "requiredProps", "Object", "keys", "for<PERSON>ach", "_", "optionalPropsGuard", "optionalProps", "valType", "hasWarned", "useRef", "current", "console", "warn", "pathname", "hasDynamicSegment", "split", "some", "segment", "startsWith", "endsWith", "useMemo", "resolvedHref", "resolvedAs", "previousHref", "previousAs", "child", "Children", "only", "type", "childRef", "ref", "setIntersectionRef", "isVisible", "resetVisible", "rootMargin", "setRef", "useCallback", "el", "useEffect", "kind", "childProps", "defaultPrevented", "priority", "__NEXT_LINK_NO_TOUCH_START", "cur<PERSON><PERSON><PERSON>", "localeDomain", "isLocaleDomain", "locales", "domainLocales", "defaultLocale", "cloneElement"], "mappings": "AAAA;;AAOA,OAAOA,WAAW,QAAO;AAEzB,SAASC,WAAW,QAAQ,iBAAgB;AAC5C,SAASC,UAAU,QAAQ,0CAAyC;AACpE,SAASC,SAAS,QAAQ,wCAAuC;AACjE,SAASC,aAAa,QAAQ,sBAAqB;AACnD,SAASC,SAAS,QAAQ,eAAc;AACxC,SAASC,aAAa,QAAQ,8CAA6C;AAC3E,SAASC,gBAAgB,QAAQ,kDAAiD;AAKlF,SAASC,eAAe,QAAQ,qBAAoB;AACpD,SAASC,eAAe,QAAQ,sBAAqB;AACrD,SAASC,WAAW,QAAQ,kBAAiB;AAC7C,SAASC,YAAY,QAAQ,mDAAkD;AA0F/E,MAAMC,aAAa,IAAIC;AAUvB,SAASC,SACPC,MAAsC,EACtCC,IAAY,EACZC,EAAU,EACVC,OAAwB,EACxBC,UAAoC,EACpCC,WAAoB;IAEpB,IAAI,OAAOC,WAAW,aAAa;QACjC;IACF;IAEA,gJAAgJ;IAChJ,IAAI,CAACD,eAAe,CAAClB,WAAWc,OAAO;QACrC;IACF;IAEA,4EAA4E;IAC5E,YAAY;IACZ,IAAI,CAACE,QAAQI,qBAAqB,EAAE;QAClC,MAAMC,SACJ,iEAAiE;QACjE,OAAOL,QAAQK,MAAM,KAAK,cACtBL,QAAQK,MAAM,GAEhB,YAAYR,SACVA,OAAOQ,MAAM,GACbC;QAEN,MAAMC,gBAAgBT,OAAO,MAAMC,KAAK,MAAMM;QAE9C,kEAAkE;QAClE,IAAIX,WAAWc,GAAG,CAACD,gBAAgB;YACjC;QACF;QAEA,+BAA+B;QAC/Bb,WAAWe,GAAG,CAACF;IACjB;IAEA,MAAMG,kBAAkBR,cACpB,AAACL,OAA6BD,QAAQ,CAACE,MAAMG,cAC7C,AAACJ,OAAsBD,QAAQ,CAACE,MAAMC,IAAIC;IAE9C,uDAAuD;IACvD,0DAA0D;IAC1D,sDAAsD;IACtD,yDAAyD;IACzDW,QAAQC,OAAO,CAACF,iBAAiBG,KAAK,CAAC,CAACC;QACtC,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YACzC,qCAAqC;YACrC,MAAMH;QACR;IACF;AACF;AAEA,SAASI,gBAAgBC,KAAuB;IAC9C,MAAMC,cAAcD,MAAME,aAAa;IACvC,MAAMC,SAASF,YAAYG,YAAY,CAAC;IACxC,OACE,AAACD,UAAUA,WAAW,WACtBH,MAAMK,OAAO,IACbL,MAAMM,OAAO,IACbN,MAAMO,QAAQ,IACdP,MAAMQ,MAAM,IAAI,6BAA6B;IAC5CR,MAAMS,WAAW,IAAIT,MAAMS,WAAW,CAACC,KAAK,KAAK;AAEtD;AAEA,SAASC,YACPC,CAAmB,EACnBlC,MAAsC,EACtCC,IAAY,EACZC,EAAU,EACViC,OAAiB,EACjBC,OAAiB,EACjBC,MAAgB,EAChB7B,MAAuB,EACvBH,WAAqB;IAErB,MAAM,EAAEiC,QAAQ,EAAE,GAAGJ,EAAEV,aAAa;IAEpC,kDAAkD;IAClD,MAAMe,mBAAmBD,SAASE,WAAW,OAAO;IAEpD,IACED,oBACClB,CAAAA,gBAAgBa,MACf,gJAAgJ;IAC/I,CAAC7B,eAAe,CAAClB,WAAWc,KAAK,GACpC;QACA,8CAA8C;QAC9C;IACF;IAEAiC,EAAEO,cAAc;IAEhB,MAAMC,WAAW;QACf,wEAAwE;QACxE,MAAMC,eAAeN,iBAAAA,SAAU;QAC/B,IAAI,oBAAoBrC,QAAQ;YAC9BA,MAAM,CAACmC,UAAU,YAAY,OAAO,CAAClC,MAAMC,IAAI;gBAC7CkC;gBACA5B;gBACA6B,QAAQM;YACV;QACF,OAAO;YACL3C,MAAM,CAACmC,UAAU,YAAY,OAAO,CAACjC,MAAMD,MAAM;gBAC/CoC,QAAQM;YACV;QACF;IACF;IAEA,IAAItC,aAAa;QACfpB,MAAM2D,eAAe,CAACF;IACxB,OAAO;QACLA;IACF;AACF;AAOA,SAASG,kBAAkBC,cAAkC;IAC3D,IAAI,OAAOA,mBAAmB,UAAU;QACtC,OAAOA;IACT;IAEA,OAAO1D,UAAU0D;AACnB;AAEA;;;;;;;CAOC,GACD,MAAMC,qBAAO9D,MAAM+D,UAAU,CAC3B,SAASC,cAAcC,KAAK,EAAEC,YAAY;IACxC,IAAIC;IAEJ,MAAM,EACJnD,MAAMoD,QAAQ,EACdnD,IAAIoD,MAAM,EACVF,UAAUG,YAAY,EACtBxD,UAAUyD,eAAe,IAAI,EAC7BC,QAAQ,EACRtB,OAAO,EACPC,OAAO,EACPC,MAAM,EACN7B,MAAM,EACNkD,OAAO,EACPC,cAAcC,gBAAgB,EAC9BC,cAAcC,gBAAgB,EAC9BC,iBAAiB,KAAK,EACtB,GAAGC,WACJ,GAAGd;IAEJE,WAAWG;IAEX,IACEQ,kBACC,CAAA,OAAOX,aAAa,YAAY,OAAOA,aAAa,QAAO,GAC5D;QACAA,yBAAW,KAACa;sBAAGb;;IACjB;IAEA,MAAMc,cAAcjF,MAAMkF,UAAU,CAAC5E;IACrC,MAAM6E,YAAYnF,MAAMkF,UAAU,CAAC3E;IACnC,MAAMQ,SAASkE,sBAAAA,cAAeE;IAE9B,0DAA0D;IAC1D,MAAM/D,cAAc,CAAC6D;IAErB,MAAMG,kBAAkBb,iBAAiB;IACzC;;;;;KAKC,GACD,MAAMc,kBACJd,iBAAiB,OAAO5D,aAAa2E,IAAI,GAAG3E,aAAa4E,IAAI;IAE/D,IAAItD,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,SAASqD,gBAAgBC,IAIxB;YACC,OAAO,IAAIC,MACT,AAAC,iCAA+BD,KAAKE,GAAG,GAAC,iBAAeF,KAAKG,QAAQ,GAAC,4BAA4BH,KAAKI,MAAM,GAAC,eAC3G,CAAA,OAAOxE,WAAW,cACf,qEACA,EAAC;QAEX;QAEA,sCAAsC;QACtC,MAAMyE,qBAAsD;YAC1D9E,MAAM;QACR;QACA,MAAM+E,gBAAqCC,OAAOC,IAAI,CACpDH;QAEFC,cAAcG,OAAO,CAAC,CAACP;YACrB,IAAIA,QAAQ,QAAQ;gBAClB,IACE1B,KAAK,CAAC0B,IAAI,IAAI,QACb,OAAO1B,KAAK,CAAC0B,IAAI,KAAK,YAAY,OAAO1B,KAAK,CAAC0B,IAAI,KAAK,UACzD;oBACA,MAAMH,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQ5B,KAAK,CAAC0B,IAAI,KAAK,OAAO,SAAS,OAAO1B,KAAK,CAAC0B,IAAI;oBAC1D;gBACF;YACF,OAAO;gBACL,sCAAsC;gBACtC,6DAA6D;gBAC7D,MAAMQ,IAAWR;YACnB;QACF;QAEA,sCAAsC;QACtC,MAAMS,qBAAsD;YAC1DnF,IAAI;YACJiC,SAAS;YACTE,QAAQ;YACRD,SAAS;YACTqB,UAAU;YACV1D,UAAU;YACVS,QAAQ;YACRkD,SAAS;YACTC,cAAc;YACdE,cAAc;YACdE,gBAAgB;QAClB;QACA,MAAMuB,gBAAqCL,OAAOC,IAAI,CACpDG;QAEFC,cAAcH,OAAO,CAAC,CAACP;YACrB,MAAMW,UAAU,OAAOrC,KAAK,CAAC0B,IAAI;YAEjC,IAAIA,QAAQ,MAAM;gBAChB,IAAI1B,KAAK,CAAC0B,IAAI,IAAIW,YAAY,YAAYA,YAAY,UAAU;oBAC9D,MAAMd,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQS;oBACV;gBACF;YACF,OAAO,IAAIX,QAAQ,UAAU;gBAC3B,IAAI1B,KAAK,CAAC0B,IAAI,IAAIW,YAAY,UAAU;oBACtC,MAAMd,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQS;oBACV;gBACF;YACF,OAAO,IACLX,QAAQ,aACRA,QAAQ,kBACRA,QAAQ,gBACR;gBACA,IAAI1B,KAAK,CAAC0B,IAAI,IAAIW,YAAY,YAAY;oBACxC,MAAMd,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQS;oBACV;gBACF;YACF,OAAO,IACLX,QAAQ,aACRA,QAAQ,YACRA,QAAQ,aACRA,QAAQ,cACRA,QAAQ,cACRA,QAAQ,kBACR;gBACA,IAAI1B,KAAK,CAAC0B,IAAI,IAAI,QAAQW,YAAY,WAAW;oBAC/C,MAAMd,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQS;oBACV;gBACF;YACF,OAAO;gBACL,sCAAsC;gBACtC,6DAA6D;gBAC7D,MAAMH,IAAWR;YACnB;QACF;QAEA,4FAA4F;QAC5F,sDAAsD;QACtD,MAAMY,YAAYvG,MAAMwG,MAAM,CAAC;QAC/B,IAAIvC,MAAMnD,QAAQ,IAAI,CAACyF,UAAUE,OAAO,IAAI,CAACrF,aAAa;YACxDmF,UAAUE,OAAO,GAAG;YACpBC,QAAQC,IAAI,CACV;QAEJ;IACF;IAEA,IAAI1E,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,IAAIf,eAAe,CAACiD,QAAQ;YAC1B,IAAIrD;YACJ,IAAI,OAAOoD,aAAa,UAAU;gBAChCpD,OAAOoD;YACT,OAAO,IACL,OAAOA,aAAa,YACpB,OAAOA,SAASwC,QAAQ,KAAK,UAC7B;gBACA5F,OAAOoD,SAASwC,QAAQ;YAC1B;YAEA,IAAI5F,MAAM;gBACR,MAAM6F,oBAAoB7F,KACvB8F,KAAK,CAAC,KACNC,IAAI,CAAC,CAACC,UAAYA,QAAQC,UAAU,CAAC,QAAQD,QAAQE,QAAQ,CAAC;gBAEjE,IAAIL,mBAAmB;oBACrB,MAAM,IAAInB,MACR,AAAC,mBAAiB1E,OAAK;gBAE3B;YACF;QACF;IACF;IAEA,MAAM,EAAEA,IAAI,EAAEC,EAAE,EAAE,GAAGjB,MAAMmH,OAAO,CAAC;QACjC,IAAI,CAAClC,aAAa;YAChB,MAAMmC,eAAexD,kBAAkBQ;YACvC,OAAO;gBACLpD,MAAMoG;gBACNnG,IAAIoD,SAAST,kBAAkBS,UAAU+C;YAC3C;QACF;QAEA,MAAM,CAACA,cAAcC,WAAW,GAAGpH,YACjCgF,aACAb,UACA;QAGF,OAAO;YACLpD,MAAMoG;YACNnG,IAAIoD,SACApE,YAAYgF,aAAaZ,UACzBgD,cAAcD;QACpB;IACF,GAAG;QAACnC;QAAab;QAAUC;KAAO;IAElC,MAAMiD,eAAetH,MAAMwG,MAAM,CAASxF;IAC1C,MAAMuG,aAAavH,MAAMwG,MAAM,CAASvF;IAExC,oFAAoF;IACpF,IAAIuG;IACJ,IAAI1C,gBAAgB;QAClB,IAAI7C,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;YAC1C,IAAIsC,SAAS;gBACXiC,QAAQC,IAAI,CACV,AAAC,oDAAoDvC,WAAS;YAElE;YACA,IAAIO,kBAAkB;gBACpB+B,QAAQC,IAAI,CACV,AAAC,yDAAyDvC,WAAS;YAEvE;YACA,IAAI;gBACFoD,QAAQxH,MAAMyH,QAAQ,CAACC,IAAI,CAACvD;YAC9B,EAAE,OAAOnC,KAAK;gBACZ,IAAI,CAACmC,UAAU;oBACb,MAAM,IAAIuB,MACR,AAAC,uDAAuDtB,WAAS;gBAErE;gBACA,MAAM,IAAIsB,MACR,AAAC,6DAA6DtB,WAAS,8FACpE,CAAA,OAAO/C,WAAW,cACf,sEACA,EAAC;YAEX;QACF,OAAO;YACLmG,QAAQxH,MAAMyH,QAAQ,CAACC,IAAI,CAACvD;QAC9B;IACF,OAAO;QACL,IAAIlC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;YAC1C,IAAI,CAACgC,4BAAD,AAACA,SAAkBwD,IAAI,MAAK,KAAK;gBACnC,MAAM,IAAIjC,MACR;YAEJ;QACF;IACF;IAEA,MAAMkC,WAAgB9C,iBAClB0C,SAAS,OAAOA,UAAU,YAAYA,MAAMK,GAAG,GAC/C3D;IAEJ,MAAM,CAAC4D,oBAAoBC,WAAWC,aAAa,GAAGxH,gBAAgB;QACpEyH,YAAY;IACd;IAEA,MAAMC,SAASlI,MAAMmI,WAAW,CAC9B,CAACC;QACC,4EAA4E;QAC5E,IAAIb,WAAWd,OAAO,KAAKxF,MAAMqG,aAAab,OAAO,KAAKzF,MAAM;YAC9DgH;YACAT,WAAWd,OAAO,GAAGxF;YACrBqG,aAAab,OAAO,GAAGzF;QACzB;QAEA8G,mBAAmBM;QACnB,IAAIR,UAAU;YACZ,IAAI,OAAOA,aAAa,YAAYA,SAASQ;iBACxC,IAAI,OAAOR,aAAa,UAAU;gBACrCA,SAASnB,OAAO,GAAG2B;YACrB;QACF;IACF,GACA;QAACnH;QAAI2G;QAAU5G;QAAMgH;QAAcF;KAAmB;IAGxD,2DAA2D;IAC3D9H,MAAMqI,SAAS,CAAC;QACd,gHAAgH;QAChH,IAAIpG,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YACzC;QACF;QAEA,IAAI,CAACpB,QAAQ;YACX;QACF;QAEA,2DAA2D;QAC3D,IAAI,CAACgH,aAAa,CAAC3C,iBAAiB;YAClC;QACF;QAEA,oBAAoB;QACpBtE,SACEC,QACAC,MACAC,IACA;YAAEM;QAAO,GACT;YACE+G,MAAMjD;QACR,GACAjE;IAEJ,GAAG;QACDH;QACAD;QACA+G;QACAxG;QACA6D;QACAH,+BAAAA,YAAa1D,MAAM;QACnBR;QACAK;QACAiE;KACD;IAED,MAAMkD,aAMF;QACFV,KAAKK;QACLzD,SAAQxB,CAAC;YACP,IAAIhB,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;gBACzC,IAAI,CAACc,GAAG;oBACN,MAAM,IAAIyC,MACP;gBAEL;YACF;YAEA,IAAI,CAACZ,kBAAkB,OAAOL,YAAY,YAAY;gBACpDA,QAAQxB;YACV;YAEA,IACE6B,kBACA0C,MAAMvD,KAAK,IACX,OAAOuD,MAAMvD,KAAK,CAACQ,OAAO,KAAK,YAC/B;gBACA+C,MAAMvD,KAAK,CAACQ,OAAO,CAACxB;YACtB;YAEA,IAAI,CAAClC,QAAQ;gBACX;YACF;YAEA,IAAIkC,EAAEuF,gBAAgB,EAAE;gBACtB;YACF;YAEAxF,YACEC,GACAlC,QACAC,MACAC,IACAiC,SACAC,SACAC,QACA7B,QACAH;QAEJ;QACAsD,cAAazB,CAAC;YACZ,IAAI,CAAC6B,kBAAkB,OAAOH,qBAAqB,YAAY;gBAC7DA,iBAAiB1B;YACnB;YAEA,IACE6B,kBACA0C,MAAMvD,KAAK,IACX,OAAOuD,MAAMvD,KAAK,CAACS,YAAY,KAAK,YACpC;gBACA8C,MAAMvD,KAAK,CAACS,YAAY,CAACzB;YAC3B;YAEA,IAAI,CAAClC,QAAQ;gBACX;YACF;YAEA,IACE,AAAC,CAAA,CAACqE,mBAAmBnD,QAAQC,GAAG,CAACC,QAAQ,KAAK,aAAY,KAC1Df,aACA;gBACA;YACF;YAEAN,SACEC,QACAC,MACAC,IACA;gBACEM;gBACAkH,UAAU;gBACV,gGAAgG;gBAChGnH,uBAAuB;YACzB,GACA;gBACEgH,MAAMjD;YACR,GACAjE;QAEJ;QACAwD,cAAc3C,QAAQC,GAAG,CAACwG,0BAA0B,GAChDlH,YACA,SAASoD,aAAa3B,CAAC;YACrB,IAAI,CAAC6B,kBAAkB,OAAOD,qBAAqB,YAAY;gBAC7DA,iBAAiB5B;YACnB;YAEA,IACE6B,kBACA0C,MAAMvD,KAAK,IACX,OAAOuD,MAAMvD,KAAK,CAACW,YAAY,KAAK,YACpC;gBACA4C,MAAMvD,KAAK,CAACW,YAAY,CAAC3B;YAC3B;YAEA,IAAI,CAAClC,QAAQ;gBACX;YACF;YAEA,IAAI,CAACqE,mBAAmBhE,aAAa;gBACnC;YACF;YAEAN,SACEC,QACAC,MACAC,IACA;gBACEM;gBACAkH,UAAU;gBACV,gGAAgG;gBAChGnH,uBAAuB;YACzB,GACA;gBACEgH,MAAMjD;YACR,GACAjE;QAEJ;IACN;IAEA,6FAA6F;IAC7F,wFAAwF;IACxF,oFAAoF;IACpF,IAAIhB,cAAca,KAAK;QACrBsH,WAAWvH,IAAI,GAAGC;IACpB,OAAO,IACL,CAAC6D,kBACDN,YACCgD,MAAMG,IAAI,KAAK,OAAO,CAAE,CAAA,UAAUH,MAAMvD,KAAK,AAAD,GAC7C;QACA,MAAM0E,YACJ,OAAOpH,WAAW,cAAcA,SAAS0D,+BAAAA,YAAa1D,MAAM;QAE9D,uEAAuE;QACvE,uEAAuE;QACvE,MAAMqH,eACJ3D,CAAAA,+BAAAA,YAAa4D,cAAc,KAC3BpI,gBACEQ,IACA0H,WACA1D,+BAAAA,YAAa6D,OAAO,EACpB7D,+BAAAA,YAAa8D,aAAa;QAG9BR,WAAWvH,IAAI,GACb4H,gBACAlI,YAAYL,UAAUY,IAAI0H,WAAW1D,+BAAAA,YAAa+D,aAAa;IACnE;IAEA,OAAOlE,+BACL9E,MAAMiJ,YAAY,CAACzB,OAAOe,4BAE1B,KAACvD;QAAG,GAAGD,SAAS;QAAG,GAAGwD,UAAU;kBAC7BpE;;AAGP;AAGF,eAAeL,KAAI"}