{"version": 3, "sources": ["../../../../src/client/components/router-reducer/refetch-inactive-parallel-segments.ts"], "names": ["applyFlightData", "fetchServerResponse", "PAGE_SEGMENT_KEY", "refreshInactiveParallelSegments", "options", "fetchedSegments", "Set", "refreshInactiveParallelSegmentsImpl", "rootTree", "updatedTree", "state", "updatedCache", "includeNextUrl", "parallelRoutes", "refetch<PERSON>ath", "refetch<PERSON><PERSON><PERSON>", "fetchPromises", "location", "pathname", "search", "has", "add", "fetchPromise", "URL", "origin", "nextUrl", "buildId", "then", "fetchResponse", "flightData", "flightDataPath", "push", "key", "parallelFetchPromise", "Promise", "all", "addRefreshMarkerToActiveParallelSegments", "tree", "path", "segment", "includes"], "mappings": "AAGA,SAASA,eAAe,QAAQ,sBAAqB;AACrD,SAASC,mBAAmB,QAAQ,0BAAyB;AAC7D,SAASC,gBAAgB,QAAQ,8BAA6B;AAS9D;;;;;;;;;;CAUC,GACD,OAAO,eAAeC,gCACpBC,OAAwC;IAExC,MAAMC,kBAAkB,IAAIC;IAC5B,MAAMC,oCAAoC;QACxC,GAAGH,OAAO;QACVI,UAAUJ,QAAQK,WAAW;QAC7BJ;IACF;AACF;AAEA,eAAeE,oCAAoC,KAUlD;IAVkD,IAAA,EACjDG,KAAK,EACLD,WAAW,EACXE,YAAY,EACZC,cAAc,EACdP,eAAe,EACfG,WAAWC,WAAW,EAIvB,GAVkD;IAWjD,MAAM,GAAGI,gBAAgBC,aAAaC,cAAc,GAAGN;IACvD,MAAMO,gBAAgB,EAAE;IAExB,IACEF,eACAA,gBAAgBG,SAASC,QAAQ,GAAGD,SAASE,MAAM,IACnDJ,kBAAkB,aAClB,4FAA4F;IAC5F,sDAAsD;IACtD,CAACV,gBAAgBe,GAAG,CAACN,cACrB;QACAT,gBAAgBgB,GAAG,CAACP,aAAa,2BAA2B;;QAE5D,wHAAwH;QACxH,kIAAkI;QAClI,MAAMQ,eAAerB,oBACnB,IAAIsB,IAAIT,aAAaG,SAASO,MAAM,GACpC,gGAAgG;QAChG,8HAA8H;QAC9H;YAAChB,QAAQ,CAAC,EAAE;YAAEA,QAAQ,CAAC,EAAE;YAAEA,QAAQ,CAAC,EAAE;YAAE;SAAU,EAClDI,iBAAiBF,MAAMe,OAAO,GAAG,MACjCf,MAAMgB,OAAO,EACbC,IAAI,CAAC,CAACC;YACN,MAAMC,aAAaD,aAAa,CAAC,EAAE;YACnC,IAAI,OAAOC,eAAe,UAAU;gBAClC,KAAK,MAAMC,kBAAkBD,WAAY;oBACvC,wFAAwF;oBACxF,4GAA4G;oBAC5G,4EAA4E;oBAC5E7B,gBAAgBW,cAAcA,cAAcmB;gBAC9C;YACF,OAAO;YACL,4GAA4G;YAC5G,+GAA+G;YAC/G,sEAAsE;YACxE;QACF;QAEAd,cAAce,IAAI,CAACT;IACrB;IAEA,IAAK,MAAMU,OAAOnB,eAAgB;QAChC,MAAMoB,uBAAuB1B,oCAAoC;YAC/DG;YACAD,aAAaI,cAAc,CAACmB,IAAI;YAChCrB;YACAC;YACAP;YACAG;QACF;QAEAQ,cAAce,IAAI,CAACE;IACrB;IAEA,MAAMC,QAAQC,GAAG,CAACnB;AACpB;AAEA;;;;;CAKC,GACD,OAAO,SAASoB,yCACdC,IAAuB,EACvBC,IAAY;IAEZ,MAAM,CAACC,SAAS1B,kBAAkBE,cAAc,GAAGsB;IACnD,oGAAoG;IACpG,IAAIE,QAAQC,QAAQ,CAACtC,qBAAqBa,kBAAkB,WAAW;QACrEsB,IAAI,CAAC,EAAE,GAAGC;QACVD,IAAI,CAAC,EAAE,GAAG;IACZ;IAEA,IAAK,MAAML,OAAOnB,eAAgB;QAChCuB,yCAAyCvB,cAAc,CAACmB,IAAI,EAAEM;IAChE;AACF"}