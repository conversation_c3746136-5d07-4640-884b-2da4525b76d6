{"version": 3, "sources": ["../../../../src/client/components/router-reducer/create-initial-router-state.ts"], "names": ["createHrefFromUrl", "fillLazyItemsTillLeafWithHead", "extractPathFromFlightRouterState", "createPrefetchCacheEntryForInitialLoad", "PrefetchKind", "addRefreshMarkerToActiveParallelSegments", "createInitialRouterState", "buildId", "initialTree", "initialSeedData", "initialCanonicalUrl", "initialParallelRoutes", "location", "initialHead", "couldBeIntercepted", "isServer", "rsc", "cache", "lazyData", "prefetchRsc", "head", "prefetchHead", "parallelRoutes", "Map", "lazyDataResolved", "loading", "canonicalUrl", "prefetchCache", "size", "undefined", "initialState", "tree", "pushRef", "pendingPush", "mpaNavigation", "preserveCustomHistoryState", "focusAndScrollRef", "apply", "onlyHashChange", "hashFragment", "segmentPaths", "nextUrl", "pathname", "url", "URL", "origin", "initialFlightData", "kind", "AUTO", "data"], "mappings": "AAQA,SAASA,iBAAiB,QAAQ,yBAAwB;AAC1D,SAASC,6BAA6B,QAAQ,wCAAuC;AACrF,SAASC,gCAAgC,QAAQ,yBAAwB;AACzE,SAASC,sCAAsC,QAAQ,yBAAwB;AAC/E,SAASC,YAAY,QAAiC,yBAAwB;AAC9E,SAASC,wCAAwC,QAAQ,uCAAsC;AAa/F,OAAO,SAASC,yBAAyB,KASV;IATU,IAAA,EACvCC,OAAO,EACPC,WAAW,EACXC,eAAe,EACfC,mBAAmB,EACnBC,qBAAqB,EACrBC,QAAQ,EACRC,WAAW,EACXC,kBAAkB,EACW,GATU;IAUvC,MAAMC,WAAW,CAACH;IAClB,MAAMI,MAAMP,eAAe,CAAC,EAAE;IAE9B,MAAMQ,QAAmB;QACvBC,UAAU;QACVF,KAAKA;QACLG,aAAa;QACbC,MAAM;QACNC,cAAc;QACd,oJAAoJ;QACpJC,gBAAgBP,WAAW,IAAIQ,QAAQZ;QACvCa,kBAAkB;QAClBC,SAAShB,eAAe,CAAC,EAAE;IAC7B;IAEA,MAAMiB,eACJ,6EAA6E;IAC7E,kJAAkJ;IAClJd,WAEIZ,kBAAkBY,YAClBF;IAENL,yCAAyCG,aAAakB;IAEtD,MAAMC,gBAAgB,IAAIJ;IAE1B,yEAAyE;IACzE,IAAIZ,0BAA0B,QAAQA,sBAAsBiB,IAAI,KAAK,GAAG;QACtE3B,8BACEgB,OACAY,WACArB,aACAC,iBACAI;IAEJ;QAsBI,sEAAsE;IACrEX;IArBL,MAAM4B,eAAe;QACnBvB;QACAwB,MAAMvB;QACNS;QACAU;QACAK,SAAS;YACPC,aAAa;YACbC,eAAe;YACf,mEAAmE;YACnE,gFAAgF;YAChFC,4BAA4B;QAC9B;QACAC,mBAAmB;YACjBC,OAAO;YACPC,gBAAgB;YAChBC,cAAc;YACdC,cAAc,EAAE;QAClB;QACAd;QACAe,SAEE,CAACvC,OAAAA,iCAAiCM,iBAAgBI,4BAAAA,SAAU8B,QAAQ,aAAnExC,OACD;IACJ;IAEA,IAAIU,UAAU;QACZ,iDAAiD;QACjD,gFAAgF;QAChF,+FAA+F;QAC/F,MAAM+B,MAAM,IAAIC,IAAIhC,SAAS8B,QAAQ,EAAE9B,SAASiC,MAAM;QAEtD,MAAMC,oBAAgC;YAAC;gBAAC;gBAAItC;gBAAa;gBAAM;aAAK;SAAC;QACrEL,uCAAuC;YACrCwC;YACAI,MAAM3C,aAAa4C,IAAI;YACvBC,MAAM;gBAACH;gBAAmBjB;gBAAW;gBAAOf;aAAmB;YAC/DiB,MAAMD,aAAaC,IAAI;YACvBJ,eAAeG,aAAaH,aAAa;YACzCc,SAASX,aAAaW,OAAO;QAC/B;IACF;IAEA,OAAOX;AACT"}