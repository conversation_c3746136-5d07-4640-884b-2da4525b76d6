{"version": 3, "sources": ["../../../src/server/web/adapter.ts"], "names": ["PageSignatureError", "fromNodeOutgoingHttpHeaders", "NextFetchEvent", "NextRequest", "NextResponse", "relativizeURL", "waitUntilSymbol", "NextURL", "stripInternalSearchParams", "normalizeRscURL", "FLIGHT_PARAMETERS", "NEXT_QUERY_PARAM_PREFIX", "ensureInstrumentationRegistered", "RequestAsyncStorageWrapper", "requestAsyncStorage", "getTracer", "MiddlewareSpan", "NextRequestHint", "constructor", "params", "input", "init", "sourcePage", "page", "request", "respondWith", "waitUntil", "headersGetter", "keys", "headers", "Array", "from", "get", "key", "undefined", "propagator", "fn", "tracer", "withPropagatedContext", "testApisIntercepted", "ensureTestApisIntercepted", "process", "env", "NEXT_PRIVATE_TEST_PROXY", "interceptTestApis", "wrapRequestHandler", "require", "adapter", "isEdgeRendering", "self", "__BUILD_MANIFEST", "prerenderManifest", "__PRERENDER_MANIFEST", "JSON", "parse", "url", "requestUrl", "nextConfig", "searchParams", "value", "getAll", "startsWith", "normalizedKey", "substring", "length", "delete", "val", "append", "buildId", "isDataReq", "pathname", "requestHeaders", "flightHeaders", "Map", "param", "toString", "toLowerCase", "set", "normalizeUrl", "__NEXT_NO_MIDDLEWARE_URL_NORMALIZE", "URL", "body", "geo", "ip", "method", "signal", "Object", "defineProperty", "enumerable", "globalThis", "__incrementalCache", "IncrementalCache", "appDir", "fetchCache", "minimalMode", "NODE_ENV", "fetchCacheKeyPrefix", "__NEXT_FETCH_CACHE_KEY_PREFIX", "dev", "requestProtocol", "getPrerenderManifest", "version", "routes", "dynamicRoutes", "notFoundRoutes", "preview", "previewModeId", "event", "response", "cookiesFromResponse", "isMiddleware", "trace", "execute", "spanName", "nextUrl", "attributes", "wrap", "req", "renderOpts", "onUpdateCookies", "cookies", "previewProps", "previewModeEncryptionKey", "previewModeSigningKey", "handler", "Response", "TypeError", "rewrite", "rewriteUrl", "forceLocale", "host", "String", "relativizedRewrite", "__NEXT_EXTERNAL_MIDDLEWARE_REWRITE_RESOLVE", "match", "redirect", "redirectURL", "finalResponse", "next", "middlewareOverrideHeaders", "overwrittenHeaders", "push", "join", "Promise", "all", "fetchMetrics"], "mappings": "AAGA,SAASA,kBAAkB,QAAQ,UAAS;AAC5C,SAASC,2BAA2B,QAAQ,UAAS;AACrD,SAASC,cAAc,QAAQ,+BAA8B;AAC7D,SAASC,WAAW,QAAQ,2BAA0B;AACtD,SAASC,YAAY,QAAQ,4BAA2B;AACxD,SAASC,aAAa,QAAQ,+CAA8C;AAC5E,SAASC,eAAe,QAAQ,+BAA8B;AAC9D,SAASC,OAAO,QAAQ,aAAY;AACpC,SAASC,yBAAyB,QAAQ,oBAAmB;AAC7D,SAASC,eAAe,QAAQ,0CAAyC;AACzE,SAASC,iBAAiB,QAAQ,6CAA4C;AAC9E,SAASC,uBAAuB,QAAQ,sBAAqB;AAC7D,SAASC,+BAA+B,QAAQ,YAAW;AAC3D,SAASC,0BAA0B,QAAQ,iDAAgD;AAC3F,SAASC,mBAAmB,QAAQ,yDAAwD;AAC5F,SAASC,SAAS,QAAQ,sBAAqB;AAE/C,SAASC,cAAc,QAAQ,yBAAwB;AAEvD,OAAO,MAAMC,wBAAwBd;IAInCe,YAAYC,MAIX,CAAE;QACD,KAAK,CAACA,OAAOC,KAAK,EAAED,OAAOE,IAAI;QAC/B,IAAI,CAACC,UAAU,GAAGH,OAAOI,IAAI;IAC/B;IAEA,IAAIC,UAAU;QACZ,MAAM,IAAIxB,mBAAmB;YAAEuB,MAAM,IAAI,CAACD,UAAU;QAAC;IACvD;IAEAG,cAAc;QACZ,MAAM,IAAIzB,mBAAmB;YAAEuB,MAAM,IAAI,CAACD,UAAU;QAAC;IACvD;IAEAI,YAAY;QACV,MAAM,IAAI1B,mBAAmB;YAAEuB,MAAM,IAAI,CAACD,UAAU;QAAC;IACvD;AACF;AAEA,MAAMK,gBAAwC;IAC5CC,MAAM,CAACC,UAAYC,MAAMC,IAAI,CAACF,QAAQD,IAAI;IAC1CI,KAAK,CAACH,SAASI,MAAQJ,QAAQG,GAAG,CAACC,QAAQC;AAC7C;AASA,IAAIC,aAA8D,CAChEX,SACAY;IAEA,MAAMC,SAAStB;IACf,OAAOsB,OAAOC,qBAAqB,CAACd,QAAQK,OAAO,EAAEO,IAAIT;AAC3D;AAEA,IAAIY,sBAAsB;AAE1B,SAASC;IACP,IAAI,CAACD,qBAAqB;QACxBA,sBAAsB;QACtB,IAAIE,QAAQC,GAAG,CAACC,uBAAuB,KAAK,QAAQ;YAClD,MAAM,EACJC,iBAAiB,EACjBC,kBAAkB,EACnB,GAAGC,QAAQ;YACZF;YACAT,aAAaU,mBAAmBV;QAClC;IACF;AACF;AAEA,OAAO,eAAeY,QACpB5B,MAAsB;IAEtBqB;IACA,MAAM5B;IAEN,yCAAyC;IACzC,MAAMoC,kBAAkB,OAAOC,KAAKC,gBAAgB,KAAK;IACzD,MAAMC,oBACJ,OAAOF,KAAKG,oBAAoB,KAAK,WACjCC,KAAKC,KAAK,CAACL,KAAKG,oBAAoB,IACpClB;IAENf,OAAOK,OAAO,CAAC+B,GAAG,GAAG9C,gBAAgBU,OAAOK,OAAO,CAAC+B,GAAG;IAEvD,MAAMC,aAAa,IAAIjD,QAAQY,OAAOK,OAAO,CAAC+B,GAAG,EAAE;QACjD1B,SAASV,OAAOK,OAAO,CAACK,OAAO;QAC/B4B,YAAYtC,OAAOK,OAAO,CAACiC,UAAU;IACvC;IAEA,yIAAyI;IACzI,4CAA4C;IAC5C,MAAM7B,OAAO;WAAI4B,WAAWE,YAAY,CAAC9B,IAAI;KAAG;IAChD,KAAK,MAAMK,OAAOL,KAAM;QACtB,MAAM+B,QAAQH,WAAWE,YAAY,CAACE,MAAM,CAAC3B;QAE7C,IACEA,QAAQtB,2BACRsB,IAAI4B,UAAU,CAAClD,0BACf;YACA,MAAMmD,gBAAgB7B,IAAI8B,SAAS,CAACpD,wBAAwBqD,MAAM;YAClER,WAAWE,YAAY,CAACO,MAAM,CAACH;YAE/B,KAAK,MAAMI,OAAOP,MAAO;gBACvBH,WAAWE,YAAY,CAACS,MAAM,CAACL,eAAeI;YAChD;YACAV,WAAWE,YAAY,CAACO,MAAM,CAAChC;QACjC;IACF;IAEA,4DAA4D;IAC5D,MAAMmC,UAAUZ,WAAWY,OAAO;IAClCZ,WAAWY,OAAO,GAAG;IAErB,MAAMC,YAAYlD,OAAOK,OAAO,CAACK,OAAO,CAAC,gBAAgB;IAEzD,IAAIwC,aAAab,WAAWc,QAAQ,KAAK,UAAU;QACjDd,WAAWc,QAAQ,GAAG;IACxB;IAEA,MAAMC,iBAAiBtE,4BAA4BkB,OAAOK,OAAO,CAACK,OAAO;IACzE,MAAM2C,gBAAgB,IAAIC;IAC1B,oDAAoD;IACpD,IAAI,CAACzB,iBAAiB;QACpB,KAAK,MAAM0B,SAAShE,kBAAmB;YACrC,MAAMuB,MAAMyC,MAAMC,QAAQ,GAAGC,WAAW;YACxC,MAAMjB,QAAQY,eAAevC,GAAG,CAACC;YACjC,IAAI0B,OAAO;gBACTa,cAAcK,GAAG,CAAC5C,KAAKsC,eAAevC,GAAG,CAACC;gBAC1CsC,eAAeN,MAAM,CAAChC;YACxB;QACF;IACF;IAEA,MAAM6C,eAAerC,QAAQC,GAAG,CAACqC,kCAAkC,GAC/D,IAAIC,IAAI7D,OAAOK,OAAO,CAAC+B,GAAG,IAC1BC;IAEJ,MAAMhC,UAAU,IAAIP,gBAAgB;QAClCM,MAAMJ,OAAOI,IAAI;QACjB,mDAAmD;QACnDH,OAAOZ,0BAA0BsE,cAAc,MAAMH,QAAQ;QAC7DtD,MAAM;YACJ4D,MAAM9D,OAAOK,OAAO,CAACyD,IAAI;YACzBC,KAAK/D,OAAOK,OAAO,CAAC0D,GAAG;YACvBrD,SAAS0C;YACTY,IAAIhE,OAAOK,OAAO,CAAC2D,EAAE;YACrBC,QAAQjE,OAAOK,OAAO,CAAC4D,MAAM;YAC7B3B,YAAYtC,OAAOK,OAAO,CAACiC,UAAU;YACrC4B,QAAQlE,OAAOK,OAAO,CAAC6D,MAAM;QAC/B;IACF;IAEA;;;;GAIC,GACD,IAAIhB,WAAW;QACbiB,OAAOC,cAAc,CAAC/D,SAAS,YAAY;YACzCgE,YAAY;YACZ7B,OAAO;QACT;IACF;IAEA,IACE,CAAC,AAAC8B,WAAmBC,kBAAkB,IACvC,AAACvE,OAAewE,gBAAgB,EAChC;QACEF,WAAmBC,kBAAkB,GAAG,IAAI,AAC5CvE,OACAwE,gBAAgB,CAAC;YACjBC,QAAQ;YACRC,YAAY;YACZC,aAAarD,QAAQC,GAAG,CAACqD,QAAQ,KAAK;YACtCC,qBAAqBvD,QAAQC,GAAG,CAACuD,6BAA6B;YAC9DC,KAAKzD,QAAQC,GAAG,CAACqD,QAAQ,KAAK;YAC9BxB,gBAAgBpD,OAAOK,OAAO,CAACK,OAAO;YACtCsE,iBAAiB;YACjBC,sBAAsB;gBACpB,OAAO;oBACLC,SAAS,CAAC;oBACVC,QAAQ,CAAC;oBACTC,eAAe,CAAC;oBAChBC,gBAAgB,EAAE;oBAClBC,SAAS;wBACPC,eAAe;oBACjB;gBACF;YACF;QACF;IACF;IAEA,MAAMC,QAAQ,IAAIzG,eAAe;QAAEsB;QAASD,MAAMJ,OAAOI,IAAI;IAAC;IAC9D,IAAIqF;IACJ,IAAIC;IAEJD,WAAW,MAAMzE,WAAWX,SAAS;QACnC,8DAA8D;QAC9D,MAAMsF,eACJ3F,OAAOI,IAAI,KAAK,iBAAiBJ,OAAOI,IAAI,KAAK;QACnD,IAAIuF,cAAc;YAChB,OAAO/F,YAAYgG,KAAK,CACtB/F,eAAegG,OAAO,EACtB;gBACEC,UAAU,CAAC,WAAW,EAAEzF,QAAQ4D,MAAM,CAAC,CAAC,EAAE5D,QAAQ0F,OAAO,CAAC5C,QAAQ,CAAC,CAAC;gBACpE6C,YAAY;oBACV,eAAe3F,QAAQ0F,OAAO,CAAC5C,QAAQ;oBACvC,eAAe9C,QAAQ4D,MAAM;gBAC/B;YACF,GACA,IACEvE,2BAA2BuG,IAAI,CAC7BtG,qBACA;oBACEuG,KAAK7F;oBACL8F,YAAY;wBACVC,iBAAiB,CAACC;4BAChBX,sBAAsBW;wBACxB;wBACA,2EAA2E;wBAC3EC,cAActE,CAAAA,qCAAAA,kBAAmBsD,OAAO,KAAI;4BAC1CC,eAAe;4BACfgB,0BAA0B;4BAC1BC,uBAAuB;wBACzB;oBACF;gBACF,GACA,IAAMxG,OAAOyG,OAAO,CAACpG,SAASmF;QAGtC;QACA,OAAOxF,OAAOyG,OAAO,CAACpG,SAASmF;IACjC;IAEA,yCAAyC;IACzC,IAAIC,YAAY,CAAEA,CAAAA,oBAAoBiB,QAAO,GAAI;QAC/C,MAAM,IAAIC,UAAU;IACtB;IAEA,IAAIlB,YAAYC,qBAAqB;QACnCD,SAAS/E,OAAO,CAACgD,GAAG,CAAC,cAAcgC;IACrC;IAEA;;;;;GAKC,GACD,MAAMkB,UAAUnB,4BAAAA,SAAU/E,OAAO,CAACG,GAAG,CAAC;IACtC,IAAI4E,YAAYmB,SAAS;QACvB,MAAMC,aAAa,IAAIzH,QAAQwH,SAAS;YACtCE,aAAa;YACbpG,SAASV,OAAOK,OAAO,CAACK,OAAO;YAC/B4B,YAAYtC,OAAOK,OAAO,CAACiC,UAAU;QACvC;QAEA,IAAI,CAAChB,QAAQC,GAAG,CAACqC,kCAAkC,EAAE;YACnD,IAAIiD,WAAWE,IAAI,KAAK1G,QAAQ0F,OAAO,CAACgB,IAAI,EAAE;gBAC5CF,WAAW5D,OAAO,GAAGA,WAAW4D,WAAW5D,OAAO;gBAClDwC,SAAS/E,OAAO,CAACgD,GAAG,CAAC,wBAAwBsD,OAAOH;YACtD;QACF;QAEA;;;;KAIC,GACD,MAAMI,qBAAqB/H,cACzB8H,OAAOH,aACPG,OAAO3E;QAGT,IACEa,aACA,kDAAkD;QAClD,oDAAoD;QACpD,yCAAyC;QACzC,CACE5B,CAAAA,QAAQC,GAAG,CAAC2F,0CAA0C,IACtDD,mBAAmBE,KAAK,CAAC,gBAAe,GAE1C;YACA1B,SAAS/E,OAAO,CAACgD,GAAG,CAAC,oBAAoBuD;QAC3C;IACF;IAEA;;;;GAIC,GACD,MAAMG,WAAW3B,4BAAAA,SAAU/E,OAAO,CAACG,GAAG,CAAC;IACvC,IAAI4E,YAAY2B,YAAY,CAACvF,iBAAiB;QAC5C,MAAMwF,cAAc,IAAIjI,QAAQgI,UAAU;YACxCN,aAAa;YACbpG,SAASV,OAAOK,OAAO,CAACK,OAAO;YAC/B4B,YAAYtC,OAAOK,OAAO,CAACiC,UAAU;QACvC;QAEA;;;KAGC,GACDmD,WAAW,IAAIiB,SAASjB,SAAS3B,IAAI,EAAE2B;QAEvC,IAAI,CAACnE,QAAQC,GAAG,CAACqC,kCAAkC,EAAE;YACnD,IAAIyD,YAAYN,IAAI,KAAK1G,QAAQ0F,OAAO,CAACgB,IAAI,EAAE;gBAC7CM,YAAYpE,OAAO,GAAGA,WAAWoE,YAAYpE,OAAO;gBACpDwC,SAAS/E,OAAO,CAACgD,GAAG,CAAC,YAAYsD,OAAOK;YAC1C;QACF;QAEA;;;;KAIC,GACD,IAAInE,WAAW;YACbuC,SAAS/E,OAAO,CAACoC,MAAM,CAAC;YACxB2C,SAAS/E,OAAO,CAACgD,GAAG,CAClB,qBACAxE,cAAc8H,OAAOK,cAAcL,OAAO3E;QAE9C;IACF;IAEA,MAAMiF,gBAAgB7B,WAAWA,WAAWxG,aAAasI,IAAI;IAE7D,iFAAiF;IACjF,MAAMC,4BAA4BF,cAAc5G,OAAO,CAACG,GAAG,CACzD;IAEF,MAAM4G,qBAA+B,EAAE;IACvC,IAAID,2BAA2B;QAC7B,KAAK,MAAM,CAAC1G,KAAK0B,MAAM,IAAIa,cAAe;YACxCiE,cAAc5G,OAAO,CAACgD,GAAG,CAAC,CAAC,qBAAqB,EAAE5C,IAAI,CAAC,EAAE0B;YACzDiF,mBAAmBC,IAAI,CAAC5G;QAC1B;QAEA,IAAI2G,mBAAmB5E,MAAM,GAAG,GAAG;YACjCyE,cAAc5G,OAAO,CAACgD,GAAG,CACvB,iCACA8D,4BAA4B,MAAMC,mBAAmBE,IAAI,CAAC;QAE9D;IACF;IAEA,OAAO;QACLlC,UAAU6B;QACV/G,WAAWqH,QAAQC,GAAG,CAACrC,KAAK,CAACrG,gBAAgB;QAC7C2I,cAAczH,QAAQyH,YAAY;IACpC;AACF"}