{"version": 3, "sources": ["../../../../src/server/web/spec-extension/unstable-cache.ts"], "names": ["CACHE_ONE_YEAR", "addImplicitTags", "validateRevalidate", "validateTags", "staticGenerationAsyncStorage", "noStoreFetchIdx", "cacheNewResult", "result", "incrementalCache", "cache<PERSON>ey", "tags", "revalidate", "fetchIdx", "fetchUrl", "set", "kind", "data", "headers", "body", "JSON", "stringify", "status", "url", "fetchCache", "unstable_cache", "cb", "keyParts", "options", "Error", "toString", "name", "fixedKey", "Array", "isArray", "join", "cachedCb", "args", "store", "getStore", "maybeIncrementalCache", "globalThis", "__incrementalCache", "invocation<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nextFetchId", "slice", "tag", "includes", "push", "implicitTags", "isOnDemandRevalidate", "isDraftMode", "cacheEntry", "get", "kindHint", "softTags", "value", "console", "error", "cachedResponse", "undefined", "parse", "isStale", "pendingRevalidates", "run", "isUnstableCacheCallback", "then", "catch", "err", "urlPathname", "isStaticGeneration", "prerenderState"], "mappings": "AAEA,SAASA,cAAc,QAAQ,yBAAwB;AACvD,SACEC,eAAe,EACfC,kBAAkB,EAClBC,YAAY,QACP,wBAAuB;AAC9B,SAASC,4BAA4B,QAAQ,sEAAqE;AAIlH,IAAIC,kBAAkB;AAEtB,eAAeC,eACbC,MAAS,EACTC,gBAAkC,EAClCC,QAAgB,EAChBC,IAAc,EACdC,UAAsC,EACtCC,QAAgB,EAChBC,QAAgB;IAEhB,MAAML,iBAAiBM,GAAG,CACxBL,UACA;QACEM,MAAM;QACNC,MAAM;YACJC,SAAS,CAAC;YACV,gCAAgC;YAChCC,MAAMC,KAAKC,SAAS,CAACb;YACrBc,QAAQ;YACRC,KAAK;QACP;QACAX,YAAY,OAAOA,eAAe,WAAWX,iBAAiBW;IAChE,GACA;QACEA;QACAY,YAAY;QACZb;QACAE;QACAC;IACF;IAEF;AACF;AAEA;;;;CAIC,GACD,OAAO,SAASW,eACdC,EAAK,EACLC,QAAmB,EACnBC,UAGI,CAAC,CAAC;IAEN,IAAIA,QAAQhB,UAAU,KAAK,GAAG;QAC5B,MAAM,IAAIiB,MACR,CAAC,wFAAwF,EAAEH,GAAGI,QAAQ,GAAG,CAAC;IAE9G;IAEA,uCAAuC;IACvC,MAAMnB,OAAOiB,QAAQjB,IAAI,GACrBP,aAAawB,QAAQjB,IAAI,EAAE,CAAC,eAAe,EAAEe,GAAGI,QAAQ,GAAG,CAAC,IAC5D,EAAE;IAEN,kCAAkC;IAClC3B,mBACEyB,QAAQhB,UAAU,EAClB,CAAC,eAAe,EAAEc,GAAGK,IAAI,IAAIL,GAAGI,QAAQ,GAAG,CAAC;IAG9C,wFAAwF;IACxF,wDAAwD;IACxD,mDAAmD;IACnD,8DAA8D;IAC9D,8FAA8F;IAC9F,iGAAiG;IACjG,gBAAgB;IAChB,MAAME,WAAW,CAAC,EAAEN,GAAGI,QAAQ,GAAG,CAAC,EACjCG,MAAMC,OAAO,CAACP,aAAaA,SAASQ,IAAI,CAAC,KAC1C,CAAC;IAEF,MAAMC,WAAW,OAAO,GAAGC;QACzB,MAAMC,QAAQjC,6BAA6BkC,QAAQ;QAEnD,mEAAmE;QACnE,MAAMC,wBAGJF,CAAAA,yBAAAA,MAAO7B,gBAAgB,KAAI,AAACgC,WAAmBC,kBAAkB;QAEnE,IAAI,CAACF,uBAAuB;YAC1B,MAAM,IAAIX,MACR,CAAC,sDAAsD,EAAEH,GAAGI,QAAQ,GAAG,CAAC;QAE5E;QACA,MAAMrB,mBAAmB+B;QAEzB,gEAAgE;QAChE,4FAA4F;QAC5F,gDAAgD;QAChD,MAAMG,gBAAgB,CAAC,EAAEX,SAAS,CAAC,EAAEZ,KAAKC,SAAS,CAACgB,MAAM,CAAC;QAC3D,MAAM3B,WAAW,MAAMD,iBAAiBmC,aAAa,CAACD;QACtD,MAAM7B,WAAW,CAAC,eAAe,EAAEY,GAAGK,IAAI,GAAG,CAAC,CAAC,EAAEL,GAAGK,IAAI,CAAC,CAAC,GAAGrB,SAAS,CAAC;QACvE,MAAMG,WAAW,AAACyB,CAAAA,QAAQA,MAAMO,WAAW,GAAGvC,eAAc,KAAM;QAElE,IAAIgC,OAAO;YACTA,MAAMO,WAAW,GAAGhC,WAAW;YAE/B,+FAA+F;YAC/F,qGAAqG;YACrG,4FAA4F;YAE5F,4FAA4F;YAC5F,IAAI,OAAOe,QAAQhB,UAAU,KAAK,UAAU;gBAC1C,IACE,OAAO0B,MAAM1B,UAAU,KAAK,YAC5B0B,MAAM1B,UAAU,GAAGgB,QAAQhB,UAAU,EACrC;gBACA,+EAA+E;gBACjF,OAAO;oBACL0B,MAAM1B,UAAU,GAAGgB,QAAQhB,UAAU;gBACvC;YACF,OAAO,IACLgB,QAAQhB,UAAU,KAAK,SACvB,OAAO0B,MAAM1B,UAAU,KAAK,aAC5B;gBACA,2EAA2E;gBAC3E0B,MAAM1B,UAAU,GAAGgB,QAAQhB,UAAU;YACvC;YAEA,sEAAsE;YACtE,IAAI,CAAC0B,MAAM3B,IAAI,EAAE;gBACf2B,MAAM3B,IAAI,GAAGA,KAAKmC,KAAK;YACzB,OAAO;gBACL,KAAK,MAAMC,OAAOpC,KAAM;oBACtB,4DAA4D;oBAC5D,IAAI,CAAC2B,MAAM3B,IAAI,CAACqC,QAAQ,CAACD,MAAM;wBAC7BT,MAAM3B,IAAI,CAACsC,IAAI,CAACF;oBAClB;gBACF;YACF;YACA,uGAAuG;YACvG,qDAAqD;YACrD,MAAMG,eAAehD,gBAAgBoC;YAErC,IACE,sDAAsD;YACtD,4CAA4C;YAC5CA,MAAMd,UAAU,KAAK,oBACrB,CAACc,MAAMa,oBAAoB,IAC3B,CAAC1C,iBAAiB0C,oBAAoB,IACtC,CAACb,MAAMc,WAAW,EAClB;gBACA,wEAAwE;gBACxE,MAAMC,aAAa,MAAM5C,iBAAiB6C,GAAG,CAAC5C,UAAU;oBACtD6C,UAAU;oBACV3C,YAAYgB,QAAQhB,UAAU;oBAC9BD;oBACA6C,UAAUN;oBACVrC;gBACF;gBAEA,IAAIwC,cAAcA,WAAWI,KAAK,EAAE;oBAClC,mCAAmC;oBACnC,IAAIJ,WAAWI,KAAK,CAACzC,IAAI,KAAK,SAAS;wBACrC,qDAAqD;wBACrD,6FAA6F;wBAC7F,0BAA0B;wBAC1B,+FAA+F;wBAC/F0C,QAAQC,KAAK,CACX,CAAC,0CAA0C,EAAEhB,cAAc,CAAC;oBAE9D,0DAA0D;oBAC5D,OAAO;wBACL,0FAA0F;wBAC1F,0DAA0D;wBAC1D,MAAMiB,iBACJP,WAAWI,KAAK,CAACxC,IAAI,CAACE,IAAI,KAAK0C,YAC3BzC,KAAK0C,KAAK,CAACT,WAAWI,KAAK,CAACxC,IAAI,CAACE,IAAI,IACrC0C;wBACN,IAAIR,WAAWU,OAAO,EAAE;4BACtB,4EAA4E;4BAC5E,IAAI,CAACzB,MAAM0B,kBAAkB,EAAE;gCAC7B1B,MAAM0B,kBAAkB,GAAG,CAAC;4BAC9B;4BACA,iFAAiF;4BACjF1B,MAAM0B,kBAAkB,CAACrB,cAAc,GACrCtC,6BACG4D,GAAG,CACF;gCACE,GAAG3B,KAAK;gCACR,8DAA8D;gCAC9D,8CAA8C;gCAC9Cd,YAAY;gCACZ0C,yBAAyB;4BAC3B,GACAxC,OACGW,MAEJ8B,IAAI,CAAC,CAAC3D;gCACL,OAAOD,eACLC,QACAC,kBACAC,UACAC,MACAiB,QAAQhB,UAAU,EAClBC,UACAC;4BAEJ,EACA,+DAA+D;6BAC9DsD,KAAK,CAAC,CAACC,MACNX,QAAQC,KAAK,CACX,CAAC,6BAA6B,EAAEhB,cAAc,CAAC,EAC/C0B;wBAGV;wBACA,kDAAkD;wBAClD,OAAOT;oBACT;gBACF;YACF;YAEA,uFAAuF;YACvF,MAAMpD,SAAS,MAAMH,6BAA6B4D,GAAG,CACnD;gBACE,GAAG3B,KAAK;gBACR,8DAA8D;gBAC9D,8CAA8C;gBAC9Cd,YAAY;gBACZ0C,yBAAyB;YAC3B,GACAxC,OACGW;YAEL9B,eACEC,QACAC,kBACAC,UACAC,MACAiB,QAAQhB,UAAU,EAClBC,UACAC;YAEF,OAAON;QACT,OAAO;YACLF,mBAAmB;YACnB,mFAAmF;YACnF,8DAA8D;YAC9D,qGAAqG;YACrG,4FAA4F;YAE5F,IAAI,CAACG,iBAAiB0C,oBAAoB,EAAE;gBAC1C,+EAA+E;gBAE/E,MAAME,aAAa,MAAM5C,iBAAiB6C,GAAG,CAAC5C,UAAU;oBACtD6C,UAAU;oBACV3C,YAAYgB,QAAQhB,UAAU;oBAC9BD;gBACF;gBAEA,IAAI0C,cAAcA,WAAWI,KAAK,EAAE;oBAClC,mCAAmC;oBACnC,IAAIJ,WAAWI,KAAK,CAACzC,IAAI,KAAK,SAAS;wBACrC,qDAAqD;wBACrD,6FAA6F;wBAC7F,0BAA0B;wBAC1B0C,QAAQC,KAAK,CACX,CAAC,0CAA0C,EAAEhB,cAAc,CAAC;oBAE9D,0DAA0D;oBAC5D,OAAO,IAAI,CAACU,WAAWU,OAAO,EAAE;wBAC9B,8DAA8D;wBAC9D,OAAOV,WAAWI,KAAK,CAACxC,IAAI,CAACE,IAAI,KAAK0C,YAClCzC,KAAK0C,KAAK,CAACT,WAAWI,KAAK,CAACxC,IAAI,CAACE,IAAI,IACrC0C;oBACN;gBACF;YACF;YAEA,uFAAuF;YACvF,8FAA8F;YAC9F,oGAAoG;YACpG,yGAAyG;YACzG,iGAAiG;YACjG,kGAAkG;YAClG,+EAA+E;YAC/E,MAAMrD,SAAS,MAAMH,6BAA6B4D,GAAG,CACnD,uHAAuH;YACvH,0GAA0G;YAC1G,uDAAuD;YACvD;gBACE,8DAA8D;gBAC9D,8CAA8C;gBAC9CzC,YAAY;gBACZ0C,yBAAyB;gBACzBI,aAAa;gBACbC,oBAAoB;gBACpBC,gBAAgB;YAClB,GACA9C,OACGW;YAEL9B,eACEC,QACAC,kBACAC,UACAC,MACAiB,QAAQhB,UAAU,EAClBC,UACAC;YAEF,OAAON;QACT;IACF;IACA,yGAAyG;IACzG,OAAO4B;AACT"}