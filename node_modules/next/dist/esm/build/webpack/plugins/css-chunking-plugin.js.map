{"version": 3, "sources": ["../../../../src/build/webpack/plugins/css-chunking-plugin.ts"], "names": ["PLUGIN_NAME", "MIN_CSS_CHUNK_SIZE", "MAX_CSS_CHUNK_SIZE", "CssChunkingPlugin", "constructor", "strict", "apply", "compiler", "summary", "process", "env", "CSS_CHUNKING_SUMMARY", "hooks", "thisCompilation", "tap", "compilation", "once", "optimizeChunks", "name", "stage", "chunkGraph", "changed", "undefined", "chunkStates", "Map", "chunkStatesByModule", "chunk", "chunks", "startsWith", "modules", "module", "getChunkModulesIterable", "type", "push", "length", "chunkState", "order", "requests", "set", "i", "moduleChunkStates", "get", "orderedModules", "sum", "values", "sort", "a", "b", "remainingModules", "Set", "map", "allDependents", "dependent", "loop", "ia", "bChunkStates", "ib", "add", "size", "newChunksByModule", "startModule", "allChunkStates", "newChunkModules", "currentSize", "potentialNextModules", "nextModule", "has", "cont", "orderedPotentialNextModules", "nextChunkStates", "maxRequests", "keys", "Math", "max", "identifier", "dep", "prevState", "delete", "newNextModule", "newChunk", "addChunk", "preventIntegration", "idNameHints", "connectChunkAndModule", "disconnectChunkAndModule", "split", "console", "log", "orderedChunkStates", "slice"], "mappings": "AAEA,MAAMA,cAAc;AAEpB;;CAEC,GACD,MAAMC,qBAAqB,KAAK;AAChC;;CAEC,GACD,MAAMC,qBAAqB,MAAM;AASjC,OAAO,MAAMC;IAEXC,YAAYC,MAAe,CAAE;QAC3B,IAAI,CAACA,MAAM,GAAGA;IAChB;IAEOC,MAAMC,QAAkB,EAAE;QAC/B,MAAMF,SAAS,IAAI,CAACA,MAAM;QAC1B,MAAMG,UAAU,CAAC,CAACC,QAAQC,GAAG,CAACC,oBAAoB;QAClDJ,SAASK,KAAK,CAACC,eAAe,CAACC,GAAG,CAACd,aAAa,CAACe;YAC/C,IAAIC,OAAO;YACXD,YAAYH,KAAK,CAACK,cAAc,CAACH,GAAG,CAClC;gBACEI,MAAMlB;gBACNmB,OAAO;YACT,GACA;gBACE,IAAIH,MAAM;oBACR;gBACF;gBACAA,OAAO;gBACP,MAAMI,aAAaL,YAAYK,UAAU;gBACzC,IAAIC,UAA4BC;gBAEhC,MAAMC,cAAc,IAAIC;gBACxB,MAAMC,sBAAsB,IAAID;gBAEhC,mEAAmE;gBACnE,KAAK,MAAME,SAASX,YAAYY,MAAM,CAAE;wBAClCD;oBAAJ,KAAIA,cAAAA,MAAMR,IAAI,qBAAVQ,YAAYE,UAAU,CAAC,WAAW;oBACtC,MAAMC,UAAU,EAAE;oBAClB,KAAK,MAAMC,UAAUV,WAAWW,uBAAuB,CAACL,OAAQ;4BACzDI;wBAAL,IAAI,GAACA,eAAAA,OAAOE,IAAI,qBAAXF,aAAaF,UAAU,CAAC,SAAQ;wBACrCC,QAAQI,IAAI,CAACH;oBACf;oBACA,IAAI,CAACD,QAAQK,MAAM,EAAE;oBACrB,MAAMC,aAAa;wBACjBT;wBACAG;wBACAO,OAAO;wBACPC,UAAUR,QAAQK,MAAM;oBAC1B;oBACAX,YAAYe,GAAG,CAACZ,OAAOS;oBACvB,IAAK,IAAII,IAAI,GAAGA,IAAIV,QAAQK,MAAM,EAAEK,IAAK;wBACvC,MAAMT,SAASD,OAAO,CAACU,EAAE;wBACzB,IAAIC,oBAAoBf,oBAAoBgB,GAAG,CAACX;wBAChD,IAAI,CAACU,mBAAmB;4BACtBA,oBAAoB,IAAIhB;4BACxBC,oBAAoBa,GAAG,CAACR,QAAQU;wBAClC;wBACAA,kBAAkBF,GAAG,CAACH,YAAYI;wBAClCd,oBAAoBa,GAAG,CAACR,QAAQU;oBAClC;gBACF;gBAEA,kCAAkC;gBAClC,MAAME,iBAAoD,EAAE;gBAE5D,KAAK,MAAM,CAACZ,QAAQU,kBAAkB,IAAIf,oBAAqB;oBAC7D,IAAIkB,MAAM;oBACV,KAAK,MAAMJ,KAAKC,kBAAkBI,MAAM,GAAI;wBAC1CD,OAAOJ;oBACT;oBACAG,eAAeT,IAAI,CAAC;wBAAEH;wBAAQa;oBAAI;gBACpC;gBAEAD,eAAeG,IAAI,CAAC,CAACC,GAAGC,IAAMD,EAAEH,GAAG,GAAGI,EAAEJ,GAAG;gBAE3C,qDAAqD;gBACrD,MAAMK,mBAAmB,IAAIC,IAC3BP,eAAeQ,GAAG,CAAC,CAAC,EAAEpB,MAAM,EAAE,GAAKA;gBAGrC,kEAAkE;gBAClE,gEAAgE;gBAChE,kDAAkD;gBAClD,MAAMqB,gBAAgB,IAAI3B;gBAE1B,IAAI,CAAC,IAAI,CAACnB,MAAM,EAAE;oBAChB,KAAK,MAAM0C,KAAKC,iBAAkB;wBAChC,MAAMI,YAAY,IAAIH;wBACtBI,MAAM,KAAK,MAAMP,KAAKE,iBAAkB;4BACtC,IAAIF,MAAMC,GAAG;4BACb,0BAA0B;4BAC1B,KAAK,MAAM,CAACZ,YAAYmB,GAAG,IAAI7B,oBAAoBgB,GAAG,CAACK,GAAK;gCAC1D,MAAMS,eAAe9B,oBAAoBgB,GAAG,CAACM;gCAC7C,MAAMS,KAAKD,aAAad,GAAG,CAACN;gCAC5B,IAAIqB,OAAOlC,WAAW;oCAEpB,SAAS+B;gCACX;gCACA,IAAIG,KAAKF,IAAI;oCAEX,SAASD;gCACX;4BACF;4BACAD,UAAUK,GAAG,CAACX;wBAChB;wBACA,IAAIM,UAAUM,IAAI,GAAG,GAAGP,cAAcb,GAAG,CAACS,GAAGK;oBAC/C;gBACF;gBAEA,wCAAwC;gBACxC,MAAMO,oBAAoB,IAAInC;gBAE9B,8BAA8B;gBAC9B,KAAK,MAAMoC,eAAeZ,iBAAkB;oBAC1C,4DAA4D;oBAC5D,IAAIa,iBAAiB,IAAIrC,IAAIC,oBAAoBgB,GAAG,CAACmB;oBAErD,mDAAmD;oBACnD,MAAME,kBAAkB,IAAIb,IAAI;wBAACW;qBAAY;oBAE7C,oCAAoC;oBACpC,IAAIG,cAAcH,YAAYF,IAAI;oBAElC,sEAAsE;oBACtE,2EAA2E;oBAC3E,0EAA0E;oBAC1E,MAAMM,uBAAuB,IAAIxC;oBAIjC,KAAK,MAAM,CAACW,YAAYI,EAAE,IAAIsB,eAAgB;wBAC5C,MAAMI,aAAa9B,WAAWN,OAAO,CAACU,IAAI,EAAE;wBAC5C,IAAI0B,cAAcjB,iBAAiBkB,GAAG,CAACD,aAAa;4BAClDD,qBAAqB1B,GAAG,CAAC2B,YAAY;gCACnCA,WAAWP,IAAI;gCACfjC,oBAAoBgB,GAAG,CAACwB;6BACzB;wBACH;oBACF;oBAEA,iEAAiE;oBACjE,IAAIE;oBACJ,GAAG;wBACDA,OAAO;wBACP,2DAA2D;wBAC3D,qCAAqC;wBACrC,MAAMC,8BAA8B,EAAE;wBACtC,KAAK,MAAM,CACTH,YACA,CAACP,MAAMW,gBAAgB,CACxB,IAAIL,qBAAsB;4BACzB,IAAIM,cAAc;4BAClB,KAAK,MAAMnC,cAAckC,gBAAgBE,IAAI,GAAI;gCAC/C,+BAA+B;gCAC/B,IAAIV,eAAeK,GAAG,CAAC/B,aAAa;oCAClCmC,cAAcE,KAAKC,GAAG,CAACH,aAAanC,WAAWE,QAAQ;gCACzD;4BACF;4BAEA+B,4BAA4BnC,IAAI,CAAC;gCAC/BgC;gCACAP;gCACAW;gCACAC;6BACD;wBACH;wBACAF,4BAA4BvB,IAAI,CAC9B,CAACC,GAAGC,IACFA,CAAC,CAAC,EAAE,GAAGD,CAAC,CAAC,EAAE,IACVA,CAAAA,CAAC,CAAC,EAAE,CAAC4B,UAAU,KAAK3B,CAAC,CAAC,EAAE,CAAC2B,UAAU,KAAK,CAAC,IAAI,CAAA;wBAGlD,6BAA6B;wBAC7BrB,MAAM,KAAK,MAAM,CACfY,YACAP,MACAW,gBACD,IAAID,4BAA6B;4BAChC,IAAIL,cAAcL,OAAOxD,oBAAoB;gCAE3C;4BACF;4BACA,IAAI,CAACG,QAAQ;gCACX,mEAAmE;gCACnE,MAAM+C,YAAYD,cAAcV,GAAG,CAACwB;gCACpC,IAAIb,WAAW;oCACb,KAAK,MAAMuB,OAAOvB,UAAW;wCAC3B,IAAIU,gBAAgBI,GAAG,CAACS,MAAM;4CAE5B,SAAStB;wCACX;oCACF;gCACF;4BACF,OAAO;gCACL,8FAA8F;gCAC9F,KAAK,MAAM,CAAClB,YAAYI,EAAE,IAAI8B,gBAAiB;oCAC7C,MAAMO,YAAYf,eAAepB,GAAG,CAACN;oCACrC,IAAIyC,cAActD,WAAW;wCAC3B,8CAA8C;wCAC9C,qCAAqC;wCACrC,IAAIyC,cAAc9D,oBAAoB;4CACpC;wCACF,OAAO;4CACL,SAASoD;wCACX;oCACF,OAAO,IAAIuB,YAAY,MAAMrC,GAAG;wCAE9B;oCACF,OAAO;wCAEL,SAASc;oCACX;gCACF;4BACF;4BACAW,qBAAqBa,MAAM,CAACZ;4BAC5BF,eAAeL;4BACf,KAAK,MAAM,CAACvB,YAAYI,EAAE,IAAI8B,gBAAiB;gCAC7C,IAAIR,eAAeK,GAAG,CAAC/B,aAAa;oCAClC,oDAAoD;oCACpDA,WAAWE,QAAQ;gCACrB;gCACAwB,eAAevB,GAAG,CAACH,YAAYI;gCAC/B,MAAMuC,gBAAgB3C,WAAWN,OAAO,CAACU,IAAI,EAAE;gCAC/C,IACEuC,iBACA9B,iBAAiBkB,GAAG,CAACY,kBACrB,CAAChB,gBAAgBI,GAAG,CAACY,gBACrB;oCACAd,qBAAqB1B,GAAG,CAACwC,eAAe;wCACtCA,cAAcpB,IAAI;wCAClBjC,oBAAoBgB,GAAG,CAACqC;qCACzB;gCACH;4BACF;4BACAhB,gBAAgBL,GAAG,CAACQ;4BACpBE,OAAO;4BACP;wBACF;oBACF,QAASA,MAAK;oBACd,MAAMY,WAAWhE,YAAYiE,QAAQ;oBACrCD,SAASE,kBAAkB,GAAG;oBAC9BF,SAASG,WAAW,CAACzB,GAAG,CAAC;oBACzB,KAAK,MAAM3B,UAAUgC,gBAAiB;wBACpCd,iBAAiB6B,MAAM,CAAC/C;wBACxBV,WAAW+D,qBAAqB,CAACJ,UAAUjD;wBAC3C6B,kBAAkBrB,GAAG,CAACR,QAAQiD;oBAChC;oBACA1D,UAAU;gBACZ;gBAEA,KAAK,MAAM,EAAEK,KAAK,EAAEG,OAAO,EAAE,IAAIN,YAAYqB,MAAM,GAAI;oBACrD,MAAMjB,SAAS,IAAIsB;oBACnB,KAAK,MAAMnB,UAAUD,QAAS;wBAC5B,MAAMkD,WAAWpB,kBAAkBlB,GAAG,CAACX;wBACvC,IAAIiD,UAAU;4BACZ3D,WAAWgE,wBAAwB,CAAC1D,OAAOI;4BAC3C,IAAIH,OAAOuC,GAAG,CAACa,WAAW;4BAC1BpD,OAAO8B,GAAG,CAACsB;4BACXrD,MAAM2D,KAAK,CAACN;wBACd;oBACF;gBACF;gBAEA,IAAIvE,SAAS;oBACX8E,QAAQC,GAAG,CAAC;oBACZ,MAAMC,qBAAqB;2BAAIjE,YAAYqB,MAAM;qBAAG;oBACpD4C,mBAAmB3C,IAAI,CAAC,CAACC,GAAGC,IAAMA,EAAEV,QAAQ,GAAGS,EAAET,QAAQ;oBACzD,KAAK,MAAM,EAAEX,KAAK,EAAEG,OAAO,EAAEQ,QAAQ,EAAE,IAAImD,mBAAmBC,KAAK,CACjE,GACA,IACC;wBACDH,QAAQC,GAAG,CACT,CAAC,EAAE,EAAElD,SAAS,cAAc,EAAEX,MAAMR,IAAI,CAAC,MAAM,EAAEW,QAAQK,MAAM,CAAC,SAAS,CAAC;oBAE9E;gBACF;gBAEA,OAAOb;YACT;QAEJ;IACF;AACF"}