{"version": 3, "sources": ["../../../../src/lib/metadata/resolvers/resolve-url.ts"], "names": ["path", "isStringOrURL", "icon", "URL", "createLocalMetadataBase", "process", "env", "PORT", "getSocialImageFallbackMetadataBase", "metadataBase", "isMetadataBaseMissing", "defaultMetadataBase", "deploymentUrl", "VERCEL_URL", "fallbackMetadataBase", "NODE_ENV", "VERCEL_ENV", "resolveUrl", "url", "parsedUrl", "basePath", "pathname", "joinedPath", "posix", "join", "resolveRelativeUrl", "startsWith", "resolve", "resolveAbsoluteUrlWithPathname", "trailingSlash", "resolvedUrl", "result", "origin", "href", "endsWith", "isRelative", "isExternal", "<PERSON><PERSON><PERSON><PERSON>", "includes"], "mappings": "AAAA,OAAOA,UAAU,sCAAqC;AAGtD,SAASC,cAAcC,IAAS;IAC9B,OAAO,OAAOA,SAAS,YAAYA,gBAAgBC;AACrD;AAEA,SAASC;IACP,OAAO,IAAID,IAAI,CAAC,iBAAiB,EAAEE,QAAQC,GAAG,CAACC,IAAI,IAAI,KAAK,CAAC;AAC/D;AAEA,uFAAuF;AACvF,qDAAqD;AACrD,OAAO,SAASC,mCAAmCC,YAAwB;IAIzE,MAAMC,wBAAwB,CAACD;IAC/B,MAAME,sBAAsBP;IAC5B,MAAMQ,gBACJP,QAAQC,GAAG,CAACO,UAAU,IAAI,IAAIV,IAAI,CAAC,QAAQ,EAAEE,QAAQC,GAAG,CAACO,UAAU,CAAC,CAAC;IAEvE,IAAIC;IACJ,IAAIT,QAAQC,GAAG,CAACS,QAAQ,KAAK,eAAe;QAC1CD,uBAAuBH;IACzB,OAAO;QACLG,uBACET,QAAQC,GAAG,CAACS,QAAQ,KAAK,gBACzBH,iBACAP,QAAQC,GAAG,CAACU,UAAU,KAAK,YACvBJ,gBACAH,gBAAgBG,iBAAiBD;IACzC;IAEA,OAAO;QACLG;QACAJ;IACF;AACF;AAQA,SAASO,WACPC,GAAoC,EACpCT,YAAwB;IAExB,IAAIS,eAAef,KAAK,OAAOe;IAC/B,IAAI,CAACA,KAAK,OAAO;IAEjB,IAAI;QACF,mEAAmE;QACnE,MAAMC,YAAY,IAAIhB,IAAIe;QAC1B,OAAOC;IACT,EAAE,OAAM,CAAC;IAET,IAAI,CAACV,cAAc;QACjBA,eAAeL;IACjB;IAEA,oCAAoC;IACpC,MAAMgB,WAAWX,aAAaY,QAAQ,IAAI;IAC1C,MAAMC,aAAatB,KAAKuB,KAAK,CAACC,IAAI,CAACJ,UAAUF;IAE7C,OAAO,IAAIf,IAAImB,YAAYb;AAC7B;AAEA,uDAAuD;AACvD,SAASgB,mBAAmBP,GAAiB,EAAEG,QAAgB;IAC7D,IAAI,OAAOH,QAAQ,YAAYA,IAAIQ,UAAU,CAAC,OAAO;QACnD,OAAO1B,KAAKuB,KAAK,CAACI,OAAO,CAACN,UAAUH;IACtC;IACA,OAAOA;AACT;AAEA,kFAAkF;AAClF,SAASU,+BACPV,GAAiB,EACjBT,YAAwB,EACxB,EAAEoB,aAAa,EAAER,QAAQ,EAAmB;IAE5C,wDAAwD;IACxDH,MAAMO,mBAAmBP,KAAKG;IAE9B,6DAA6D;IAC7D,yDAAyD;IACzD,IAAIS,cAAc;IAClB,MAAMC,SAAStB,eAAeQ,WAAWC,KAAKT,gBAAgBS;IAC9D,IAAI,OAAOa,WAAW,UAAU;QAC9BD,cAAcC;IAChB,OAAO;QACLD,cAAcC,OAAOV,QAAQ,KAAK,MAAMU,OAAOC,MAAM,GAAGD,OAAOE,IAAI;IACrE;IAEA,oEAAoE;IACpE,gDAAgD;IAChD,uBAAuB;IACvB,IAAIJ,iBAAiB,CAACC,YAAYI,QAAQ,CAAC,MAAM;QAC/C,IAAIC,aAAaL,YAAYJ,UAAU,CAAC;QACxC,IAAIU,aAAa;QACjB,IAAIC,WAAWP,YAAYQ,QAAQ,CAAC;QACpC,IAAI,CAACH,YAAY;YACf,IAAI;gBACF,MAAMhB,YAAY,IAAIhB,IAAI2B;gBAC1BM,aACE3B,gBAAgB,QAAQU,UAAUa,MAAM,KAAKvB,aAAauB,MAAM;YACpE,EAAE,OAAM;gBACN,gDAAgD;gBAChDI,aAAa;YACf;YACA,IAAI,CAACA,cAAc,CAACC,UAAU,OAAO,CAAC,EAAEP,YAAY,CAAC,CAAC;QACxD;IACF;IAEA,OAAOA;AACT;AAEA,SACE7B,aAAa,EACbgB,UAAU,EACVQ,kBAAkB,EAClBG,8BAA8B,KAC/B"}