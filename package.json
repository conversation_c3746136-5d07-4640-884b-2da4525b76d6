{"name": "streamit-pro-nextjs", "version": "3.0.0", "type": "module", "description": "Professional video conferencing platform built with Next.js, TypeScript, TailwindCSS, and WebRTC", "scripts": {"dev": "concurrently \"npm run dev:server\" \"npm run dev:next\"", "dev:next": "next dev -p 3010", "dev:server": "tsx watch server/index.mts", "build": "next build", "start": "tsx server/index.mts", "heroku-postbuild": "npm run build", "lint": "next lint", "type-check": "tsc --noEmit"}, "keywords": ["video-conferencing", "webrtc", "typescript", "nextjs", "tailwindcss", "shadcn-ui", "zustand", "socket.io", "recording", "chat", "screen-sharing"], "author": "<PERSON>", "license": "MIT", "repository": {"type": "module", "url": "https://github.com/joelgriiyo/streamit2.git"}, "homepage": "https://github.com/joelgriiyo/streamit2", "engines": {"node": "18.x", "npm": ">=9.0.0"}, "dependencies": {"@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-toast": "^1.1.5", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "cors": "^2.8.5", "express": "^4.18.2", "express-rate-limit": "^7.5.1", "lucide-react": "^0.294.0", "next": "14.2.3", "react": "^18.2.0", "react-dom": "^18.2.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "tailwind-merge": "^2.0.0", "tailwindcss-animate": "^1.0.7", "tsx": "^4.20.3", "zustand": "^4.4.0"}, "devDependencies": {"@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.9", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/node": "^20.11.28", "@types/react": "^18.2.56", "@types/react-dom": "^18.2.19", "autoprefixer": "^10.4.17", "concurrently": "^9.2.0", "eslint": "^8.56.0", "eslint-config-next": "14.1.0", "postcss": "^8.4.35", "postcss-import": "^16.0.1", "postcss-nesting": "^12.0.2", "tailwindcss": "^3.4.1", "typescript": "^5.0.0"}}