import { create } from 'zustand'
import { devtools } from 'zustand/middleware'

export interface Participant {
  id: string
  name: string
  stream?: MediaStream
  isAudioMuted: boolean
  isVideoMuted: boolean
  isScreenSharing: boolean
  role: 'host' | 'co-host' | 'participant'
  isHandRaised: boolean
  joinedAt: Date
  lastActivity: Date
}

export interface ChatMessage {
  id: string
  userId: string
  userName: string
  message: string
  timestamp: Date
  isSpam?: boolean
  isBlocked?: boolean
}

export interface SecuritySettings {
  encryptionEnabled: boolean
  antiSpamEnabled: boolean
  maxMessagesPerMinute: number
  allowScreenShare: boolean
  allowFileSharing: boolean
  requireApprovalToJoin: boolean
}

export interface AdminControls {
  canMuteAll: boolean
  canMuteParticipant: boolean
  canRemoveParticipant: boolean
  canControlCamera: boolean
  canManageRoles: boolean
}

export interface VideoCallState {
  // Room state
  roomId: string | null
  isConnected: boolean
  roomLocked: boolean

  // User state
  currentUser: Participant | null
  participants: Map<string, Participant>

  // Media state
  localStream: MediaStream | null
  isAudioMuted: boolean
  isVideoMuted: boolean
  isScreenSharing: boolean

  // Chat state
  messages: ChatMessage[]
  unreadCount: number
  messageHistory: Map<string, number>

  // UI state
  isChatOpen: boolean
  isSettingsOpen: boolean

  // Security & Admin
  securitySettings: SecuritySettings
  adminControls: AdminControls
  blockedUsers: Set<string>
  spamDetection: Map<string, { count: number, lastReset: number }>

  // Actions
  setRoomId: (roomId: string) => void
  setConnected: (connected: boolean) => void
  setCurrentUser: (user: Participant) => void
  addParticipant: (participant: Participant) => void
  removeParticipant: (participantId: string) => void
  updateParticipant: (participantId: string, updates: Partial<Participant>) => void
  setLocalStream: (stream: MediaStream | null) => void
  toggleAudio: () => void
  toggleVideo: () => void
  toggleScreenShare: () => void
  addMessage: (message: ChatMessage) => void
  clearUnreadCount: () => void
  toggleChat: () => void
  toggleSettings: () => void

  // Admin Actions
  muteParticipant: (participantId: string) => void
  muteAllParticipants: () => void
  unmuteAllParticipants: () => void
  promoteToCoHost: (participantId: string) => void
  demoteFromCoHost: (participantId: string) => void
  blockUser: (participantId: string) => void
  unblockUser: (participantId: string) => void
  lockRoom: () => void
  unlockRoom: () => void
  updateSecuritySettings: (settings: Partial<SecuritySettings>) => void

  // Utility Actions
  reset: () => void
}

const initialState = {
  roomId: null,
  isConnected: false,
  roomLocked: false,
  currentUser: null,
  participants: new Map<string, Participant>(),
  localStream: null,
  isAudioMuted: false,
  isVideoMuted: false,
  isScreenSharing: false,
  messages: [],
  unreadCount: 0,
  messageHistory: new Map<string, number>(),
  isChatOpen: false,
  isSettingsOpen: false,
  securitySettings: {
    encryptionEnabled: true,
    antiSpamEnabled: true,
    maxMessagesPerMinute: 10,
    allowScreenShare: true,
    allowFileSharing: false,
    requireApprovalToJoin: false,
  },
  adminControls: {
    canMuteAll: true,
    canMuteParticipant: true,
    canRemoveParticipant: true,
    canControlCamera: true,
    canManageRoles: true,
  },
  blockedUsers: new Set<string>(),
  spamDetection: new Map<string, { count: number, lastReset: number }>(),
}

export const useVideoCallStore = create<VideoCallState>()(
  devtools(
    (set, get) => ({
      ...initialState,

      // Room actions
      setRoomId: (roomId: string) => set({ roomId }),
      setConnected: (isConnected: boolean) => set({ isConnected }),

      // User actions
      setCurrentUser: (currentUser: Participant) => set({ currentUser }),

      addParticipant: (participant: Participant) => {
        const participants = new Map(get().participants)
        participants.set(participant.id, participant)
        set({ participants })
      },

      removeParticipant: (participantId: string) => {
        const participants = new Map(get().participants)
        participants.delete(participantId)
        set({ participants })
      },

      updateParticipant: (participantId: string, updates: Partial<Participant>) => {
        const participants = new Map(get().participants)
        const participant = participants.get(participantId)
        if (participant) {
          participants.set(participantId, { ...participant, ...updates })
          set({ participants })
        }
      },

      // Media actions
      setLocalStream: (localStream: MediaStream | null) => set({ localStream }),

      toggleAudio: () => {
        const { isAudioMuted, localStream } = get()
        if (localStream) {
          localStream.getAudioTracks().forEach(track => {
            track.enabled = isAudioMuted
          })
        }
        set({ isAudioMuted: !isAudioMuted })
      },

      toggleVideo: () => {
        const { isVideoMuted, localStream } = get()
        if (localStream) {
          localStream.getVideoTracks().forEach(track => {
            track.enabled = isVideoMuted
          })
        }
        set({ isVideoMuted: !isVideoMuted })
      },

      toggleScreenShare: () => {
        set(state => ({ isScreenSharing: !state.isScreenSharing }))
      },

      // Chat actions
      addMessage: (message: ChatMessage) => {
        const { messages, securitySettings, spamDetection } = get()

        // Anti-spam check
        if (securitySettings.antiSpamEnabled) {
          const now = Date.now()
          const userSpam = spamDetection.get(message.userId) || { count: 0, lastReset: now }

          // Reset count if more than a minute has passed
          if (now - userSpam.lastReset > 60000) {
            userSpam.count = 0
            userSpam.lastReset = now
          }

          userSpam.count++
          spamDetection.set(message.userId, userSpam)

          // Mark as spam if exceeding limit
          if (userSpam.count > securitySettings.maxMessagesPerMinute) {
            message.isSpam = true
          }
        }

        set({
          messages: [...messages, message],
          unreadCount: get().isChatOpen ? 0 : get().unreadCount + 1,
          spamDetection: new Map(spamDetection)
        })
      },

      clearUnreadCount: () => set({ unreadCount: 0 }),

      // UI actions
      toggleChat: () => {
        const isChatOpen = !get().isChatOpen
        set({
          isChatOpen,
          unreadCount: isChatOpen ? 0 : get().unreadCount
        })
      },

      toggleSettings: () => set(state => ({ isSettingsOpen: !state.isSettingsOpen })),

      // Admin actions
      muteParticipant: (participantId: string) => {
        get().updateParticipant(participantId, { isAudioMuted: true })
      },

      muteAllParticipants: () => {
        const { participants } = get()
        participants.forEach((_, id) => {
          get().updateParticipant(id, { isAudioMuted: true })
        })
      },

      unmuteAllParticipants: () => {
        const { participants } = get()
        participants.forEach((_, id) => {
          get().updateParticipant(id, { isAudioMuted: false })
        })
      },

      promoteToCoHost: (participantId: string) => {
        get().updateParticipant(participantId, { role: 'co-host' })
      },

      demoteFromCoHost: (participantId: string) => {
        get().updateParticipant(participantId, { role: 'participant' })
      },

      blockUser: (participantId: string) => {
        const blockedUsers = new Set(get().blockedUsers)
        blockedUsers.add(participantId)
        set({ blockedUsers })
        get().removeParticipant(participantId)
      },

      unblockUser: (participantId: string) => {
        const blockedUsers = new Set(get().blockedUsers)
        blockedUsers.delete(participantId)
        set({ blockedUsers })
      },

      lockRoom: () => set({ roomLocked: true }),
      unlockRoom: () => set({ roomLocked: false }),

      updateSecuritySettings: (settings: Partial<SecuritySettings>) => {
        set(state => ({
          securitySettings: { ...state.securitySettings, ...settings }
        }))
      },

      // Utility actions
      reset: () => set(initialState),
    }),
    {
      name: 'video-call-store',
    }
  )
)

export default useVideoCallStore