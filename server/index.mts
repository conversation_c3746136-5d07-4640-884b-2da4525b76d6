import express, { type Request, type Response } from 'express';
import { createServer } from 'http';
import { Server } from 'socket.io';
import cors from 'cors';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// Add type for Express Request with user
interface AuthenticatedRequest extends Request {
  user?: any;
}
// import rateLimit from 'express-rate-limit'

const app = express();
const server = createServer(app);

// Configure CORS
app.use(cors({
  origin: process.env.NODE_ENV === 'production' 
    ? process.env.FRONTEND_URL || true
    : ['http://localhost:3000', 'http://127.0.0.1:3000'],
  credentials: true
}))

app.use(express.json())

// Rate limiting for DDoS protection (commented out for now)
// const limiter = rateLimit({
//   windowMs: 1 * 60 * 1000, // 1 minute
//   max: 100, // Limit each IP to 100 requests per windowMs
//   message: 'Too many requests from this IP, please try again later.',
//   standardHeaders: true,
//   legacyHeaders: false,
// })

// app.use(limiter)

// Socket.IO setup
const io = new Server(server, {
  cors: {
    origin: process.env.NODE_ENV === 'production' 
      ? process.env.FRONTEND_URL || true
      : ['http://localhost:3000', 'http://127.0.0.1:3000'],
    methods: ['GET', 'POST'],
    credentials: true
  },
  transports: ['websocket', 'polling']
});

// Serve static files in production
if (process.env.NODE_ENV === 'production') {
  const __filename = fileURLToPath(import.meta.url);
  const __dirname = dirname(__filename);
  const staticPath = path.join(__dirname, '../../.next');
  
  app.use(express.static(staticPath));
  
  // Handle SPA routing
  app.get('*', (req: Request, res: Response) => {
    res.sendFile(path.join(staticPath, 'index.html'));
  });
}

// Store for rooms and participants
interface Participant {
  id: string
  name: string
  socketId: string
  roomId: string
}

interface Room {
  id: string
  participants: Map<string, Participant>
  createdAt: Date
}

const rooms = new Map<string, Room>()
const participants = new Map<string, Participant>()

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log(`🔌 User connected: ${socket.id}`)

  // Handle joining a room
  socket.on('join-room', (data: { roomId: string, userId: string, userName: string }) => {
    const { roomId, userId, userName } = data
    
    console.log(`👤 User ${userName} (${userId}) joining room ${roomId}`)

    // Create room if it doesn't exist
    if (!rooms.has(roomId)) {
      rooms.set(roomId, {
        id: roomId,
        participants: new Map(),
        createdAt: new Date()
      })
      console.log(`🏠 Created new room: ${roomId}`)
    }

    const room = rooms.get(roomId)!
    
    // Create participant
    const participant: Participant = {
      id: userId,
      name: userName,
      socketId: socket.id,
      roomId
    }

    // Add participant to room and global map
    room.participants.set(userId, participant)
    participants.set(socket.id, participant)

    // Join socket room
    socket.join(roomId)

    // Notify existing participants about new user
    socket.to(roomId).emit('user-joined', {
      userId,
      userName
    })

    // Send current participants to new user
    const currentParticipants = Array.from(room.participants.values())
      .filter(p => p.id !== userId)
      .map(p => ({ userId: p.id, userName: p.name }))

    socket.emit('room-participants', currentParticipants)

    console.log(`✅ User ${userName} joined room ${roomId}. Total participants: ${room.participants.size}`)
  })

  // Handle leaving a room
  socket.on('leave-room', (data: { roomId: string }) => {
    const participant = participants.get(socket.id)
    if (!participant) return

    const { roomId } = data
    const room = rooms.get(roomId)
    
    if (room) {
      // Remove participant from room
      room.participants.delete(participant.id)
      
      // Notify other participants
      socket.to(roomId).emit('user-left', {
        userId: participant.id,
        userName: participant.name
      })

      // Clean up empty room
      if (room.participants.size === 0) {
        rooms.delete(roomId)
        console.log(`🗑️ Deleted empty room: ${roomId}`)
      }

      console.log(`👋 User ${participant.name} left room ${roomId}`)
    }

    // Remove from global participants map
    participants.delete(socket.id)
    socket.leave(roomId)
  })

  // Handle WebRTC signaling
  socket.on('signal', (data: { roomId: string, targetUserId: string, signal: any }) => {
    const { roomId, targetUserId, signal } = data
    const participant = participants.get(socket.id)
    
    if (!participant) return

    const room = rooms.get(roomId)
    if (!room) return

    const targetParticipant = room.participants.get(targetUserId)
    if (!targetParticipant) return

    // Forward signal to target user
    socket.to(targetParticipant.socketId).emit('signal', {
      fromUserId: participant.id,
      signal
    })

    console.log(`📡 Signal forwarded from ${participant.name} to ${targetParticipant.name}`)
  })

  // Handle chat messages
  socket.on('chat-message', (data: { roomId: string, message: string, userName: string, timestamp: string }) => {
    const { roomId, message, userName, timestamp } = data
    const participant = participants.get(socket.id)
    
    if (!participant) return

    const room = rooms.get(roomId)
    if (!room) return

    // Broadcast message to all participants in the room
    io.to(roomId).emit('chat-message', {
      userId: participant.id,
      userName,
      message,
      timestamp
    })

    console.log(`💬 Chat message from ${userName} in room ${roomId}: ${message}`)
  })

  // Handle participant updates (mute/unmute, etc.)
  socket.on('participant-update', (data: { roomId: string, updates: any }) => {
    const { roomId, updates } = data
    const participant = participants.get(socket.id)
    
    if (!participant) return

    // Broadcast update to other participants
    socket.to(roomId).emit('participant-updated', {
      userId: participant.id,
      updates
    })
  })

  // Handle disconnect
  socket.on('disconnect', () => {
    const participant = participants.get(socket.id)
    
    if (participant) {
      const room = rooms.get(participant.roomId)
      
      if (room) {
        // Remove participant from room
        room.participants.delete(participant.id)
        
        // Notify other participants
        socket.to(participant.roomId).emit('user-left', {
          userId: participant.id,
          userName: participant.name
        })

        // Clean up empty room
        if (room.participants.size === 0) {
          rooms.delete(participant.roomId)
          console.log(`🗑️ Deleted empty room: ${participant.roomId}`)
        }

        console.log(`🔌 User ${participant.name} disconnected from room ${participant.roomId}`)
      }

      // Remove from global participants map
      participants.delete(socket.id)
    }

    console.log(`🔌 User disconnected: ${socket.id}`)
  })
})

// Health check endpoint
app.get('/health', (req: express.Request, res: express.Response) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    rooms: rooms.size,
    participants: participants.size
  })
})

// Get room info endpoint
app.get('/api/room/:roomId', (req, res) => {
  const { roomId } = req.params
  const room = rooms.get(roomId)
  
  if (!room) {
    return res.status(404).json({ error: 'Room not found' })
  }

  const participantsList = Array.from(room.participants.values()).map(p => ({
    id: p.id,
    name: p.name
  }))

  res.json({
    id: room.id,
    participants: participantsList,
    participantCount: room.participants.size,
    createdAt: room.createdAt
  })
})

<<<<<<< HEAD
const PORT = process.env.PORT || 3002
=======
const PORT = process.env.PORT || 3011
>>>>>>> 52af1f050aa2dea8a741d2d9a618c392237d65cf

server.listen(PORT, () => {
  console.log(`🚀 StreamIt Pro server running on port ${PORT}`)
  console.log(`🌐 Socket.IO server ready for connections`)
  console.log(`📊 Health check available at http://localhost:${PORT}/health`)
})
