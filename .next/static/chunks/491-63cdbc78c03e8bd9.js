"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[491],{8954:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(1066).Z)("Video",[["path",{d:"m22 8-6 4 6 4V8Z",key:"50v9me"}],["rect",{width:"14",height:"12",x:"2",y:"6",rx:"2",ry:"2",key:"1rqjg6"}]])},4697:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(1066).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},6246:function(e,t,n){/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=n(2265),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=r.useState,s=r.useEffect,u=r.useLayoutEffect,a=r.useDebugValue;function l(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!o(e,n)}catch(e){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=i({inst:{value:n,getSnapshot:t}}),o=r[0].inst,c=r[1];return u(function(){o.value=n,o.getSnapshot=t,l(o)&&c({inst:o})},[e,n,t]),s(function(){return l(o)&&c({inst:o}),e(function(){l(o)&&c({inst:o})})},[e]),a(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:c},4453:function(e,t,n){/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=n(2265),o=n(554),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},s=o.useSyncExternalStore,u=r.useRef,a=r.useEffect,l=r.useMemo,c=r.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,n,r,o){var d=u(null);if(null===d.current){var f={hasValue:!1,value:null};d.current=f}else f=d.current;var p=s(e,(d=l(function(){function e(e){if(!a){if(a=!0,s=e,e=r(e),void 0!==o&&f.hasValue){var t=f.value;if(o(t,e))return u=t}return u=e}if(t=u,i(s,e))return t;var n=r(e);return void 0!==o&&o(t,n)?(s=e,t):(s=e,u=n)}var s,u,a=!1,l=void 0===n?null:n;return[function(){return e(t())},null===l?void 0:function(){return e(l())}]},[t,n,r,o]))[0],d[1]);return a(function(){f.hasValue=!0,f.value=p},[p]),c(p),p}},554:function(e,t,n){e.exports=n(6246)},5006:function(e,t,n){e.exports=n(4453)},9099:function(e,t,n){n.d(t,{Ue:function(){return p},oR:function(){return d}});let r=e=>{let t;let n=new Set,r=(e,r)=>{let o="function"==typeof e?e(t):e;if(!Object.is(o,t)){let e=t;t=(null!=r?r:"object"!=typeof o||null===o)?o:Object.assign({},t,o),n.forEach(n=>n(t,e))}},o=()=>t,i={setState:r,getState:o,getInitialState:()=>s,subscribe:e=>(n.add(e),()=>n.delete(e)),destroy:()=>{console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),n.clear()}},s=t=e(r,o,i);return i},o=e=>e?r(e):r;var i=n(2265),s=n(5006);let{useDebugValue:u}=i,{useSyncExternalStoreWithSelector:a}=s,l=!1,c=e=>e;function d(e,t=c,n){n&&!l&&(console.warn("[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937"),l=!0);let r=a(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,n);return u(r),r}let f=e=>{"function"!=typeof e&&console.warn("[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.");let t="function"==typeof e?o(e):e,n=(e,n)=>d(t,e,n);return Object.assign(n,t),n},p=e=>e?f(e):f},9291:function(e,t,n){n.d(t,{mW:function(){return s}});let r=new Map,o=e=>{let t=r.get(e);return t?Object.fromEntries(Object.entries(t.stores).map(([e,t])=>[e,t.getState()])):{}},i=(e,t,n)=>{if(void 0===e)return{type:"untracked",connection:t.connect(n)};let o=r.get(n.name);if(o)return{type:"tracked",store:e,...o};let i={connection:t.connect(n),stores:{}};return r.set(n.name,i),{type:"tracked",store:e,...i}},s=(e,t={})=>(n,r,s)=>{let a;let{enabled:l,anonymousActionType:c,store:d,...f}=t;try{a=(null==l||l)&&window.__REDUX_DEVTOOLS_EXTENSION__}catch(e){}if(!a)return l&&console.warn("[zustand devtools middleware] Please install/enable Redux devtools extension"),e(n,r,s);let{connection:p,...v}=i(d,a,f),y=!0;s.setState=(e,t,i)=>{let u=n(e,t);if(!y)return u;let a=void 0===i?{type:c||"anonymous"}:"string"==typeof i?{type:i}:i;return void 0===d?null==p||p.send(a,r()):null==p||p.send({...a,type:`${d}/${a.type}`},{...o(f.name),[d]:s.getState()}),u};let S=(...e)=>{let t=y;y=!1,n(...e),y=t},h=e(s.setState,r,s);if("untracked"===v.type?null==p||p.init(h):(v.stores[v.store]=s,null==p||p.init(Object.fromEntries(Object.entries(v.stores).map(([e,t])=>[e,e===v.store?h:t.getState()])))),s.dispatchFromDevtools&&"function"==typeof s.dispatch){let e=!1,t=s.dispatch;s.dispatch=(...n)=>{"__setState"!==n[0].type||e||(console.warn('[zustand devtools middleware] "__setState" action type is reserved to set state from the devtools. Avoid using it.'),e=!0),t(...n)}}return p.subscribe(e=>{var t;switch(e.type){case"ACTION":if("string"!=typeof e.payload){console.error("[zustand devtools middleware] Unsupported action format");return}return u(e.payload,e=>{if("__setState"===e.type){if(void 0===d){S(e.state);return}1!==Object.keys(e.state).length&&console.error(`
                    [zustand devtools middleware] Unsupported __setState action format. 
                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),
                    and value of this only key should be a state object. Example: { "type": "__setState", "state": { "abc123Store": { "foo": "bar" } } }
                    `);let t=e.state[d];if(null==t)return;JSON.stringify(s.getState())!==JSON.stringify(t)&&S(t);return}s.dispatchFromDevtools&&"function"==typeof s.dispatch&&s.dispatch(e)});case"DISPATCH":switch(e.payload.type){case"RESET":if(S(h),void 0===d)return null==p?void 0:p.init(s.getState());return null==p?void 0:p.init(o(f.name));case"COMMIT":if(void 0===d){null==p||p.init(s.getState());break}return null==p?void 0:p.init(o(f.name));case"ROLLBACK":return u(e.state,e=>{if(void 0===d){S(e),null==p||p.init(s.getState());return}S(e[d]),null==p||p.init(o(f.name))});case"JUMP_TO_STATE":case"JUMP_TO_ACTION":return u(e.state,e=>{if(void 0===d){S(e);return}JSON.stringify(s.getState())!==JSON.stringify(e[d])&&S(e[d])});case"IMPORT_STATE":{let{nextLiftedState:n}=e.payload,r=null==(t=n.computedStates.slice(-1)[0])?void 0:t.state;if(!r)return;void 0===d?S(r):S(r[d]),null==p||p.send(null,n);break}case"PAUSE_RECORDING":return y=!y}return}}),h},u=(e,t)=>{let n;try{n=JSON.parse(e)}catch(e){console.error("[zustand devtools middleware] Could not parse the received json",e)}void 0!==n&&t(n)}}}]);