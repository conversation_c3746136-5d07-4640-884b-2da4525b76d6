(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[896],{5135:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(9763).Z)("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},598:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(9763).Z)("<PERSON>edin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]])},2351:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(9763).Z)("Twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]])},4742:function(e){e.exports={style:{fontFamily:"'__Inter_e8ce0c', '__Inter_Fallback_e8ce0c'",fontStyle:"normal"},className:"__className_e8ce0c"}},4080:function(e,t,r){"use strict";r.d(t,{aU:function(){return ev},x8:function(){return eb},dk:function(){return em},zt:function(){return ed},fC:function(){return ep},Dx:function(){return ef},l_:function(){return eu}});var n,o=r(2265),l=r.t(o,2),i=r(4887);function s(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function c(...e){return t=>{let r=!1,n=e.map(e=>{let n=a(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():a(e[t],null)}}}}function d(...e){return o.useCallback(c(...e),e)}var u=r(7437);function p(e,t=[]){let r=[],n=()=>{let t=r.map(e=>o.createContext(e));return function(r){let n=r?.[e]||t;return o.useMemo(()=>({[`__scope${e}`]:{...r,[e]:n}}),[r,n])}};return n.scopeName=e,[function(t,n){let l=o.createContext(n),i=r.length;r=[...r,n];let s=t=>{let{scope:r,children:n,...s}=t,a=r?.[e]?.[i]||l,c=o.useMemo(()=>s,Object.values(s));return(0,u.jsx)(a.Provider,{value:c,children:n})};return s.displayName=t+"Provider",[s,function(r,s){let a=s?.[e]?.[i]||l,c=o.useContext(a);if(c)return c;if(void 0!==n)return n;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let n=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return o.useMemo(()=>({[`__scope${t.scopeName}`]:n}),[n])}};return r.scopeName=t.scopeName,r}(n,...t)]}function f(e){let t=function(e){let t=o.forwardRef((e,t)=>{let{children:r,...n}=e;if(o.isValidElement(r)){let e,l;let i=(e=Object.getOwnPropertyDescriptor(r.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.ref:(e=Object.getOwnPropertyDescriptor(r,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.props.ref:r.props.ref||r.ref,s=function(e,t){let r={...t};for(let n in t){let o=e[n],l=t[n];/^on[A-Z]/.test(n)?o&&l?r[n]=(...e)=>{let t=l(...e);return o(...e),t}:o&&(r[n]=o):"style"===n?r[n]={...o,...l}:"className"===n&&(r[n]=[o,l].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props);return r.type!==o.Fragment&&(s.ref=t?c(t,i):i),o.cloneElement(r,s)}return o.Children.count(r)>1?o.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=o.forwardRef((e,r)=>{let{children:n,...l}=e,i=o.Children.toArray(n),s=i.find(v);if(s){let e=s.props.children,n=i.map(t=>t!==s?t:o.Children.count(e)>1?o.Children.only(null):o.isValidElement(e)?e.props.children:null);return(0,u.jsx)(t,{...l,ref:r,children:o.isValidElement(e)?o.cloneElement(e,void 0,n):null})}return(0,u.jsx)(t,{...l,ref:r,children:n})});return r.displayName=`${e}.Slot`,r}var m=Symbol("radix.slottable");function v(e){return o.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===m}var b=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=f(`Primitive.${t}`),n=o.forwardRef((e,n)=>{let{asChild:o,...l}=e,i=o?r:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,u.jsx)(i,{...l,ref:n})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function g(e,t){e&&i.flushSync(()=>e.dispatchEvent(t))}function y(e){let t=o.useRef(e);return o.useEffect(()=>{t.current=e}),o.useMemo(()=>(...e)=>t.current?.(...e),[])}var h="dismissableLayer.update",w=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),x=o.forwardRef((e,t)=>{var r,l;let{disableOutsidePointerEvents:i=!1,onEscapeKeyDown:a,onPointerDownOutside:c,onFocusOutside:p,onInteractOutside:f,onDismiss:m,...v}=e,g=o.useContext(w),[x,E]=o.useState(null),T=null!==(l=null==x?void 0:x.ownerDocument)&&void 0!==l?l:null===(r=globalThis)||void 0===r?void 0:r.document,[,N]=o.useState({}),P=d(t,e=>E(e)),R=Array.from(g.layers),[j]=[...g.layersWithOutsidePointerEventsDisabled].slice(-1),S=R.indexOf(j),L=x?R.indexOf(x):-1,z=g.layersWithOutsidePointerEventsDisabled.size>0,D=L>=S,M=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,n=y(e),l=o.useRef(!1),i=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!l.current){let t=function(){C("dismissableLayer.pointerDownOutside",n,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(r.removeEventListener("click",i.current),i.current=t,r.addEventListener("click",i.current,{once:!0})):t()}else r.removeEventListener("click",i.current);l.current=!1},t=window.setTimeout(()=>{r.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),r.removeEventListener("pointerdown",e),r.removeEventListener("click",i.current)}},[r,n]),{onPointerDownCapture:()=>l.current=!0}}(e=>{let t=e.target,r=[...g.branches].some(e=>e.contains(t));!D||r||(null==c||c(e),null==f||f(e),e.defaultPrevented||null==m||m())},T),O=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,n=y(e),l=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!l.current&&C("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return r.addEventListener("focusin",e),()=>r.removeEventListener("focusin",e)},[r,n]),{onFocusCapture:()=>l.current=!0,onBlurCapture:()=>l.current=!1}}(e=>{let t=e.target;[...g.branches].some(e=>e.contains(t))||(null==p||p(e),null==f||f(e),e.defaultPrevented||null==m||m())},T);return!function(e,t=globalThis?.document){let r=y(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{L!==g.layers.size-1||(null==a||a(e),!e.defaultPrevented&&m&&(e.preventDefault(),m()))},T),o.useEffect(()=>{if(x)return i&&(0===g.layersWithOutsidePointerEventsDisabled.size&&(n=T.body.style.pointerEvents,T.body.style.pointerEvents="none"),g.layersWithOutsidePointerEventsDisabled.add(x)),g.layers.add(x),k(),()=>{i&&1===g.layersWithOutsidePointerEventsDisabled.size&&(T.body.style.pointerEvents=n)}},[x,T,i,g]),o.useEffect(()=>()=>{x&&(g.layers.delete(x),g.layersWithOutsidePointerEventsDisabled.delete(x),k())},[x,g]),o.useEffect(()=>{let e=()=>N({});return document.addEventListener(h,e),()=>document.removeEventListener(h,e)},[]),(0,u.jsx)(b.div,{...v,ref:P,style:{pointerEvents:z?D?"auto":"none":void 0,...e.style},onFocusCapture:s(e.onFocusCapture,O.onFocusCapture),onBlurCapture:s(e.onBlurCapture,O.onBlurCapture),onPointerDownCapture:s(e.onPointerDownCapture,M.onPointerDownCapture)})});x.displayName="DismissableLayer";var E=o.forwardRef((e,t)=>{let r=o.useContext(w),n=o.useRef(null),l=d(t,n);return o.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,u.jsx)(b.div,{...e,ref:l})});function k(){let e=new CustomEvent(h);document.dispatchEvent(e)}function C(e,t,r,n){let{discrete:o}=n,l=r.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&l.addEventListener(e,t,{once:!0}),o?g(l,i):l.dispatchEvent(i)}E.displayName="DismissableLayerBranch";var T=globalThis?.document?o.useLayoutEffect:()=>{},N=o.forwardRef((e,t)=>{var r,n;let{container:l,...s}=e,[a,c]=o.useState(!1);T(()=>c(!0),[]);let d=l||a&&(null===(n=globalThis)||void 0===n?void 0:null===(r=n.document)||void 0===r?void 0:r.body);return d?i.createPortal((0,u.jsx)(b.div,{...s,ref:t}),d):null});N.displayName="Portal";var P=e=>{var t,r;let n,l;let{present:i,children:s}=e,a=function(e){var t,r;let[n,l]=o.useState(),i=o.useRef(null),s=o.useRef(e),a=o.useRef("none"),[c,d]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},o.useReducer((e,t)=>{let n=r[e][t];return null!=n?n:e},t));return o.useEffect(()=>{let e=R(i.current);a.current="mounted"===c?e:"none"},[c]),T(()=>{let t=i.current,r=s.current;if(r!==e){let n=a.current,o=R(t);e?d("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?d("UNMOUNT"):r&&n!==o?d("ANIMATION_OUT"):d("UNMOUNT"),s.current=e}},[e,d]),T(()=>{if(n){var e;let t;let r=null!==(e=n.ownerDocument.defaultView)&&void 0!==e?e:window,o=e=>{let o=R(i.current).includes(e.animationName);if(e.target===n&&o&&(d("ANIMATION_END"),!s.current)){let e=n.style.animationFillMode;n.style.animationFillMode="forwards",t=r.setTimeout(()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=e)})}},l=e=>{e.target===n&&(a.current=R(i.current))};return n.addEventListener("animationstart",l),n.addEventListener("animationcancel",o),n.addEventListener("animationend",o),()=>{r.clearTimeout(t),n.removeEventListener("animationstart",l),n.removeEventListener("animationcancel",o),n.removeEventListener("animationend",o)}}d("ANIMATION_END")},[n,d]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:o.useCallback(e=>{i.current=e?getComputedStyle(e):null,l(e)},[])}}(i),c="function"==typeof s?s({present:a.isPresent}):o.Children.only(s),u=d(a.ref,(n=null===(t=Object.getOwnPropertyDescriptor(c.props,"ref"))||void 0===t?void 0:t.get)&&"isReactWarning"in n&&n.isReactWarning?c.ref:(n=null===(r=Object.getOwnPropertyDescriptor(c,"ref"))||void 0===r?void 0:r.get)&&"isReactWarning"in n&&n.isReactWarning?c.props.ref:c.props.ref||c.ref);return"function"==typeof s||a.isPresent?o.cloneElement(c,{ref:u}):null};function R(e){return(null==e?void 0:e.animationName)||"none"}P.displayName="Presence";var j=l[" useInsertionEffect ".trim().toString()]||T;Symbol("RADIX:SYNC_STATE");var S=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),L=o.forwardRef((e,t)=>(0,u.jsx)(b.span,{...e,ref:t,style:{...S,...e.style}}));L.displayName="VisuallyHidden";var z="ToastProvider",[D,M,O]=function(e){let t=e+"CollectionProvider",[r,n]=p(t),[l,i]=r(t,{collectionRef:{current:null},itemMap:new Map}),s=e=>{let{scope:t,children:r}=e,n=o.useRef(null),i=o.useRef(new Map).current;return(0,u.jsx)(l,{scope:t,itemMap:i,collectionRef:n,children:r})};s.displayName=t;let a=e+"CollectionSlot",c=f(a),m=o.forwardRef((e,t)=>{let{scope:r,children:n}=e,o=d(t,i(a,r).collectionRef);return(0,u.jsx)(c,{ref:o,children:n})});m.displayName=a;let v=e+"CollectionItemSlot",b="data-radix-collection-item",g=f(v),y=o.forwardRef((e,t)=>{let{scope:r,children:n,...l}=e,s=o.useRef(null),a=d(t,s),c=i(v,r);return o.useEffect(()=>(c.itemMap.set(s,{ref:s,...l}),()=>void c.itemMap.delete(s))),(0,u.jsx)(g,{[b]:"",ref:a,children:n})});return y.displayName=v,[{Provider:s,Slot:m,ItemSlot:y},function(t){let r=i(e+"CollectionConsumer",t);return o.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(b,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},n]}("Toast"),[I,A]=p("Toast",[O]),[_,F]=I(z),W=e=>{let{__scopeToast:t,label:r="Notification",duration:n=5e3,swipeDirection:l="right",swipeThreshold:i=50,children:s}=e,[a,c]=o.useState(null),[d,p]=o.useState(0),f=o.useRef(!1),m=o.useRef(!1);return r.trim()||console.error("Invalid prop `label` supplied to `".concat(z,"`. Expected non-empty `string`.")),(0,u.jsx)(D.Provider,{scope:t,children:(0,u.jsx)(_,{scope:t,label:r,duration:n,swipeDirection:l,swipeThreshold:i,toastCount:d,viewport:a,onViewportChange:c,onToastAdd:o.useCallback(()=>p(e=>e+1),[]),onToastRemove:o.useCallback(()=>p(e=>e-1),[]),isFocusedToastEscapeKeyDownRef:f,isClosePausedRef:m,children:s})})};W.displayName=z;var $="ToastViewport",U=["F8"],K="toast.viewportPause",G="toast.viewportResume",V=o.forwardRef((e,t)=>{let{__scopeToast:r,hotkey:n=U,label:l="Notifications ({hotkey})",...i}=e,s=F($,r),a=M(r),c=o.useRef(null),p=o.useRef(null),f=o.useRef(null),m=o.useRef(null),v=d(t,m,s.onViewportChange),g=n.join("+").replace(/Key/g,"").replace(/Digit/g,""),y=s.toastCount>0;o.useEffect(()=>{let e=e=>{var t;0!==n.length&&n.every(t=>e[t]||e.code===t)&&(null===(t=m.current)||void 0===t||t.focus())};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[n]),o.useEffect(()=>{let e=c.current,t=m.current;if(y&&e&&t){let r=()=>{if(!s.isClosePausedRef.current){let e=new CustomEvent(K);t.dispatchEvent(e),s.isClosePausedRef.current=!0}},n=()=>{if(s.isClosePausedRef.current){let e=new CustomEvent(G);t.dispatchEvent(e),s.isClosePausedRef.current=!1}},o=t=>{e.contains(t.relatedTarget)||n()},l=()=>{e.contains(document.activeElement)||n()};return e.addEventListener("focusin",r),e.addEventListener("focusout",o),e.addEventListener("pointermove",r),e.addEventListener("pointerleave",l),window.addEventListener("blur",r),window.addEventListener("focus",n),()=>{e.removeEventListener("focusin",r),e.removeEventListener("focusout",o),e.removeEventListener("pointermove",r),e.removeEventListener("pointerleave",l),window.removeEventListener("blur",r),window.removeEventListener("focus",n)}}},[y,s.isClosePausedRef]);let h=o.useCallback(e=>{let{tabbingDirection:t}=e,r=a().map(e=>{let r=e.ref.current,n=[r,...function(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}(r)];return"forwards"===t?n:n.reverse()});return("forwards"===t?r.reverse():r).flat()},[a]);return o.useEffect(()=>{let e=m.current;if(e){let t=t=>{let r=t.altKey||t.ctrlKey||t.metaKey;if("Tab"===t.key&&!r){var n,o,l;let r=document.activeElement,i=t.shiftKey;if(t.target===e&&i){null===(n=p.current)||void 0===n||n.focus();return}let s=h({tabbingDirection:i?"backwards":"forwards"}),a=s.findIndex(e=>e===r);ec(s.slice(a+1))?t.preventDefault():i?null===(o=p.current)||void 0===o||o.focus():null===(l=f.current)||void 0===l||l.focus()}};return e.addEventListener("keydown",t),()=>e.removeEventListener("keydown",t)}},[a,h]),(0,u.jsxs)(E,{ref:c,role:"region","aria-label":l.replace("{hotkey}",g),tabIndex:-1,style:{pointerEvents:y?void 0:"none"},children:[y&&(0,u.jsx)(q,{ref:p,onFocusFromOutsideViewport:()=>{ec(h({tabbingDirection:"forwards"}))}}),(0,u.jsx)(D.Slot,{scope:r,children:(0,u.jsx)(b.ol,{tabIndex:-1,...i,ref:v})}),y&&(0,u.jsx)(q,{ref:f,onFocusFromOutsideViewport:()=>{ec(h({tabbingDirection:"backwards"}))}})]})});V.displayName=$;var Z="ToastFocusProxy",q=o.forwardRef((e,t)=>{let{__scopeToast:r,onFocusFromOutsideViewport:n,...o}=e,l=F(Z,r);return(0,u.jsx)(L,{"aria-hidden":!0,tabIndex:0,...o,ref:t,style:{position:"fixed"},onFocus:e=>{var t;let r=e.relatedTarget;(null===(t=l.viewport)||void 0===t?void 0:t.contains(r))||n()}})});q.displayName=Z;var B="Toast",X=o.forwardRef((e,t)=>{let{forceMount:r,open:n,defaultOpen:l,onOpenChange:i,...a}=e,[c,d]=function({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){let[l,i,s]=function({defaultProp:e,onChange:t}){let[r,n]=o.useState(e),l=o.useRef(r),i=o.useRef(t);return j(()=>{i.current=t},[t]),o.useEffect(()=>{l.current!==r&&(i.current?.(r),l.current=r)},[r,l]),[r,n,i]}({defaultProp:t,onChange:r}),a=void 0!==e,c=a?e:l;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==a){let t=a?"controlled":"uncontrolled";console.warn(`${n} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=a},[a,n])}return[c,o.useCallback(t=>{if(a){let r="function"==typeof t?t(e):t;r!==e&&s.current?.(r)}else i(t)},[a,e,i,s])]}({prop:n,defaultProp:null==l||l,onChange:i,caller:B});return(0,u.jsx)(P,{present:r||c,children:(0,u.jsx)(J,{open:c,...a,ref:t,onClose:()=>d(!1),onPause:y(e.onPause),onResume:y(e.onResume),onSwipeStart:s(e.onSwipeStart,e=>{e.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:s(e.onSwipeMove,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","move"),e.currentTarget.style.setProperty("--radix-toast-swipe-move-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-move-y","".concat(r,"px"))}),onSwipeCancel:s(e.onSwipeCancel,e=>{e.currentTarget.setAttribute("data-swipe","cancel"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:s(e.onSwipeEnd,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","end"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.setProperty("--radix-toast-swipe-end-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-end-y","".concat(r,"px")),d(!1)})})})});X.displayName=B;var[H,Y]=I(B,{onClose(){}}),J=o.forwardRef((e,t)=>{let{__scopeToast:r,type:n="foreground",duration:l,open:a,onClose:c,onEscapeKeyDown:p,onPause:f,onResume:m,onSwipeStart:v,onSwipeMove:g,onSwipeCancel:h,onSwipeEnd:w,...E}=e,k=F(B,r),[C,T]=o.useState(null),N=d(t,e=>T(e)),P=o.useRef(null),R=o.useRef(null),j=l||k.duration,S=o.useRef(0),L=o.useRef(j),z=o.useRef(0),{onToastAdd:M,onToastRemove:O}=k,I=y(()=>{var e;(null==C?void 0:C.contains(document.activeElement))&&(null===(e=k.viewport)||void 0===e||e.focus()),c()}),A=o.useCallback(e=>{e&&e!==1/0&&(window.clearTimeout(z.current),S.current=new Date().getTime(),z.current=window.setTimeout(I,e))},[I]);o.useEffect(()=>{let e=k.viewport;if(e){let t=()=>{A(L.current),null==m||m()},r=()=>{let e=new Date().getTime()-S.current;L.current=L.current-e,window.clearTimeout(z.current),null==f||f()};return e.addEventListener(K,r),e.addEventListener(G,t),()=>{e.removeEventListener(K,r),e.removeEventListener(G,t)}}},[k.viewport,j,f,m,A]),o.useEffect(()=>{a&&!k.isClosePausedRef.current&&A(j)},[a,j,k.isClosePausedRef,A]),o.useEffect(()=>(M(),()=>O()),[M,O]);let _=o.useMemo(()=>C?function e(t){let r=[];return Array.from(t.childNodes).forEach(t=>{if(t.nodeType===t.TEXT_NODE&&t.textContent&&r.push(t.textContent),t.nodeType===t.ELEMENT_NODE){let n=t.ariaHidden||t.hidden||"none"===t.style.display,o=""===t.dataset.radixToastAnnounceExclude;if(!n){if(o){let e=t.dataset.radixToastAnnounceAlt;e&&r.push(e)}else r.push(...e(t))}}}),r}(C):null,[C]);return k.viewport?(0,u.jsxs)(u.Fragment,{children:[_&&(0,u.jsx)(Q,{__scopeToast:r,role:"status","aria-live":"foreground"===n?"assertive":"polite","aria-atomic":!0,children:_}),(0,u.jsx)(H,{scope:r,onClose:I,children:i.createPortal((0,u.jsx)(D.ItemSlot,{scope:r,children:(0,u.jsx)(x,{asChild:!0,onEscapeKeyDown:s(p,()=>{k.isFocusedToastEscapeKeyDownRef.current||I(),k.isFocusedToastEscapeKeyDownRef.current=!1}),children:(0,u.jsx)(b.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":a?"open":"closed","data-swipe-direction":k.swipeDirection,...E,ref:N,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:s(e.onKeyDown,e=>{"Escape"!==e.key||(null==p||p(e.nativeEvent),e.nativeEvent.defaultPrevented||(k.isFocusedToastEscapeKeyDownRef.current=!0,I()))}),onPointerDown:s(e.onPointerDown,e=>{0===e.button&&(P.current={x:e.clientX,y:e.clientY})}),onPointerMove:s(e.onPointerMove,e=>{if(!P.current)return;let t=e.clientX-P.current.x,r=e.clientY-P.current.y,n=!!R.current,o=["left","right"].includes(k.swipeDirection),l=["left","up"].includes(k.swipeDirection)?Math.min:Math.max,i=o?l(0,t):0,s=o?0:l(0,r),a="touch"===e.pointerType?10:2,c={x:i,y:s},d={originalEvent:e,delta:c};n?(R.current=c,es("toast.swipeMove",g,d,{discrete:!1})):ea(c,k.swipeDirection,a)?(R.current=c,es("toast.swipeStart",v,d,{discrete:!1}),e.target.setPointerCapture(e.pointerId)):(Math.abs(t)>a||Math.abs(r)>a)&&(P.current=null)}),onPointerUp:s(e.onPointerUp,e=>{let t=R.current,r=e.target;if(r.hasPointerCapture(e.pointerId)&&r.releasePointerCapture(e.pointerId),R.current=null,P.current=null,t){let r=e.currentTarget,n={originalEvent:e,delta:t};ea(t,k.swipeDirection,k.swipeThreshold)?es("toast.swipeEnd",w,n,{discrete:!0}):es("toast.swipeCancel",h,n,{discrete:!0}),r.addEventListener("click",e=>e.preventDefault(),{once:!0})}})})})}),k.viewport)})]}):null}),Q=e=>{let{__scopeToast:t,children:r,...n}=e,l=F(B,t),[i,s]=o.useState(!1),[a,c]=o.useState(!1);return function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:()=>{},t=y(e);T(()=>{let e=0,r=0;return e=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(e),window.cancelAnimationFrame(r)}},[t])}(()=>s(!0)),o.useEffect(()=>{let e=window.setTimeout(()=>c(!0),1e3);return()=>window.clearTimeout(e)},[]),a?null:(0,u.jsx)(N,{asChild:!0,children:(0,u.jsx)(L,{...n,children:i&&(0,u.jsxs)(u.Fragment,{children:[l.label," ",r]})})})},ee=o.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e;return(0,u.jsx)(b.div,{...n,ref:t})});ee.displayName="ToastTitle";var et=o.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e;return(0,u.jsx)(b.div,{...n,ref:t})});et.displayName="ToastDescription";var er="ToastAction",en=o.forwardRef((e,t)=>{let{altText:r,...n}=e;return r.trim()?(0,u.jsx)(ei,{altText:r,asChild:!0,children:(0,u.jsx)(el,{...n,ref:t})}):(console.error("Invalid prop `altText` supplied to `".concat(er,"`. Expected non-empty `string`.")),null)});en.displayName=er;var eo="ToastClose",el=o.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e,o=Y(eo,r);return(0,u.jsx)(ei,{asChild:!0,children:(0,u.jsx)(b.button,{type:"button",...n,ref:t,onClick:s(e.onClick,o.onClose)})})});el.displayName=eo;var ei=o.forwardRef((e,t)=>{let{__scopeToast:r,altText:n,...o}=e;return(0,u.jsx)(b.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":n||void 0,...o,ref:t})});function es(e,t,r,n){let{discrete:o}=n,l=r.originalEvent.currentTarget,i=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:r});t&&l.addEventListener(e,t,{once:!0}),o?g(l,i):l.dispatchEvent(i)}var ea=function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=Math.abs(e.x),o=Math.abs(e.y),l=n>o;return"left"===t||"right"===t?l&&n>r:!l&&o>r};function ec(e){let t=document.activeElement;return e.some(e=>e===t||(e.focus(),document.activeElement!==t))}var ed=W,eu=V,ep=X,ef=ee,em=et,ev=en,eb=el},535:function(e,t,r){"use strict";r.d(t,{j:function(){return i}});var n=r(1994);let o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,l=n.W,i=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return l(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:i,defaultVariants:s}=t,a=Object.keys(i).map(e=>{let t=null==r?void 0:r[e],n=null==s?void 0:s[e];if(null===t)return null;let l=o(t)||o(n);return i[e][l]}),c=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return l(e,a,null==t?void 0:null===(n=t.compoundVariants)||void 0===n?void 0:n.reduce((e,t)=>{let{class:r,className:n,...o}=t;return Object.entries(o).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...s,...c}[t]):({...s,...c})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},1994:function(e,t,r){"use strict";function n(){for(var e,t,r=0,n="",o=arguments.length;r<o;r++)(e=arguments[r])&&(t=function e(t){var r,n,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t){if(Array.isArray(t)){var l=t.length;for(r=0;r<l;r++)t[r]&&(n=e(t[r]))&&(o&&(o+=" "),o+=n)}else for(n in t)t[n]&&(o&&(o+=" "),o+=n)}return o}(e))&&(n&&(n+=" "),n+=t);return n}r.d(t,{W:function(){return n}})},3335:function(e,t,r){"use strict";r.d(t,{m6:function(){return X}});let n=e=>{let t=s(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:n}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),o(r,t)||i(e)},getConflictingClassGroupIds:(e,t)=>{let o=r[e]||[];return t&&n[e]?[...o,...n[e]]:o}}},o=(e,t)=>{if(0===e.length)return t.classGroupId;let r=e[0],n=t.nextPart.get(r),l=n?o(e.slice(1),n):void 0;if(l)return l;if(0===t.validators.length)return;let i=e.join("-");return t.validators.find(({validator:e})=>e(i))?.classGroupId},l=/^\[(.+)\]$/,i=e=>{if(l.test(e)){let t=l.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},s=e=>{let{theme:t,prefix:r}=e,n={nextPart:new Map,validators:[]};return u(Object.entries(e.classGroups),r).forEach(([e,r])=>{a(r,n,e,t)}),n},a=(e,t,r,n)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:c(t,e)).classGroupId=r;return}if("function"==typeof e){if(d(e)){a(e(n),t,r,n);return}t.validators.push({validator:e,classGroupId:r});return}Object.entries(e).forEach(([e,o])=>{a(o,c(t,e),r,n)})})},c=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},d=e=>e.isThemeGetter,u=(e,t)=>t?e.map(([e,r])=>[e,r.map(e=>"string"==typeof e?t+e:"object"==typeof e?Object.fromEntries(Object.entries(e).map(([e,r])=>[t+e,r])):e)]):e,p=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,n=new Map,o=(o,l)=>{r.set(o,l),++t>e&&(t=0,n=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=n.get(e))?(o(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):o(e,t)}}},f=e=>{let{separator:t,experimentalParseClassName:r}=e,n=1===t.length,o=t[0],l=t.length,i=e=>{let r;let i=[],s=0,a=0;for(let c=0;c<e.length;c++){let d=e[c];if(0===s){if(d===o&&(n||e.slice(c,c+l)===t)){i.push(e.slice(a,c)),a=c+l;continue}if("/"===d){r=c;continue}}"["===d?s++:"]"===d&&s--}let c=0===i.length?e:e.substring(a),d=c.startsWith("!"),u=d?c.substring(1):c;return{modifiers:i,hasImportantModifier:d,baseClassName:u,maybePostfixModifierPosition:r&&r>a?r-a:void 0}};return r?e=>r({className:e,parseClassName:i}):i},m=e=>{if(e.length<=1)return e;let t=[],r=[];return e.forEach(e=>{"["===e[0]?(t.push(...r.sort(),e),r=[]):r.push(e)}),t.push(...r.sort()),t},v=e=>({cache:p(e.cacheSize),parseClassName:f(e),...n(e)}),b=/\s+/,g=(e,t)=>{let{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:o}=t,l=[],i=e.trim().split(b),s="";for(let e=i.length-1;e>=0;e-=1){let t=i[e],{modifiers:a,hasImportantModifier:c,baseClassName:d,maybePostfixModifierPosition:u}=r(t),p=!!u,f=n(p?d.substring(0,u):d);if(!f){if(!p||!(f=n(d))){s=t+(s.length>0?" "+s:s);continue}p=!1}let v=m(a).join(":"),b=c?v+"!":v,g=b+f;if(l.includes(g))continue;l.push(g);let y=o(f,p);for(let e=0;e<y.length;++e){let t=y[e];l.push(b+t)}s=t+(s.length>0?" "+s:s)}return s};function y(){let e,t,r=0,n="";for(;r<arguments.length;)(e=arguments[r++])&&(t=h(e))&&(n&&(n+=" "),n+=t);return n}let h=e=>{let t;if("string"==typeof e)return e;let r="";for(let n=0;n<e.length;n++)e[n]&&(t=h(e[n]))&&(r&&(r+=" "),r+=t);return r},w=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},x=/^\[(?:([a-z-]+):)?(.+)\]$/i,E=/^\d+\/\d+$/,k=new Set(["px","full","screen"]),C=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,T=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,N=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,P=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,R=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,j=e=>L(e)||k.has(e)||E.test(e),S=e=>G(e,"length",V),L=e=>!!e&&!Number.isNaN(Number(e)),z=e=>G(e,"number",L),D=e=>!!e&&Number.isInteger(Number(e)),M=e=>e.endsWith("%")&&L(e.slice(0,-1)),O=e=>x.test(e),I=e=>C.test(e),A=new Set(["length","size","percentage"]),_=e=>G(e,A,Z),F=e=>G(e,"position",Z),W=new Set(["image","url"]),$=e=>G(e,W,B),U=e=>G(e,"",q),K=()=>!0,G=(e,t,r)=>{let n=x.exec(e);return!!n&&(n[1]?"string"==typeof t?n[1]===t:t.has(n[1]):r(n[2]))},V=e=>T.test(e)&&!N.test(e),Z=()=>!1,q=e=>P.test(e),B=e=>R.test(e),X=function(e,...t){let r,n,o;let l=function(s){return n=(r=v(t.reduce((e,t)=>t(e),e()))).cache.get,o=r.cache.set,l=i,i(s)};function i(e){let t=n(e);if(t)return t;let l=g(e,r);return o(e,l),l}return function(){return l(y.apply(null,arguments))}}(()=>{let e=w("colors"),t=w("spacing"),r=w("blur"),n=w("brightness"),o=w("borderColor"),l=w("borderRadius"),i=w("borderSpacing"),s=w("borderWidth"),a=w("contrast"),c=w("grayscale"),d=w("hueRotate"),u=w("invert"),p=w("gap"),f=w("gradientColorStops"),m=w("gradientColorStopPositions"),v=w("inset"),b=w("margin"),g=w("opacity"),y=w("padding"),h=w("saturate"),x=w("scale"),E=w("sepia"),k=w("skew"),C=w("space"),T=w("translate"),N=()=>["auto","contain","none"],P=()=>["auto","hidden","clip","visible","scroll"],R=()=>["auto",O,t],A=()=>[O,t],W=()=>["",j,S],G=()=>["auto",L,O],V=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],Z=()=>["solid","dashed","dotted","double","none"],q=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],B=()=>["start","end","center","between","around","evenly","stretch"],X=()=>["","0",O],H=()=>["auto","avoid","all","avoid-page","page","left","right","column"],Y=()=>[L,O];return{cacheSize:500,separator:":",theme:{colors:[K],spacing:[j,S],blur:["none","",I,O],brightness:Y(),borderColor:[e],borderRadius:["none","","full",I,O],borderSpacing:A(),borderWidth:W(),contrast:Y(),grayscale:X(),hueRotate:Y(),invert:X(),gap:A(),gradientColorStops:[e],gradientColorStopPositions:[M,S],inset:R(),margin:R(),opacity:Y(),padding:A(),saturate:Y(),scale:Y(),sepia:X(),skew:Y(),space:A(),translate:A()},classGroups:{aspect:[{aspect:["auto","square","video",O]}],container:["container"],columns:[{columns:[I]}],"break-after":[{"break-after":H()}],"break-before":[{"break-before":H()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...V(),O]}],overflow:[{overflow:P()}],"overflow-x":[{"overflow-x":P()}],"overflow-y":[{"overflow-y":P()}],overscroll:[{overscroll:N()}],"overscroll-x":[{"overscroll-x":N()}],"overscroll-y":[{"overscroll-y":N()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[v]}],"inset-x":[{"inset-x":[v]}],"inset-y":[{"inset-y":[v]}],start:[{start:[v]}],end:[{end:[v]}],top:[{top:[v]}],right:[{right:[v]}],bottom:[{bottom:[v]}],left:[{left:[v]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",D,O]}],basis:[{basis:R()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",O]}],grow:[{grow:X()}],shrink:[{shrink:X()}],order:[{order:["first","last","none",D,O]}],"grid-cols":[{"grid-cols":[K]}],"col-start-end":[{col:["auto",{span:["full",D,O]},O]}],"col-start":[{"col-start":G()}],"col-end":[{"col-end":G()}],"grid-rows":[{"grid-rows":[K]}],"row-start-end":[{row:["auto",{span:[D,O]},O]}],"row-start":[{"row-start":G()}],"row-end":[{"row-end":G()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",O]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",O]}],gap:[{gap:[p]}],"gap-x":[{"gap-x":[p]}],"gap-y":[{"gap-y":[p]}],"justify-content":[{justify:["normal",...B()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...B(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...B(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[y]}],px:[{px:[y]}],py:[{py:[y]}],ps:[{ps:[y]}],pe:[{pe:[y]}],pt:[{pt:[y]}],pr:[{pr:[y]}],pb:[{pb:[y]}],pl:[{pl:[y]}],m:[{m:[b]}],mx:[{mx:[b]}],my:[{my:[b]}],ms:[{ms:[b]}],me:[{me:[b]}],mt:[{mt:[b]}],mr:[{mr:[b]}],mb:[{mb:[b]}],ml:[{ml:[b]}],"space-x":[{"space-x":[C]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[C]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",O,t]}],"min-w":[{"min-w":[O,t,"min","max","fit"]}],"max-w":[{"max-w":[O,t,"none","full","min","max","fit","prose",{screen:[I]},I]}],h:[{h:[O,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[O,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[O,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[O,t,"auto","min","max","fit"]}],"font-size":[{text:["base",I,S]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",z]}],"font-family":[{font:[K]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",O]}],"line-clamp":[{"line-clamp":["none",L,z]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",j,O]}],"list-image":[{"list-image":["none",O]}],"list-style-type":[{list:["none","disc","decimal",O]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[g]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[g]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...Z(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",j,S]}],"underline-offset":[{"underline-offset":["auto",j,O]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:A()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",O]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",O]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[g]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...V(),F]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",_]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},$]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[m]}],"gradient-via-pos":[{via:[m]}],"gradient-to-pos":[{to:[m]}],"gradient-from":[{from:[f]}],"gradient-via":[{via:[f]}],"gradient-to":[{to:[f]}],rounded:[{rounded:[l]}],"rounded-s":[{"rounded-s":[l]}],"rounded-e":[{"rounded-e":[l]}],"rounded-t":[{"rounded-t":[l]}],"rounded-r":[{"rounded-r":[l]}],"rounded-b":[{"rounded-b":[l]}],"rounded-l":[{"rounded-l":[l]}],"rounded-ss":[{"rounded-ss":[l]}],"rounded-se":[{"rounded-se":[l]}],"rounded-ee":[{"rounded-ee":[l]}],"rounded-es":[{"rounded-es":[l]}],"rounded-tl":[{"rounded-tl":[l]}],"rounded-tr":[{"rounded-tr":[l]}],"rounded-br":[{"rounded-br":[l]}],"rounded-bl":[{"rounded-bl":[l]}],"border-w":[{border:[s]}],"border-w-x":[{"border-x":[s]}],"border-w-y":[{"border-y":[s]}],"border-w-s":[{"border-s":[s]}],"border-w-e":[{"border-e":[s]}],"border-w-t":[{"border-t":[s]}],"border-w-r":[{"border-r":[s]}],"border-w-b":[{"border-b":[s]}],"border-w-l":[{"border-l":[s]}],"border-opacity":[{"border-opacity":[g]}],"border-style":[{border:[...Z(),"hidden"]}],"divide-x":[{"divide-x":[s]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[s]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[g]}],"divide-style":[{divide:Z()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-s":[{"border-s":[o]}],"border-color-e":[{"border-e":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...Z()]}],"outline-offset":[{"outline-offset":[j,O]}],"outline-w":[{outline:[j,S]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:W()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[g]}],"ring-offset-w":[{"ring-offset":[j,S]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",I,U]}],"shadow-color":[{shadow:[K]}],opacity:[{opacity:[g]}],"mix-blend":[{"mix-blend":[...q(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":q()}],filter:[{filter:["","none"]}],blur:[{blur:[r]}],brightness:[{brightness:[n]}],contrast:[{contrast:[a]}],"drop-shadow":[{"drop-shadow":["","none",I,O]}],grayscale:[{grayscale:[c]}],"hue-rotate":[{"hue-rotate":[d]}],invert:[{invert:[u]}],saturate:[{saturate:[h]}],sepia:[{sepia:[E]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[r]}],"backdrop-brightness":[{"backdrop-brightness":[n]}],"backdrop-contrast":[{"backdrop-contrast":[a]}],"backdrop-grayscale":[{"backdrop-grayscale":[c]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[d]}],"backdrop-invert":[{"backdrop-invert":[u]}],"backdrop-opacity":[{"backdrop-opacity":[g]}],"backdrop-saturate":[{"backdrop-saturate":[h]}],"backdrop-sepia":[{"backdrop-sepia":[E]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[i]}],"border-spacing-x":[{"border-spacing-x":[i]}],"border-spacing-y":[{"border-spacing-y":[i]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",O]}],duration:[{duration:Y()}],ease:[{ease:["linear","in","out","in-out",O]}],delay:[{delay:Y()}],animate:[{animate:["none","spin","ping","pulse","bounce",O]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[x]}],"scale-x":[{"scale-x":[x]}],"scale-y":[{"scale-y":[x]}],rotate:[{rotate:[D,O]}],"translate-x":[{"translate-x":[T]}],"translate-y":[{"translate-y":[T]}],"skew-x":[{"skew-x":[k]}],"skew-y":[{"skew-y":[k]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",O]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",O]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":A()}],"scroll-mx":[{"scroll-mx":A()}],"scroll-my":[{"scroll-my":A()}],"scroll-ms":[{"scroll-ms":A()}],"scroll-me":[{"scroll-me":A()}],"scroll-mt":[{"scroll-mt":A()}],"scroll-mr":[{"scroll-mr":A()}],"scroll-mb":[{"scroll-mb":A()}],"scroll-ml":[{"scroll-ml":A()}],"scroll-p":[{"scroll-p":A()}],"scroll-px":[{"scroll-px":A()}],"scroll-py":[{"scroll-py":A()}],"scroll-ps":[{"scroll-ps":A()}],"scroll-pe":[{"scroll-pe":A()}],"scroll-pt":[{"scroll-pt":A()}],"scroll-pr":[{"scroll-pr":A()}],"scroll-pb":[{"scroll-pb":A()}],"scroll-pl":[{"scroll-pl":A()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",O]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[j,S,z]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}})}}]);