{"version": 3, "file": "static/chunks/pages/_app-62e8dfa5ca0e8d5d.js", "mappings": "oFACA,CAAAA,OAAAC,QAAA,CAAAD,OAAAC,QAAA,MAAAC,IAAA,EACA,QACA,WACA,OAAeC,EAAQ,KACvB,EACA", "sources": ["webpack://_N_E/"], "sourcesContent": ["\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/_app\",\n      function () {\n        return require(\"next/dist/pages/_app\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/_app\"])\n      });\n    }\n  "], "names": ["window", "__NEXT_P", "push", "__webpack_require__"], "sourceRoot": ""}