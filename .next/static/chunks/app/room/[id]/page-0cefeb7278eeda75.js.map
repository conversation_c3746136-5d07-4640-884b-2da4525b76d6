{"version": 3, "file": "static/chunks/app/room/[id]/page-0cefeb7278eeda75.js", "mappings": "oFAAAA,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,0ICEA,OAAME,EAIJC,SAAU,KACJC,QAAJ,QAAIA,CAAAA,EAAA,IAAI,CAACC,MAAM,GAAXD,KAAA,IAAAA,EAAA,OAAAA,EAAaE,SAAS,IAE1B,IAAI,CAACD,MAAM,CAAGE,CAAAA,EAAAA,EAAAA,EAAAA,EACVC,OAAOC,QAAQ,CAACC,MAAM,CACG,CAC3BC,WAAY,CAAC,YAAa,UAAU,CACpCC,QAAS,EACX,GAEA,IAAI,CAACP,MAAM,CAACQ,EAAE,CAAC,UAAW,SACYT,EAApCU,QAAQC,GAAG,CAAC,8BAAwBX,CAAAA,EAAA,IAAI,CAACC,MAAM,GAAXD,KAAA,IAAAA,EAAA,OAAAA,EAAaY,EAAE,CACrD,GAEA,IAAI,CAACX,MAAM,CAACQ,EAAE,CAAC,aAAc,KAC3BC,QAAQC,GAAG,CAAC,2BACd,GAEA,IAAI,CAACV,MAAM,CAACQ,EAAE,CAAC,gBAAiB,IAC9BC,QAAQG,KAAK,CAAC,oBAAqBA,EACrC,IAnBmC,IAAI,CAACZ,MAAM,CAwBhDa,YAAa,CACP,IAAI,CAACb,MAAM,GACb,IAAI,CAACA,MAAM,CAACa,UAAU,GACtB,IAAI,CAACb,MAAM,CAAG,KAElB,CAEAc,SAASC,CAAc,CAAEC,CAAc,CAAEC,CAAgB,CAAE,CACpD,IAAI,CAACjB,MAAM,GAEhB,IAAI,CAACe,MAAM,CAAGA,EACd,IAAI,CAACf,MAAM,CAACkB,IAAI,CAAC,YAAa,CAAEH,OAAAA,EAAQC,OAAAA,EAAQC,SAAAA,CAAS,GAC3D,CAEAE,WAAY,CACL,IAAI,CAACnB,MAAM,EAAK,IAAI,CAACe,MAAM,GAEhC,IAAI,CAACf,MAAM,CAACkB,IAAI,CAAC,aAAc,CAAEH,OAAQ,IAAI,CAACA,MAAM,GACpD,IAAI,CAACA,MAAM,CAAG,KAChB,CAEAK,WAAWC,CAAoB,CAAEC,CAAW,CAAE,CACvC,IAAI,CAACtB,MAAM,EAAK,IAAI,CAACe,MAAM,EAEhC,IAAI,CAACf,MAAM,CAACkB,IAAI,CAAC,SAAU,CACzBH,OAAQ,IAAI,CAACA,MAAM,CACnBM,aAAAA,EACAC,OAAAA,CACF,EACF,CAEAC,gBAAgBC,CAAe,CAAEP,CAAgB,CAAE,CAC5C,IAAI,CAACjB,MAAM,EAAK,IAAI,CAACe,MAAM,EAEhC,IAAI,CAACf,MAAM,CAACkB,IAAI,CAAC,eAAgB,CAC/BH,OAAQ,IAAI,CAACA,MAAM,CACnBS,QAAAA,EACAP,SAAAA,EACAQ,UAAW,IAAIC,OAAOC,WAAW,EACnC,EACF,CAEAnB,GAAGoB,CAAa,CAAEC,CAAkC,CAAE,CAC/C,IAAI,CAAC7B,MAAM,EAEhB,IAAI,CAACA,MAAM,CAACQ,EAAE,CAACoB,EAAOC,EACxB,CAEAC,IAAIF,CAAa,CAAEC,CAAmC,CAAE,CACjD,IAAI,CAAC7B,MAAM,EAEhB,IAAI,CAACA,MAAM,CAAC8B,GAAG,CAACF,EAAOC,EACzB,CAEAE,WAAY,CACV,OAAO,IAAI,CAAC/B,MAAM,CAGpBgC,aAAc,KACLjC,EAAP,MAAO,QAAAA,CAAAA,EAAA,IAAI,CAACC,MAAM,GAAXD,KAAA,IAAAA,EAAA,OAAAA,EAAaE,SAAS,GAAI,EACnC,oBAxFQD,MAAAA,CAAwB,UACxBe,MAAAA,CAAwB,KAwFlC,CAEO,IAAMkB,EAAgB,IAAIpC,EC3F3BqC,EAA+B,CACnCC,WAAY,CACV,CAAEC,KAAM,8BAA+B,EACvC,CAAEA,KAAM,+BAAgC,EACzC,CACDC,qBAAsB,GACtBC,aAAc,aACdC,cAAe,SACjB,EAGaC,EAA4C,CACvDC,MAAO,CACLC,MAAO,CAAEC,MAAO,KAAMC,IAAK,IAAK,EAChCC,OAAQ,CAAEF,MAAO,IAAKC,IAAK,IAAK,EAChCE,UAAW,CAAEH,MAAO,GAAIC,IAAK,EAAG,EAChCG,WAAY,MACd,EACAC,MAAO,CACLC,iBAAkB,GAClBC,iBAAkB,GAClBC,gBAAiB,GACjBC,WAAY,IACd,CACF,EAEaC,EAA2B,CACtCZ,MAAO,CACLC,MAAO,CAAEC,MAAO,KAAMC,IAAK,IAAK,EAChCC,OAAQ,CAAEF,MAAO,KAAMC,IAAK,IAAK,EACjCE,UAAW,CAAEH,MAAO,GAAIC,IAAK,EAAG,CAClC,EACAI,MAAO,EACT,CAEO,OAAMM,EAcHC,oBAAqB,CAGzBpD,OAAOqD,gBAAgB,CAAC,eAAgB,KACtC,IAAI,CAACC,OAAO,EACd,EAEJ,CAEA,MAAMC,cAA4F,KAA/EC,EAAAA,UAAAA,MAAAA,CAAAA,GAAAA,KAAAA,IAAAA,SAAAA,CAAAA,EAAAA,CAAAA,SAAAA,CAAAA,EAAAA,CAAsCnB,EACvD,GAAI,CACF,IAAI,CAACoB,WAAW,CAAG,MAAMC,UAAUC,YAAY,CAACJ,YAAY,CAACC,GAC7D,IAAI,CAACI,YAAY,CAAG,IAAI,CAACH,WAAW,CAGpC,IAAMI,EAAc,IAAI,CAACJ,WAAW,CAACK,cAAc,EAC/CD,CAAAA,EAAYE,MAAM,CAAG,GACvB,KAAI,CAACC,oBAAoB,CAAGH,CAAW,CAAC,EAAE,CAACI,WAAW,GAAGC,QAAQ,EAAI,MAGvE,IAAMC,EAAc,IAAI,CAACV,WAAW,CAACW,cAAc,GAKnD,OAJID,EAAYJ,MAAM,CAAG,GACvB,KAAI,CAACM,oBAAoB,CAAGF,CAAW,CAAC,EAAE,CAACF,WAAW,GAAGC,QAAQ,EAAI,MAGhE,IAAI,CAACT,WAAW,CACvB,MAAOhD,EAAO,CAEd,MADAH,QAAQG,KAAK,CAAC,iCAAkCA,GAC1CA,CACR,CACF,CAGA,MAAM6D,iBAAsG,KAAtFd,EAAAA,UAAAA,MAAAA,CAAAA,GAAAA,KAAAA,IAAAA,SAAAA,CAAAA,EAAAA,CAAAA,SAAAA,CAAAA,EAAAA,CAAsCN,EAC1D,GAAI,CAQF,OAPA,IAAI,CAACqB,YAAY,CAAG,MAAMb,UAAUC,YAAY,CAACW,eAAe,CAACd,GAGjE,IAAI,CAACe,YAAY,CAACT,cAAc,EAAE,CAAC,EAAE,CAACU,OAAO,CAAG,KAC9C,IAAI,CAACC,iBAAiB,EACxB,EAEO,IAAI,CAACF,YAAY,CACxB,MAAO9D,EAAO,CAEd,MADAH,QAAQG,KAAK,CAAC,gCAAiCA,GACzCA,CACR,CACF,CAIAiE,kBAAkBC,CAAmB,CAAE,CACrC,GAAI,CAAC,IAAI,CAAClB,WAAW,CAAE,OAGvB,IAAI,CAACA,WAAW,CAACW,cAAc,GAAGQ,OAAO,CAACC,GAASA,EAAMC,IAAI,IAG7D,IAAMC,EAAaJ,EAAOP,cAAc,EAAE,CAAC,EAAE,CACzCW,GACF,IAAI,CAACtB,WAAW,CAACuB,QAAQ,CAACD,GAI5B,IAAI,CAACE,iBAAiB,EACxB,CAEAR,mBAAoB,CAClB,GAAI,CAAC,IAAI,CAAChB,WAAW,EAAI,CAAC,IAAI,CAACG,YAAY,CAAE,OAG7C,IAAI,CAACH,WAAW,CAACK,cAAc,GAAGc,OAAO,CAACC,GAASA,EAAMC,IAAI,IAG7D,IAAMI,EAAa,IAAI,CAACtB,YAAY,CAACE,cAAc,EAAE,CAAC,EAAE,CACpDoB,GACF,IAAI,CAACzB,WAAW,CAACuB,QAAQ,CAACE,EAAWC,KAAK,IAI5C,IAAI,CAACC,iBAAiB,GAGlB,IAAI,CAACb,YAAY,GACnB,IAAI,CAACA,YAAY,CAACc,SAAS,GAAGT,OAAO,CAACC,GAASA,EAAMC,IAAI,IACzD,IAAI,CAACP,YAAY,CAAG,KAExB,CAEQa,mBAAoB,CAC1B,GAAI,CAAC,IAAI,CAAC3B,WAAW,CAAE,OAEvB,IAAMyB,EAAa,IAAI,CAACzB,WAAW,CAACK,cAAc,EAAE,CAAC,EAAE,CAClDoB,GAEL,IAAI,CAACI,eAAe,CAACV,OAAO,CAACW,IAC3B,IAAMC,EAASD,EAAGE,UAAU,GAAGC,IAAI,CAACC,QAAKA,QAAAA,CAAAA,OAAAA,CAAAA,EAAAA,EAAEd,KAAK,GAAPc,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAASC,IAAI,IAAK,UACvDJ,GACFA,EAAOK,YAAY,CAACX,EAExB,EACF,CAEQD,mBAAoB,CAC1B,GAAI,CAAC,IAAI,CAACxB,WAAW,CAAE,OAEvB,IAAMsB,EAAa,IAAI,CAACtB,WAAW,CAACW,cAAc,EAAE,CAAC,EAAE,CAClDW,GAEL,IAAI,CAACO,eAAe,CAACV,OAAO,CAACW,IAC3B,IAAMC,EAASD,EAAGE,UAAU,GAAGC,IAAI,CAACC,QAAKA,QAAAA,CAAAA,OAAAA,CAAAA,EAAAA,EAAEd,KAAK,GAAPc,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAASC,IAAI,IAAK,UACvDJ,GACFA,EAAOK,YAAY,CAACd,EAExB,EACF,CAEA,MAAMe,aAAa5B,CAAgB,CAAE,CACnC,GAAK,IAAI,CAACT,WAAW,CAErB,GAAI,CACF,IAAMkB,EAAS,MAAMjB,UAAUC,YAAY,CAACJ,YAAY,CAAC,CACvDjB,MAAO,CAAE4B,SAAU,CAAE6B,MAAO7B,CAAS,CAAE,EACvCrB,MAAO,KAAI,CAACwB,oBAAoB,EAAG,CAAEH,SAAU,CAAE6B,MAAO,IAAI,CAAC1B,oBAAoB,CAAG,CACtF,GAaA,OAVA,IAAI,CAACZ,WAAW,CAAC4B,SAAS,GAAGT,OAAO,CAACC,GAASA,EAAMC,IAAI,IAGxD,IAAI,CAACrB,WAAW,CAAGkB,EACnB,IAAI,CAACf,YAAY,CAAGe,EACpB,IAAI,CAACX,oBAAoB,CAAGE,EAG5B,IAAI,CAACkB,iBAAiB,GAEfT,CACT,CAAE,MAAOlE,EAAO,CAEd,MADAH,QAAQG,KAAK,CAAC,0BAA2BA,GACnC,MAAU,0BAClB,CACF,CAEAuF,qBAAqBnF,CAAc,CAAqB,CACtD,IAAM0E,EAAK,IAAIU,kBAAkBlE,GAyCjC,OAtCI,IAAI,CAAC0B,WAAW,EAClB,IAAI,CAACA,WAAW,CAAC4B,SAAS,GAAGT,OAAO,CAACC,IACnCU,EAAGP,QAAQ,CAACH,EAAO,IAAI,CAACpB,WAAW,CACrC,GAIF8B,EAAGW,OAAO,CAAG,IACX,GAAM,CAACC,EAAa,CAAG1E,EAAM2E,OAAO,CAChC,IAAI,CAACC,gBAAgB,EACvB,IAAI,CAACA,gBAAgB,CAACxF,EAAQsF,EAElC,EAGAZ,EAAGe,cAAc,CAAG,IACd7E,EAAM8E,SAAS,EAEjB,IAAI,CAACtF,UAAU,CAACJ,EAAQ,CACtB2F,KAAM,gBACND,UAAW9E,EAAM8E,SAAS,EAGhC,EAGAhB,EAAGkB,uBAAuB,CAAG,KAC3BnG,QAAQC,GAAG,CAAC,wBAA+BmG,MAAA,CAAP7F,EAAO,KAAI0E,EAAGoB,eAAe,EAE7DpB,CAAAA,iBAAAA,EAAGoB,eAAe,EAAuBpB,WAAAA,EAAGoB,eAAe,IAC7D,IAAI,CAACC,oBAAoB,CAAC/F,GACtB,IAAI,CAACgG,0BAA0B,EACjC,IAAI,CAACA,0BAA0B,CAAChG,GAGtC,EAEA,IAAI,CAACyE,eAAe,CAACwB,GAAG,CAACjG,EAAQ0E,GAC1BA,CACT,CAEA,MAAMwB,YAAYlG,CAAc,CAAsC,CACpE,IAAM0E,EAAK,IAAI,CAACD,eAAe,CAAC0B,GAAG,CAACnG,IAAW,IAAI,CAACmF,oBAAoB,CAACnF,GAEnEoG,EAAQ,MAAM1B,EAAGwB,WAAW,CAAC,CACjCG,oBAAqB,GACrBC,oBAAqB,EACvB,GAGA,OADA,MAAM5B,EAAG6B,mBAAmB,CAACH,GACtBA,CACT,CAEA,MAAMI,aAAaxG,CAAc,CAAEoG,CAAgC,CAAsC,CACvG,IAAM1B,EAAK,IAAI,CAACD,eAAe,CAAC0B,GAAG,CAACnG,IAAW,IAAI,CAACmF,oBAAoB,CAACnF,EAEzE,OAAM0E,EAAG+B,oBAAoB,CAACL,GAC9B,IAAMM,EAAS,MAAMhC,EAAG8B,YAAY,GAGpC,OAFA,MAAM9B,EAAG6B,mBAAmB,CAACG,GAEtBA,CACT,CAEA,MAAMC,aAAa3G,CAAc,CAAE0G,CAAiC,CAAiB,CACnF,IAAMhC,EAAK,IAAI,CAACD,eAAe,CAAC0B,GAAG,CAACnG,GAChC0E,GACF,MAAMA,EAAG+B,oBAAoB,CAACC,EAElC,CAEA,MAAME,mBAAmB5G,CAAc,CAAE0F,CAA8B,CAAiB,CACtF,IAAMhB,EAAK,IAAI,CAACD,eAAe,CAAC0B,GAAG,CAACnG,GAChC0E,GACF,MAAMA,EAAGmC,eAAe,CAACnB,EAE7B,CAEAK,qBAAqB/F,CAAc,CAAQ,CACzC,IAAM0E,EAAK,IAAI,CAACD,eAAe,CAAC0B,GAAG,CAACnG,GAChC0E,IACFA,EAAGoC,KAAK,GACR,IAAI,CAACrC,eAAe,CAACsC,MAAM,CAAC/G,GAEhC,CAEAgH,kBAAkBC,CAAsB,CAAQ,CAC9C,IAAM5C,EAAa4C,EAAUhE,cAAc,EAAE,CAAC,EAAE,CAEhD,IAAI,CAACwB,eAAe,CAACV,OAAO,CAAC,MAAOW,IAClC,IAAMC,EAASD,EAAGE,UAAU,GAAGC,IAAI,CAACC,GAClCA,EAAEd,KAAK,EAAIc,UAAAA,EAAEd,KAAK,CAACe,IAAI,EAGrBJ,GAAUN,GACZ,MAAMM,EAAOK,YAAY,CAACX,EAE9B,EACF,CAEA6C,SAASrG,CAAuD,CAAQ,CACtE,IAAI,CAAC2E,gBAAgB,CAAG3E,CAC1B,CAEAsG,mBAAmBtG,CAAkC,CAAQ,CAC3D,IAAI,CAACmF,0BAA0B,CAAGnF,CACpC,CAEQT,WAAWJ,CAAc,CAAEM,CAAW,CAAQ,CAGtD,CAEA8G,sBAAsBvG,CAA+C,CAAQ,CAC3E,IAAI,CAACT,UAAU,CAAGS,CACpB,CAEA4B,SAAgB,CAEd,IAAI,CAACgC,eAAe,CAACV,OAAO,CAACW,GAAMA,EAAGoC,KAAK,IAC3C,IAAI,CAACrC,eAAe,CAAC4C,KAAK,GAGtB,IAAI,CAACzE,WAAW,GAClB,IAAI,CAACA,WAAW,CAAC4B,SAAS,GAAGT,OAAO,CAACC,GAASA,EAAMC,IAAI,IACxD,IAAI,CAACrB,WAAW,CAAG,KAEvB,CAEA0E,gBAAqC,CACnC,OAAO,IAAI,CAAC1E,WAAW,CAGzB2E,kBAAkBvH,CAAc,CAAiC,CAC/D,OAAO,IAAI,CAACyE,eAAe,CAAC0B,GAAG,CAACnG,EAClC,CA9RAwH,aAAc,MATN/C,eAAAA,CAAkD,IAAIgD,SACtD7E,WAAAA,CAAkC,UAClCc,YAAAA,CAAmC,UACnCX,YAAAA,CAAmC,UACnCI,oBAAAA,CAAsC,UACtCK,oBAAAA,CAAsC,KAK5C,IAAI,CAACjB,kBAAkB,EACzB,CA6RF,CAEO,IAAMmF,EAAa,IAAIpF,iECpUvB,SAASqF,EAAUC,CAAoE,KAApE,CAAEC,YAAAA,CAAW,CAAE/D,OAAAA,CAAM,CAAEgE,QAAAA,CAAO,CAAEC,WAAAA,EAAa,EAAK,CAAkB,CAApEH,EAClBI,EAAWC,CAAAA,EAAAA,EAAAA,MAAAA,EAAyB,MAE1CC,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,KACJF,EAASG,OAAO,EAAIrE,GACtBkE,CAAAA,EAASG,OAAO,CAACC,SAAS,CAAGtE,CAAAA,CAEjC,EAAG,CAACA,EAAO,EAEX,IAAMuE,EAAWvE,GAAUA,EAAOb,cAAc,GAAGC,MAAM,CAAG,GAAK,CAAC2E,EAAYS,YAAY,CACpFC,EAAWzE,GAAUA,EAAOP,cAAc,GAAGL,MAAM,CAAG,GAAK,CAAC2E,EAAYW,YAAY,CAO1F,MACE,GAAAC,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAW,4CAAqD/C,MAAA,CANtDkC,EAAa,gBAAkB,6CAO3CM,EACC,GAAAI,EAAAI,GAAA,EAACpH,QAAAA,CACCqH,IAAKd,EACLe,SAAQ,GACRC,YAAW,GACXC,MAAOnB,EACPc,UAAU,0CAGZ,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,iHACb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,wBACb,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAW,GAAc/C,MAAA,CAjBrBkC,EAAa,YAAc,YAiBN,+HAC5B,GAAAU,EAAAI,GAAA,EAACK,OAAAA,CAAKN,UAAW,cAAuB/C,MAAA,CAjBnCkC,EAAa,UAAY,UAiBU,uBACrCF,EAAYsB,IAAI,CAACC,MAAM,CAAC,GAAGC,WAAW,OAG3C,GAAAZ,EAAAI,GAAA,EAACS,IAAAA,CAAEV,UAAW,cAAuB/C,MAAA,CApB9BkC,EAAa,UAAY,UAoBK,yBAAgBF,EAAYsB,IAAI,GACrE,GAAAV,EAAAI,GAAA,EAACS,IAAAA,CAAEV,UAAU,iCAAwB,uBAM3C,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,+EACb,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,2CACb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,oCACZd,GAAW,GAAAW,EAAAI,GAAA,EAACU,EAAAA,CAAKA,CAAAA,CAACX,UAAU,4BAC7B,GAAAH,EAAAI,GAAA,EAACK,OAAAA,CAAKN,UAAU,0CACbd,EAAU,MAAQD,EAAYsB,IAAI,QAKzC,GAAAV,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,wCAEb,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAW,oBAAmE/C,MAAA,CAA/C0C,EAAW,kBAAoB,gBAAgB,8BAChFA,EACC,GAAAE,EAAAI,GAAA,EAACW,EAAAA,CAAGA,CAAAA,CAACZ,UAAU,uBAEf,GAAAH,EAAAI,GAAA,EAACY,EAAAA,CAAMA,CAAAA,CAACb,UAAU,yBAKtB,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAW,oBAAmE/C,MAAA,CAA/CwC,EAAW,kBAAoB,gBAAgB,8BAChFA,EACC,GAAAI,EAAAI,GAAA,EAACa,EAAAA,CAAKA,CAAAA,CAACd,UAAU,uBAEjB,GAAAH,EAAAI,GAAA,EAACc,EAAAA,CAAQA,CAAAA,CAACf,UAAU,yBAKvBf,EAAY+B,eAAe,EAC1B,GAAAnB,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,4DACb,GAAAH,EAAAI,GAAA,EAACgB,EAAAA,CAAOA,CAAAA,CAACjB,UAAU,+BAO1Bd,GACC,GAAAW,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,iCACb,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,2CACb,GAAAH,EAAAC,IAAA,EAACQ,OAAAA,CAAKN,UAAU,mEACd,GAAAH,EAAAI,GAAA,EAACU,EAAAA,CAAKA,CAAAA,CAACX,UAAU,4BAA4B,cAQrD,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,kCACb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,6BACb,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,wCACf,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,wCACf,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,+CAKzB,CC9GO,SAASkB,IACd,GAAM,CAAEC,YAAAA,CAAW,CAAEC,aAAAA,CAAY,CAAEpH,YAAAA,CAAW,CAAE,CAAGqH,CAAAA,EAAAA,EAAAA,CAAAA,IAE7CC,EAAmBC,MAAMC,IAAI,CAACJ,EAAaK,MAAM,IACjDC,EAAoBJ,EAAiBhH,MAAM,CAAG,EAmBpD,MACE,GAAAuF,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,sBACb,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,4CACb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAW,4CAGZ2B,MAAAA,CArBR,IAoBqBD,GAnBjBE,IAmBiBF,EApBG,cAoBHA,GAlBR,GACTE,GAAS,EADU,cAEhB,cAgBiC,gBACD3E,MAAA,CAbvC,IAaoByE,EAbI,cACV,IAYMA,GAXhBE,GAAS,EADW,cAGjB,cASgC,sDAIhCT,GACC,GAAAtB,EAAAI,GAAA,EAAClB,EAASA,CAERE,YAAakC,EACbjG,OAAQlB,EACRkF,QAAS,GACTC,WAAYuC,IAAAA,GAJPP,EAAYpK,EAAE,EAStBuK,EAAiBO,GAAG,CAAC,CAAC5C,EAAa6C,IAClC,GAAAjC,EAAAI,GAAA,EAAClB,EAASA,CAERE,YAAaA,EACb/D,OAAQ+D,EAAY/D,MAAM,CAC1BgE,QAAS,GACTC,WAAYuC,IAAAA,GAA2BI,IAAAA,GAJlC7C,EAAYlI,EAAE,SAWjC,yEChCO,SAASgL,EAAc/C,CAAuC,MAsLxCgD,EAuDAC,KA7OC,CAAEC,OAAAA,CAAM,CAAEC,QAAAA,CAAO,CAAsB,CAAvCnD,EACtB,CAACoD,EAAWC,EAAa,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,SACrC,CAACC,EAASC,EAAW,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,EAAwB,EAAE,EAClD,CAACG,EAAgBC,EAAkB,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IAC/C,CAACK,EAAoBC,EAAsB,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IACvD,CAACO,EAAiBC,EAAmB,CAAGR,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IACjD,CAACS,EAAgBC,EAAkB,CAAGV,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IAC/C,CAACW,EAAoBC,EAAsB,CAAGZ,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,QACvD,CAACa,EAAcC,EAAgB,CAAGd,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,MAC3C,CAACe,EAAcC,EAAgB,CAAGhB,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,QAC3C,CAAChJ,EAAkBiK,EAAoB,CAAGjB,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IACnD,CAACjJ,EAAkBmK,EAAoB,CAAGlB,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IACnD,CAAC/I,EAAiBkK,EAAmB,CAAGnB,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IACjD,CAACoB,EAAmBC,EAAqB,CAAGrB,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IACrD,CAACsB,EAAiBC,EAAmB,CAAGvB,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IACjD,CAACwB,EAAoBC,EAAsB,CAAGzB,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IACvD,CAAC0B,EAAiBC,EAAmB,CAAG3B,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IAEjD,CAAEnB,YAAAA,CAAW,CAAE,CAAGE,CAAAA,EAAAA,EAAAA,CAAAA,IAqBxB/B,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,KACR,IAAM4E,EAAc,UAClB,GAAI,CAEF,IAAMhK,EAA8BiK,CADjB,MAAMlK,UAAUC,YAAY,CAACkK,gBAAgB,IACjBvC,GAAG,CAACwC,GAAW,EAC5D5J,SAAU4J,EAAO5J,QAAQ,CACzB6J,MAAOD,EAAOC,KAAK,EAAI,GAAkBD,MAAAA,CAAfA,EAAOlI,IAAI,CAAC,KAA+Bc,MAAA,CAA5BoH,EAAO5J,QAAQ,CAAC8J,KAAK,CAAC,EAAG,IAClEpI,KAAMkI,EAAOlI,IAAI,CACnB,GACAqG,EAAWtI,GAGX,IAAMsK,EAAgBtK,EAAa+B,IAAI,CAACwI,GAAKA,eAAAA,EAAEtI,IAAI,EAC7CuI,EAAaxK,EAAa+B,IAAI,CAACwI,GAAKA,eAAAA,EAAEtI,IAAI,EAC1CwI,EAAiBzK,EAAa+B,IAAI,CAACwI,GAAKA,gBAAAA,EAAEtI,IAAI,EAEhDqI,GAAe9B,EAAkB8B,EAAc/J,QAAQ,EACvDiK,GAAY9B,EAAsB8B,EAAWjK,QAAQ,EACrDkK,GAAgB7B,EAAmB6B,EAAelK,QAAQ,CAChE,CAAE,MAAOzD,EAAO,CACdH,QAAQG,KAAK,CAAC,yBAA0BA,EAC1C,CACF,EAEIkL,GACFgC,GAEJ,EAAG,CAAChC,EAAO,EAEX,IAAM0C,EAAqB,MAAOnK,IAChC,GAAI,CACFiI,EAAkBjI,GASlB,IAAMS,EAAS,MAAMjB,UAAUC,YAAY,CAACJ,YAAY,CARpC,CAClBjB,MAAO,CACL4B,SAAU,CAAE6B,MAAO7B,CAAS,EAC5B3B,MAAO,CAAEC,MAAO,IAAK,EACrBE,OAAQ,CAAEF,MAAO,GAAI,CACvB,EACAK,MAAO,EACT,GAEA0F,EAAWV,iBAAiB,CAAClD,GAC7B6I,EAAsB,GACxB,CAAE,MAAO/M,EAAO,CACdH,QAAQG,KAAK,CAAC,yBAA0BA,EAC1C,CACF,EAEM6N,EAAyB,MAAOpK,IACpC,GAAI,CACFmI,EAAsBnI,GAUP,MAAMR,UAAUC,YAAY,CAACJ,YAAY,CATpC,CAClBjB,MAAO,GACPO,MAAO,CACLqB,SAAU,CAAE6B,MAAO7B,CAAS,EAC5BpB,iBAAAA,EACAC,iBAAAA,EACAC,gBAAAA,CACF,CACF,GAGA0K,EAAmB,GACrB,CAAE,MAAOjN,EAAO,CACdH,QAAQG,KAAK,CAAC,6BAA8BA,EAC9C,CACF,EAEM8N,EAAwB,IAC5B5B,EAAsB6B,EAGxB,EAEMC,GAAoB,IACxB5B,EAAgB6B,EAElB,EAEA,GAAI,CAAC/C,EAAQ,OAAO,KAEpB,IAAMF,GAAUO,EAAQ2C,MAAM,CAACT,GAAKA,eAAAA,EAAEtI,IAAI,EACpC8F,GAAcM,EAAQ2C,MAAM,CAACT,GAAKA,eAAAA,EAAEtI,IAAI,EAG9C,OAFiBoG,EAAQ2C,MAAM,CAACT,GAAKA,gBAAAA,EAAEtI,IAAI,EAGzC,GAAA0D,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,gGACb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,gEAEb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,2EACb,GAAAH,EAAAC,IAAA,EAACqF,KAAAA,CAAGnF,UAAU,kEACZ,GAAAH,EAAAI,GAAA,EAACmF,EAAAA,CAAQA,CAAAA,CAACpF,UAAU,YAAY,sBAGlC,GAAAH,EAAAI,GAAA,EAACoF,SAAAA,CACCC,QAASnD,EACTnC,UAAU,6DAEV,GAAAH,EAAAI,GAAA,EAACsF,EAAAA,CAACA,CAAAA,CAACvF,UAAU,iBAIjB,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,2BAEb,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,6CACb,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,qBACZ,CACC,CAAEjJ,GAAI,QAASwJ,KAAM,QAASiF,KAAMC,EAAAA,CAAMA,EAC1C,CAAE1O,GAAI,QAASwJ,KAAM,QAASiF,KAAM5E,EAAAA,CAAGA,EACvC,CAAE7J,GAAI,aAAcwJ,KAAM,aAAciF,KAAME,EAAAA,CAAKA,EACnD,CAAE3O,GAAI,WAAYwJ,KAAM,WAAYiF,KAAMG,EAAAA,CAAMA,EAChD,CAAE5O,GAAI,UAAWwJ,KAAM,UAAWiF,KAAMJ,EAAAA,CAAQA,EACjD,CAACvD,GAAG,CAAC+D,GACJ,GAAA/F,EAAAC,IAAA,EAACuF,SAAAA,CAECC,QAAS,IAAMjD,EAAauD,EAAI7O,EAAE,EAClCiJ,UAAW,gEAIV/C,MAAA,CAHCmF,IAAcwD,EAAI7O,EAAE,CAChB,8BACA,8DAGN,GAAA8I,EAAAI,GAAA,EAAC2F,EAAIJ,IAAI,EAACxF,UAAU,YACnB4F,EAAIrF,IAAI,GATJqF,EAAI7O,EAAE,OAgBnB,GAAA8I,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,uCACZoC,UAAAA,GACC,GAAAvC,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,sBACb,GAAAH,EAAAI,GAAA,EAAC4F,KAAAA,CAAG7F,UAAU,iDAAwC,mBAGtD,GAAAH,EAAAC,IAAA,EAACC,MAAAA,WACC,GAAAF,EAAAI,GAAA,EAACqE,QAAAA,CAAMtE,UAAU,qDAA4C,WAC7D,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,qBACb,GAAAH,EAAAC,IAAA,EAACuF,SAAAA,CACCC,QAAS,IAAMvB,EAAsB,CAACD,GACtC9D,UAAU,iEAEV,GAAAH,EAAAI,GAAA,EAACK,OAAAA,UAAM0B,CAAAA,OAAAA,CAAAA,EAAAA,GAAQ/F,IAAI,CAAC6J,GAAKA,EAAErL,QAAQ,GAAKgI,EAAAA,GAAjCT,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAkDsC,KAAK,GAAI,kBAClE,GAAAzE,EAAAI,GAAA,EAAC8F,EAAAA,CAAWA,CAAAA,CAAC/F,UAAU,eAExB8D,GACC,GAAAjE,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,mGACZgC,GAAQH,GAAG,CAACmE,GACX,GAAAnG,EAAAC,IAAA,EAACuF,SAAAA,CAECC,QAAS,IAAMV,EAAmBoB,EAAOvL,QAAQ,EACjDuF,UAAU,gGAETgG,EAAO1B,KAAK,CACZ7B,IAAmBuD,EAAOvL,QAAQ,EAAI,GAAAoF,EAAAI,GAAA,EAACgG,EAAAA,CAAKA,CAAAA,CAACjG,UAAU,cALnDgG,EAAOvL,QAAQ,WAchC,GAAAoF,EAAAC,IAAA,EAACC,MAAAA,WACC,GAAAF,EAAAI,GAAA,EAACqE,QAAAA,CAAMtE,UAAU,qDAA4C,kBAC7D,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,kCACZkG,CA9KjB,CAAEnP,GAAI,KAAMwJ,KAAM,YAAa4F,WAAY,SAAU,EACrD,CAAEpP,GAAI,KAAMwJ,KAAM,YAAa4F,WAAY,UAAW,EACtD,CAAEpP,GAAI,MAAOwJ,KAAM,kBAAmB4F,WAAY,WAAY,EAC9D,CAAEpP,GAAI,KAAMwJ,KAAM,aAAc4F,WAAY,WAAY,EACzD,CA0KsCtE,GAAG,CAACuE,GACvB,GAAAvG,EAAAC,IAAA,EAACuF,SAAAA,CAECC,QAAS,IAAMN,GAAkBoB,EAAOrP,EAAE,EAC1CiJ,UAAW,wCAIV/C,MAAA,CAHCkG,IAAiBiD,EAAOrP,EAAE,CACtB,gDACA,yEAGN,GAAA8I,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,uBAAeoG,EAAO7F,IAAI,GACzC,GAAAV,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,8BAAsBoG,EAAOD,UAAU,KATjDC,EAAOrP,EAAE,WAiBzBqL,UAAAA,GACC,GAAAvC,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,sBACb,GAAAH,EAAAI,GAAA,EAAC4F,KAAAA,CAAG7F,UAAU,iDAAwC,mBAGtD,GAAAH,EAAAC,IAAA,EAACC,MAAAA,WACC,GAAAF,EAAAI,GAAA,EAACqE,QAAAA,CAAMtE,UAAU,qDAA4C,eAC7D,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,qBACb,GAAAH,EAAAC,IAAA,EAACuF,SAAAA,CACCC,QAAS,IAAMrB,EAAmB,CAACD,GACnChE,UAAU,iEAEV,GAAAH,EAAAI,GAAA,EAACK,OAAAA,UAAM2B,CAAAA,OAAAA,CAAAA,EAAAA,GAAYhG,IAAI,CAACoK,GAAKA,EAAE5L,QAAQ,GAAKkI,EAAAA,GAArCV,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAA0DqC,KAAK,GAAI,sBAC1E,GAAAzE,EAAAI,GAAA,EAAC8F,EAAAA,CAAWA,CAAAA,CAAC/F,UAAU,eAExBgE,GACC,GAAAnE,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,mGACZiC,GAAYJ,GAAG,CAACyE,GACf,GAAAzG,EAAAC,IAAA,EAACuF,SAAAA,CAECC,QAAS,IAAMT,EAAuByB,EAAI7L,QAAQ,EAClDuF,UAAU,gGAETsG,EAAIhC,KAAK,CACT3B,IAAuB2D,EAAI7L,QAAQ,EAAI,GAAAoF,EAAAI,GAAA,EAACgG,EAAAA,CAAKA,CAAAA,CAACjG,UAAU,cALpDsG,EAAI7L,QAAQ,WAc7B,GAAAoF,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,sBACb,GAAAH,EAAAI,GAAA,EAACsG,KAAAA,CAAGvG,UAAU,kCAAyB,sBAEvC,GAAAH,EAAAC,IAAA,EAACwE,QAAAA,CAAMtE,UAAU,8CACf,GAAAH,EAAAI,GAAA,EAACK,OAAAA,CAAKN,UAAU,sBAAa,sBAC7B,GAAAH,EAAAI,GAAA,EAACuG,QAAAA,CACCzJ,KAAK,WACL0J,QAASnN,EACToN,SAAU,GAAOnD,EAAoBoD,EAAEC,MAAM,CAACH,OAAO,EACrDzG,UAAU,mDAId,GAAAH,EAAAC,IAAA,EAACwE,QAAAA,CAAMtE,UAAU,8CACf,GAAAH,EAAAI,GAAA,EAACK,OAAAA,CAAKN,UAAU,sBAAa,sBAC7B,GAAAH,EAAAI,GAAA,EAACuG,QAAAA,CACCzJ,KAAK,WACL0J,QAASpN,EACTqN,SAAU,GAAOlD,EAAoBmD,EAAEC,MAAM,CAACH,OAAO,EACrDzG,UAAU,mDAId,GAAAH,EAAAC,IAAA,EAACwE,QAAAA,CAAMtE,UAAU,8CACf,GAAAH,EAAAI,GAAA,EAACK,OAAAA,CAAKN,UAAU,sBAAa,sBAC7B,GAAAH,EAAAI,GAAA,EAACuG,QAAAA,CACCzJ,KAAK,WACL0J,QAASlN,EACTmN,SAAU,GAAOjD,EAAmBkD,EAAEC,MAAM,CAACH,OAAO,EACpDzG,UAAU,yDAOnBoC,eAAAA,GACC,GAAAvC,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,sBACb,GAAAH,EAAAI,GAAA,EAAC4F,KAAAA,CAAG7F,UAAU,iDAAwC,uBAEtD,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,kCACZ6G,CArRf,CAAE9P,GAAI,OAAQwJ,KAAM,OAAQuG,QAAS,IAAK,EAC1C,CAAE/P,GAAI,OAAQwJ,KAAM,kBAAmBuG,QAAS,IAAK,EACrD,CAAE/P,GAAI,SAAUwJ,KAAM,gBAAiBuG,QAAS,yBAA0B,EAC1E,CAAE/P,GAAI,SAAUwJ,KAAM,eAAgBuG,QAAS,yBAA0B,EACzE,CAAE/P,GAAI,WAAYwJ,KAAM,gBAAiBuG,QAAS,2BAA4B,EAC9E,CAAE/P,GAAI,WAAYwJ,KAAM,kBAAmBuG,QAAS,2BAA4B,EACjF,CA+QkCjF,GAAG,CAACkF,GACrB,GAAAlH,EAAAC,IAAA,EAACuF,SAAAA,CAECC,QAAS,IAAMR,EAAsBiC,EAAGhQ,EAAE,EAC1CiJ,UAAW,0CAIV/C,MAAA,CAHCgG,IAAuB8D,EAAGhQ,EAAE,CACxB,qCACA,+DAGN,GAAA8I,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,oHACZ+G,SAAAA,EAAGhQ,EAAE,CAAc,GAAA8I,EAAAI,GAAA,EAACgB,EAAAA,CAAOA,CAAAA,CAACjB,UAAU,uBAA0B,GAAAH,EAAAI,GAAA,EAACyF,EAAAA,CAAKA,CAAAA,CAAC1F,UAAU,yBAEpF,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,0CAAkC+G,EAAGxG,IAAI,KAXnDwG,EAAGhQ,EAAE,QAkBnBqL,aAAAA,GACC,GAAAvC,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,sBACb,GAAAH,EAAAI,GAAA,EAAC4F,KAAAA,CAAG7F,UAAU,iDAAwC,sBAEtD,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,sBACb,GAAAH,EAAAC,IAAA,EAACwE,QAAAA,CAAMtE,UAAU,8CACf,GAAAH,EAAAC,IAAA,EAACC,MAAAA,WACC,GAAAF,EAAAI,GAAA,EAACK,OAAAA,CAAKN,UAAU,kCAAyB,0BACzC,GAAAH,EAAAI,GAAA,EAACS,IAAAA,CAAEV,UAAU,iCAAwB,2CAEvC,GAAAH,EAAAI,GAAA,EAACuG,QAAAA,CACCzJ,KAAK,WACL0J,QAAS/C,EACTgD,SAAU,GAAO/C,EAAqBgD,EAAEC,MAAM,CAACH,OAAO,EACtDzG,UAAU,mDAId,GAAAH,EAAAC,IAAA,EAACwE,QAAAA,CAAMtE,UAAU,8CACf,GAAAH,EAAAC,IAAA,EAACC,MAAAA,WACC,GAAAF,EAAAI,GAAA,EAACK,OAAAA,CAAKN,UAAU,kCAAyB,yBACzC,GAAAH,EAAAI,GAAA,EAACS,IAAAA,CAAEV,UAAU,iCAAwB,wCAEvC,GAAAH,EAAAI,GAAA,EAACuG,QAAAA,CACCzJ,KAAK,WACL0J,QAAS7C,EACT8C,SAAU,GAAO7C,EAAmB8C,EAAEC,MAAM,CAACH,OAAO,EACpDzG,UAAU,yDAOnBoC,YAAAA,GACC,GAAAvC,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,sBACb,GAAAH,EAAAI,GAAA,EAAC4F,KAAAA,CAAG7F,UAAU,iDAAwC,qBAEtD,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,sBACb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,WACC,GAAAF,EAAAI,GAAA,EAACqE,QAAAA,CAAMtE,UAAU,qDAA4C,iBAC7D,GAAAH,EAAAI,GAAA,EAACuG,QAAAA,CACCzJ,KAAK,OACLiK,MAAO7F,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAaZ,IAAI,GAAI,GAC5BP,UAAU,qBACViH,YAAY,yBAIhB,GAAApH,EAAAC,IAAA,EAACC,MAAAA,WACC,GAAAF,EAAAI,GAAA,EAACqE,QAAAA,CAAMtE,UAAU,qDAA4C,kBAC7D,GAAAH,EAAAC,IAAA,EAACoH,SAAAA,CAAOlH,UAAU,+BAChB,GAAAH,EAAAI,GAAA,EAACmG,SAAAA,CAAOY,MAAM,gBAAO,eACrB,GAAAnH,EAAAI,GAAA,EAACmG,SAAAA,CAAOY,MAAM,iBAAQ,gBACtB,GAAAnH,EAAAI,GAAA,EAACmG,SAAAA,CAAOY,MAAM,gBAAO,2BAUnC,GAAAnH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,6EACb,GAAAH,EAAAI,GAAA,EAACoF,SAAAA,CACCC,QAASnD,EACTnC,UAAU,mCACX,WAGD,GAAAH,EAAAI,GAAA,EAACoF,SAAAA,CACCC,QAASnD,EACTnC,UAAU,iCACX,wBAOX,mCCrZO,SAASmH,IACd,GAAM,CAACC,EAAsBC,EAAmB,CAAG/E,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IACtD,CAACgF,EAAcC,EAAgB,CAAGjF,CAAAA,EAAAA,EAAAA,QAAAA,EAAwB,MAC1D,CAACwB,EAAoBC,EAAsB,CAAGzB,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IACvD,CAAC0B,EAAiBC,EAAmB,CAAG3B,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IACjD,CAACkF,EAAmBC,EAAqB,CAAGnF,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IACrD,CAACC,EAASC,EAAW,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,EAAwB,EAAE,EAClD,CAACG,EAAgBC,EAAkB,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IAC/C,CAACK,EAAoBC,EAAsB,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IAEvD,CACJoF,YAAAA,CAAW,CACXC,YAAAA,CAAW,CACX/H,aAAAA,CAAY,CACZF,aAAAA,CAAY,CAEZkI,eAAAA,CAAc,CACf,CAAGvG,CAAAA,EAAAA,EAAAA,CAAAA,IAGE6C,EAAc2D,CAAAA,EAAAA,EAAAA,WAAAA,EAAY,UAC9B,GAAI,CAEF,MAAM5N,UAAUC,YAAY,CAACJ,YAAY,CAAC,CAAEV,MAAO,GAAMP,MAAO,EAAK,GAGrE,IAAMqB,EAA8BiK,CAFjB,MAAMlK,UAAUC,YAAY,CAACkK,gBAAgB,IAG7Dc,MAAM,CAACb,GAAUA,eAAAA,EAAOlI,IAAI,EAAqBkI,eAAAA,EAAOlI,IAAI,EAC5D0F,GAAG,CAACwC,GAAW,EACd5J,SAAU4J,EAAO5J,QAAQ,CACzB6J,MAAOD,EAAOC,KAAK,EAAI,GAAkBD,MAAAA,CAAfA,EAAOlI,IAAI,CAAC,KAA+Bc,MAAA,CAA5BoH,EAAO5J,QAAQ,CAAC8J,KAAK,CAAC,EAAG,IAClEpI,KAAMkI,EAAOlI,IAAI,CACnB,GAKF,GAHAqG,EAAWtI,GAGP,CAACuI,EAAgB,CACnB,IAAM+B,EAAgBtK,EAAa+B,IAAI,CAACwI,GAAKA,eAAAA,EAAEtI,IAAI,EAC/CqI,GAAe9B,EAAkB8B,EAAc/J,QAAQ,CAC7D,CAEA,GAAI,CAACkI,EAAoB,CACvB,IAAM+B,EAAaxK,EAAa+B,IAAI,CAACwI,GAAKA,eAAAA,EAAEtI,IAAI,EAC5CuI,GAAY9B,EAAsB8B,EAAWjK,QAAQ,CAC3D,CACF,CAAE,MAAOzD,EAAO,CACdH,QAAQG,KAAK,CAAC,yBAA0BA,EAC1C,CAEF,EAAG,CAACyL,EAAgBE,EAAmB,EAEvCrD,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,KACR4E,IAGAjK,UAAUC,YAAY,CAACN,gBAAgB,CAAC,eAAgBsK,GAEjD,KACLjK,UAAUC,YAAY,CAAC4N,mBAAmB,CAAC,eAAgB5D,EAC7D,GACC,CAACA,EAAY,EAEhB,IAAM6D,EAAmB,IACvBR,EAAgB3P,GAChBoQ,WAAW,IAAMT,EAAgB,MAAO,IAC1C,EAgEMU,EAA0B,UAC9B,GAAI,CACF,GAAKb,EAeHc,QAfyB,CAEzB,IAAMpN,EAAe,MAAMgE,EAAWjE,eAAe,EAGrD,OAAMiE,EAAWV,iBAAiB,CAACtD,GAGnCA,EAAaT,cAAc,EAAE,CAAC,EAAE,CAACU,OAAO,CAAG,KACzCmN,GACF,EAEAb,EAAmB,IACnBU,EAAiB,yBACnB,CAGF,CAAE,MAAO/Q,EAAO,CACdH,QAAQG,KAAK,CAAC,+BAAgCA,GAC9C+Q,EAAiB,gCACnB,CACF,EAEMG,EAAwB,UAC5B,GAAI,CAEF,IAAM/N,EAAe,MAAM2E,EAAWhF,YAAY,GAGlDgF,EAAWV,iBAAiB,CAACjE,GAE7BkN,EAAmB,IACnBU,EAAiB,yBACnB,CAAE,MAAO/Q,EAAO,CACdH,QAAQG,KAAK,CAAC,+BAAgCA,GAC9C+Q,EAAiB,8BACnB,CACF,EAEMI,EAAqBN,CAAAA,EAAAA,EAAAA,WAAAA,EAAY,MAAOpN,IAC5C,GAAIA,IAAagI,EAAgB,CAC/BsB,EAAsB,IACtB,MACF,CAEA,GAAI,CACF,MAAMjF,EAAWzC,YAAY,CAAC5B,GAC9BiI,EAAkBjI,GAClBsJ,EAAsB,IACtBgE,EAAiB,iBACnB,CAAE,MAAO/Q,EAAO,CACdH,QAAQG,KAAK,CAAC,0BAA2BA,GACzC+Q,EAAiB,0BACnB,CACF,EAAG,CAACtF,EAAe,EAEb2F,EAAyBP,CAAAA,EAAAA,EAAAA,WAAAA,EAAY,MAAOpN,IAChD,GAAIA,IAAakI,EAAoB,CACnCsB,EAAmB,IACnB,MACF,CAEA,GAAI,CAGFrB,EAAsBnI,GACtBwJ,EAAmB,IACnB8D,EAAiB,qBACnB,CAAE,MAAO/Q,EAAO,CACdH,QAAQG,KAAK,CAAC,8BAA+BA,GAC7C+Q,EAAiB,8BACnB,CACF,EAAG,CAACpF,EAAmB,EAEjBX,EAAUO,EAAQ2C,MAAM,CAAC,GAAoBT,eAAAA,EAAEtI,IAAI,EACnD8F,EAAcM,EAAQ2C,MAAM,CAAC,GAAoBT,eAAAA,EAAEtI,IAAI,EAE7D,MACE,GAAA0D,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,gBACb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,2BAEb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,qBACb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,iBACb,GAAAH,EAAAI,GAAA,EAACoF,SAAAA,CACCC,QAhGc,KACxBoC,IACAK,EAAiBnI,EAAe,qBAAuB,mBACzD,EA8FYI,UAAW,eAAoD/C,MAAA,CAArC2C,EAAe,SAAW,WAAW,mBAC/DyI,MAAOzI,EAAe,oBAAsB,2BAE3CA,EACC,GAAAC,EAAAI,GAAA,EAACY,EAAAA,CAAMA,CAAAA,CAACb,UAAU,YAElB,GAAAH,EAAAI,GAAA,EAACW,EAAAA,CAAGA,CAAAA,CAACZ,UAAU,cAGlBiC,EAAY3H,MAAM,CAAG,GACpB,GAAAuF,EAAAI,GAAA,EAACoF,SAAAA,CACCC,QAAS,IAAMrB,EAAmB,CAACD,GACnChE,UAAU,mEACVqI,MAAM,6BAEN,GAAAxI,EAAAI,GAAA,EAACqI,EAAAA,CAASA,CAAAA,CAACtI,UAAU,iBAK1BgE,GAAmB/B,EAAY3H,MAAM,CAAG,GACvC,GAAAuF,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,uGACb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,gBACb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,wEACb,GAAAH,EAAAI,GAAA,EAACsI,EAAAA,CAAUA,CAAAA,CAACvI,UAAU,YAAY,uBAGnCiC,EAAYJ,GAAG,CAAC,GACf,GAAAhC,EAAAI,GAAA,EAACF,MAAAA,CAECC,UAAU,0EACVsF,QAAS,IAAM8C,EAAuB9B,EAAI7L,QAAQ,WAElD,GAAAoF,EAAAI,GAAA,EAACK,OAAAA,CAAKN,UAAW2C,IAAuB2D,EAAI7L,QAAQ,CAAG,gBAAkB,YACtE6L,EAAIhC,KAAK,IALPgC,EAAI7L,QAAQ,WAe7B,GAAAoF,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,qBACb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,iBACb,GAAAH,EAAAI,GAAA,EAACoF,SAAAA,CACCC,QA3Ic,KACxBqC,IACAI,EAAiBrI,EAAe,mBAAqB,oBACvD,EAyIYM,UAAW,eAAoD/C,MAAA,CAArCyC,EAAe,SAAW,WAAW,mBAC/D2I,MAAO3I,EAAe,iBAAmB,2BAExCA,EACC,GAAAG,EAAAI,GAAA,EAACc,EAAAA,CAAQA,CAAAA,CAACf,UAAU,YAEpB,GAAAH,EAAAI,GAAA,EAACa,EAAAA,CAAKA,CAAAA,CAACd,UAAU,cAGpBgC,EAAQ1H,MAAM,CAAG,GAChB,GAAAuF,EAAAI,GAAA,EAACoF,SAAAA,CACCC,QAAS,IAAMvB,EAAsB,CAACD,GACtC9D,UAAU,mEACVqI,MAAM,yBAEN,GAAAxI,EAAAI,GAAA,EAACqI,EAAAA,CAASA,CAAAA,CAACtI,UAAU,iBAK1B8D,GAAsB9B,EAAQ1H,MAAM,CAAG,GACtC,GAAAuF,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,uGACb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,gBACb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,wEACb,GAAAH,EAAAI,GAAA,EAACwF,EAAAA,CAAMA,CAAAA,CAACzF,UAAU,YAAY,mBAG/BgC,EAAQH,GAAG,CAAC,GACX,GAAAhC,EAAAI,GAAA,EAACF,MAAAA,CAECC,UAAU,0EACVsF,QAAS,IAAM6C,EAAmBnC,EAAOvL,QAAQ,WAEjD,GAAAoF,EAAAI,GAAA,EAACK,OAAAA,CAAKN,UAAWyC,IAAmBuD,EAAOvL,QAAQ,CAAG,gBAAkB,YACrEuL,EAAO1B,KAAK,IALV0B,EAAOvL,QAAQ,WAehC,GAAAoF,EAAAI,GAAA,EAACoF,SAAAA,CACCC,QAAS2C,EACTjI,UAAW,eAA4D/C,MAAA,CAA7CmK,EAAuB,SAAW,YAC5DiB,MAAOjB,EAAuB,sBAAwB,wBAErDA,EACC,GAAAvH,EAAAI,GAAA,EAACuI,EAAAA,CAAUA,CAAAA,CAACxI,UAAU,YAEtB,GAAAH,EAAAI,GAAA,EAACgB,EAAAA,CAAOA,CAAAA,CAACjB,UAAU,cAKvB,GAAAH,EAAAI,GAAA,EAACoF,SAAAA,CACCC,QAAS,IAAMmC,EAAqB,IACpCzH,UAAU,uDACVyI,aAAW,oBAEX,GAAA5I,EAAAI,GAAA,EAACyI,EAAAA,CAAYA,CAAAA,CAAC1I,UAAU,iBAK5B,GAAAH,EAAAI,GAAA,EAAC8B,EAAaA,CACZG,OAAQsF,EACRrF,QAAS,IAAMsF,EAAqB,MAIrCH,GACC,GAAAzH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,uHACZsH,MAKX,wBC3WO,SAASqB,IACd,GAAM,CAAC/Q,EAASgR,EAAW,CAAGtG,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IACjCuG,EAAiBxJ,CAAAA,EAAAA,EAAAA,MAAAA,EAAuB,MAExC,CACJyJ,SAAAA,CAAQ,CACR3H,YAAAA,CAAW,CACX4H,WAAAA,CAAU,CACVC,iBAAAA,CAAgB,CAChBC,iBAAAA,CAAgB,CAEhBC,UAAAA,CAAS,CACV,CAAG7H,CAAAA,EAAAA,EAAAA,CAAAA,IAEE8H,EAAUhI,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAaiI,IAAI,IAAK,QAAUjI,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAaiI,IAAI,IAAK,UAGtE9J,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,SACRuJ,CAAsB,QAAtBA,CAAAA,EAAAA,EAAetJ,OAAO,GAAtBsJ,KAAAA,IAAAA,GAAAA,EAAwBQ,cAAc,CAAC,CAAEC,SAAU,QAAS,EAC9D,EAAG,CAACR,EAAS,EAGbxJ,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,KACR0J,GACF,EAAG,CAACA,EAAiB,EAerB,IAAMO,EAAkB,IAClBJ,GACFD,EAAU9R,EAEd,EAEMoS,EAAa,GACVC,EAAKC,kBAAkB,CAAC,EAAE,CAAE,CAAEC,KAAM,UAAWC,OAAQ,SAAU,GAG1E,MACE,GAAA/J,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,gDAEb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,2EACb,GAAAH,EAAAC,IAAA,EAAC+F,KAAAA,CAAG7F,UAAU,qEACZ,GAAAH,EAAAI,GAAA,EAAC4J,EAAAA,CAAaA,CAAAA,CAAC7J,UAAU,YAAY,UAGvC,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,mCACZ8I,EAASxO,MAAM,CAAG,GACjB,GAAAuF,EAAAC,IAAA,EAACQ,OAAAA,CAAKN,UAAU,kCACb8I,EAASxO,MAAM,CAAC,oBAOzB,GAAAuF,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,iDACZ8I,IAAAA,EAASxO,MAAM,CACd,GAAAuF,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,2CACb,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,6HACb,GAAAH,EAAAI,GAAA,EAAC6J,EAAAA,CAAIA,CAAAA,CAAC9J,UAAU,yBAElB,GAAAH,EAAAI,GAAA,EAACS,IAAAA,CAAEV,UAAU,oCAA2B,oBACxC,GAAAH,EAAAI,GAAA,EAACS,IAAAA,CAAEV,UAAU,mBAAU,+BAGzB8I,EACG5D,MAAM,CAAC6E,GAAO,CAACA,EAAIC,SAAS,EAC5BnI,GAAG,CAAC,GACL,GAAAhC,EAAAC,IAAA,EAACC,MAAAA,CAECC,UAAW,iBAEP+J,MAAAA,CADFA,EAAI3S,MAAM,GAAK+J,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAapK,EAAE,EAAG,YAAc,cAChD,KAAkCkG,MAAA,CAA/B8M,EAAIE,MAAM,CAAG,aAAe,cAEhC,GAAApK,EAAAC,IAAA,EAACC,MAAAA,CACCC,UAAW,yCAMV/C,MAAA,CALC8M,EAAI3S,MAAM,GAAK+J,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAapK,EAAE,EAC1B,+CACAgT,EAAIE,MAAM,CACR,yCACA,yBAGR,GAAApK,EAAAI,GAAA,EAACS,IAAAA,CAAEV,UAAU,8CAAsC+J,EAAInS,OAAO,GAC7DmS,EAAIE,MAAM,EACT,GAAApK,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,gFAAuE,YAK1F,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,mEACb,GAAAH,EAAAI,GAAA,EAACK,OAAAA,CAAKN,UAAU,+BAAuB+J,EAAI1S,QAAQ,GACnD,GAAAwI,EAAAI,GAAA,EAACK,OAAAA,UAAK,MACN,GAAAT,EAAAI,GAAA,EAACK,OAAAA,CAAKN,UAAU,mBAAWwJ,EAAWO,EAAIlS,SAAS,IAClDsR,GAAWY,EAAI3S,MAAM,GAAK+J,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAapK,EAAE,GACxC,GAAA8I,EAAAI,GAAA,EAACoF,SAAAA,CACCC,QAAS,IAAMiE,EAAgBQ,EAAI3S,MAAM,EACzC4I,UAAU,wDACX,eA7BA+J,EAAIhT,EAAE,GAqCjB,GAAA8I,EAAAI,GAAA,EAACF,MAAAA,CAAIG,IAAK2I,OAIZ,GAAAhJ,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,wCACb,GAAAH,EAAAC,IAAA,EAACoK,OAAAA,CAAKC,SAjGc,IACxBxD,EAAEyD,cAAc,GAEXxS,EAAQyS,IAAI,IAAOlJ,IAKxB9I,EAAcV,eAAe,CAACC,EAAQyS,IAAI,GAAIlJ,EAAYZ,IAAI,EAE9DqI,EAAW,IACb,EAsFyC5I,UAAU,2BAC3C,GAAAH,EAAAI,GAAA,EAACuG,QAAAA,CACCzJ,KAAK,OACLiK,MAAOpP,EACP8O,SAAU,GAAOkC,EAAWjC,EAAEC,MAAM,CAACI,KAAK,EAC1CC,YAAY,oBACZjH,UAAU,kCACVsK,UAAW,IACXC,aAAa,QAEf,GAAA1K,EAAAI,GAAA,EAACoF,SAAAA,CACCtI,KAAK,SACLyN,SAAU,CAAC5S,EAAQyS,IAAI,GACvBrK,UAAW,iCAIV/C,MAAA,CAHCrF,EAAQyS,IAAI,GACR,kGACA,yDAGN,GAAAxK,EAAAI,GAAA,EAAC6J,EAAAA,CAAIA,CAAAA,CAAC9J,UAAU,qBAM5B,2ECrIO,SAASyK,IACd,GAAM,CAACC,EAAeC,EAAiB,CAAGrI,CAAAA,EAAAA,EAAAA,QAAAA,EAAwB,MAE5D,CACJnB,YAAAA,CAAW,CACXC,aAAAA,CAAY,CACZwJ,WAAAA,CAAU,CACVC,gBAAAA,CAAe,CACfC,oBAAAA,CAAmB,CACnBC,kBAAAA,CAAiB,CACjBC,gBAAAA,CAAe,CACfC,iBAAAA,CAAgB,CAChBC,SAAAA,CAAQ,CACRC,WAAAA,CAAU,CACVjC,UAAAA,CAAS,CACV,CAAG7H,CAAAA,EAAAA,EAAAA,CAAAA,IAEEC,EAAmBC,MAAMC,IAAI,CAACJ,EAAaK,MAAM,IACjD2J,EAAkBjK,EAAc,CAACA,KAAgBG,EAAiB,CAAGA,EAErE+J,EAAgB,IACpB,IAAMC,EAAQ,EAAE,CAkBhB,OAhBIrM,EAAYW,YAAY,CAC1B0L,EAAMC,IAAI,CAAC,GAAA1L,EAAAI,GAAA,EAACY,EAAAA,CAAMA,CAAAA,CAAWb,UAAU,wBAAhB,QAEvBsL,EAAMC,IAAI,CAAC,GAAA1L,EAAAI,GAAA,EAACW,EAAAA,CAAGA,CAAAA,CAAWZ,UAAU,0BAAhB,QAGlBf,EAAYS,YAAY,CAC1B4L,EAAMC,IAAI,CAAC,GAAA1L,EAAAI,GAAA,EAACc,EAAAA,CAAQA,CAAAA,CAAaf,UAAU,wBAAlB,UAEzBsL,EAAMC,IAAI,CAAC,GAAA1L,EAAAI,GAAA,EAACa,EAAAA,CAAKA,CAAAA,CAAad,UAAU,0BAAlB,UAGpBf,EAAY+B,eAAe,EAC7BsK,EAAMC,IAAI,CAAC,GAAA1L,EAAAI,GAAA,EAACgB,EAAAA,CAAOA,CAAAA,CAAcjB,UAAU,yBAAnB,WAGnBsL,CACT,EAEMnC,EAAUhI,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAaiI,IAAI,IAAK,QAAUjI,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAaiI,IAAI,IAAK,UAChEoC,EAASrK,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAaiI,IAAI,IAAK,OAE/BqC,EAAoB,CAACC,EAAgBC,KACzC,OAAQD,GACN,IAAK,OACHb,EAAgBc,GAChB,KACF,KAAK,SACHZ,EAAkBY,GAClB,KACF,KAAK,UACHX,EAAgBW,GAChB,KACF,KAAK,SACHV,EAAiBU,GACjB,KACF,KAAK,QACHzC,EAAUyC,EAEd,CACAhB,EAAiB,KACnB,EAEA,MACE,GAAA9K,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,gDAEb,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,wCACb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,8CACb,GAAAH,EAAAC,IAAA,EAAC+F,KAAAA,CAAG7F,UAAU,qEACZ,GAAAH,EAAAI,GAAA,EAAC2L,EAAAA,CAAKA,CAAAA,CAAC5L,UAAU,YAAY,iBACdoL,EAAgB9Q,MAAM,CAAC,IACrCsQ,GAAc,GAAA/K,EAAAI,GAAA,EAAC4L,EAAAA,CAAIA,CAAAA,CAAC7L,UAAU,+BAGhCmJ,GACC,GAAAtJ,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,oCACb,GAAAH,EAAAI,GAAA,EAACoF,SAAAA,CACCC,QAASwF,EACT9K,UAAU,kDACVqI,MAAM,iCAEN,GAAAxI,EAAAI,GAAA,EAACY,EAAAA,CAAMA,CAAAA,CAACb,UAAU,cAGnBwL,GACC,GAAA3L,EAAAI,GAAA,EAACoF,SAAAA,CACCC,QAAS,IAAMsF,EAAaO,IAAeD,IAC3ClL,UAAU,kDACVqI,MAAOuC,EAAa,cAAgB,qBAEnCA,EAAa,GAAA/K,EAAAI,GAAA,EAAC6L,EAAAA,CAAMA,CAAAA,CAAC9L,UAAU,YAAe,GAAAH,EAAAI,GAAA,EAAC4L,EAAAA,CAAIA,CAAAA,CAAC7L,UAAU,sBAS3E,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,gDACZoL,EAAgBvJ,GAAG,CAAC,IACnB,IAAMkK,EAAgB9M,EAAYlI,EAAE,GAAKoK,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAapK,EAAE,EAExD,MACE,GAAA8I,EAAAI,GAAA,EAACF,MAAAA,CAECC,UAAU,sEAEV,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,8CACb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,wCAEb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,wHACb,GAAAH,EAAAI,GAAA,EAACK,OAAAA,CAAKN,UAAU,wCACbf,EAAYsB,IAAI,CAACC,MAAM,CAAC,GAAGC,WAAW,KAExCxB,EAAY+M,YAAY,EACvB,GAAAnM,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,wGACb,GAAAH,EAAAI,GAAA,EAACgM,EAAAA,CAAIA,CAAAA,CAACjM,UAAU,4BAMtB,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,yCACb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,wCACb,GAAAH,EAAAC,IAAA,EAACQ,OAAAA,CAAKN,UAAU,oDACbf,EAAYsB,IAAI,CAChBwL,GAAiB,YAInB9M,SAAAA,EAAYmK,IAAI,EACf,GAAAvJ,EAAAI,GAAA,EAACU,EAAAA,CAAKA,CAAAA,CAACX,UAAU,0CAElBf,YAAAA,EAAYmK,IAAI,EACf,GAAAvJ,EAAAI,GAAA,EAAC0F,EAAAA,CAAMA,CAAAA,CAAC3F,UAAU,2CAGtB,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,uCACZqL,EAAcpM,WAMpB,CAAC8M,GAAiB5C,GACjB,GAAAtJ,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,qBACb,GAAAH,EAAAI,GAAA,EAACoF,SAAAA,CACCC,QAAS,IAAMqF,EAAiBD,IAAkBzL,EAAYlI,EAAE,CAAG,KAAOkI,EAAYlI,EAAE,EACxFiJ,UAAU,2DAEV,GAAAH,EAAAI,GAAA,EAACiM,EAAAA,CAAYA,CAAAA,CAAClM,UAAU,cAIzB0K,IAAkBzL,EAAYlI,EAAE,EAC/B,GAAA8I,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,qGACb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,gBACb,GAAAH,EAAAC,IAAA,EAACuF,SAAAA,CACCC,QAAS,IAAMmG,EAAkB,OAAQxM,EAAYlI,EAAE,EACvDiJ,UAAU,sGAEV,GAAAH,EAAAI,GAAA,EAACY,EAAAA,CAAMA,CAAAA,CAACb,UAAU,YAAY,UAI/BwL,GAAUvM,gBAAAA,EAAYmK,IAAI,EACzB,GAAAvJ,EAAAC,IAAA,EAACuF,SAAAA,CACCC,QAAS,IAAMmG,EAAkB,UAAWxM,EAAYlI,EAAE,EAC1DiJ,UAAU,sGAEV,GAAAH,EAAAI,GAAA,EAAC0F,EAAAA,CAAMA,CAAAA,CAAC3F,UAAU,YAAY,kBAKjCwL,GAAUvM,YAAAA,EAAYmK,IAAI,EACzB,GAAAvJ,EAAAC,IAAA,EAACuF,SAAAA,CACCC,QAAS,IAAMmG,EAAkB,SAAUxM,EAAYlI,EAAE,EACzDiJ,UAAU,sGAEV,GAAAH,EAAAI,GAAA,EAACkM,EAAAA,CAASA,CAAAA,CAACnM,UAAU,YAAY,oBAKrC,GAAAH,EAAAC,IAAA,EAACuF,SAAAA,CACCC,QAAS,IAAMmG,EAAkB,QAASxM,EAAYlI,EAAE,EACxDiJ,UAAU,wGAEV,GAAAH,EAAAI,GAAA,EAACmM,EAAAA,CAASA,CAAAA,CAACpM,UAAU,YAAY,gBAInC,GAAAH,EAAAC,IAAA,EAACuF,SAAAA,CACCC,QAAS,IAAMmG,EAAkB,SAAUxM,EAAYlI,EAAE,EACzDiJ,UAAU,wGAEV,GAAAH,EAAAI,GAAA,EAACmM,EAAAA,CAASA,CAAAA,CAACpM,UAAU,YAAY,wBA7F1Cf,EAAYlI,EAAE,CAwGzB,OAIR,qDCpNO,SAASsV,EAAcrN,CAA8B,KAA9B,CAAE7H,OAAAA,CAAM,CAAsB,CAA9B6H,EACtBsN,EAASC,CAAAA,EAAAA,EAAAA,SAAAA,IACT,CAACC,EAAeC,EAAiB,CAAGnK,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IAE7C,CAACoK,EAAcC,EAAgB,CAAGrK,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IAC3C,CAACsK,EAAcC,EAAgB,CAAGvK,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IAC3C,CAACgF,EAAcC,EAAgB,CAAGjF,CAAAA,EAAAA,EAAAA,QAAAA,EAAwB,MAE1D,CACJnB,YAAAA,CAAW,CACXC,aAAAA,CAAY,CACZ0L,aAAAA,CAAY,CACZC,eAAAA,CAAc,CACdhC,kBAAAA,CAAiB,CACjBiC,kBAAAA,CAAiB,CACjBpF,eAAAA,CAAc,CACdqF,WAAAA,CAAU,CACVC,MAAAA,CAAK,CACN,CAAG7L,CAAAA,EAAAA,EAAAA,CAAAA,IAGE0G,EAAmB,IACvBR,EAAgB3P,GAChBoQ,WAAW,IAAMT,EAAgB,MAAO,IAC1C,EAGAjI,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,KACR,IAAM6N,EAAiB,UACrB,GAAI,CAIF,GAAI,CAFW9U,EAAcnC,OAAO,GAGlC,MAAM,MAAU,+BAIlB,IAAMgF,EAAS,MAAM4D,EAAWhF,YAAY,GAC5C8N,EAAe1M,GAGf4D,EAAWN,qBAAqB,CAAC,CAACpH,EAAQM,KACxCW,EAAcb,UAAU,CAACJ,EAAQM,EACnC,GAGAoH,EAAWR,QAAQ,CAAC,CAAClH,EAAQ8D,KAC3B8R,EAAkB5V,EAAQ,CAAE8D,OAAAA,CAAO,EACrC,GAGA4D,EAAWP,kBAAkB,CAAC,IAC5BwM,EAAkB3T,GAClB2Q,EAAiB,qCACnB,GAGI5G,GACF9I,EAAcnB,QAAQ,CAACC,EAAQgK,EAAYpK,EAAE,CAAEoK,EAAYZ,IAAI,EAGjEkM,EAAiB,IACjBK,EAAa,IACb/E,EAAiB,oCAEnB,CAAE,MAAO/Q,EAAO,CACdH,QAAQG,KAAK,CAAC,6BAA8BA,GAC5C+Q,EAAiB,yDACnB,CACF,EAMA,OAJI5G,GAAe,CAACqL,GAClBW,IAGK,KAEDX,IACFnU,EAAcd,SAAS,GACvBuH,EAAWjF,OAAO,GAClBqT,IAEJ,CAEF,EAAG,CAAC/L,EAAahK,EAAQqV,EAAc,EAGvClN,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,KACR,GAAI,CAACkN,EAAe,OAEpB,IAAMpW,EAASiC,EAAcF,SAAS,GACtC,GAAI,CAAC/B,EAAQ,OAGb,IAAMgX,EAAmB,MAAOC,IAC9BxW,QAAQC,GAAG,CAAC,eAAgBuW,GAE5BN,EAAe,CACbhW,GAAIsW,EAAKjW,MAAM,CACfmJ,KAAM8M,EAAKhW,QAAQ,CACnBuI,aAAc,GACdF,aAAc,GACdsB,gBAAiB,GACjBoI,KAAM,cACN4C,aAAc,GACdsB,SAAU,IAAIxV,KACdyV,aAAc,IAAIzV,IACpB,GAGA,GAAI,CACF,IAAM0F,EAAQ,MAAMsB,EAAWxB,WAAW,CAAC+P,EAAKjW,MAAM,EACtDiB,EAAcb,UAAU,CAAC6V,EAAKjW,MAAM,CAAE,CACpC2F,KAAM,QACNS,MAAAA,CACF,EACF,CAAE,MAAOxG,EAAO,CACdH,QAAQG,KAAK,CAAC,wBAAyBA,EACzC,CAEA+Q,EAAiB,GAAiB9K,MAAA,CAAdoQ,EAAKhW,QAAQ,CAAC,uBACpC,EAGMmW,EAAiB,IACrB3W,QAAQC,GAAG,CAAC,aAAcuW,GAC1BtC,EAAkBsC,EAAKjW,MAAM,EAC7B0H,EAAW3B,oBAAoB,CAACkQ,EAAKjW,MAAM,EAC3C2Q,EAAiB,GAAiB9K,MAAA,CAAdoQ,EAAKhW,QAAQ,CAAC,qBACpC,EAGMoW,EAAe,MAAOJ,IAC1BxW,QAAQC,GAAG,CAAC,mBAAoBuW,GAEhC,GAAI,CACF,GAAM,CAAEK,WAAAA,CAAU,CAAEhW,OAAAA,CAAM,CAAE,CAAG2V,EAE/B,OAAQ3V,EAAOqF,IAAI,EACjB,IAAK,QACH,IAAMe,EAAS,MAAMgB,EAAWlB,YAAY,CAAC8P,EAAYhW,EAAO8F,KAAK,EACrEnF,EAAcb,UAAU,CAACkW,EAAY,CACnC3Q,KAAM,SACNe,OAAAA,CACF,GACA,KAEF,KAAK,SACH,MAAMgB,EAAWf,YAAY,CAAC2P,EAAYhW,EAAOoG,MAAM,EACvD,KAEF,KAAK,gBACH,MAAMgB,EAAWd,kBAAkB,CAAC0P,EAAYhW,EAAOoF,SAAS,CAEpE,CACF,CAAE,MAAO9F,EAAO,CACdH,QAAQG,KAAK,CAAC,yBAA0BA,EAC1C,CACF,EAGM2W,EAAoB,IACxBV,EAAW,CACTlW,GAAI6W,KAAKC,MAAM,GAAGC,QAAQ,CAAC,IAAIC,SAAS,CAAC,EAAG,IAC5C3W,OAAQiW,EAAKjW,MAAM,CACnBC,SAAUgW,EAAKhW,QAAQ,CACvBO,QAASyV,EAAKzV,OAAO,CACrBC,UAAW,IAAIC,KAAKuV,EAAKxV,SAAS,CACpC,EACF,EAQA,OALAzB,EAAOQ,EAAE,CAAC,cAAewW,GACzBhX,EAAOQ,EAAE,CAAC,YAAa4W,GACvBpX,EAAOQ,EAAE,CAAC,SAAU6W,GACpBrX,EAAOQ,EAAE,CAAC,eAAgB+W,GAEnB,KACLvX,EAAO8B,GAAG,CAAC,cAAekV,GAC1BhX,EAAO8B,GAAG,CAAC,YAAasV,GACxBpX,EAAO8B,GAAG,CAAC,SAAUuV,GACrBrX,EAAO8B,GAAG,CAAC,eAAgByV,EAC7B,CAEF,EAAG,CAACnB,EAAc,EAElB,GAAM,CAACwB,EAAkBC,EAAoB,CAAG3L,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,WAsCzD,GAAuBnB,EAgBrB,GAAAtB,EAAAC,IAAA,EAAAD,EAAAqO,QAAA,YAEE,GAAArO,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,gBAEf,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,2DAEb,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,0DACb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,8CACb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,wCACb,GAAAH,EAAAI,GAAA,EAACkO,KAAAA,CAAGnO,UAAU,4CAAmC,yBAGjD,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,sDACb,GAAAH,EAAAI,GAAA,EAAC2L,EAAAA,CAAKA,CAAAA,CAAC5L,UAAU,YACjB,GAAAH,EAAAC,IAAA,EAACQ,OAAAA,WAAMc,EAAagN,IAAI,CAAG,EAAE,sBAE/B,GAAAvO,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,kCAAwB,OAChC7I,QAIT,GAAA0I,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,wCACb,GAAAH,EAAAI,GAAA,EAACoF,SAAAA,CACCC,QA5DU,KACtB,IAAM+I,EAAc,GAAkClX,MAAAA,CAA/BZ,OAAOC,QAAQ,CAACC,MAAM,CAAC,UAAewG,MAAA,CAAP9F,GACtD8C,UAAUqU,SAAS,CAACC,SAAS,CAACF,GAC9BtG,EAAiB,oCACnB,EAyDc/H,UAAU,oDACVqI,MAAM,6BAEN,GAAAxI,EAAAI,GAAA,EAACuO,EAAAA,CAAKA,CAAAA,CAACxO,UAAU,cAGnB,GAAAH,EAAAI,GAAA,EAACoF,SAAAA,CACCC,QA9DS,KACrBrL,UAAUqU,SAAS,CAACC,SAAS,CAACpX,GAC9B4Q,EAAiB,kCACnB,EA4Dc/H,UAAU,oDACVqI,MAAM,2BAEN,GAAAxI,EAAAI,GAAA,EAACwO,EAAAA,CAAIA,CAAAA,CAACzO,UAAU,cAGlB,GAAAH,EAAAI,GAAA,EAACoF,SAAAA,CACCC,QAAS,IAAMqH,EAAgB,CAACD,GAChC1M,UAAW,qDAA4F/C,MAAA,CAAvCyP,EAAe,mBAAqB,IACpGrE,MAAM,oBAEN,GAAAxI,EAAAI,GAAA,EAACmF,EAAAA,CAAQA,CAAAA,CAACpF,UAAU,cAGtB,GAAAH,EAAAI,GAAA,EAACoF,SAAAA,CACCC,QAzEW,KAClBoJ,SAASC,iBAAiB,EAI7BD,SAASE,cAAc,GACvB/B,EAAgB,MAJhB6B,SAASG,eAAe,CAACC,iBAAiB,GAC1CjC,EAAgB,IAKpB,EAkEc7M,UAAU,oDACVqI,MAAM,6BAELuE,EAAe,GAAA/M,EAAAI,GAAA,EAAC8O,EAAAA,CAASA,CAAAA,CAAC/O,UAAU,YAAe,GAAAH,EAAAI,GAAA,EAAC+O,EAAAA,CAASA,CAAAA,CAAChP,UAAU,cAG3E,GAAAH,EAAAI,GAAA,EAACoF,SAAAA,CACCC,QA3GU,KACtB2I,EAAoB,GACtB,EA0GcjO,UAAU,gFACVqI,MAAM,yBAEN,GAAAxI,EAAAI,GAAA,EAACgP,EAAAA,CAAQA,CAAAA,CAACjP,UAAU,sBAO5B,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,6CAEb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,gCACb,GAAAH,EAAAI,GAAA,EAACiB,EAASA,CAAAA,GACV,GAAArB,EAAAI,GAAA,EAACkH,EAAaA,CAAAA,MAIhB,GAAAtH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,sCAEb,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,iBACb,GAAAH,EAAAI,GAAA,EAACwK,EAAgBA,CAAAA,KAInB,GAAA5K,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,iBACb,GAAAH,EAAAI,GAAA,EAAC0I,EAAIA,CAAAA,WAMX,GAAA9I,EAAAI,GAAA,EAAC8B,EAAaA,CACZG,OAAQwK,EACRvK,QAAS,IAAMwK,EAAgB,MAIhCqB,GACC,GAAAnO,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,+EACb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,iDACb,GAAAH,EAAAI,GAAA,EAAC4F,KAAAA,CAAG7F,UAAU,iDAAwC,mBACtD,GAAAH,EAAAI,GAAA,EAACS,IAAAA,CAAEV,UAAU,8BAAqB,gDAClC,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,uCACb,GAAAH,EAAAI,GAAA,EAACoF,SAAAA,CACCC,QA9II,KAClB2I,EAAoB,GACtB,EA6IgBjO,UAAU,2FACX,WAGD,GAAAH,EAAAI,GAAA,EAACoF,SAAAA,CACCC,QA3JK,KACnBjN,EAAcd,SAAS,GACvBuH,EAAWjF,OAAO,GAClBqT,IACAZ,EAAOf,IAAI,CAAC,IACd,EAuJgBvL,UAAU,yFACX,kBASRsH,GACC,GAAAzH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,uFACZsH,UAtIP,GAAAzH,EAAAC,IAAA,EAAAD,EAAAqO,QAAA,YACE,GAAArO,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,gBACf,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,uEACb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,kCACb,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,6FACf,GAAAH,EAAAI,GAAA,EAACkF,KAAAA,CAAGnF,UAAU,kDAAyC,0BACvD,GAAAH,EAAAI,GAAA,EAACS,IAAAA,CAAEV,UAAU,yBAAgB,0DAsIzC,CCjYe,SAASkP,IACtB,IAAMC,EAASC,CAAAA,EAAAA,EAAAA,SAAAA,IACT9C,EAASC,CAAAA,EAAAA,EAAAA,SAAAA,IACTpV,EAASgY,EAAOpY,EAAE,CAClB,CAACM,EAAUgY,EAAY,CAAG/M,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IACnC,CAACgN,EAAWC,EAAa,CAAGjN,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IACrC,CAACkN,EAAWC,EAAa,CAAGnN,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IAErC,CAAEoN,UAAAA,CAAS,CAAEC,eAAAA,CAAc,CAAE,CAAGtO,CAAAA,EAAAA,EAAAA,CAAAA,IAEhCuO,EAAiB/H,CAAAA,EAAAA,EAAAA,WAAAA,EAAY,MAAOtH,IACxC,GAAI,CAACA,EAAK8J,IAAI,GAAI,CAChBwF,MAAM,0BACN,MACF,CAEAN,EAAa,IAEb,GAAI,CAEFG,EAAUvY,GAGV,IAAM2Y,EAAO,CACX/Y,GAAI6W,KAAKC,MAAM,GAAGC,QAAQ,CAAC,IAAIC,SAAS,CAAC,EAAG,IAC5CxN,KAAMA,EAAK8J,IAAI,GACfzK,aAAc,GACdF,aAAc,GACdsB,gBAAiB,GACjBoI,KAAM,OACN4C,aAAc,GACdsB,SAAU,IAAIxV,KACdyV,aAAc,IAAIzV,IACpB,EAGA6X,EAAeG,GAGfC,aAAaC,OAAO,CAAC,WAAYzP,EAAK8J,IAAI,IAE1CoF,EAAa,GACf,CAAE,MAAOzY,EAAO,CACdH,QAAQG,KAAK,CAAC,sBAAuBA,GACrC6Y,MAAM,yCACR,QAAU,CACRN,EAAa,GACf,CACF,EAAG,CAACpY,EAAQwY,EAAgBD,EAAU,SAEtCpQ,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,KAER,IAAM2Q,EAAaF,aAAaG,OAAO,CAAC,YACpCD,IACFZ,EAAYY,GACZL,EAAeK,GAEnB,EAAG,CAACL,EAAe,EAOdJ,GAgDE,GAAA3P,EAAAI,GAAA,EAACoM,EAAaA,CAAClV,OAAQA,IA9C1B,GAAA0I,EAAAC,IAAA,EAAAD,EAAAqO,QAAA,YACE,GAAArO,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,gBACf,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,2EACb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,sCACb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,6BACb,GAAAH,EAAAI,GAAA,EAACkF,KAAAA,CAAGnF,UAAU,8CAAqC,iBACnD,GAAAH,EAAAC,IAAA,EAACY,IAAAA,CAAEV,UAAU,0BAAgB,eACf,GAAAH,EAAAI,GAAA,EAACK,OAAAA,CAAKN,UAAU,mDAA2C7I,UAI3E,GAAA0I,EAAAC,IAAA,EAACoK,OAAAA,CAAKC,SAlBS,IACvBxD,EAAEyD,cAAc,GAChBwF,EAAevY,EACjB,EAe4C2I,UAAU,sBAC1C,GAAAH,EAAAI,GAAA,EAACuG,QAAAA,CACCzJ,KAAK,OACLkK,YAAY,kBACZD,MAAO3P,EACPqP,SAAU,GAAO2I,EAAY1I,EAAEC,MAAM,CAACI,KAAK,EAC3CwD,SAAU8E,EACVa,UAAS,GACTnQ,UAAU,uBAGZ,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,uBACb,GAAAH,EAAAI,GAAA,EAACoF,SAAAA,CACCtI,KAAK,SACLuI,QAAS,IAAMgH,EAAOf,IAAI,CAAC,KAC3Bf,SAAU8E,EACVtP,UAAU,gCACX,SAGD,GAAAH,EAAAI,GAAA,EAACoF,SAAAA,CACCtI,KAAK,SACLyN,SAAU8E,GAAa,CAACjY,EAASgT,IAAI,GACrCrK,UAAU,8BAETsP,EAAY,aAAe,8BAW9C,6FCfA,IAAMc,EAAe,CACnBjZ,OAAQ,KACRiB,YAAa,GACbwS,WAAY,GACZzJ,YAAa,KACbC,aAAc,IAAIvC,IAClB7E,YAAa,KACb4F,aAAc,GACdF,aAAc,GACdsB,gBAAiB,GACjB8H,SAAU,EAAE,CACZuH,YAAa,EACbC,eAAgB,IAAIzR,IACpB0R,WAAY,GACZC,eAAgB,GAChBvH,iBAAkB,CAChBvF,kBAAmB,GACnBE,gBAAiB,GACjB6M,qBAAsB,GACtBC,iBAAkB,GAClBC,iBAAkB,GAClBC,sBAAuB,EACzB,EACAC,cAAe,CACbC,WAAY,GACZC,mBAAoB,GACpBC,qBAAsB,GACtBC,iBAAkB,GAClBC,eAAgB,EAClB,EACAC,aAAc,IAAIC,IAClBC,cAAe,IAAIxS,GACrB,EAEawC,EAAoBiQ,CAAAA,EAAAA,EAAAA,EAAAA,IAC/BC,CAAAA,EAAAA,EAAAA,EAAAA,EACE,CAAClU,EAAKE,IAAS,EACb,GAAG6S,CAAY,CAGfV,UAAW,GAAoBrS,EAAI,CAAElG,OAAAA,CAAO,GAC5C2V,aAAc,GAA0BzP,EAAI,CAAEjF,YAAAA,CAAY,GAG1DuX,eAAgB,GAA8BtS,EAAI,CAAE8D,YAAAA,CAAY,GAEhE4L,eAAgB,IACd,IAAM3L,EAAe,IAAIvC,IAAItB,IAAM6D,YAAY,EAC/CA,EAAa/D,GAAG,CAAC4B,EAAYlI,EAAE,CAAEkI,GACjC5B,EAAI,CAAE+D,aAAAA,CAAa,EACrB,EAEA2J,kBAAmB,IACjB,IAAM3J,EAAe,IAAIvC,IAAItB,IAAM6D,YAAY,EAC/CA,EAAajD,MAAM,CAACwN,GACpBtO,EAAI,CAAE+D,aAAAA,CAAa,EACrB,EAEA4L,kBAAmB,CAACrB,EAAuB6F,KACzC,IAAMpQ,EAAe,IAAIvC,IAAItB,IAAM6D,YAAY,EACzCnC,EAAcmC,EAAa7D,GAAG,CAACoO,GACjC1M,IACFmC,EAAa/D,GAAG,CAACsO,EAAe,CAAE,GAAG1M,CAAW,CAAE,GAAGuS,CAAO,GAC5DnU,EAAI,CAAE+D,aAAAA,CAAa,GAEvB,EAGAwG,eAAgB,GAAqCvK,EAAI,CAAErD,YAAAA,CAAY,GAEvE0N,YAAa,KACX,GAAM,CAAE9H,aAAAA,CAAY,CAAE5F,YAAAA,CAAW,CAAE,CAAGuD,IAClCvD,GACFA,EAAYW,cAAc,GAAGQ,OAAO,CAACC,IACnCA,EAAMqW,OAAO,CAAG7R,CAClB,GAEFvC,EAAI,CAAEuC,aAAc,CAACA,CAAa,EACpC,EAEA+H,YAAa,KACX,GAAM,CAAEjI,aAAAA,CAAY,CAAE1F,YAAAA,CAAW,CAAE,CAAGuD,IAClCvD,GACFA,EAAYK,cAAc,GAAGc,OAAO,CAACC,IACnCA,EAAMqW,OAAO,CAAG/R,CAClB,GAEFrC,EAAI,CAAEqC,aAAc,CAACA,CAAa,EACpC,EAEAgS,kBAAmB,KACjBrU,EAAIsU,GAAU,EAAE3Q,gBAAiB,CAAC2Q,EAAM3Q,eAAe,CAAC,EAC1D,EAGAiM,WAAY,IACV,GAAM,CAAEnE,SAAAA,CAAQ,CAAEG,iBAAAA,CAAgB,CAAEoI,cAAAA,CAAa,CAAE,CAAG9T,IAGtD,GAAI0L,EAAiBrF,eAAe,CAAE,CACpC,IAAMgO,EAAM9Z,KAAK8Z,GAAG,GACdC,EAAWR,EAAc9T,GAAG,CAAC3F,EAAQR,MAAM,GAAK,CAAEwK,MAAO,EAAGkQ,UAAWF,CAAI,EAG7EA,EAAMC,EAASC,SAAS,CAAG,MAC7BD,EAASjQ,KAAK,CAAG,EACjBiQ,EAASC,SAAS,CAAGF,GAGvBC,EAASjQ,KAAK,GACdyP,EAAchU,GAAG,CAACzF,EAAQR,MAAM,CAAEya,GAG9BA,EAASjQ,KAAK,CAAGqH,EAAiBwH,oBAAoB,EACxD7Y,CAAAA,EAAQqS,MAAM,CAAG,GAErB,CAEA5M,EAAI,CACFyL,SAAU,IAAIA,EAAUlR,EAAQ,CAChCyY,YAAa9S,IAAMgT,UAAU,CAAG,EAAIhT,IAAM8S,WAAW,CAAG,EACxDgB,cAAe,IAAIxS,IAAIwS,EACzB,EACF,EAEArI,iBAAkB,IAAM3L,EAAI,CAAEgT,YAAa,CAAE,GAG7CtH,WAAY,KACV,IAAMwH,EAAa,CAAChT,IAAMgT,UAAU,CACpClT,EAAI,CACFkT,WAAAA,EACAF,YAAaE,EAAa,EAAIhT,IAAM8S,WAAW,EAEnD,EAEA0B,eAAgB,IAAM1U,EAAIsU,GAAU,EAAEnB,eAAgB,CAACmB,EAAMnB,cAAc,CAAC,GAG5E3F,gBAAiB,IACftN,IAAMyP,iBAAiB,CAACrB,EAAe,CAAE/L,aAAc,EAAK,EAC9D,EAEAkL,oBAAqB,KACnB,GAAM,CAAE1J,aAAAA,CAAY,CAAE,CAAG7D,IACzB6D,EAAajG,OAAO,CAAC,CAAC6W,EAAGjb,KACvBwG,IAAMyP,iBAAiB,CAACjW,EAAI,CAAE6I,aAAc,EAAK,EACnD,EACF,EAEAqS,sBAAuB,KACrB,GAAM,CAAE7Q,aAAAA,CAAY,CAAE,CAAG7D,IACzB6D,EAAajG,OAAO,CAAC,CAAC6W,EAAGjb,KACvBwG,IAAMyP,iBAAiB,CAACjW,EAAI,CAAE6I,aAAc,EAAM,EACpD,EACF,EAEAoL,gBAAiB,IACfzN,IAAMyP,iBAAiB,CAACrB,EAAe,CAAEvC,KAAM,SAAU,EAC3D,EAEA6B,iBAAkB,IAChB1N,IAAMyP,iBAAiB,CAACrB,EAAe,CAAEvC,KAAM,aAAc,EAC/D,EAEAF,UAAW,IACT,IAAMiI,EAAe,IAAIC,IAAI7T,IAAM4T,YAAY,EAC/CA,EAAae,GAAG,CAACvG,GACjBtO,EAAI,CAAE8T,aAAAA,CAAa,GACnB5T,IAAMwN,iBAAiB,CAACY,EAC1B,EAEAwG,YAAa,IACX,IAAMhB,EAAe,IAAIC,IAAI7T,IAAM4T,YAAY,EAC/CA,EAAahT,MAAM,CAACwN,GACpBtO,EAAI,CAAE8T,aAAAA,CAAa,EACrB,EAEAjG,SAAU,IAAM7N,EAAI,CAAEuN,WAAY,EAAK,GACvCO,WAAY,IAAM9N,EAAI,CAAEuN,WAAY,EAAM,GAE1CwH,uBAAwB,IACtB/U,EAAIsU,GAAU,EACZ1I,iBAAkB,CAAE,GAAG0I,EAAM1I,gBAAgB,CAAE,GAAGoJ,CAAQ,CAC5D,GACF,EAGAnF,MAAO,IAAM7P,EAAI+S,EACnB,GACA,CACE7P,KAAM,kBACR", "sources": ["webpack://_N_E/?3048", "webpack://_N_E/./lib/socket.ts", "webpack://_N_E/./lib/rtc.ts", "webpack://_N_E/./components/VideoCall/VideoTile.tsx", "webpack://_N_E/./components/VideoCall/VideoGrid.tsx", "webpack://_N_E/./components/VideoCall/SettingsModal.tsx", "webpack://_N_E/./components/VideoCall/VideoControls.tsx", "webpack://_N_E/./components/VideoCall/Chat.tsx", "webpack://_N_E/./components/VideoCall/ParticipantsList.tsx", "webpack://_N_E/./components/VideoCall/VideoCallRoom.tsx", "webpack://_N_E/./app/room/[id]/page.tsx", "webpack://_N_E/./lib/store.ts"], "sourcesContent": ["import(/* webpackMode: \"eager\" */ \"/Volumes/Apps/Websites/streamit-main/app/room/[id]/page.tsx\");\n", "import { io, Socket } from 'socket.io-client'\n\nclass SocketManager {\n  private socket: Socket | null = null\n  private roomId: string | null = null\n\n  connect() {\n    if (this.socket?.connected) return this.socket\n\n    this.socket = io(process.env.NODE_ENV === 'production'\n      ? window.location.origin\n      : 'http://localhost:3002', {\n      transports: ['websocket', 'polling'],\n      upgrade: true,\n    })\n\n    this.socket.on('connect', () => {\n      console.log('Connected to server:', this.socket?.id)\n    })\n\n    this.socket.on('disconnect', () => {\n      console.log('Disconnected from server')\n    })\n\n    this.socket.on('connect_error', (error) => {\n      console.error('Connection error:', error)\n    })\n\n    return this.socket\n  }\n\n  disconnect() {\n    if (this.socket) {\n      this.socket.disconnect()\n      this.socket = null\n    }\n  }\n\n  joinRoom(roomId: string, userId: string, userName: string) {\n    if (!this.socket) return\n\n    this.roomId = roomId\n    this.socket.emit('join-room', { roomId, userId, userName })\n  }\n\n  leaveRoom() {\n    if (!this.socket || !this.roomId) return\n\n    this.socket.emit('leave-room', { roomId: this.roomId })\n    this.roomId = null\n  }\n\n  sendSignal(targetUserId: string, signal: any) {\n    if (!this.socket || !this.roomId) return\n\n    this.socket.emit('signal', {\n      roomId: this.roomId,\n      targetUserId,\n      signal\n    })\n  }\n\n  sendChatMessage(message: string, userName: string) {\n    if (!this.socket || !this.roomId) return\n\n    this.socket.emit('chat-message', {\n      roomId: this.roomId,\n      message,\n      userName,\n      timestamp: new Date().toISOString()\n    })\n  }\n\n  on(event: string, callback: (...args: any[]) => void) {\n    if (!this.socket) return\n\n    this.socket.on(event, callback)\n  }\n\n  off(event: string, callback?: (...args: any[]) => void) {\n    if (!this.socket) return\n\n    this.socket.off(event, callback)\n  }\n\n  getSocket() {\n    return this.socket\n  }\n\n  isConnected() {\n    return this.socket?.connected || false\n  }\n}\n\nexport const socketManager = new SocketManager()\n", "import { streamEncryption } from './encryption'\n\n// WebRTC configuration with enhanced security\nconst RTC_CONFIG: RTCConfiguration = {\n  iceServers: [\n    { urls: 'stun:stun.l.google.com:19302' },\n    { urls: 'stun:stun1.l.google.com:19302' },\n  ],\n  iceCandidatePoolSize: 10,\n  bundlePolicy: 'max-bundle',\n  rtcpMuxPolicy: 'require',\n}\n\n// High-quality video constraints\nexport const VIDEO_CONSTRAINTS: MediaStreamConstraints = {\n  video: {\n    width: { ideal: 1280, max: 1920 },\n    height: { ideal: 720, max: 1080 },\n    frameRate: { ideal: 30, max: 60 },\n    facingMode: 'user'\n  },\n  audio: {\n    echoCancellation: true,\n    noiseSuppression: true,\n    autoGainControl: true,\n    sampleRate: 48000\n  }\n}\n\nexport const SCREEN_SHARE_CONSTRAINTS = {\n  video: {\n    width: { ideal: 1920, max: 3840 },\n    height: { ideal: 1080, max: 2160 },\n    frameRate: { ideal: 30, max: 60 }\n  },\n  audio: true\n}\n\nexport class RTCManager {\n  private peerConnections: Map<string, RTCPeerConnection> = new Map()\n  private localStream: MediaStream | null = null\n  private screenStream: MediaStream | null = null\n  private cameraStream: MediaStream | null = null\n  private currentVideoDeviceId: string | null = null\n  private currentAudioDeviceId: string | null = null\n  private onStreamCallback?: (userId: string, stream: MediaStream) => void\n  private onUserDisconnectedCallback?: (userId: string) => void\n\n  constructor() {\n    this.setupEventHandlers()\n  }\n\n  private setupEventHandlers() {\n    // Handle page unload (only in browser)\n    if (typeof window !== 'undefined') {\n      window.addEventListener('beforeunload', () => {\n        this.cleanup()\n      })\n    }\n  }\n\n  async getUserMedia(constraints: MediaStreamConstraints = VIDEO_CONSTRAINTS): Promise<MediaStream> {\n    try {\n      this.localStream = await navigator.mediaDevices.getUserMedia(constraints)\n      this.cameraStream = this.localStream\n      \n      // Store device IDs if available\n      const videoTracks = this.localStream.getVideoTracks()\n      if (videoTracks.length > 0) {\n        this.currentVideoDeviceId = videoTracks[0].getSettings().deviceId || null\n      }\n      \n      const audioTracks = this.localStream.getAudioTracks()\n      if (audioTracks.length > 0) {\n        this.currentAudioDeviceId = audioTracks[0].getSettings().deviceId || null\n      }\n      \n      return this.localStream\n    } catch (error) {\n      console.error('Error accessing media devices:', error)\n      throw error\n    }\n  }\n  \n  // Screen sharing and media device methods\n  async getDisplayMedia(constraints: MediaStreamConstraints = SCREEN_SHARE_CONSTRAINTS): Promise<MediaStream> {\n    try {\n      this.screenStream = await navigator.mediaDevices.getDisplayMedia(constraints)\n      \n      // Handle when user stops screen sharing\n      this.screenStream.getVideoTracks()[0].onended = () => {\n        this.restoreVideoTrack()\n      }\n      \n      return this.screenStream\n    } catch (error) {\n      console.error('Error accessing screen share:', error)\n      throw error\n    }\n  }\n  \n  // replaceVideoTrack is implemented below with a more comprehensive version\n  \n  replaceAudioTrack(stream: MediaStream) {\n    if (!this.localStream) return\n    \n    // Stop existing audio tracks\n    this.localStream.getAudioTracks().forEach(track => track.stop())\n    \n    // Add new audio track\n    const audioTrack = stream.getAudioTracks()[0]\n    if (audioTrack) {\n      this.localStream.addTrack(audioTrack)\n    }\n    \n    // Update all peer connections\n    this.updateAudioTracks()\n  }\n  \n  restoreVideoTrack() {\n    if (!this.localStream || !this.cameraStream) return\n    \n    // Stop existing video tracks\n    this.localStream.getVideoTracks().forEach(track => track.stop())\n    \n    // Add camera video track back\n    const videoTrack = this.cameraStream.getVideoTracks()[0]\n    if (videoTrack) {\n      this.localStream.addTrack(videoTrack.clone())\n    }\n    \n    // Update all peer connections\n    this.updateVideoTracks()\n    \n    // Stop and clear screen stream\n    if (this.screenStream) {\n      this.screenStream.getTracks().forEach(track => track.stop())\n      this.screenStream = null\n    }\n  }\n  \n  private updateVideoTracks() {\n    if (!this.localStream) return\n    \n    const videoTrack = this.localStream.getVideoTracks()[0]\n    if (!videoTrack) return\n    \n    this.peerConnections.forEach(pc => {\n      const sender = pc.getSenders().find(s => s.track?.kind === 'video')\n      if (sender) {\n        sender.replaceTrack(videoTrack)\n      }\n    })\n  }\n  \n  private updateAudioTracks() {\n    if (!this.localStream) return\n    \n    const audioTrack = this.localStream.getAudioTracks()[0]\n    if (!audioTrack) return\n    \n    this.peerConnections.forEach(pc => {\n      const sender = pc.getSenders().find(s => s.track?.kind === 'audio')\n      if (sender) {\n        sender.replaceTrack(audioTrack)\n      }\n    })\n  }\n  \n  async switchCamera(deviceId: string) {\n    if (!this.localStream) return\n    \n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({\n        video: { deviceId: { exact: deviceId } },\n        audio: this.currentAudioDeviceId ? { deviceId: { exact: this.currentAudioDeviceId } } : true\n      })\n      \n      // Stop old tracks\n      this.localStream.getTracks().forEach(track => track.stop())\n      \n      // Replace with new stream\n      this.localStream = stream\n      this.cameraStream = stream\n      this.currentVideoDeviceId = deviceId\n      \n      // Update all peer connections\n      this.updateVideoTracks()\n      \n      return stream\n    } catch (error) {\n      console.error('Error switching camera:', error)\n      throw new Error('Failed to switch camera')\n    }\n  }\n\n  createPeerConnection(userId: string): RTCPeerConnection {\n    const pc = new RTCPeerConnection(RTC_CONFIG)\n\n    // Add local stream tracks\n    if (this.localStream) {\n      this.localStream.getTracks().forEach(track => {\n        pc.addTrack(track, this.localStream!)\n      })\n    }\n\n    // Handle incoming stream\n    pc.ontrack = (event) => {\n      const [remoteStream] = event.streams\n      if (this.onStreamCallback) {\n        this.onStreamCallback(userId, remoteStream)\n      }\n    }\n\n    // Handle ICE candidates\n    pc.onicecandidate = (event) => {\n      if (event.candidate) {\n        // Send ICE candidate to remote peer via signaling\n        this.sendSignal(userId, {\n          type: 'ice-candidate',\n          candidate: event.candidate\n        })\n      }\n    }\n\n    // Handle connection state changes\n    pc.onconnectionstatechange = () => {\n      console.log(`Connection state for ${userId}:`, pc.connectionState)\n      \n      if (pc.connectionState === 'disconnected' || pc.connectionState === 'failed') {\n        this.removePeerConnection(userId)\n        if (this.onUserDisconnectedCallback) {\n          this.onUserDisconnectedCallback(userId)\n        }\n      }\n    }\n\n    this.peerConnections.set(userId, pc)\n    return pc\n  }\n\n  async createOffer(userId: string): Promise<RTCSessionDescriptionInit> {\n    const pc = this.peerConnections.get(userId) || this.createPeerConnection(userId)\n    \n    const offer = await pc.createOffer({\n      offerToReceiveAudio: true,\n      offerToReceiveVideo: true\n    })\n    \n    await pc.setLocalDescription(offer)\n    return offer\n  }\n\n  async createAnswer(userId: string, offer: RTCSessionDescriptionInit): Promise<RTCSessionDescriptionInit> {\n    const pc = this.peerConnections.get(userId) || this.createPeerConnection(userId)\n    \n    await pc.setRemoteDescription(offer)\n    const answer = await pc.createAnswer()\n    await pc.setLocalDescription(answer)\n    \n    return answer\n  }\n\n  async handleAnswer(userId: string, answer: RTCSessionDescriptionInit): Promise<void> {\n    const pc = this.peerConnections.get(userId)\n    if (pc) {\n      await pc.setRemoteDescription(answer)\n    }\n  }\n\n  async handleIceCandidate(userId: string, candidate: RTCIceCandidateInit): Promise<void> {\n    const pc = this.peerConnections.get(userId)\n    if (pc) {\n      await pc.addIceCandidate(candidate)\n    }\n  }\n\n  removePeerConnection(userId: string): void {\n    const pc = this.peerConnections.get(userId)\n    if (pc) {\n      pc.close()\n      this.peerConnections.delete(userId)\n    }\n  }\n\n  replaceVideoTrack(newStream: MediaStream): void {\n    const videoTrack = newStream.getVideoTracks()[0]\n    \n    this.peerConnections.forEach(async (pc) => {\n      const sender = pc.getSenders().find(s => \n        s.track && s.track.kind === 'video'\n      )\n      \n      if (sender && videoTrack) {\n        await sender.replaceTrack(videoTrack)\n      }\n    })\n  }\n\n  onStream(callback: (userId: string, stream: MediaStream) => void): void {\n    this.onStreamCallback = callback\n  }\n\n  onUserDisconnected(callback: (userId: string) => void): void {\n    this.onUserDisconnectedCallback = callback\n  }\n\n  private sendSignal(userId: string, signal: any): void {\n    // This will be implemented by the component using RTCManager\n    // to send signals via Socket.IO\n  }\n\n  setSendSignalCallback(callback: (userId: string, signal: any) => void): void {\n    this.sendSignal = callback\n  }\n\n  cleanup(): void {\n    // Close all peer connections\n    this.peerConnections.forEach(pc => pc.close())\n    this.peerConnections.clear()\n\n    // Stop local stream\n    if (this.localStream) {\n      this.localStream.getTracks().forEach(track => track.stop())\n      this.localStream = null\n    }\n  }\n\n  getLocalStream(): MediaStream | null {\n    return this.localStream\n  }\n\n  getPeerConnection(userId: string): RTCPeerConnection | undefined {\n    return this.peerConnections.get(userId)\n  }\n}\n\nexport const rtcManager = new RTCManager()\n", "'use client'\n\nimport { useEffect, useRef } from 'react'\nimport { Participant } from '@/lib/store'\nimport { Mi<PERSON>, MicOff, Video, VideoOff, Monitor, Crown } from 'lucide-react'\n\ninterface VideoTileProps {\n  participant: Participant\n  stream?: MediaStream | null\n  isLocal: boolean\n  isFeatured?: boolean\n}\n\nexport function VideoTile({ participant, stream, isLocal, isFeatured = false }: VideoTileProps) {\n  const videoRef = useRef<HTMLVideoElement>(null)\n\n  useEffect(() => {\n    if (videoRef.current && stream) {\n      videoRef.current.srcObject = stream\n    }\n  }, [stream])\n\n  const hasVideo = stream && stream.getVideoTracks().length > 0 && !participant.isVideoMuted\n  const hasAudio = stream && stream.getAudioTracks().length > 0 && !participant.isAudioMuted\n\n  const tileSize = isFeatured ? 'w-full h-full' : 'w-full h-full max-w-sm max-h-64'\n  const avatarSize = isFeatured ? 'w-24 h-24' : 'w-16 h-16'\n  const textSize = isFeatured ? 'text-xl' : 'text-lg'\n  const nameSize = isFeatured ? 'text-lg' : 'text-sm'\n\n  return (\n    <div className={`video-container relative overflow-hidden ${tileSize}`}>\n      {hasVideo ? (\n        <video\n          ref={videoRef}\n          autoPlay\n          playsInline\n          muted={isLocal} // Mute local video to prevent feedback\n          className=\"w-full h-full object-cover rounded-lg\"\n        />\n      ) : (\n        <div className=\"w-full h-full bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center rounded-lg\">\n          <div className=\"text-center\">\n            <div className={`${avatarSize} bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-3 shadow-lg`}>\n              <span className={`text-white ${textSize} font-bold`}>\n                {participant.name.charAt(0).toUpperCase()}\n              </span>\n            </div>\n            <p className={`text-white ${nameSize} font-medium`}>{participant.name}</p>\n            <p className=\"text-white/60 text-xs\">Camera is off</p>\n          </div>\n        </div>\n      )}\n\n      {/* Participant info overlay */}\n      <div className=\"absolute bottom-2 left-2 right-2 flex items-center justify-between\">\n        <div className=\"glass-dark px-2 py-1 rounded-lg\">\n          <div className=\"flex items-center gap-1\">\n            {isLocal && <Crown className=\"h-3 w-3 text-yellow-400\" />}\n            <span className=\"text-white text-xs font-medium\">\n              {isLocal ? 'You' : participant.name}\n            </span>\n          </div>\n        </div>\n\n        <div className=\"flex items-center space-x-1\">\n          {/* Audio indicator */}\n          <div className={`p-1 rounded-full ${hasAudio ? 'bg-green-500/80' : 'bg-red-500/80'} backdrop-blur-sm`}>\n            {hasAudio ? (\n              <Mic className=\"h-3 w-3 text-white\" />\n            ) : (\n              <MicOff className=\"h-3 w-3 text-white\" />\n            )}\n          </div>\n\n          {/* Video indicator */}\n          <div className={`p-1 rounded-full ${hasVideo ? 'bg-green-500/80' : 'bg-red-500/80'} backdrop-blur-sm`}>\n            {hasVideo ? (\n              <Video className=\"h-3 w-3 text-white\" />\n            ) : (\n              <VideoOff className=\"h-3 w-3 text-white\" />\n            )}\n          </div>\n\n          {/* Screen sharing indicator */}\n          {participant.isScreenSharing && (\n            <div className=\"p-1 rounded-full bg-blue-500/80 backdrop-blur-sm\">\n              <Monitor className=\"h-3 w-3 text-white\" />\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Local user indicator */}\n      {isLocal && (\n        <div className=\"absolute top-2 left-2\">\n          <div className=\"glass-dark px-2 py-1 rounded-lg\">\n            <span className=\"text-white text-xs font-medium flex items-center gap-1\">\n              <Crown className=\"h-2 w-2 text-yellow-400\" />\n              Host\n            </span>\n          </div>\n        </div>\n      )}\n\n      {/* Connection quality indicator */}\n      <div className=\"absolute top-2 right-2\">\n        <div className=\"flex space-x-0.5\">\n          <div className=\"w-0.5 h-2 bg-green-400 rounded-full\"></div>\n          <div className=\"w-0.5 h-3 bg-green-400 rounded-full\"></div>\n          <div className=\"w-0.5 h-4 bg-green-400 rounded-full\"></div>\n        </div>\n      </div>\n    </div>\n  )\n}\n", "'use client'\n\nimport { useVideoCallStore } from '@/lib/store'\nimport { VideoTile } from './VideoTile'\n\nexport function VideoGrid() {\n  const { currentUser, participants, localStream } = useVideoCallStore()\n\n  const participantsList = Array.from(participants.values())\n  const totalParticipants = participantsList.length + 1 // +1 for current user\n\n  // Calculate grid layout for smaller, more organized tiles\n  const getGridClass = (count: number) => {\n    if (count === 1) return 'grid-cols-1'\n    if (count === 2) return 'grid-cols-1'\n    if (count <= 4) return 'grid-cols-2'\n    if (count <= 6) return 'grid-cols-2'\n    return 'grid-cols-3'\n  }\n\n  const getGridRows = (count: number) => {\n    if (count === 1) return 'grid-rows-1'\n    if (count === 2) return 'grid-rows-2'\n    if (count <= 4) return 'grid-rows-2'\n    if (count <= 6) return 'grid-rows-3'\n    return 'grid-rows-3'\n  }\n\n  return (\n    <div className=\"flex-1 p-4\">\n      <div className=\"glass h-full p-4 overflow-hidden\">\n        <div className={`\n          grid gap-3 h-full\n          ${getGridClass(totalParticipants)}\n          ${getGridRows(totalParticipants)}\n          place-items-center\n        `}>\n          {/* Local user video - Featured if alone */}\n          {currentUser && (\n            <VideoTile\n              key={currentUser.id}\n              participant={currentUser}\n              stream={localStream}\n              isLocal={true}\n              isFeatured={totalParticipants === 1}\n            />\n          )}\n\n          {/* Remote participants */}\n          {participantsList.map((participant, index) => (\n            <VideoTile\n              key={participant.id}\n              participant={participant}\n              stream={participant.stream}\n              isLocal={false}\n              isFeatured={totalParticipants === 2 && index === 0}\n            />\n          ))}\n        </div>\n      </div>\n    </div>\n  )\n}\n", "'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useVideoCallStore } from '@/lib/store'\nimport { rtcManager } from '@/lib/rtc'\nimport { \n  X, \n  Camera, \n  Mic, \n  Monitor, \n  Volume2, \n  Settings, \n  Shield,\n\n  Image,\n  ChevronDown,\n  Check\n} from 'lucide-react'\n\ninterface MediaDevice {\n  deviceId: string\n  label: string\n  kind: MediaDeviceKind\n}\n\ninterface SettingsModalProps {\n  isOpen: boolean\n  onClose: () => void\n}\n\nexport function SettingsModal({ isOpen, onClose }: SettingsModalProps) {\n  const [activeTab, setActiveTab] = useState('video')\n  const [devices, setDevices] = useState<MediaDevice[]>([])\n  const [selectedCamera, setSelectedCamera] = useState('')\n  const [selectedMicrophone, setSelectedMicrophone] = useState('')\n  const [selectedSpeaker, setSelectedSpeaker] = useState('')\n  const [backgroundBlur, setBackgroundBlur] = useState(false)\n  const [selectedBackground, setSelectedBackground] = useState('none')\n  const [videoQuality, setVideoQuality] = useState('hd')\n  const [audioQuality, setAudioQuality] = useState('high')\n  const [noiseSuppression, setNoiseSuppression] = useState(true)\n  const [echoCancellation, setEchoCancellation] = useState(true)\n  const [autoGainControl, setAutoGainControl] = useState(true)\n  const [encryptionEnabled, setEncryptionEnabled] = useState(true)\n  const [antiSpamEnabled, setAntiSpamEnabled] = useState(true)\n  const [showCameraDropdown, setShowCameraDropdown] = useState(false)\n  const [showMicDropdown, setShowMicDropdown] = useState(false)\n\n  const { currentUser } = useVideoCallStore()\n\n  // Background options\n  const backgroundOptions = [\n    { id: 'none', name: 'None', preview: null },\n    { id: 'blur', name: 'Blur Background', preview: null },\n    { id: 'office', name: 'Modern Office', preview: '/backgrounds/office.jpg' },\n    { id: 'nature', name: 'Nature Scene', preview: '/backgrounds/nature.jpg' },\n    { id: 'abstract', name: 'Abstract Blue', preview: '/backgrounds/abstract.jpg' },\n    { id: 'gradient', name: 'Purple Gradient', preview: '/backgrounds/gradient.jpg' }\n  ]\n\n  // Video quality options\n  const videoQualityOptions = [\n    { id: 'sd', name: 'SD (480p)', resolution: '640x480' },\n    { id: 'hd', name: 'HD (720p)', resolution: '1280x720' },\n    { id: 'fhd', name: 'Full HD (1080p)', resolution: '1920x1080' },\n    { id: '4k', name: '4K (2160p)', resolution: '3840x2160' }\n  ]\n\n  // Load available devices\n  useEffect(() => {\n    const loadDevices = async () => {\n      try {\n        const deviceList = await navigator.mediaDevices.enumerateDevices()\n        const mediaDevices: MediaDevice[] = deviceList.map(device => ({\n          deviceId: device.deviceId,\n          label: device.label || `${device.kind} ${device.deviceId.slice(0, 8)}`,\n          kind: device.kind\n        }))\n        setDevices(mediaDevices)\n\n        // Set default devices\n        const defaultCamera = mediaDevices.find(d => d.kind === 'videoinput')\n        const defaultMic = mediaDevices.find(d => d.kind === 'audioinput')\n        const defaultSpeaker = mediaDevices.find(d => d.kind === 'audiooutput')\n\n        if (defaultCamera) setSelectedCamera(defaultCamera.deviceId)\n        if (defaultMic) setSelectedMicrophone(defaultMic.deviceId)\n        if (defaultSpeaker) setSelectedSpeaker(defaultSpeaker.deviceId)\n      } catch (error) {\n        console.error('Error loading devices:', error)\n      }\n    }\n\n    if (isOpen) {\n      loadDevices()\n    }\n  }, [isOpen])\n\n  const handleCameraChange = async (deviceId: string) => {\n    try {\n      setSelectedCamera(deviceId)\n      const constraints = {\n        video: { \n          deviceId: { exact: deviceId },\n          width: { ideal: 1280 },\n          height: { ideal: 720 }\n        },\n        audio: false\n      }\n      const stream = await navigator.mediaDevices.getUserMedia(constraints)\n      rtcManager.replaceVideoTrack(stream)\n      setShowCameraDropdown(false)\n    } catch (error) {\n      console.error('Error changing camera:', error)\n    }\n  }\n\n  const handleMicrophoneChange = async (deviceId: string) => {\n    try {\n      setSelectedMicrophone(deviceId)\n      const constraints = {\n        video: false,\n        audio: { \n          deviceId: { exact: deviceId },\n          echoCancellation,\n          noiseSuppression,\n          autoGainControl\n        }\n      }\n      const stream = await navigator.mediaDevices.getUserMedia(constraints)\n      // Replace audio track logic would go here\n      setShowMicDropdown(false)\n    } catch (error) {\n      console.error('Error changing microphone:', error)\n    }\n  }\n\n  const applyBackgroundEffect = (backgroundId: string) => {\n    setSelectedBackground(backgroundId)\n    // Background effect logic would be implemented here\n    // This would typically involve canvas manipulation or WebGL shaders\n  }\n\n  const applyVideoQuality = (quality: string) => {\n    setVideoQuality(quality)\n    // Video quality change logic would go here\n  }\n\n  if (!isOpen) return null\n\n  const cameras = devices.filter(d => d.kind === 'videoinput')\n  const microphones = devices.filter(d => d.kind === 'audioinput')\n  const speakers = devices.filter(d => d.kind === 'audiooutput')\n\n  return (\n    <div className=\"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50\">\n      <div className=\"glass max-w-4xl w-full max-h-[90vh] overflow-hidden\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-6 border-b border-white/10\">\n          <h2 className=\"text-2xl font-bold text-white flex items-center gap-3\">\n            <Settings className=\"h-6 w-6\" />\n            Meeting Settings\n          </h2>\n          <button\n            onClick={onClose}\n            className=\"glass-button p-2 text-white hover:text-purple-300\"\n          >\n            <X className=\"h-6 w-6\" />\n          </button>\n        </div>\n\n        <div className=\"flex h-[600px]\">\n          {/* Sidebar */}\n          <div className=\"w-64 p-4 border-r border-white/10\">\n            <div className=\"space-y-2\">\n              {[\n                { id: 'video', name: 'Video', icon: Camera },\n                { id: 'audio', name: 'Audio', icon: Mic },\n                { id: 'background', name: 'Background', icon: Image },\n                { id: 'security', name: 'Security', icon: Shield },\n                { id: 'general', name: 'General', icon: Settings }\n              ].map(tab => (\n                <button\n                  key={tab.id}\n                  onClick={() => setActiveTab(tab.id)}\n                  className={`w-full flex items-center gap-3 p-3 rounded-lg transition-all ${\n                    activeTab === tab.id \n                      ? 'bg-purple-500/30 text-white' \n                      : 'text-white/70 hover:bg-white/10 hover:text-white'\n                  }`}\n                >\n                  <tab.icon className=\"h-5 w-5\" />\n                  {tab.name}\n                </button>\n              ))}\n            </div>\n          </div>\n\n          {/* Content */}\n          <div className=\"flex-1 p-6 overflow-y-auto\">\n            {activeTab === 'video' && (\n              <div className=\"space-y-6\">\n                <h3 className=\"text-xl font-semibold text-white mb-4\">Video Settings</h3>\n                \n                {/* Camera Selection */}\n                <div>\n                  <label className=\"block text-white text-sm font-medium mb-2\">Camera</label>\n                  <div className=\"relative\">\n                    <button\n                      onClick={() => setShowCameraDropdown(!showCameraDropdown)}\n                      className=\"glass-input w-full flex items-center justify-between\"\n                    >\n                      <span>{cameras.find(c => c.deviceId === selectedCamera)?.label || 'Select Camera'}</span>\n                      <ChevronDown className=\"h-4 w-4\" />\n                    </button>\n                    {showCameraDropdown && (\n                      <div className=\"absolute top-full left-0 right-0 mt-1 glass-dark rounded-lg border border-white/20 z-10\">\n                        {cameras.map(camera => (\n                          <button\n                            key={camera.deviceId}\n                            onClick={() => handleCameraChange(camera.deviceId)}\n                            className=\"w-full p-3 text-left text-white hover:bg-white/10 flex items-center justify-between\"\n                          >\n                            {camera.label}\n                            {selectedCamera === camera.deviceId && <Check className=\"h-4 w-4\" />}\n                          </button>\n                        ))}\n                      </div>\n                    )}\n                  </div>\n                </div>\n\n                {/* Video Quality */}\n                <div>\n                  <label className=\"block text-white text-sm font-medium mb-2\">Video Quality</label>\n                  <div className=\"grid grid-cols-2 gap-2\">\n                    {videoQualityOptions.map(option => (\n                      <button\n                        key={option.id}\n                        onClick={() => applyVideoQuality(option.id)}\n                        className={`p-3 rounded-lg border transition-all ${\n                          videoQuality === option.id\n                            ? 'bg-purple-500/30 border-purple-500 text-white'\n                            : 'bg-white/10 border-white/20 text-white/70 hover:bg-white/20'\n                        }`}\n                      >\n                        <div className=\"font-medium\">{option.name}</div>\n                        <div className=\"text-xs opacity-70\">{option.resolution}</div>\n                      </button>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {activeTab === 'audio' && (\n              <div className=\"space-y-6\">\n                <h3 className=\"text-xl font-semibold text-white mb-4\">Audio Settings</h3>\n                \n                {/* Microphone Selection */}\n                <div>\n                  <label className=\"block text-white text-sm font-medium mb-2\">Microphone</label>\n                  <div className=\"relative\">\n                    <button\n                      onClick={() => setShowMicDropdown(!showMicDropdown)}\n                      className=\"glass-input w-full flex items-center justify-between\"\n                    >\n                      <span>{microphones.find(m => m.deviceId === selectedMicrophone)?.label || 'Select Microphone'}</span>\n                      <ChevronDown className=\"h-4 w-4\" />\n                    </button>\n                    {showMicDropdown && (\n                      <div className=\"absolute top-full left-0 right-0 mt-1 glass-dark rounded-lg border border-white/20 z-10\">\n                        {microphones.map(mic => (\n                          <button\n                            key={mic.deviceId}\n                            onClick={() => handleMicrophoneChange(mic.deviceId)}\n                            className=\"w-full p-3 text-left text-white hover:bg-white/10 flex items-center justify-between\"\n                          >\n                            {mic.label}\n                            {selectedMicrophone === mic.deviceId && <Check className=\"h-4 w-4\" />}\n                          </button>\n                        ))}\n                      </div>\n                    )}\n                  </div>\n                </div>\n\n                {/* Audio Enhancement */}\n                <div className=\"space-y-4\">\n                  <h4 className=\"text-white font-medium\">Audio Enhancement</h4>\n                  \n                  <label className=\"flex items-center justify-between\">\n                    <span className=\"text-white\">Noise Suppression</span>\n                    <input\n                      type=\"checkbox\"\n                      checked={noiseSuppression}\n                      onChange={(e) => setNoiseSuppression(e.target.checked)}\n                      className=\"w-5 h-5 rounded bg-white/10 border-white/20\"\n                    />\n                  </label>\n                  \n                  <label className=\"flex items-center justify-between\">\n                    <span className=\"text-white\">Echo Cancellation</span>\n                    <input\n                      type=\"checkbox\"\n                      checked={echoCancellation}\n                      onChange={(e) => setEchoCancellation(e.target.checked)}\n                      className=\"w-5 h-5 rounded bg-white/10 border-white/20\"\n                    />\n                  </label>\n                  \n                  <label className=\"flex items-center justify-between\">\n                    <span className=\"text-white\">Auto Gain Control</span>\n                    <input\n                      type=\"checkbox\"\n                      checked={autoGainControl}\n                      onChange={(e) => setAutoGainControl(e.target.checked)}\n                      className=\"w-5 h-5 rounded bg-white/10 border-white/20\"\n                    />\n                  </label>\n                </div>\n              </div>\n            )}\n\n            {activeTab === 'background' && (\n              <div className=\"space-y-6\">\n                <h3 className=\"text-xl font-semibold text-white mb-4\">Background Effects</h3>\n                \n                <div className=\"grid grid-cols-3 gap-4\">\n                  {backgroundOptions.map(bg => (\n                    <button\n                      key={bg.id}\n                      onClick={() => applyBackgroundEffect(bg.id)}\n                      className={`p-4 rounded-lg border-2 transition-all ${\n                        selectedBackground === bg.id\n                          ? 'border-purple-500 bg-purple-500/20'\n                          : 'border-white/20 bg-white/10 hover:border-white/40'\n                      }`}\n                    >\n                      <div className=\"w-full h-20 bg-gradient-to-br from-gray-600 to-gray-800 rounded-lg mb-2 flex items-center justify-center\">\n                        {bg.id === 'blur' ? <Monitor className=\"h-8 w-8 text-white\" /> : <Image className=\"h-8 w-8 text-white\" />}\n                      </div>\n                      <div className=\"text-white text-sm font-medium\">{bg.name}</div>\n                    </button>\n                  ))}\n                </div>\n              </div>\n            )}\n\n            {activeTab === 'security' && (\n              <div className=\"space-y-6\">\n                <h3 className=\"text-xl font-semibold text-white mb-4\">Security Settings</h3>\n                \n                <div className=\"space-y-4\">\n                  <label className=\"flex items-center justify-between\">\n                    <div>\n                      <span className=\"text-white font-medium\">End-to-End Encryption</span>\n                      <p className=\"text-white/60 text-sm\">Encrypt all video and audio streams</p>\n                    </div>\n                    <input\n                      type=\"checkbox\"\n                      checked={encryptionEnabled}\n                      onChange={(e) => setEncryptionEnabled(e.target.checked)}\n                      className=\"w-5 h-5 rounded bg-white/10 border-white/20\"\n                    />\n                  </label>\n                  \n                  <label className=\"flex items-center justify-between\">\n                    <div>\n                      <span className=\"text-white font-medium\">Anti-Spam Protection</span>\n                      <p className=\"text-white/60 text-sm\">Prevent message flooding in chat</p>\n                    </div>\n                    <input\n                      type=\"checkbox\"\n                      checked={antiSpamEnabled}\n                      onChange={(e) => setAntiSpamEnabled(e.target.checked)}\n                      className=\"w-5 h-5 rounded bg-white/10 border-white/20\"\n                    />\n                  </label>\n                </div>\n              </div>\n            )}\n\n            {activeTab === 'general' && (\n              <div className=\"space-y-6\">\n                <h3 className=\"text-xl font-semibold text-white mb-4\">General Settings</h3>\n                \n                <div className=\"space-y-4\">\n                  <div>\n                    <label className=\"block text-white text-sm font-medium mb-2\">Display Name</label>\n                    <input\n                      type=\"text\"\n                      value={currentUser?.name || ''}\n                      className=\"glass-input w-full\"\n                      placeholder=\"Your display name\"\n                    />\n                  </div>\n                  \n                  <div>\n                    <label className=\"block text-white text-sm font-medium mb-2\">Meeting Theme</label>\n                    <select className=\"glass-input w-full\">\n                      <option value=\"dark\">Dark Theme</option>\n                      <option value=\"light\">Light Theme</option>\n                      <option value=\"auto\">Auto</option>\n                    </select>\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Footer */}\n        <div className=\"flex items-center justify-end gap-3 p-6 border-t border-white/10\">\n          <button\n            onClick={onClose}\n            className=\"btn-secondary px-6 py-2\"\n          >\n            Cancel\n          </button>\n          <button\n            onClick={onClose}\n            className=\"btn-primary px-6 py-2\"\n          >\n            Save Changes\n          </button>\n        </div>\n      </div>\n    </div>\n  )\n}\n", "'use client'\n\nimport { useState, useEffect, useCallback } from 'react'\nimport { useVideoCallStore } from '@/lib/store'\nimport { rtcManager } from '@/lib/rtc'\nimport { SettingsModal } from './SettingsModal'\nimport {\n  Mic,\n  MicOff,\n  Video,\n  VideoOff,\n  Monitor,\n  MonitorOff,\n  Settings as SettingsIcon,\n  ChevronUp,\n  Camera,\n  Headphones\n} from 'lucide-react'\n\ninterface MediaDevice {\n  deviceId: string\n  label: string\n  kind: MediaDeviceKind\n}\n\nexport function VideoControls() {\n  const [isScreenSharingState, setIsScreenSharing] = useState(false)\n  const [notification, setNotification] = useState<string | null>(null)\n  const [showCameraDropdown, setShowCameraDropdown] = useState(false)\n  const [showMicDropdown, setShowMicDropdown] = useState(false)\n  const [showSettingsModal, setShowSettingsModal] = useState(false)\n  const [devices, setDevices] = useState<MediaDevice[]>([])\n  const [selectedCamera, setSelectedCamera] = useState('')\n  const [selectedMicrophone, setSelectedMicrophone] = useState('')\n\n  const {\n    toggleAudio,\n    toggleVideo,\n    isAudioMuted,\n    isVideoMuted,\n\n    setLocalStream\n  } = useVideoCallStore()\n\n  // Load available devices\n  const loadDevices = useCallback(async () => {\n    try {\n      // Request permissions first to get non-empty device labels\n      await navigator.mediaDevices.getUserMedia({ audio: true, video: true })\n      const deviceList = await navigator.mediaDevices.enumerateDevices()\n      \n      const mediaDevices: MediaDevice[] = deviceList\n        .filter(device => device.kind === 'audioinput' || device.kind === 'videoinput')\n        .map(device => ({\n          deviceId: device.deviceId,\n          label: device.label || `${device.kind} ${device.deviceId.slice(0, 8)}`,\n          kind: device.kind as MediaDeviceKind\n        }))\n        \n      setDevices(mediaDevices)\n\n      // Set default devices if not already set\n      if (!selectedCamera) {\n        const defaultCamera = mediaDevices.find(d => d.kind === 'videoinput')\n        if (defaultCamera) setSelectedCamera(defaultCamera.deviceId)\n      }\n      \n      if (!selectedMicrophone) {\n        const defaultMic = mediaDevices.find(d => d.kind === 'audioinput')\n        if (defaultMic) setSelectedMicrophone(defaultMic.deviceId)\n      }\n    } catch (error) {\n      console.error('Error loading devices:', error)\n    }\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [selectedCamera, selectedMicrophone])\n\n  useEffect(() => {\n    loadDevices()\n    \n    // Add event listener for device changes\n    navigator.mediaDevices.addEventListener('devicechange', loadDevices)\n    \n    return () => {\n      navigator.mediaDevices.removeEventListener('devicechange', loadDevices)\n    }\n  }, [loadDevices])\n\n  const showNotification = (message: string) => {\n    setNotification(message)\n    setTimeout(() => setNotification(null), 2000)\n  }\n\n  const handleCameraChange = async (deviceId: string) => {\n    try {\n      setSelectedCamera(deviceId)\n      const constraints = {\n        video: {\n          deviceId: { exact: deviceId },\n          width: { ideal: 1280 },\n          height: { ideal: 720 }\n        },\n        audio: false\n      }\n      const stream = await navigator.mediaDevices.getUserMedia(constraints)\n\n      // Update the local stream in the store\n      setLocalStream(stream)\n\n      // Replace the video track in RTC connections\n      await rtcManager.replaceVideoTrack(stream)\n\n      setShowCameraDropdown(false)\n      showNotification('Camera changed successfully')\n    } catch (error) {\n      console.error('Error changing camera:', error)\n      showNotification('Failed to change camera')\n    }\n  }\n\n  const handleMicrophoneChange = async (deviceId: string) => {\n    try {\n      setSelectedMicrophone(deviceId)\n      const constraints = {\n        video: false,\n        audio: {\n          deviceId: { exact: deviceId },\n          echoCancellation: true,\n          noiseSuppression: true,\n          autoGainControl: true\n        }\n      }\n      const stream = await navigator.mediaDevices.getUserMedia(constraints)\n\n      // Replace audio track logic would go here\n      // For now, just update the selected device\n\n      setShowMicDropdown(false)\n      showNotification('Microphone changed successfully')\n    } catch (error) {\n      console.error('Error changing microphone:', error)\n      showNotification('Failed to change microphone')\n    }\n  }\n\n  const handleToggleAudio = () => {\n    toggleAudio()\n    showNotification(isAudioMuted ? 'Microphone unmuted' : 'Microphone muted')\n  }\n\n  const handleToggleVideo = () => {\n    toggleVideo()\n    showNotification(isVideoMuted ? 'Camera turned on' : 'Camera turned off')\n  }\n\n  const handleToggleScreenShare = async () => {\n    try {\n      if (!isScreenSharingState) {\n        // Start screen sharing\n        const screenStream = await rtcManager.getDisplayMedia()\n\n        // Replace video track with screen share\n        await rtcManager.replaceVideoTrack(screenStream)\n\n        // Listen for screen share end\n        screenStream.getVideoTracks()[0].onended = () => {\n          handleStopScreenShare()\n        }\n\n        setIsScreenSharing(true)\n        showNotification('Screen sharing started')\n      } else {\n        handleStopScreenShare()\n      }\n    } catch (error) {\n      console.error('Error toggling screen share:', error)\n      showNotification('Failed to toggle screen share')\n    }\n  }\n\n  const handleStopScreenShare = async () => {\n    try {\n      // Get camera stream back\n      const cameraStream = await rtcManager.getUserMedia()\n\n      // Replace screen share with camera\n      rtcManager.replaceVideoTrack(cameraStream)\n\n      setIsScreenSharing(false)\n      showNotification('Screen sharing stopped')\n    } catch (error) {\n      console.error('Error stopping screen share:', error)\n      showNotification('Failed to stop screen share')\n    }\n  }\n\n  const handleCameraSelect = useCallback(async (deviceId: string) => {\n    if (deviceId === selectedCamera) {\n      setShowCameraDropdown(false)\n      return\n    }\n    \n    try {\n      await rtcManager.switchCamera(deviceId)\n      setSelectedCamera(deviceId)\n      setShowCameraDropdown(false)\n      showNotification('Camera changed')\n    } catch (error) {\n      console.error('Error switching camera:', error)\n      showNotification('Failed to switch camera')\n    }\n  }, [selectedCamera])\n\n  const handleMicrophoneSelect = useCallback(async (deviceId: string) => {\n    if (deviceId === selectedMicrophone) {\n      setShowMicDropdown(false)\n      return\n    }\n    \n    try {\n      // For now, we'll just update the selected microphone\n      // The actual device switching will be handled by the RTC manager\n      setSelectedMicrophone(deviceId)\n      setShowMicDropdown(false)\n      showNotification('Microphone changed')\n    } catch (error) {\n      console.error('Error switching microphone:', error)\n      showNotification('Failed to switch microphone')\n    }\n  }, [selectedMicrophone])\n\n  const cameras = devices.filter((d: MediaDevice) => d.kind === 'videoinput')\n  const microphones = devices.filter((d: MediaDevice) => d.kind === 'audioinput')\n\n  return (\n    <div className=\"p-4\">\n      <div className=\"video-controls\">\n        {/* Audio controls with dropdown */}\n        <div className=\"relative\">\n          <div className=\"flex\">\n            <button\n              onClick={handleToggleAudio}\n              className={`control-btn ${isAudioMuted ? 'active' : 'inactive'} rounded-r-none`}\n              title={isAudioMuted ? 'Unmute microphone' : 'Mute microphone'}\n            >\n              {isAudioMuted ? (\n                <MicOff className=\"h-6 w-6\" />\n              ) : (\n                <Mic className=\"h-6 w-6\" />\n              )}\n            </button>\n            {microphones.length > 1 && (\n              <button\n                onClick={() => setShowMicDropdown(!showMicDropdown)}\n                className=\"control-btn inactive rounded-l-none border-l border-white/20 w-8\"\n                title=\"Select microphone\"\n              >\n                <ChevronUp className=\"h-4 w-4\" />\n              </button>\n            )}\n          </div>\n\n          {showMicDropdown && microphones.length > 1 && (\n            <div className=\"absolute bottom-full left-0 mb-2 glass-dark rounded-lg border border-white/20 min-w-48 z-10\">\n              <div className=\"p-2\">\n                <div className=\"text-white text-xs font-medium mb-2 flex items-center gap-2\">\n                  <Headphones className=\"h-3 w-3\" />\n                  Select Microphone\n                </div>\n                {microphones.map((mic: MediaDevice) => (\n                  <div \n                    key={mic.deviceId}\n                    className=\"px-4 py-2 text-white hover:bg-white/10 cursor-pointer flex items-center\"\n                    onClick={() => handleMicrophoneSelect(mic.deviceId)}\n                  >\n                    <span className={selectedMicrophone === mic.deviceId ? 'font-semibold' : ''}>\n                      {mic.label}\n                    </span>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Video controls with dropdown */}\n        <div className=\"relative\">\n          <div className=\"flex\">\n            <button\n              onClick={handleToggleVideo}\n              className={`control-btn ${isVideoMuted ? 'active' : 'inactive'} rounded-r-none`}\n              title={isVideoMuted ? 'Turn on camera' : 'Turn off camera'}\n            >\n              {isVideoMuted ? (\n                <VideoOff className=\"h-6 w-6\" />\n              ) : (\n                <Video className=\"h-6 w-6\" />\n              )}\n            </button>\n            {cameras.length > 1 && (\n              <button\n                onClick={() => setShowCameraDropdown(!showCameraDropdown)}\n                className=\"control-btn inactive rounded-l-none border-l border-white/20 w-8\"\n                title=\"Select camera\"\n              >\n                <ChevronUp className=\"h-4 w-4\" />\n              </button>\n            )}\n          </div>\n\n          {showCameraDropdown && cameras.length > 1 && (\n            <div className=\"absolute bottom-full left-0 mb-2 glass-dark rounded-lg border border-white/20 min-w-48 z-10\">\n              <div className=\"p-2\">\n                <div className=\"text-white text-xs font-medium mb-2 flex items-center gap-2\">\n                  <Camera className=\"h-3 w-3\" />\n                  Select Camera\n                </div>\n                {cameras.map((camera: MediaDevice) => (\n                  <div \n                    key={camera.deviceId}\n                    className=\"px-4 py-2 text-white hover:bg-white/10 cursor-pointer flex items-center\"\n                    onClick={() => handleCameraSelect(camera.deviceId)}\n                  >\n                    <span className={selectedCamera === camera.deviceId ? 'font-semibold' : ''}>\n                      {camera.label}\n                    </span>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Screen share toggle */}\n        <button\n          onClick={handleToggleScreenShare}\n          className={`control-btn ${isScreenSharingState ? 'active' : 'inactive'}`}\n          title={isScreenSharingState ? 'Stop screen sharing' : 'Share screen'}\n        >\n          {isScreenSharingState ? (\n            <MonitorOff className=\"h-6 w-6\" />\n          ) : (\n            <Monitor className=\"h-6 w-6\" />\n          )}\n        </button>\n\n        {/* Settings */}\n        <button\n          onClick={() => setShowSettingsModal(true)}\n          className=\"p-2 rounded-full hover:bg-gray-200 transition-colors\"\n          aria-label=\"Settings\"\n        >\n          <SettingsIcon className=\"w-6 h-6\" />\n        </button>\n      </div>\n\n      {/* Settings Modal */}\n      <SettingsModal\n        isOpen={showSettingsModal}\n        onClose={() => setShowSettingsModal(false)}\n      />\n\n      {/* Notification */}\n      {notification && (\n        <div className=\"fixed bottom-20 left-1/2 transform -translate-x-1/2 glass-dark px-4 py-2 rounded-lg text-white z-50 fade-in\">\n          {notification}\n        </div>\n      )}\n    </div>\n  )\n}\n", "'use client'\n\nimport { useState, useRef, useEffect } from 'react'\nimport { useVideoCallStore } from '@/lib/store'\nimport { socketManager } from '@/lib/socket'\nimport { Send, X, MessageCircle } from 'lucide-react'\n\nexport function Chat() {\n  const [message, setMessage] = useState('')\n  const messagesEndRef = useRef<HTMLDivElement>(null)\n\n  const {\n    messages,\n    currentUser,\n    toggleChat,\n    clearUnreadCount,\n    securitySettings,\n\n    blockUser\n  } = useVideoCallStore()\n\n  const isAdmin = currentUser?.role === 'host' || currentUser?.role === 'co-host'\n\n  // Auto-scroll to bottom when new messages arrive\n  useEffect(() => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })\n  }, [messages])\n\n  // Clear unread count when chat is opened\n  useEffect(() => {\n    clearUnreadCount()\n  }, [clearUnreadCount])\n\n  const handleSendMessage = (e: React.FormEvent) => {\n    e.preventDefault()\n\n    if (!message.trim() || !currentUser) return\n\n    // Spam detection is handled in the store when adding messages\n\n    // Send message via Socket.IO\n    socketManager.sendChatMessage(message.trim(), currentUser.name)\n\n    setMessage('')\n  }\n\n  const handleBlockUser = (userId: string) => {\n    if (isAdmin) {\n      blockUser(userId)\n    }\n  }\n\n  const formatTime = (date: Date) => {\n    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })\n  }\n\n  return (\n    <div className=\"chat-container flex flex-col h-full\">\n      {/* Chat header */}\n      <div className=\"flex items-center justify-between p-3 border-b border-white/10\">\n        <h3 className=\"text-white font-semibold text-sm flex items-center gap-2\">\n          <MessageCircle className=\"h-4 w-4\" />\n          Chat\n        </h3>\n        <div className=\"flex items-center gap-2\">\n          {messages.length > 0 && (\n            <span className=\"text-white/60 text-xs\">\n              {messages.length} messages\n            </span>\n          )}\n        </div>\n      </div>\n\n      {/* Messages */}\n      <div className=\"flex-1 overflow-y-auto p-3 space-y-3\">\n        {messages.length === 0 ? (\n          <div className=\"text-center text-white/60 py-8\">\n            <div className=\"w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-3\">\n              <Send className=\"h-6 w-6 text-white\" />\n            </div>\n            <p className=\"text-sm font-medium mb-1\">No messages yet</p>\n            <p className=\"text-xs\">Start the conversation!</p>\n          </div>\n        ) : (\n          messages\n            .filter(msg => !msg.isBlocked) // Filter out blocked messages\n            .map((msg) => (\n            <div\n              key={msg.id}\n              className={`flex flex-col ${\n                msg.userId === currentUser?.id ? 'items-end' : 'items-start'\n              } ${msg.isSpam ? 'opacity-50' : ''}`}\n            >\n              <div\n                className={`chat-message max-w-[90%] p-2 relative ${\n                  msg.userId === currentUser?.id\n                    ? 'bg-gradient-to-r from-purple-500 to-pink-500'\n                    : msg.isSpam\n                      ? 'bg-red-500/20 border border-red-500/50'\n                      : 'bg-white/10'\n                }`}\n              >\n                <p className=\"text-white text-xs leading-relaxed\">{msg.message}</p>\n                {msg.isSpam && (\n                  <div className=\"absolute top-0 right-0 bg-red-500 text-white text-xs px-1 rounded-bl\">\n                    SPAM\n                  </div>\n                )}\n              </div>\n              <div className=\"flex items-center space-x-1 mt-1 text-xs text-white/50\">\n                <span className=\"font-medium text-xs\">{msg.userName}</span>\n                <span>•</span>\n                <span className=\"text-xs\">{formatTime(msg.timestamp)}</span>\n                {isAdmin && msg.userId !== currentUser?.id && (\n                  <button\n                    onClick={() => handleBlockUser(msg.userId)}\n                    className=\"text-red-400 hover:text-red-300 text-xs ml-2\"\n                  >\n                    Block\n                  </button>\n                )}\n              </div>\n            </div>\n          ))\n        )}\n        <div ref={messagesEndRef} />\n      </div>\n\n      {/* Message input */}\n      <div className=\"p-3 border-t border-white/10\">\n        <form onSubmit={handleSendMessage} className=\"flex space-x-2\">\n          <input\n            type=\"text\"\n            value={message}\n            onChange={(e) => setMessage(e.target.value)}\n            placeholder=\"Type a message...\"\n            className=\"glass-input flex-1 text-sm py-2\"\n            maxLength={500}\n            autoComplete=\"off\"\n          />\n          <button\n            type=\"submit\"\n            disabled={!message.trim()}\n            className={`p-2 rounded-lg transition-all ${\n              message.trim()\n                ? 'bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white'\n                : 'bg-white/10 text-white/50 cursor-not-allowed'\n            }`}\n          >\n            <Send className=\"h-4 w-4\" />\n          </button>\n        </form>\n      </div>\n    </div>\n  )\n}\n", "'use client'\n\nimport { useState } from 'react'\nimport { useVideoCallStore } from '@/lib/store'\nimport {\n  Mic,\n  MicOff,\n  Video,\n  VideoOff,\n  Monitor,\n  Crown,\n  MoreVertical,\n  Users,\n  UserMinus,\n  UserCheck,\n  Shield,\n  ShieldOff,\n  Hand,\n  Lock,\n  Unlock\n} from 'lucide-react'\n\nexport function ParticipantsList() {\n  const [showAdminMenu, setShowAdminMenu] = useState<string | null>(null)\n\n  const {\n    currentUser,\n    participants,\n    roomLocked,\n    muteParticipant,\n    muteAllParticipants,\n    removeParticipant,\n    promoteToCoHost,\n    demoteFromCoHost,\n    lockRoom,\n    unlockRoom,\n    blockUser\n  } = useVideoCallStore()\n\n  const participantsList = Array.from(participants.values())\n  const allParticipants = currentUser ? [currentUser, ...participantsList] : participantsList\n\n  const getStatusIcon = (participant: any) => {\n    const icons = []\n\n    if (participant.isAudioMuted) {\n      icons.push(<MicOff key=\"mic\" className=\"h-3 w-3 text-red-400\" />)\n    } else {\n      icons.push(<Mic key=\"mic\" className=\"h-3 w-3 text-green-400\" />)\n    }\n\n    if (participant.isVideoMuted) {\n      icons.push(<VideoOff key=\"video\" className=\"h-3 w-3 text-red-400\" />)\n    } else {\n      icons.push(<Video key=\"video\" className=\"h-3 w-3 text-green-400\" />)\n    }\n\n    if (participant.isScreenSharing) {\n      icons.push(<Monitor key=\"screen\" className=\"h-3 w-3 text-blue-400\" />)\n    }\n\n    return icons\n  }\n\n  const isAdmin = currentUser?.role === 'host' || currentUser?.role === 'co-host'\n  const isHost = currentUser?.role === 'host'\n\n  const handleAdminAction = (action: string, participantId: string) => {\n    switch (action) {\n      case 'mute':\n        muteParticipant(participantId)\n        break\n      case 'remove':\n        removeParticipant(participantId)\n        break\n      case 'promote':\n        promoteToCoHost(participantId)\n        break\n      case 'demote':\n        demoteFromCoHost(participantId)\n        break\n      case 'block':\n        blockUser(participantId)\n        break\n    }\n    setShowAdminMenu(null)\n  }\n\n  return (\n    <div className=\"chat-container flex flex-col h-full\">\n      {/* Header */}\n      <div className=\"p-3 border-b border-white/10\">\n        <div className=\"flex items-center justify-between\">\n          <h3 className=\"text-white font-semibold text-sm flex items-center gap-2\">\n            <Users className=\"h-4 w-4\" />\n            Participants ({allParticipants.length})\n            {roomLocked && <Lock className=\"h-3 w-3 text-yellow-400\" />}\n          </h3>\n\n          {isAdmin && (\n            <div className=\"flex items-center gap-1\">\n              <button\n                onClick={muteAllParticipants}\n                className=\"glass-button p-1 text-white/60 hover:text-white\"\n                title=\"Mute all participants\"\n              >\n                <MicOff className=\"h-3 w-3\" />\n              </button>\n\n              {isHost && (\n                <button\n                  onClick={() => roomLocked ? unlockRoom() : lockRoom()}\n                  className=\"glass-button p-1 text-white/60 hover:text-white\"\n                  title={roomLocked ? 'Unlock room' : 'Lock room'}\n                >\n                  {roomLocked ? <Unlock className=\"h-3 w-3\" /> : <Lock className=\"h-3 w-3\" />}\n                </button>\n              )}\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Participants list */}\n      <div className=\"flex-1 overflow-y-auto p-2 space-y-2\">\n        {allParticipants.map((participant) => {\n          const isCurrentUser = participant.id === currentUser?.id\n\n          return (\n            <div\n              key={participant.id}\n              className=\"glass-button p-2 hover:bg-white/20 transition-all relative\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center space-x-2\">\n                  {/* Avatar */}\n                  <div className=\"w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center relative\">\n                    <span className=\"text-white text-xs font-bold\">\n                      {participant.name.charAt(0).toUpperCase()}\n                    </span>\n                    {participant.isHandRaised && (\n                      <div className=\"absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 rounded-full flex items-center justify-center\">\n                        <Hand className=\"h-2 w-2 text-black\" />\n                      </div>\n                    )}\n                  </div>\n\n                  {/* Name and status */}\n                  <div className=\"flex flex-col flex-1 min-w-0\">\n                    <div className=\"flex items-center space-x-1\">\n                      <span className=\"text-white text-xs font-medium truncate\">\n                        {participant.name}\n                        {isCurrentUser && ' (You)'}\n                      </span>\n\n                      {/* Role indicators */}\n                      {participant.role === 'host' && (\n                        <Crown className=\"h-3 w-3 text-yellow-400 flex-shrink-0\" />\n                      )}\n                      {participant.role === 'co-host' && (\n                        <Shield className=\"h-3 w-3 text-blue-400 flex-shrink-0\" />\n                      )}\n                    </div>\n                    <div className=\"flex items-center space-x-1\">\n                      {getStatusIcon(participant)}\n                    </div>\n                  </div>\n                </div>\n\n                {/* Actions */}\n                {!isCurrentUser && isAdmin && (\n                  <div className=\"relative\">\n                    <button\n                      onClick={() => setShowAdminMenu(showAdminMenu === participant.id ? null : participant.id)}\n                      className=\"glass-button p-1 text-white/60 hover:text-white\"\n                    >\n                      <MoreVertical className=\"h-3 w-3\" />\n                    </button>\n\n                    {/* Admin Menu */}\n                    {showAdminMenu === participant.id && (\n                      <div className=\"absolute right-0 top-full mt-1 glass-dark rounded-lg border border-white/20 min-w-32 z-10\">\n                        <div className=\"p-1\">\n                          <button\n                            onClick={() => handleAdminAction('mute', participant.id)}\n                            className=\"w-full p-2 text-left text-white text-xs hover:bg-white/10 rounded flex items-center gap-2\"\n                          >\n                            <MicOff className=\"h-3 w-3\" />\n                            Mute\n                          </button>\n\n                          {isHost && participant.role === 'participant' && (\n                            <button\n                              onClick={() => handleAdminAction('promote', participant.id)}\n                              className=\"w-full p-2 text-left text-white text-xs hover:bg-white/10 rounded flex items-center gap-2\"\n                            >\n                              <Shield className=\"h-3 w-3\" />\n                              Make Co-Host\n                            </button>\n                          )}\n\n                          {isHost && participant.role === 'co-host' && (\n                            <button\n                              onClick={() => handleAdminAction('demote', participant.id)}\n                              className=\"w-full p-2 text-left text-white text-xs hover:bg-white/10 rounded flex items-center gap-2\"\n                            >\n                              <ShieldOff className=\"h-3 w-3\" />\n                              Remove Co-Host\n                            </button>\n                          )}\n\n                          <button\n                            onClick={() => handleAdminAction('block', participant.id)}\n                            className=\"w-full p-2 text-left text-red-400 text-xs hover:bg-white/10 rounded flex items-center gap-2\"\n                          >\n                            <UserMinus className=\"h-3 w-3\" />\n                            Block User\n                          </button>\n\n                          <button\n                            onClick={() => handleAdminAction('remove', participant.id)}\n                            className=\"w-full p-2 text-left text-red-400 text-xs hover:bg-white/10 rounded flex items-center gap-2\"\n                          >\n                            <UserMinus className=\"h-3 w-3\" />\n                            Remove\n                          </button>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                )}\n              </div>\n            </div>\n          )\n        })}\n      </div>\n    </div>\n  )\n}\n", "'use client'\n\nimport { useEffect, useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useVideoCallStore } from '@/lib/store'\nimport { socketManager } from '@/lib/socket'\nimport { rtcManager } from '@/lib/rtc'\nimport { VideoGrid } from './VideoGrid'\nimport { VideoControls } from './VideoControls'\nimport { Chat } from './Chat'\nimport { ParticipantsList } from './ParticipantsList'\nimport { SettingsModal } from './SettingsModal'\nimport {\n  Users,\n  Settings,\n  PhoneOff,\n  Copy,\n  Share,\n  Maximize2,\n  Minimize2\n} from 'lucide-react'\n\ninterface VideoCallRoomProps {\n  roomId: string\n}\n\nexport function VideoCallRoom({ roomId }: VideoCallRoomProps) {\n  const router = useRouter()\n  const [isInitialized, setIsInitialized] = useState(false)\n\n  const [showSettings, setShowSettings] = useState(false)\n  const [isFullscreen, setIsFullscreen] = useState(false)\n  const [notification, setNotification] = useState<string | null>(null)\n\n  const {\n    currentUser,\n    participants,\n    setConnected,\n    addParticipant,\n    removeParticipant,\n    updateParticipant,\n    setLocalStream,\n    addMessage,\n    reset\n  } = useVideoCallStore()\n\n  // Show notification\n  const showNotification = (message: string) => {\n    setNotification(message)\n    setTimeout(() => setNotification(null), 3000)\n  }\n\n  // Initialize WebRTC and Socket.IO\n  useEffect(() => {\n    const initializeCall = async () => {\n      try {\n        // Connect to Socket.IO server\n        const socket = socketManager.connect()\n\n        if (!socket) {\n          throw new Error('Failed to connect to server')\n        }\n\n        // Get user media\n        const stream = await rtcManager.getUserMedia()\n        setLocalStream(stream)\n\n        // Set up RTC signal callback\n        rtcManager.setSendSignalCallback((userId, signal) => {\n          socketManager.sendSignal(userId, signal)\n        })\n\n        // Set up RTC stream callback\n        rtcManager.onStream((userId, stream) => {\n          updateParticipant(userId, { stream })\n        })\n\n        // Set up RTC disconnect callback\n        rtcManager.onUserDisconnected((userId) => {\n          removeParticipant(userId)\n          showNotification('A participant has left the meeting')\n        })\n\n        // Join the room\n        if (currentUser) {\n          socketManager.joinRoom(roomId, currentUser.id, currentUser.name)\n        }\n\n        setIsInitialized(true)\n        setConnected(true)\n        showNotification('Successfully connected to meeting')\n\n      } catch (error) {\n        console.error('Failed to initialize call:', error)\n        showNotification('Failed to join meeting. Please check your permissions.')\n      }\n    }\n\n    if (currentUser && !isInitialized) {\n      initializeCall()\n    }\n\n    return () => {\n      // Cleanup on unmount\n      if (isInitialized) {\n        socketManager.leaveRoom()\n        rtcManager.cleanup()\n        reset()\n      }\n    }\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [currentUser, roomId, isInitialized])\n\n  // Set up Socket.IO event listeners\n  useEffect(() => {\n    if (!isInitialized) return\n\n    const socket = socketManager.getSocket()\n    if (!socket) return\n\n    // Handle new user joined\n    const handleUserJoined = async (data: { userId: string, userName: string }) => {\n      console.log('User joined:', data)\n\n      addParticipant({\n        id: data.userId,\n        name: data.userName,\n        isAudioMuted: false,\n        isVideoMuted: false,\n        isScreenSharing: false,\n        role: 'participant',\n        isHandRaised: false,\n        joinedAt: new Date(),\n        lastActivity: new Date()\n      })\n\n      // Create offer for new user\n      try {\n        const offer = await rtcManager.createOffer(data.userId)\n        socketManager.sendSignal(data.userId, {\n          type: 'offer',\n          offer\n        })\n      } catch (error) {\n        console.error('Error creating offer:', error)\n      }\n\n      showNotification(`${data.userName} joined the meeting`)\n    }\n\n    // Handle user left\n    const handleUserLeft = (data: { userId: string, userName: string }) => {\n      console.log('User left:', data)\n      removeParticipant(data.userId)\n      rtcManager.removePeerConnection(data.userId)\n      showNotification(`${data.userName} left the meeting`)\n    }\n\n    // Handle WebRTC signals\n    const handleSignal = async (data: { fromUserId: string, signal: any }) => {\n      console.log('Received signal:', data)\n\n      try {\n        const { fromUserId, signal } = data\n\n        switch (signal.type) {\n          case 'offer':\n            const answer = await rtcManager.createAnswer(fromUserId, signal.offer)\n            socketManager.sendSignal(fromUserId, {\n              type: 'answer',\n              answer\n            })\n            break\n\n          case 'answer':\n            await rtcManager.handleAnswer(fromUserId, signal.answer)\n            break\n\n          case 'ice-candidate':\n            await rtcManager.handleIceCandidate(fromUserId, signal.candidate)\n            break\n        }\n      } catch (error) {\n        console.error('Error handling signal:', error)\n      }\n    }\n\n    // Handle chat messages\n    const handleChatMessage = (data: { userId: string, userName: string, message: string, timestamp: string }) => {\n      addMessage({\n        id: Math.random().toString(36).substring(2, 15),\n        userId: data.userId,\n        userName: data.userName,\n        message: data.message,\n        timestamp: new Date(data.timestamp)\n      })\n    }\n\n    // Register event listeners\n    socket.on('user-joined', handleUserJoined)\n    socket.on('user-left', handleUserLeft)\n    socket.on('signal', handleSignal)\n    socket.on('chat-message', handleChatMessage)\n\n    return () => {\n      socket.off('user-joined', handleUserJoined)\n      socket.off('user-left', handleUserLeft)\n      socket.off('signal', handleSignal)\n      socket.off('chat-message', handleChatMessage)\n    }\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [isInitialized])\n\n  const [showLeaveConfirm, setShowLeaveConfirm] = useState(false)\n\n  const handleLeaveCall = () => {\n    setShowLeaveConfirm(true)\n  }\n\n  const confirmLeave = () => {\n    socketManager.leaveRoom()\n    rtcManager.cleanup()\n    reset()\n    router.push('/')\n  }\n\n  const cancelLeave = () => {\n    setShowLeaveConfirm(false)\n  }\n\n  const copyMeetingLink = () => {\n    const meetingLink = `${window.location.origin}/room/${roomId}`\n    navigator.clipboard.writeText(meetingLink)\n    showNotification('Meeting link copied to clipboard!')\n  }\n\n  const shareMeetingId = () => {\n    navigator.clipboard.writeText(roomId)\n    showNotification('Meeting ID copied to clipboard!')\n  }\n\n  const toggleFullscreen = () => {\n    if (!document.fullscreenElement) {\n      document.documentElement.requestFullscreen()\n      setIsFullscreen(true)\n    } else {\n      document.exitFullscreen()\n      setIsFullscreen(false)\n    }\n  }\n\n  if (!isInitialized || !currentUser) {\n    return (\n      <>\n        <div className=\"animated-bg\"></div>\n        <div className=\"min-h-screen flex items-center justify-center relative z-10\">\n          <div className=\"glass p-8 text-center\">\n            <div className=\"animate-spin rounded-full h-16 w-16 border-4 border-white/30 border-t-white mx-auto mb-6\"></div>\n            <h2 className=\"text-2xl font-semibold text-white mb-2\">Connecting to Meeting</h2>\n            <p className=\"text-white/70\">Please wait while we set up your video call...</p>\n          </div>\n        </div>\n      </>\n    )\n  }\n\n  return (\n    <>\n      {/* Animated Background */}\n      <div className=\"animated-bg\"></div>\n\n      <div className=\"min-h-screen flex flex-col relative z-10 pt-20\">\n        {/* Header - Fixed at top */}\n        <div className=\"glass-dark p-4 fixed top-0 left-0 right-0 z-50\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-4\">\n              <h1 className=\"text-white text-xl font-semibold\">\n                StreamIt Pro Meeting\n              </h1>\n              <div className=\"flex items-center space-x-2 text-white/70\">\n                <Users className=\"h-4 w-4\" />\n                <span>{participants.size + 1} participants</span>\n              </div>\n              <div className=\"text-white/50 text-sm\">\n                ID: {roomId}\n              </div>\n            </div>\n\n            <div className=\"flex items-center space-x-2\">\n              <button\n                onClick={copyMeetingLink}\n                className=\"glass-button p-2 text-white hover:text-purple-300\"\n                title=\"Copy meeting link\"\n              >\n                <Share className=\"h-5 w-5\" />\n              </button>\n\n              <button\n                onClick={shareMeetingId}\n                className=\"glass-button p-2 text-white hover:text-purple-300\"\n                title=\"Copy meeting ID\"\n              >\n                <Copy className=\"h-5 w-5\" />\n              </button>\n\n              <button\n                onClick={() => setShowSettings(!showSettings)}\n                className={`glass-button p-2 text-white hover:text-purple-300 ${showSettings ? 'bg-purple-500/30' : ''}`}\n                title=\"Settings\"\n              >\n                <Settings className=\"h-5 w-5\" />\n              </button>\n\n              <button\n                onClick={toggleFullscreen}\n                className=\"glass-button p-2 text-white hover:text-purple-300\"\n                title=\"Toggle fullscreen\"\n              >\n                {isFullscreen ? <Minimize2 className=\"h-5 w-5\" /> : <Maximize2 className=\"h-5 w-5\" />}\n              </button>\n\n              <button\n                onClick={handleLeaveCall}\n                className=\"bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors\"\n                title=\"Leave meeting\"\n              >\n                <PhoneOff className=\"h-5 w-5\" />\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Main content - Add padding top to account for fixed header */}\n        <div className=\"flex-1 flex p-4 pt-20 gap-4 mt-4\">\n          {/* Video area - Made smaller */}\n          <div className=\"w-2/3 flex flex-col\">\n            <VideoGrid />\n            <VideoControls />\n          </div>\n\n          {/* Right Sidebar - Always visible */}\n          <div className=\"w-1/3 flex flex-col gap-4\">\n            {/* Participants Panel */}\n            <div className=\"h-1/2\">\n              <ParticipantsList />\n            </div>\n\n            {/* Chat Panel */}\n            <div className=\"h-1/2\">\n              <Chat />\n            </div>\n          </div>\n        </div>\n\n        {/* Settings Modal */}\n        <SettingsModal\n          isOpen={showSettings}\n          onClose={() => setShowSettings(false)}\n        />\n\n        {/* Leave Meeting Confirmation Modal */}\n        {showLeaveConfirm && (\n          <div className=\"fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4\">\n            <div className=\"glass p-6 rounded-lg max-w-md w-full\">\n              <h3 className=\"text-xl font-semibold text-white mb-4\">Leave Meeting?</h3>\n              <p className=\"text-white/80 mb-6\">Are you sure you want to leave the meeting?</p>\n              <div className=\"flex justify-end space-x-3\">\n                <button\n                  onClick={cancelLeave}\n                  className=\"px-4 py-2 rounded-lg bg-gray-600 text-white hover:bg-gray-500 transition-colors\"\n                >\n                  Cancel\n                </button>\n                <button\n                  onClick={confirmLeave}\n                  className=\"px-4 py-2 rounded-lg bg-red-600 text-white hover:bg-red-500 transition-colors\"\n                >\n                  Leave\n                </button>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Notification */}\n        {notification && (\n          <div className=\"fixed top-20 right-4 glass p-4 text-white z-50 fade-in rounded-lg shadow-lg\">\n            {notification}\n          </div>\n        )}\n      </div>\n    </>\n  )\n}\n", "'use client'\n\nimport { useEffect, useState, useCallback } from 'react'\nimport { usePara<PERSON>, useRouter } from 'next/navigation'\nimport { VideoCallRoom } from '@/components/VideoCall/VideoCallRoom'\nimport { useVideoCallStore } from '@/lib/store'\n\n\nexport default function RoomPage() {\n  const params = useParams()\n  const router = useRouter()\n  const roomId = params.id as string\n  const [userName, setUserName] = useState('')\n  const [isJoining, setIsJoining] = useState(false)\n  const [hasJoined, setHasJoined] = useState(false)\n  \n  const { setRoomId, setCurrentUser } = useVideoCallStore()\n\n  const handleJoinRoom = useCallback(async (name: string) => {\n    if (!name.trim()) {\n      alert('Please enter your name')\n      return\n    }\n\n    setIsJoining(true)\n    \n    try {\n      // Set room ID in store\n      setRoomId(roomId)\n      \n      // Create user object\n      const user = {\n        id: Math.random().toString(36).substring(2, 15),\n        name: name.trim(),\n        isAudioMuted: false,\n        isVideoMuted: false,\n        isScreenSharing: false,\n        role: 'host' as const, // First user is always host\n        isHandRaised: false,\n        joinedAt: new Date(),\n        lastActivity: new Date()\n      }\n      \n      // Set current user in store\n      setCurrentUser(user)\n      \n      // Store user name for future sessions\n      localStorage.setItem('userName', name.trim())\n      \n      setHasJoined(true)\n    } catch (error) {\n      console.error('Error joining room:', error)\n      alert('Failed to join room. Please try again.')\n    } finally {\n      setIsJoining(false)\n    }\n  }, [roomId, setCurrentUser, setRoomId])\n\n  useEffect(() => {\n    // Check if user name is already stored\n    const storedName = localStorage.getItem('userName')\n    if (storedName) {\n      setUserName(storedName)\n      handleJoinRoom(storedName)\n    }\n  }, [handleJoinRoom])\n\n  const handleNameSubmit = (e: React.FormEvent) => {\n    e.preventDefault()\n    handleJoinRoom(userName)\n  }\n\n  if (!hasJoined) {\n    return (\n      <>\n        <div className=\"animated-bg\"></div>\n        <div className=\"min-h-screen flex items-center justify-center p-4 relative z-10\">\n          <div className=\"glass p-8 max-w-md w-full\">\n            <div className=\"text-center mb-6\">\n              <h2 className=\"text-2xl font-bold text-white mb-2\">Join Meeting</h2>\n              <p className=\"text-white/70\">\n                Meeting ID: <span className=\"font-mono font-semibold text-purple-300\">{roomId}</span>\n              </p>\n            </div>\n\n            <form onSubmit={handleNameSubmit} className=\"space-y-4\">\n              <input\n                type=\"text\"\n                placeholder=\"Enter your name\"\n                value={userName}\n                onChange={(e) => setUserName(e.target.value)}\n                disabled={isJoining}\n                autoFocus\n                className=\"glass-input w-full\"\n              />\n\n              <div className=\"flex gap-3\">\n                <button\n                  type=\"button\"\n                  onClick={() => router.push('/')}\n                  disabled={isJoining}\n                  className=\"btn-secondary flex-1\"\n                >\n                  Back\n                </button>\n                <button\n                  type=\"submit\"\n                  disabled={isJoining || !userName.trim()}\n                  className=\"btn-primary flex-1\"\n                >\n                  {isJoining ? 'Joining...' : 'Join Meeting'}\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      </>\n    )\n  }\n\n  return <VideoCallRoom roomId={roomId} />\n}\n", "import { create } from 'zustand'\nimport { devtools } from 'zustand/middleware'\n\nexport interface Participant {\n  id: string\n  name: string\n  stream?: MediaStream\n  isAudioMuted: boolean\n  isVideoMuted: boolean\n  isScreenSharing: boolean\n  role: 'host' | 'co-host' | 'participant'\n  isHandRaised: boolean\n  joinedAt: Date\n  lastActivity: Date\n}\n\nexport interface ChatMessage {\n  id: string\n  userId: string\n  userName: string\n  message: string\n  timestamp: Date\n  isSpam?: boolean\n  isBlocked?: boolean\n}\n\nexport interface SecuritySettings {\n  encryptionEnabled: boolean\n  antiSpamEnabled: boolean\n  maxMessagesPerMinute: number\n  allowScreenShare: boolean\n  allowFileSharing: boolean\n  requireApprovalToJoin: boolean\n}\n\nexport interface AdminControls {\n  canMuteAll: boolean\n  canMuteParticipant: boolean\n  canRemoveParticipant: boolean\n  canControlCamera: boolean\n  canManageRoles: boolean\n}\n\nexport interface VideoCallState {\n  // Room state\n  roomId: string | null\n  isConnected: boolean\n  roomLocked: boolean\n\n  // User state\n  currentUser: Participant | null\n  participants: Map<string, Participant>\n\n  // Media state\n  localStream: MediaStream | null\n  isAudioMuted: boolean\n  isVideoMuted: boolean\n  isScreenSharing: boolean\n\n  // Chat state\n  messages: ChatMessage[]\n  unreadCount: number\n  messageHistory: Map<string, number>\n\n  // UI state\n  isChatOpen: boolean\n  isSettingsOpen: boolean\n\n  // Security & Admin\n  securitySettings: SecuritySettings\n  adminControls: AdminControls\n  blockedUsers: Set<string>\n  spamDetection: Map<string, { count: number, lastReset: number }>\n\n  // Actions\n  setRoomId: (roomId: string) => void\n  setConnected: (connected: boolean) => void\n  setCurrentUser: (user: Participant) => void\n  addParticipant: (participant: Participant) => void\n  removeParticipant: (participantId: string) => void\n  updateParticipant: (participantId: string, updates: Partial<Participant>) => void\n  setLocalStream: (stream: MediaStream | null) => void\n  toggleAudio: () => void\n  toggleVideo: () => void\n  toggleScreenShare: () => void\n  addMessage: (message: ChatMessage) => void\n  clearUnreadCount: () => void\n  toggleChat: () => void\n  toggleSettings: () => void\n\n  // Admin Actions\n  muteParticipant: (participantId: string) => void\n  muteAllParticipants: () => void\n  unmuteAllParticipants: () => void\n  promoteToCoHost: (participantId: string) => void\n  demoteFromCoHost: (participantId: string) => void\n  blockUser: (participantId: string) => void\n  unblockUser: (participantId: string) => void\n  lockRoom: () => void\n  unlockRoom: () => void\n  updateSecuritySettings: (settings: Partial<SecuritySettings>) => void\n\n  // Utility Actions\n  reset: () => void\n}\n\nconst initialState = {\n  roomId: null,\n  isConnected: false,\n  roomLocked: false,\n  currentUser: null,\n  participants: new Map<string, Participant>(),\n  localStream: null,\n  isAudioMuted: false,\n  isVideoMuted: false,\n  isScreenSharing: false,\n  messages: [],\n  unreadCount: 0,\n  messageHistory: new Map<string, number>(),\n  isChatOpen: false,\n  isSettingsOpen: false,\n  securitySettings: {\n    encryptionEnabled: true,\n    antiSpamEnabled: true,\n    maxMessagesPerMinute: 10,\n    allowScreenShare: true,\n    allowFileSharing: false,\n    requireApprovalToJoin: false,\n  },\n  adminControls: {\n    canMuteAll: true,\n    canMuteParticipant: true,\n    canRemoveParticipant: true,\n    canControlCamera: true,\n    canManageRoles: true,\n  },\n  blockedUsers: new Set<string>(),\n  spamDetection: new Map<string, { count: number, lastReset: number }>(),\n}\n\nexport const useVideoCallStore = create<VideoCallState>()(\n  devtools(\n    (set, get) => ({\n      ...initialState,\n\n      // Room actions\n      setRoomId: (roomId: string) => set({ roomId }),\n      setConnected: (isConnected: boolean) => set({ isConnected }),\n\n      // User actions\n      setCurrentUser: (currentUser: Participant) => set({ currentUser }),\n\n      addParticipant: (participant: Participant) => {\n        const participants = new Map(get().participants)\n        participants.set(participant.id, participant)\n        set({ participants })\n      },\n\n      removeParticipant: (participantId: string) => {\n        const participants = new Map(get().participants)\n        participants.delete(participantId)\n        set({ participants })\n      },\n\n      updateParticipant: (participantId: string, updates: Partial<Participant>) => {\n        const participants = new Map(get().participants)\n        const participant = participants.get(participantId)\n        if (participant) {\n          participants.set(participantId, { ...participant, ...updates })\n          set({ participants })\n        }\n      },\n\n      // Media actions\n      setLocalStream: (localStream: MediaStream | null) => set({ localStream }),\n\n      toggleAudio: () => {\n        const { isAudioMuted, localStream } = get()\n        if (localStream) {\n          localStream.getAudioTracks().forEach(track => {\n            track.enabled = isAudioMuted\n          })\n        }\n        set({ isAudioMuted: !isAudioMuted })\n      },\n\n      toggleVideo: () => {\n        const { isVideoMuted, localStream } = get()\n        if (localStream) {\n          localStream.getVideoTracks().forEach(track => {\n            track.enabled = isVideoMuted\n          })\n        }\n        set({ isVideoMuted: !isVideoMuted })\n      },\n\n      toggleScreenShare: () => {\n        set(state => ({ isScreenSharing: !state.isScreenSharing }))\n      },\n\n      // Chat actions\n      addMessage: (message: ChatMessage) => {\n        const { messages, securitySettings, spamDetection } = get()\n\n        // Anti-spam check\n        if (securitySettings.antiSpamEnabled) {\n          const now = Date.now()\n          const userSpam = spamDetection.get(message.userId) || { count: 0, lastReset: now }\n\n          // Reset count if more than a minute has passed\n          if (now - userSpam.lastReset > 60000) {\n            userSpam.count = 0\n            userSpam.lastReset = now\n          }\n\n          userSpam.count++\n          spamDetection.set(message.userId, userSpam)\n\n          // Mark as spam if exceeding limit\n          if (userSpam.count > securitySettings.maxMessagesPerMinute) {\n            message.isSpam = true\n          }\n        }\n\n        set({\n          messages: [...messages, message],\n          unreadCount: get().isChatOpen ? 0 : get().unreadCount + 1,\n          spamDetection: new Map(spamDetection)\n        })\n      },\n\n      clearUnreadCount: () => set({ unreadCount: 0 }),\n\n      // UI actions\n      toggleChat: () => {\n        const isChatOpen = !get().isChatOpen\n        set({\n          isChatOpen,\n          unreadCount: isChatOpen ? 0 : get().unreadCount\n        })\n      },\n\n      toggleSettings: () => set(state => ({ isSettingsOpen: !state.isSettingsOpen })),\n\n      // Admin actions\n      muteParticipant: (participantId: string) => {\n        get().updateParticipant(participantId, { isAudioMuted: true })\n      },\n\n      muteAllParticipants: () => {\n        const { participants } = get()\n        participants.forEach((_, id) => {\n          get().updateParticipant(id, { isAudioMuted: true })\n        })\n      },\n\n      unmuteAllParticipants: () => {\n        const { participants } = get()\n        participants.forEach((_, id) => {\n          get().updateParticipant(id, { isAudioMuted: false })\n        })\n      },\n\n      promoteToCoHost: (participantId: string) => {\n        get().updateParticipant(participantId, { role: 'co-host' })\n      },\n\n      demoteFromCoHost: (participantId: string) => {\n        get().updateParticipant(participantId, { role: 'participant' })\n      },\n\n      blockUser: (participantId: string) => {\n        const blockedUsers = new Set(get().blockedUsers)\n        blockedUsers.add(participantId)\n        set({ blockedUsers })\n        get().removeParticipant(participantId)\n      },\n\n      unblockUser: (participantId: string) => {\n        const blockedUsers = new Set(get().blockedUsers)\n        blockedUsers.delete(participantId)\n        set({ blockedUsers })\n      },\n\n      lockRoom: () => set({ roomLocked: true }),\n      unlockRoom: () => set({ roomLocked: false }),\n\n      updateSecuritySettings: (settings: Partial<SecuritySettings>) => {\n        set(state => ({\n          securitySettings: { ...state.securitySettings, ...settings }\n        }))\n      },\n\n      // Utility actions\n      reset: () => set(initialState),\n    }),\n    {\n      name: 'video-call-store',\n    }\n  )\n)\n\nexport default useVideoCallStore"], "names": ["Promise", "resolve", "then", "__webpack_require__", "bind", "SocketManager", "connect", "_this_socket", "socket", "connected", "io", "window", "location", "origin", "transports", "upgrade", "on", "console", "log", "id", "error", "disconnect", "joinRoom", "roomId", "userId", "userName", "emit", "leaveRoom", "sendSignal", "targetUserId", "signal", "sendChatMessage", "message", "timestamp", "Date", "toISOString", "event", "callback", "off", "getSocket", "isConnected", "socketManager", "RTC_CONFIG", "iceServers", "urls", "iceCandidatePoolSize", "bundlePolicy", "rtcpMuxPolicy", "VIDEO_CONSTRAINTS", "video", "width", "ideal", "max", "height", "frameRate", "facingMode", "audio", "echoCancellation", "noiseSuppression", "autoGainControl", "sampleRate", "SCREEN_SHARE_CONSTRAINTS", "RTCManager", "setupEventHandlers", "addEventListener", "cleanup", "getUserMedia", "constraints", "localStream", "navigator", "mediaDevices", "cameraStream", "videoTracks", "getVideoTracks", "length", "currentVideoDeviceId", "getSettings", "deviceId", "audioTracks", "getAudioTracks", "currentAudioDeviceId", "getDisplayMedia", "screenStream", "onended", "restoreVideoTrack", "replaceAudioTrack", "stream", "for<PERSON>ach", "track", "stop", "audioTrack", "addTrack", "updateAudioTracks", "videoTrack", "clone", "updateVideoTracks", "getTracks", "peerConnections", "pc", "sender", "getSenders", "find", "s", "kind", "replaceTrack", "switchCamera", "exact", "createPeerConnection", "RTCPeerConnection", "ontrack", "remoteStream", "streams", "onStreamCallback", "onicecandidate", "candidate", "type", "onconnectionstatechange", "concat", "connectionState", "removePeerConnection", "onUserDisconnectedCallback", "set", "createOffer", "get", "offer", "offerToReceiveAudio", "offerToReceiveVideo", "setLocalDescription", "createAnswer", "setRemoteDescription", "answer", "handleAnswer", "handleIceCandidate", "addIceCandidate", "close", "delete", "replaceVideoTrack", "newStream", "onStream", "onUserDisconnected", "setSendSignalCallback", "clear", "getLocalStream", "getPeerConnection", "constructor", "Map", "rtcManager", "VideoTile", "param", "participant", "isLocal", "isFeatured", "videoRef", "useRef", "useEffect", "current", "srcObject", "hasVideo", "isVideoMuted", "hasAudio", "isAudioMuted", "jsx_runtime", "jsxs", "div", "className", "jsx", "ref", "autoPlay", "playsInline", "muted", "span", "name", "char<PERSON>t", "toUpperCase", "p", "Crown", "Mic", "<PERSON><PERSON><PERSON><PERSON>", "Video", "VideoOff", "isScreenSharing", "Monitor", "VideoGrid", "currentUser", "participants", "useVideoCallStore", "participantsList", "Array", "from", "values", "totalParticipants", "getGridRows", "count", "map", "index", "SettingsModal", "cameras", "microphones", "isOpen", "onClose", "activeTab", "setActiveTab", "useState", "devices", "setDevices", "selectedCamera", "setSelectedCamera", "selectedMicrophone", "setSelectedMicrophone", "selectedSpeaker", "setSelectedSpeaker", "backgroundBlur", "setBackgroundBlur", "selectedBackground", "setSelectedBackground", "videoQuality", "setVideoQuality", "audioQuality", "setAudioQuality", "setNoiseSuppression", "setEchoCancellation", "setAutoGainControl", "encryptionEnabled", "setEncryptionEnabled", "antiSpamEnabled", "setAntiSpamEnabled", "showCameraDropdown", "setShowCameraDropdown", "showMicDropdown", "setShowMicDropdown", "loadDevices", "deviceList", "enumerateDevices", "device", "label", "slice", "defaultCamera", "d", "defaultMic", "defaultSpeaker", "handleCameraChange", "handleMicrophoneChange", "applyBackgroundEffect", "backgroundId", "applyVideoQuality", "quality", "filter", "h2", "Settings", "button", "onClick", "X", "icon", "Camera", "Image", "Shield", "tab", "h3", "c", "ChevronDown", "camera", "Check", "videoQualityOptions", "resolution", "option", "m", "mic", "h4", "input", "checked", "onChange", "e", "target", "backgroundOptions", "preview", "bg", "value", "placeholder", "select", "VideoControls", "isScreenSharingState", "setIsScreenSharing", "notification", "setNotification", "showSettingsModal", "setShowSettingsModal", "toggleAudio", "toggleVideo", "setLocalStream", "useCallback", "removeEventListener", "showNotification", "setTimeout", "handleToggleScreenShare", "handleStopScreenShare", "handleCameraSelect", "handleMicrophoneSelect", "title", "ChevronUp", "Headphones", "MonitorOff", "aria-label", "SettingsIcon", "Cha<PERSON>", "setMessage", "messagesEndRef", "messages", "toggleChat", "clearUnreadCount", "securitySettings", "blockUser", "isAdmin", "role", "scrollIntoView", "behavior", "handleBlockUser", "formatTime", "date", "toLocaleTimeString", "hour", "minute", "MessageCircle", "Send", "msg", "isBlocked", "isSpam", "form", "onSubmit", "preventDefault", "trim", "max<PERSON><PERSON><PERSON>", "autoComplete", "disabled", "ParticipantsList", "showAdminMenu", "setShowAdminMenu", "roomLocked", "muteParticipant", "muteAllParticipants", "removeParticipant", "promoteToCoHost", "demoteFromCoHost", "lockRoom", "unlockRoom", "allParticipants", "getStatusIcon", "icons", "push", "isHost", "handleAdminAction", "action", "participantId", "Users", "Lock", "Unlock", "isCurrentUser", "isHandRaised", "Hand", "MoreVertical", "ShieldOff", "UserMinus", "VideoCallRoom", "router", "useRouter", "isInitialized", "setIsInitialized", "showSettings", "setShowSettings", "isFullscreen", "setIsFullscreen", "setConnected", "addParticipant", "updateParticipant", "addMessage", "reset", "initializeCall", "handleUserJoined", "data", "joinedAt", "lastActivity", "handleUserLeft", "handleSignal", "fromUserId", "handleChatMessage", "Math", "random", "toString", "substring", "showLeaveConfirm", "setShowLeaveConfirm", "Fragment", "h1", "size", "meetingLink", "clipboard", "writeText", "Share", "Copy", "document", "fullscreenElement", "exitFullscreen", "documentElement", "requestFullscreen", "Minimize2", "Maximize2", "PhoneOff", "RoomPage", "params", "useParams", "setUserName", "isJoining", "setIsJoining", "hasJoined", "setHasJoined", "setRoomId", "setCurrentUser", "handleJoinRoom", "alert", "user", "localStorage", "setItem", "storedName", "getItem", "autoFocus", "initialState", "unreadCount", "messageHistory", "isChatOpen", "isSettingsOpen", "maxMessagesPerMinute", "allowScreenShare", "allowFileSharing", "requireApprovalToJoin", "adminControls", "canMuteAll", "canMuteParticipant", "canRemoveParticipant", "canControlCamera", "canManageRoles", "blockedUsers", "Set", "spamDetection", "create", "devtools", "updates", "enabled", "toggleScreenShare", "state", "now", "userSpam", "last<PERSON><PERSON>t", "toggleSettings", "_", "unmuteAllParticipants", "add", "unblockUser", "updateSecuritySettings", "settings"], "sourceRoot": ""}