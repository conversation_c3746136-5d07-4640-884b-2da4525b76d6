{"version": 3, "file": "static/chunks/app/room/[id]/page-3a8532ecb2244098.js", "mappings": "oFAAAA,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,8IC0GA,IAAME,EAAe,CACnBC,OAAQ,KACRC,YAAa,GACbC,WAAY,GACZC,YAAa,KACbC,aAAc,IAAIC,IAClBC,YAAa,KACbC,aAAc,GACdC,aAAc,GACdC,gBAAiB,GACjBC,SAAU,EAAE,CACZC,YAAa,EACbC,eAAgB,IAAIP,IACpBQ,WAAY,GACZC,eAAgB,GAChBC,iBAAkB,CAChBC,kBAAmB,GACnBC,gBAAiB,GACjBC,qBAAsB,GACtBC,iBAAkB,GAClBC,iBAAkB,GAClBC,sBAAuB,EACzB,EACAC,cAAe,CACbC,WAAY,GACZC,mBAAoB,GACpBC,qBAAsB,GACtBC,iBAAkB,GAClBC,eAAgB,EAClB,EACAC,aAAc,IAAIC,IAClBC,cAAe,IAAIzB,GACrB,EAEa0B,EAAoBC,CAAAA,EAAAA,EAAAA,EAAAA,IAC/BC,CAAAA,EAAAA,EAAAA,EAAAA,EACE,CAACC,EAAKC,IAAS,EACb,GAAGpC,CAAY,CAGfqC,UAAW,GAAoBF,EAAI,CAAElC,OAAAA,CAAO,GAC5CqC,aAAc,GAA0BH,EAAI,CAAEjC,YAAAA,CAAY,GAG1DqC,eAAgB,GAA8BJ,EAAI,CAAE/B,YAAAA,CAAY,GAEhEoC,eAAgB,IACd,IAAMnC,EAAe,IAAIC,IAAI8B,IAAM/B,YAAY,EAC/CA,EAAa8B,GAAG,CAACM,EAAYC,EAAE,CAAED,GACjCN,EAAI,CAAE9B,aAAAA,CAAa,EACrB,EAEAsC,kBAAmB,IACjB,IAAMtC,EAAe,IAAIC,IAAI8B,IAAM/B,YAAY,EAC/CA,EAAauC,MAAM,CAACC,GACpBV,EAAI,CAAE9B,aAAAA,CAAa,EACrB,EAEAyC,kBAAmB,CAACD,EAAuBE,KACzC,IAAM1C,EAAe,IAAIC,IAAI8B,IAAM/B,YAAY,EACzCoC,EAAcpC,EAAa+B,GAAG,CAACS,GACjCJ,IACFpC,EAAa8B,GAAG,CAACU,EAAe,CAAE,GAAGJ,CAAW,CAAE,GAAGM,CAAO,GAC5DZ,EAAI,CAAE9B,aAAAA,CAAa,GAEvB,EAGA2C,eAAgB,GAAqCb,EAAI,CAAE5B,YAAAA,CAAY,GAEvE0C,YAAa,KACX,GAAM,CAAEzC,aAAAA,CAAY,CAAED,YAAAA,CAAW,CAAE,CAAG6B,IAClC7B,GACFA,EAAY2C,cAAc,GAAGC,OAAO,CAACC,IACnCA,EAAMC,OAAO,CAAG7C,CAClB,GAEF2B,EAAI,CAAE3B,aAAc,CAACA,CAAa,EACpC,EAEA8C,YAAa,KACX,GAAM,CAAE7C,aAAAA,CAAY,CAAEF,YAAAA,CAAW,CAAE,CAAG6B,IAClC7B,GACFA,EAAYgD,cAAc,GAAGJ,OAAO,CAACC,IACnCA,EAAMC,OAAO,CAAG5C,CAClB,GAEF0B,EAAI,CAAE1B,aAAc,CAACA,CAAa,EACpC,EAEA+C,kBAAmB,KACjBrB,EAAIsB,GAAU,EAAE/C,gBAAiB,CAAC+C,EAAM/C,eAAe,CAAC,EAC1D,EAGAgD,WAAY,IACV,GAAM,CAAE/C,SAAAA,CAAQ,CAAEK,iBAAAA,CAAgB,CAAEe,cAAAA,CAAa,CAAE,CAAGK,IAGtD,GAAIpB,EAAiBE,eAAe,CAAE,CACpC,IAAMyC,EAAMC,KAAKD,GAAG,GACdE,EAAW9B,EAAcK,GAAG,CAAC0B,EAAQC,MAAM,GAAK,CAAEC,MAAO,EAAGC,UAAWN,CAAI,EAG7EA,EAAME,EAASI,SAAS,CAAG,MAC7BJ,EAASG,KAAK,CAAG,EACjBH,EAASI,SAAS,CAAGN,GAGvBE,EAASG,KAAK,GACdjC,EAAcI,GAAG,CAAC2B,EAAQC,MAAM,CAAEF,GAG9BA,EAASG,KAAK,CAAGhD,EAAiBG,oBAAoB,EACxD2C,CAAAA,EAAQI,MAAM,CAAG,GAErB,CAEA/B,EAAI,CACFxB,SAAU,IAAIA,EAAUmD,EAAQ,CAChClD,YAAawB,IAAMtB,UAAU,CAAG,EAAIsB,IAAMxB,WAAW,CAAG,EACxDmB,cAAe,IAAIzB,IAAIyB,EACzB,EACF,EAEAoC,iBAAkB,IAAMhC,EAAI,CAAEvB,YAAa,CAAE,GAG7CwD,WAAY,KACV,IAAMtD,EAAa,CAACsB,IAAMtB,UAAU,CACpCqB,EAAI,CACFrB,WAAAA,EACAF,YAAaE,EAAa,EAAIsB,IAAMxB,WAAW,EAEnD,EAEAyD,eAAgB,IAAMlC,EAAIsB,GAAU,EAAE1C,eAAgB,CAAC0C,EAAM1C,cAAc,CAAC,GAG5EuD,gBAAiB,IACflC,IAAMU,iBAAiB,CAACD,EAAe,CAAErC,aAAc,EAAK,EAC9D,EAEA+D,oBAAqB,KACnB,GAAM,CAAElE,aAAAA,CAAY,CAAE,CAAG+B,IACzB/B,EAAa8C,OAAO,CAAC,CAACqB,EAAG9B,KACvBN,IAAMU,iBAAiB,CAACJ,EAAI,CAAElC,aAAc,EAAK,EACnD,EACF,EAEAiE,sBAAuB,KACrB,GAAM,CAAEpE,aAAAA,CAAY,CAAE,CAAG+B,IACzB/B,EAAa8C,OAAO,CAAC,CAACqB,EAAG9B,KACvBN,IAAMU,iBAAiB,CAACJ,EAAI,CAAElC,aAAc,EAAM,EACpD,EACF,EAEAkE,gBAAiB,IACftC,IAAMU,iBAAiB,CAACD,EAAe,CAAE8B,KAAM,SAAU,EAC3D,EAEAC,iBAAkB,IAChBxC,IAAMU,iBAAiB,CAACD,EAAe,CAAE8B,KAAM,aAAc,EAC/D,EAEAE,UAAW,IACT,IAAMhD,EAAe,IAAIC,IAAIM,IAAMP,YAAY,EAC/CA,EAAaiD,GAAG,CAACjC,GACjBV,EAAI,CAAEN,aAAAA,CAAa,GACnBO,IAAMO,iBAAiB,CAACE,EAC1B,EAEAkC,YAAa,IACX,IAAMlD,EAAe,IAAIC,IAAIM,IAAMP,YAAY,EAC/CA,EAAae,MAAM,CAACC,GACpBV,EAAI,CAAEN,aAAAA,CAAa,EACrB,EAEAmD,SAAU,IAAM7C,EAAI,CAAEhC,WAAY,EAAK,GACvC8E,WAAY,IAAM9C,EAAI,CAAEhC,WAAY,EAAM,GAE1C+E,uBAAwB,IACtB/C,EAAIsB,GAAU,EACZzC,iBAAkB,CAAE,GAAGyC,EAAMzC,gBAAgB,CAAE,GAAGmE,CAAQ,CAC5D,GACF,EAGAC,MAAO,IAAMjD,EAAInC,EACnB,GACA,CACEqF,KAAM,kBACR,iBCxSJ,OAAMC,EAIJC,SAAU,KACJC,QAAJ,QAAIA,CAAAA,EAAA,IAAI,CAACC,MAAM,GAAXD,KAAA,IAAAA,EAAA,OAAAA,EAAaE,SAAS,IAE1B,IAAI,CAACD,MAAM,CAAGE,CAAAA,EAAAA,EAAAA,EAAAA,EACVC,OAAOC,QAAQ,CAACC,MAAM,CACG,CAC3BC,WAAY,CAAC,YAAa,UAAU,CACpCC,QAAS,EACX,GAEA,IAAI,CAACP,MAAM,CAACQ,EAAE,CAAC,UAAW,SACYT,EAApCU,QAAQC,GAAG,CAAC,8BAAwBX,CAAAA,EAAA,IAAI,CAACC,MAAM,GAAXD,KAAA,IAAAA,EAAA,OAAAA,EAAa9C,EAAE,CACrD,GAEA,IAAI,CAAC+C,MAAM,CAACQ,EAAE,CAAC,aAAc,KAC3BC,QAAQC,GAAG,CAAC,2BACd,GAEA,IAAI,CAACV,MAAM,CAACQ,EAAE,CAAC,gBAAiB,IAC9BC,QAAQE,KAAK,CAAC,oBAAqBA,EACrC,IAnBmC,IAAI,CAACX,MAAM,CAwBhDY,YAAa,CACP,IAAI,CAACZ,MAAM,GACb,IAAI,CAACA,MAAM,CAACY,UAAU,GACtB,IAAI,CAACZ,MAAM,CAAG,KAElB,CAEAa,SAASrG,CAAc,CAAE8D,CAAc,CAAEwC,CAAgB,CAAE,CACpD,IAAI,CAACd,MAAM,GAEhB,IAAI,CAACxF,MAAM,CAAGA,EACd,IAAI,CAACwF,MAAM,CAACe,IAAI,CAAC,YAAa,CAAEvG,OAAAA,EAAQ8D,OAAAA,EAAQwC,SAAAA,CAAS,GAC3D,CAEAE,WAAY,CACL,IAAI,CAAChB,MAAM,EAAK,IAAI,CAACxF,MAAM,GAEhC,IAAI,CAACwF,MAAM,CAACe,IAAI,CAAC,aAAc,CAAEvG,OAAQ,IAAI,CAACA,MAAM,GACpD,IAAI,CAACA,MAAM,CAAG,KAChB,CAEAyG,WAAWC,CAAoB,CAAEC,CAAW,CAAE,CACvC,IAAI,CAACnB,MAAM,EAAK,IAAI,CAACxF,MAAM,EAEhC,IAAI,CAACwF,MAAM,CAACe,IAAI,CAAC,SAAU,CACzBvG,OAAQ,IAAI,CAACA,MAAM,CACnB0G,aAAAA,EACAC,OAAAA,CACF,EACF,CAEAC,gBAAgB/C,CAAe,CAAEyC,CAAgB,CAAE,CAC5C,IAAI,CAACd,MAAM,EAAK,IAAI,CAACxF,MAAM,EAEhC,IAAI,CAACwF,MAAM,CAACe,IAAI,CAAC,eAAgB,CAC/BvG,OAAQ,IAAI,CAACA,MAAM,CACnB6D,QAAAA,EACAyC,SAAAA,EACAO,UAAW,IAAIlD,OAAOmD,WAAW,EACnC,EACF,CAEAd,GAAGe,CAAa,CAAEC,CAAkC,CAAE,CAC/C,IAAI,CAACxB,MAAM,EAEhB,IAAI,CAACA,MAAM,CAACQ,EAAE,CAACe,EAAOC,EACxB,CAEAC,IAAIF,CAAa,CAAEC,CAAmC,CAAE,CACjD,IAAI,CAACxB,MAAM,EAEhB,IAAI,CAACA,MAAM,CAACyB,GAAG,CAACF,EAAOC,EACzB,CAEAE,WAAY,CACV,OAAO,IAAI,CAAC1B,MAAM,CAGpBvF,aAAc,KACLsF,EAAP,MAAO,QAAAA,CAAAA,EAAA,IAAI,CAACC,MAAM,GAAXD,KAAA,IAAAA,EAAA,OAAAA,EAAaE,SAAS,GAAI,EACnC,oBAxFQD,MAAAA,CAAwB,UACxBxF,MAAAA,CAAwB,KAwFlC,CAEO,IAAMmH,EAAgB,IAAI9B,EC3F3B+B,EAA+B,CACnCC,WAAY,CACV,CAAEC,KAAM,8BAA+B,EACvC,CAAEA,KAAM,+BAAgC,EACzC,CACDC,qBAAsB,GACtBC,aAAc,aACdC,cAAe,SACjB,EAGaC,EAA4C,CACvDC,MAAO,CACLC,MAAO,CAAEC,MAAO,KAAMC,IAAK,IAAK,EAChCC,OAAQ,CAAEF,MAAO,IAAKC,IAAK,IAAK,EAChCE,UAAW,CAAEH,MAAO,GAAIC,IAAK,EAAG,EAChCG,WAAY,MACd,EACAC,MAAO,CACLC,iBAAkB,GAClBC,iBAAkB,GAClBC,gBAAiB,GACjBC,WAAY,IACd,CACF,EAEaC,EAA2B,CACtCZ,MAAO,CACLC,MAAO,CAAEC,MAAO,KAAMC,IAAK,IAAK,EAChCC,OAAQ,CAAEF,MAAO,KAAMC,IAAK,IAAK,EACjCE,UAAW,CAAEH,MAAO,GAAIC,IAAK,EAAG,CAClC,EACAI,MAAO,EACT,CAEO,OAAMM,EAcHC,oBAAqB,CAGzB9C,OAAO+C,gBAAgB,CAAC,eAAgB,KACtC,IAAI,CAACC,OAAO,EACd,EAEJ,CAEA,MAAMC,cAA4F,KAA/EC,EAAAA,UAAAA,MAAAA,CAAAA,GAAAA,KAAAA,IAAAA,SAAAA,CAAAA,EAAAA,CAAAA,SAAAA,CAAAA,EAAAA,CAAsCnB,EACvD,GAAI,CACF,IAAI,CAACpH,WAAW,CAAG,MAAMwI,UAAUC,YAAY,CAACH,YAAY,CAACC,GAC7D,IAAI,CAACG,YAAY,CAAG,IAAI,CAAC1I,WAAW,CAGpC,IAAM2I,EAAc,IAAI,CAAC3I,WAAW,CAACgD,cAAc,EAC/C2F,CAAAA,EAAYC,MAAM,CAAG,GACvB,KAAI,CAACC,oBAAoB,CAAGF,CAAW,CAAC,EAAE,CAACG,WAAW,GAAGC,QAAQ,EAAI,MAGvE,IAAMC,EAAc,IAAI,CAAChJ,WAAW,CAAC2C,cAAc,GAKnD,OAJIqG,EAAYJ,MAAM,CAAG,GACvB,KAAI,CAACK,oBAAoB,CAAGD,CAAW,CAAC,EAAE,CAACF,WAAW,GAAGC,QAAQ,EAAI,MAGhE,IAAI,CAAC/I,WAAW,CACvB,MAAO6F,EAAO,CAEd,MADAF,QAAQE,KAAK,CAAC,iCAAkCA,GAC1CA,CACR,CACF,CAGA,MAAMqD,iBAAsG,KAAtFX,EAAAA,UAAAA,MAAAA,CAAAA,GAAAA,KAAAA,IAAAA,SAAAA,CAAAA,EAAAA,CAAAA,SAAAA,CAAAA,EAAAA,CAAsCN,EAC1D,GAAI,CAQF,OAPA,IAAI,CAACkB,YAAY,CAAG,MAAMX,UAAUC,YAAY,CAACS,eAAe,CAACX,GAGjE,IAAI,CAACY,YAAY,CAACnG,cAAc,EAAE,CAAC,EAAE,CAACoG,OAAO,CAAG,KAC9C,IAAI,CAACC,iBAAiB,EACxB,EAEO,IAAI,CAACF,YAAY,CACxB,MAAOtD,EAAO,CAEd,MADAF,QAAQE,KAAK,CAAC,gCAAiCA,GACzCA,CACR,CACF,CAIAyD,kBAAkBC,CAAmB,CAAE,CACrC,GAAI,CAAC,IAAI,CAACvJ,WAAW,CAAE,OAGvB,IAAI,CAACA,WAAW,CAAC2C,cAAc,GAAGC,OAAO,CAACC,GAASA,EAAM2G,IAAI,IAG7D,IAAMC,EAAaF,EAAO5G,cAAc,EAAE,CAAC,EAAE,CACzC8G,GACF,IAAI,CAACzJ,WAAW,CAAC0J,QAAQ,CAACD,GAI5B,IAAI,CAACE,iBAAiB,EACxB,CAEAN,mBAAoB,CAClB,GAAI,CAAC,IAAI,CAACrJ,WAAW,EAAI,CAAC,IAAI,CAAC0I,YAAY,CAAE,OAG7C,IAAI,CAAC1I,WAAW,CAACgD,cAAc,GAAGJ,OAAO,CAACC,GAASA,EAAM2G,IAAI,IAG7D,IAAMI,EAAa,IAAI,CAAClB,YAAY,CAAC1F,cAAc,EAAE,CAAC,EAAE,CACpD4G,GACF,IAAI,CAAC5J,WAAW,CAAC0J,QAAQ,CAACE,EAAWC,KAAK,IAI5C,IAAI,CAACC,iBAAiB,GAGlB,IAAI,CAACX,YAAY,GACnB,IAAI,CAACA,YAAY,CAACY,SAAS,GAAGnH,OAAO,CAACC,GAASA,EAAM2G,IAAI,IACzD,IAAI,CAACL,YAAY,CAAG,KAExB,CAEQW,mBAAoB,CAC1B,GAAI,CAAC,IAAI,CAAC9J,WAAW,CAAE,OAEvB,IAAM4J,EAAa,IAAI,CAAC5J,WAAW,CAACgD,cAAc,EAAE,CAAC,EAAE,CAClD4G,GAEL,IAAI,CAACI,eAAe,CAACpH,OAAO,CAACqH,IAC3B,IAAMC,EAASD,EAAGE,UAAU,GAAGC,IAAI,CAACC,QAAKA,QAAAA,CAAAA,OAAAA,CAAAA,EAAAA,EAAExH,KAAK,GAAPwH,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAASC,IAAI,IAAK,UACvDJ,GACFA,EAAOK,YAAY,CAACX,EAExB,EACF,CAEQD,mBAAoB,CAC1B,GAAI,CAAC,IAAI,CAAC3J,WAAW,CAAE,OAEvB,IAAMyJ,EAAa,IAAI,CAACzJ,WAAW,CAAC2C,cAAc,EAAE,CAAC,EAAE,CAClD8G,GAEL,IAAI,CAACO,eAAe,CAACpH,OAAO,CAACqH,IAC3B,IAAMC,EAASD,EAAGE,UAAU,GAAGC,IAAI,CAACC,QAAKA,QAAAA,CAAAA,OAAAA,CAAAA,EAAAA,EAAExH,KAAK,GAAPwH,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAASC,IAAI,IAAK,UACvDJ,GACFA,EAAOK,YAAY,CAACd,EAExB,EACF,CAEA,MAAMe,aAAazB,CAAgB,CAAE,CACnC,GAAK,IAAI,CAAC/I,WAAW,CAErB,GAAI,CACF,IAAMuJ,EAAS,MAAMf,UAAUC,YAAY,CAACH,YAAY,CAAC,CACvDjB,MAAO,CAAE0B,SAAU,CAAE0B,MAAO1B,CAAS,CAAE,EACvCnB,MAAO,KAAI,CAACqB,oBAAoB,EAAG,CAAEF,SAAU,CAAE0B,MAAO,IAAI,CAACxB,oBAAoB,CAAG,CACtF,GAaA,OAVA,IAAI,CAACjJ,WAAW,CAAC+J,SAAS,GAAGnH,OAAO,CAACC,GAASA,EAAM2G,IAAI,IAGxD,IAAI,CAACxJ,WAAW,CAAGuJ,EACnB,IAAI,CAACb,YAAY,CAAGa,EACpB,IAAI,CAACV,oBAAoB,CAAGE,EAG5B,IAAI,CAACe,iBAAiB,GAEfP,CACT,CAAE,MAAO1D,EAAO,CAEd,MADAF,QAAQE,KAAK,CAAC,0BAA2BA,GACnC,MAAU,0BAClB,CACF,CAEA6E,qBAAqBlH,CAAc,CAAqB,CACtD,IAAMyG,EAAK,IAAIU,kBAAkB7D,GAyCjC,OAtCI,IAAI,CAAC9G,WAAW,EAClB,IAAI,CAACA,WAAW,CAAC+J,SAAS,GAAGnH,OAAO,CAACC,IACnCoH,EAAGP,QAAQ,CAAC7G,EAAO,IAAI,CAAC7C,WAAW,CACrC,GAIFiK,EAAGW,OAAO,CAAG,IACX,GAAM,CAACC,EAAa,CAAGpE,EAAMqE,OAAO,CAChC,IAAI,CAACC,gBAAgB,EACvB,IAAI,CAACA,gBAAgB,CAACvH,EAAQqH,EAElC,EAGAZ,EAAGe,cAAc,CAAG,IACdvE,EAAMwE,SAAS,EAEjB,IAAI,CAAC9E,UAAU,CAAC3C,EAAQ,CACtB0H,KAAM,gBACND,UAAWxE,EAAMwE,SAAS,EAGhC,EAGAhB,EAAGkB,uBAAuB,CAAG,KAC3BxF,QAAQC,GAAG,CAAC,wBAA+BwF,MAAA,CAAP5H,EAAO,KAAIyG,EAAGoB,eAAe,EAE7DpB,CAAAA,iBAAAA,EAAGoB,eAAe,EAAuBpB,WAAAA,EAAGoB,eAAe,IAC7D,IAAI,CAACC,oBAAoB,CAAC9H,GACtB,IAAI,CAAC+H,0BAA0B,EACjC,IAAI,CAACA,0BAA0B,CAAC/H,GAGtC,EAEA,IAAI,CAACwG,eAAe,CAACpI,GAAG,CAAC4B,EAAQyG,GAC1BA,CACT,CAEA,MAAMuB,YAAYhI,CAAc,CAAsC,CACpE,IAAMyG,EAAK,IAAI,CAACD,eAAe,CAACnI,GAAG,CAAC2B,IAAW,IAAI,CAACkH,oBAAoB,CAAClH,GAEnEiI,EAAQ,MAAMxB,EAAGuB,WAAW,CAAC,CACjCE,oBAAqB,GACrBC,oBAAqB,EACvB,GAGA,OADA,MAAM1B,EAAG2B,mBAAmB,CAACH,GACtBA,CACT,CAEA,MAAMI,aAAarI,CAAc,CAAEiI,CAAgC,CAAsC,CACvG,IAAMxB,EAAK,IAAI,CAACD,eAAe,CAACnI,GAAG,CAAC2B,IAAW,IAAI,CAACkH,oBAAoB,CAAClH,EAEzE,OAAMyG,EAAG6B,oBAAoB,CAACL,GAC9B,IAAMM,EAAS,MAAM9B,EAAG4B,YAAY,GAGpC,OAFA,MAAM5B,EAAG2B,mBAAmB,CAACG,GAEtBA,CACT,CAEA,MAAMC,aAAaxI,CAAc,CAAEuI,CAAiC,CAAiB,CACnF,IAAM9B,EAAK,IAAI,CAACD,eAAe,CAACnI,GAAG,CAAC2B,GAChCyG,GACF,MAAMA,EAAG6B,oBAAoB,CAACC,EAElC,CAEA,MAAME,mBAAmBzI,CAAc,CAAEyH,CAA8B,CAAiB,CACtF,IAAMhB,EAAK,IAAI,CAACD,eAAe,CAACnI,GAAG,CAAC2B,GAChCyG,GACF,MAAMA,EAAGiC,eAAe,CAACjB,EAE7B,CAEAK,qBAAqB9H,CAAc,CAAQ,CACzC,IAAMyG,EAAK,IAAI,CAACD,eAAe,CAACnI,GAAG,CAAC2B,GAChCyG,IACFA,EAAGkC,KAAK,GACR,IAAI,CAACnC,eAAe,CAAC3H,MAAM,CAACmB,GAEhC,CAEA4I,kBAAkBC,CAAsB,CAAQ,CAC9C,IAAMzC,EAAayC,EAAUrJ,cAAc,EAAE,CAAC,EAAE,CAEhD,IAAI,CAACgH,eAAe,CAACpH,OAAO,CAAC,MAAOqH,IAClC,IAAMC,EAASD,EAAGE,UAAU,GAAGC,IAAI,CAACC,GAClCA,EAAExH,KAAK,EAAIwH,UAAAA,EAAExH,KAAK,CAACyH,IAAI,EAGrBJ,GAAUN,GACZ,MAAMM,EAAOK,YAAY,CAACX,EAE9B,EACF,CAEA0C,SAAS5F,CAAuD,CAAQ,CACtE,IAAI,CAACqE,gBAAgB,CAAGrE,CAC1B,CAEA6F,mBAAmB7F,CAAkC,CAAQ,CAC3D,IAAI,CAAC6E,0BAA0B,CAAG7E,CACpC,CAEQP,WAAW3C,CAAc,CAAE6C,CAAW,CAAQ,CAGtD,CAEAmG,sBAAsB9F,CAA+C,CAAQ,CAC3E,IAAI,CAACP,UAAU,CAAGO,CACpB,CAEA2B,SAAgB,CAEd,IAAI,CAAC2B,eAAe,CAACpH,OAAO,CAACqH,GAAMA,EAAGkC,KAAK,IAC3C,IAAI,CAACnC,eAAe,CAACyC,KAAK,GAGtB,IAAI,CAACzM,WAAW,GAClB,IAAI,CAACA,WAAW,CAAC+J,SAAS,GAAGnH,OAAO,CAACC,GAASA,EAAM2G,IAAI,IACxD,IAAI,CAACxJ,WAAW,CAAG,KAEvB,CAEA0M,gBAAqC,CACnC,OAAO,IAAI,CAAC1M,WAAW,CAGzB2M,kBAAkBnJ,CAAc,CAAiC,CAC/D,OAAO,IAAI,CAACwG,eAAe,CAACnI,GAAG,CAAC2B,EAClC,CA9RAoJ,aAAc,MATN5C,eAAAA,CAAkD,IAAIjK,SACtDC,WAAAA,CAAkC,UAClCmJ,YAAAA,CAAmC,UACnCT,YAAAA,CAAmC,UACnCG,oBAAAA,CAAsC,UACtCI,oBAAAA,CAAsC,KAK5C,IAAI,CAACd,kBAAkB,EACzB,CA6RF,CAEO,IAAM0E,EAAa,IAAI3E,iECpUvB,SAAS4E,EAAUC,CAAoE,KAApE,CAAE7K,YAAAA,CAAW,CAAEqH,OAAAA,CAAM,CAAEyD,QAAAA,CAAO,CAAEC,WAAAA,EAAa,EAAK,CAAkB,CAApEF,EAClBG,EAAWC,CAAAA,EAAAA,EAAAA,MAAAA,EAAyB,MAE1CC,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,KACJF,EAASG,OAAO,EAAI9D,GACtB2D,CAAAA,EAASG,OAAO,CAACC,SAAS,CAAG/D,CAAAA,CAEjC,EAAG,CAACA,EAAO,EAEX,IAAMgE,EAAWhE,GAAUA,EAAOvG,cAAc,GAAG4F,MAAM,CAAG,GAAK,CAAC1G,EAAYhC,YAAY,CACpFsN,EAAWjE,GAAUA,EAAO5G,cAAc,GAAGiG,MAAM,CAAG,GAAK,CAAC1G,EAAYjC,YAAY,CAO1F,MACE,GAAAwN,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAW,4CAAqDxC,MAAA,CANtD6B,EAAa,gBAAkB,6CAO3CM,EACC,GAAAE,EAAAI,GAAA,EAACxG,QAAAA,CACCyG,IAAKZ,EACLa,SAAQ,GACRC,YAAW,GACXC,MAAOjB,EACPY,UAAU,0CAGZ,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,iHACb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,wBACb,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAW,GAAcxC,MAAA,CAjBrB6B,EAAa,YAAc,YAiBN,+HAC5B,GAAAQ,EAAAI,GAAA,EAACK,OAAAA,CAAKN,UAAW,cAAuBxC,MAAA,CAjBnC6B,EAAa,UAAY,UAiBU,uBACrC/K,EAAY4C,IAAI,CAACqJ,MAAM,CAAC,GAAGC,WAAW,OAG3C,GAAAX,EAAAI,GAAA,EAACQ,IAAAA,CAAET,UAAW,cAAuBxC,MAAA,CApB9B6B,EAAa,UAAY,UAoBK,yBAAgB/K,EAAY4C,IAAI,GACrE,GAAA2I,EAAAI,GAAA,EAACQ,IAAAA,CAAET,UAAU,iCAAwB,uBAM3C,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,+EACb,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,2CACb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,oCACZZ,GAAW,GAAAS,EAAAI,GAAA,EAACS,EAAAA,CAAKA,CAAAA,CAACV,UAAU,4BAC7B,GAAAH,EAAAI,GAAA,EAACK,OAAAA,CAAKN,UAAU,0CACbZ,EAAU,MAAQ9K,EAAY4C,IAAI,QAKzC,GAAA2I,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,wCAEb,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAW,oBAAmExC,MAAA,CAA/CoC,EAAW,kBAAoB,gBAAgB,8BAChFA,EACC,GAAAC,EAAAI,GAAA,EAACU,EAAAA,CAAGA,CAAAA,CAACX,UAAU,uBAEf,GAAAH,EAAAI,GAAA,EAACW,EAAAA,CAAMA,CAAAA,CAACZ,UAAU,yBAKtB,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAW,oBAAmExC,MAAA,CAA/CmC,EAAW,kBAAoB,gBAAgB,8BAChFA,EACC,GAAAE,EAAAI,GAAA,EAACY,EAAAA,CAAKA,CAAAA,CAACb,UAAU,uBAEjB,GAAAH,EAAAI,GAAA,EAACa,EAAAA,CAAQA,CAAAA,CAACd,UAAU,yBAKvB1L,EAAY/B,eAAe,EAC1B,GAAAsN,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,4DACb,GAAAH,EAAAI,GAAA,EAACc,EAAAA,CAAOA,CAAAA,CAACf,UAAU,+BAO1BZ,GACC,GAAAS,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,iCACb,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,2CACb,GAAAH,EAAAC,IAAA,EAACQ,OAAAA,CAAKN,UAAU,mEACd,GAAAH,EAAAI,GAAA,EAACS,EAAAA,CAAKA,CAAAA,CAACV,UAAU,4BAA4B,cAQrD,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,kCACb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,6BACb,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,wCACf,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,wCACf,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,+CAKzB,CC9GO,SAASgB,IACd,GAAM,CAAE/O,YAAAA,CAAW,CAAEC,aAAAA,CAAY,CAAEE,YAAAA,CAAW,CAAE,CAAGyB,IAE7CoN,EAAmBC,MAAMC,IAAI,CAACjP,EAAakP,MAAM,IACjDC,EAAoBJ,EAAiBjG,MAAM,CAAG,EAmBpD,MACE,GAAA6E,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,sBACb,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,4CACb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAW,4CAGZsB,MAAAA,CArBR,IAoBqBD,GAnBjBxL,IAmBiBwL,EApBG,cAoBHA,GAlBR,GACTxL,GAAS,EADU,cAEhB,cAgBiC,gBACD2H,MAAA,CAbvC,IAaoB6D,EAbI,cACV,IAYMA,GAXhBxL,GAAS,EADW,cAGjB,cASgC,sDAIhC5D,GACC,GAAA4N,EAAAI,GAAA,EAACf,EAASA,CAER5K,YAAarC,EACb0J,OAAQvJ,EACRgN,QAAS,GACTC,WAAYgC,IAAAA,GAJPpP,EAAYsC,EAAE,EAStB0M,EAAiBM,GAAG,CAAC,CAACjN,EAAakN,IAClC,GAAA3B,EAAAI,GAAA,EAACf,EAASA,CAER5K,YAAaA,EACbqH,OAAQrH,EAAYqH,MAAM,CAC1ByD,QAAS,GACTC,WAAYgC,IAAAA,GAA2BG,IAAAA,GAJlClN,EAAYC,EAAE,SAWjC,yEChCO,SAASkN,EAActC,CAAuC,MAsLxCuC,EAuDAC,KA7OC,CAAEC,OAAAA,CAAM,CAAEC,QAAAA,CAAO,CAAsB,CAAvC1C,EACtB,CAAC2C,EAAWC,EAAa,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,SACrC,CAACC,EAASC,EAAW,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,EAAwB,EAAE,EAClD,CAACG,EAAgBC,EAAkB,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IAC/C,CAACK,EAAoBC,EAAsB,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IACvD,CAACO,EAAiBC,EAAmB,CAAGR,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IACjD,CAACS,EAAgBC,EAAkB,CAAGV,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IAC/C,CAACW,EAAoBC,EAAsB,CAAGZ,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,QACvD,CAACa,EAAcC,EAAgB,CAAGd,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,MAC3C,CAACe,EAAcC,EAAgB,CAAGhB,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,QAC3C,CAAC9H,EAAkB+I,EAAoB,CAAGjB,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IACnD,CAAC/H,EAAkBiJ,EAAoB,CAAGlB,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IACnD,CAAC7H,EAAiBgJ,EAAmB,CAAGnB,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IACjD,CAAClP,EAAmBsQ,EAAqB,CAAGpB,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IACrD,CAACjP,EAAiBsQ,EAAmB,CAAGrB,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IACjD,CAACsB,EAAoBC,EAAsB,CAAGvB,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IACvD,CAACwB,EAAiBC,EAAmB,CAAGzB,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IAEjD,CAAE/P,YAAAA,CAAW,CAAE,CAAG4B,IAqBxB2L,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,KACR,IAAMkE,EAAc,UAClB,GAAI,CAEF,IAAM7I,EAA8B8I,CADjB,MAAM/I,UAAUC,YAAY,CAAC+I,gBAAgB,IACjBrC,GAAG,CAACsC,GAAW,EAC5D1I,SAAU0I,EAAO1I,QAAQ,CACzB2I,MAAOD,EAAOC,KAAK,EAAI,GAAkBD,MAAAA,CAAfA,EAAOnH,IAAI,CAAC,KAA+Bc,MAAA,CAA5BqG,EAAO1I,QAAQ,CAAC4I,KAAK,CAAC,EAAG,IAClErH,KAAMmH,EAAOnH,IAAI,CACnB,GACAwF,EAAWrH,GAGX,IAAMmJ,EAAgBnJ,EAAa2B,IAAI,CAACyH,GAAKA,eAAAA,EAAEvH,IAAI,EAC7CwH,EAAarJ,EAAa2B,IAAI,CAACyH,GAAKA,eAAAA,EAAEvH,IAAI,EAC1CyH,EAAiBtJ,EAAa2B,IAAI,CAACyH,GAAKA,gBAAAA,EAAEvH,IAAI,EAEhDsH,GAAe5B,EAAkB4B,EAAc7I,QAAQ,EACvD+I,GAAY5B,EAAsB4B,EAAW/I,QAAQ,EACrDgJ,GAAgB3B,EAAmB2B,EAAehJ,QAAQ,CAChE,CAAE,MAAOlD,EAAO,CACdF,QAAQE,KAAK,CAAC,yBAA0BA,EAC1C,CACF,EAEI2J,GACF8B,GAEJ,EAAG,CAAC9B,EAAO,EAEX,IAAMwC,EAAqB,MAAOjJ,IAChC,GAAI,CACFiH,EAAkBjH,GASlB,IAAMQ,EAAS,MAAMf,UAAUC,YAAY,CAACH,YAAY,CARpC,CAClBjB,MAAO,CACL0B,SAAU,CAAE0B,MAAO1B,CAAS,EAC5BzB,MAAO,CAAEC,MAAO,IAAK,EACrBE,OAAQ,CAAEF,MAAO,GAAI,CACvB,EACAK,MAAO,EACT,GAEAiF,EAAWT,iBAAiB,CAAC7C,GAC7B4H,EAAsB,GACxB,CAAE,MAAOtL,EAAO,CACdF,QAAQE,KAAK,CAAC,yBAA0BA,EAC1C,CACF,EAEMoM,EAAyB,MAAOlJ,IACpC,GAAI,CACFmH,EAAsBnH,GAUP,MAAMP,UAAUC,YAAY,CAACH,YAAY,CATpC,CAClBjB,MAAO,GACPO,MAAO,CACLmB,SAAU,CAAE0B,MAAO1B,CAAS,EAC5BlB,iBAAAA,EACAC,iBAAAA,EACAC,gBAAAA,CACF,CACF,GAGAsJ,EAAmB,GACrB,CAAE,MAAOxL,EAAO,CACdF,QAAQE,KAAK,CAAC,6BAA8BA,EAC9C,CACF,EAEMqM,EAAwB,IAC5B1B,EAAsB2B,EAGxB,EAEMC,GAAoB,IACxB1B,EAAgB2B,EAElB,EAEA,GAAI,CAAC7C,EAAQ,OAAO,KAEpB,IAAMF,GAAUO,EAAQyC,MAAM,CAACT,GAAKA,eAAAA,EAAEvH,IAAI,EACpCiF,GAAcM,EAAQyC,MAAM,CAACT,GAAKA,eAAAA,EAAEvH,IAAI,EAG9C,OAFiBuF,EAAQyC,MAAM,CAACT,GAAKA,gBAAAA,EAAEvH,IAAI,EAGzC,GAAAmD,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,gGACb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,gEAEb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,2EACb,GAAAH,EAAAC,IAAA,EAAC6E,KAAAA,CAAG3E,UAAU,kEACZ,GAAAH,EAAAI,GAAA,EAAC2E,EAAAA,CAAQA,CAAAA,CAAC5E,UAAU,YAAY,sBAGlC,GAAAH,EAAAI,GAAA,EAAC4E,SAAAA,CACCC,QAASjD,EACT7B,UAAU,6DAEV,GAAAH,EAAAI,GAAA,EAAC8E,EAAAA,CAACA,CAAAA,CAAC/E,UAAU,iBAIjB,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,2BAEb,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,6CACb,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,qBACZ,CACC,CAAEzL,GAAI,QAAS2C,KAAM,QAAS8N,KAAMC,EAAAA,CAAMA,EAC1C,CAAE1Q,GAAI,QAAS2C,KAAM,QAAS8N,KAAMrE,EAAAA,CAAGA,EACvC,CAAEpM,GAAI,aAAc2C,KAAM,aAAc8N,KAAME,EAAAA,CAAKA,EACnD,CAAE3Q,GAAI,WAAY2C,KAAM,WAAY8N,KAAMG,EAAAA,CAAMA,EAChD,CAAE5Q,GAAI,UAAW2C,KAAM,UAAW8N,KAAMJ,EAAAA,CAAQA,EACjD,CAACrD,GAAG,CAAC6D,GACJ,GAAAvF,EAAAC,IAAA,EAAC+E,SAAAA,CAECC,QAAS,IAAM/C,EAAaqD,EAAI7Q,EAAE,EAClCyL,UAAW,gEAIVxC,MAAA,CAHCsE,IAAcsD,EAAI7Q,EAAE,CAChB,8BACA,8DAGN,GAAAsL,EAAAI,GAAA,EAACmF,EAAIJ,IAAI,EAAChF,UAAU,YACnBoF,EAAIlO,IAAI,GATJkO,EAAI7Q,EAAE,OAgBnB,GAAAsL,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,uCACZ8B,UAAAA,GACC,GAAAjC,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,sBACb,GAAAH,EAAAI,GAAA,EAACoF,KAAAA,CAAGrF,UAAU,iDAAwC,mBAGtD,GAAAH,EAAAC,IAAA,EAACC,MAAAA,WACC,GAAAF,EAAAI,GAAA,EAAC6D,QAAAA,CAAM9D,UAAU,qDAA4C,WAC7D,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,qBACb,GAAAH,EAAAC,IAAA,EAAC+E,SAAAA,CACCC,QAAS,IAAMvB,EAAsB,CAACD,GACtCtD,UAAU,iEAEV,GAAAH,EAAAI,GAAA,EAACK,OAAAA,UAAMoB,CAAAA,OAAAA,CAAAA,EAAAA,GAAQlF,IAAI,CAAC8I,GAAKA,EAAEnK,QAAQ,GAAKgH,EAAAA,GAAjCT,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAAkDoC,KAAK,GAAI,kBAClE,GAAAjE,EAAAI,GAAA,EAACsF,EAAAA,CAAWA,CAAAA,CAACvF,UAAU,eAExBsD,GACC,GAAAzD,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,mGACZ0B,GAAQH,GAAG,CAACiE,GACX,GAAA3F,EAAAC,IAAA,EAAC+E,SAAAA,CAECC,QAAS,IAAMV,EAAmBoB,EAAOrK,QAAQ,EACjD6E,UAAU,gGAETwF,EAAO1B,KAAK,CACZ3B,IAAmBqD,EAAOrK,QAAQ,EAAI,GAAA0E,EAAAI,GAAA,EAACwF,EAAAA,CAAKA,CAAAA,CAACzF,UAAU,cALnDwF,EAAOrK,QAAQ,WAchC,GAAA0E,EAAAC,IAAA,EAACC,MAAAA,WACC,GAAAF,EAAAI,GAAA,EAAC6D,QAAAA,CAAM9D,UAAU,qDAA4C,kBAC7D,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,kCACZ0F,CA9KjB,CAAEnR,GAAI,KAAM2C,KAAM,YAAayO,WAAY,SAAU,EACrD,CAAEpR,GAAI,KAAM2C,KAAM,YAAayO,WAAY,UAAW,EACtD,CAAEpR,GAAI,MAAO2C,KAAM,kBAAmByO,WAAY,WAAY,EAC9D,CAAEpR,GAAI,KAAM2C,KAAM,aAAcyO,WAAY,WAAY,EACzD,CA0KsCpE,GAAG,CAACqE,GACvB,GAAA/F,EAAAC,IAAA,EAAC+E,SAAAA,CAECC,QAAS,IAAMN,GAAkBoB,EAAOrR,EAAE,EAC1CyL,UAAW,wCAIVxC,MAAA,CAHCqF,IAAiB+C,EAAOrR,EAAE,CACtB,gDACA,yEAGN,GAAAsL,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,uBAAe4F,EAAO1O,IAAI,GACzC,GAAA2I,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,8BAAsB4F,EAAOD,UAAU,KATjDC,EAAOrR,EAAE,WAiBzBuN,UAAAA,GACC,GAAAjC,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,sBACb,GAAAH,EAAAI,GAAA,EAACoF,KAAAA,CAAGrF,UAAU,iDAAwC,mBAGtD,GAAAH,EAAAC,IAAA,EAACC,MAAAA,WACC,GAAAF,EAAAI,GAAA,EAAC6D,QAAAA,CAAM9D,UAAU,qDAA4C,eAC7D,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,qBACb,GAAAH,EAAAC,IAAA,EAAC+E,SAAAA,CACCC,QAAS,IAAMrB,EAAmB,CAACD,GACnCxD,UAAU,iEAEV,GAAAH,EAAAI,GAAA,EAACK,OAAAA,UAAMqB,CAAAA,OAAAA,CAAAA,EAAAA,GAAYnF,IAAI,CAACqJ,GAAKA,EAAE1K,QAAQ,GAAKkH,EAAAA,GAArCV,KAAAA,IAAAA,EAAAA,KAAAA,EAAAA,EAA0DmC,KAAK,GAAI,sBAC1E,GAAAjE,EAAAI,GAAA,EAACsF,EAAAA,CAAWA,CAAAA,CAACvF,UAAU,eAExBwD,GACC,GAAA3D,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,mGACZ2B,GAAYJ,GAAG,CAACuE,GACf,GAAAjG,EAAAC,IAAA,EAAC+E,SAAAA,CAECC,QAAS,IAAMT,EAAuByB,EAAI3K,QAAQ,EAClD6E,UAAU,gGAET8F,EAAIhC,KAAK,CACTzB,IAAuByD,EAAI3K,QAAQ,EAAI,GAAA0E,EAAAI,GAAA,EAACwF,EAAAA,CAAKA,CAAAA,CAACzF,UAAU,cALpD8F,EAAI3K,QAAQ,WAc7B,GAAA0E,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,sBACb,GAAAH,EAAAI,GAAA,EAAC8F,KAAAA,CAAG/F,UAAU,kCAAyB,sBAEvC,GAAAH,EAAAC,IAAA,EAACgE,QAAAA,CAAM9D,UAAU,8CACf,GAAAH,EAAAI,GAAA,EAACK,OAAAA,CAAKN,UAAU,sBAAa,sBAC7B,GAAAH,EAAAI,GAAA,EAAC+F,QAAAA,CACC1I,KAAK,WACL2I,QAAS/L,EACTgM,SAAU,GAAOjD,EAAoBkD,EAAEC,MAAM,CAACH,OAAO,EACrDjG,UAAU,mDAId,GAAAH,EAAAC,IAAA,EAACgE,QAAAA,CAAM9D,UAAU,8CACf,GAAAH,EAAAI,GAAA,EAACK,OAAAA,CAAKN,UAAU,sBAAa,sBAC7B,GAAAH,EAAAI,GAAA,EAAC+F,QAAAA,CACC1I,KAAK,WACL2I,QAAShM,EACTiM,SAAU,GAAOhD,EAAoBiD,EAAEC,MAAM,CAACH,OAAO,EACrDjG,UAAU,mDAId,GAAAH,EAAAC,IAAA,EAACgE,QAAAA,CAAM9D,UAAU,8CACf,GAAAH,EAAAI,GAAA,EAACK,OAAAA,CAAKN,UAAU,sBAAa,sBAC7B,GAAAH,EAAAI,GAAA,EAAC+F,QAAAA,CACC1I,KAAK,WACL2I,QAAS9L,EACT+L,SAAU,GAAO/C,EAAmBgD,EAAEC,MAAM,CAACH,OAAO,EACpDjG,UAAU,yDAOnB8B,eAAAA,GACC,GAAAjC,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,sBACb,GAAAH,EAAAI,GAAA,EAACoF,KAAAA,CAAGrF,UAAU,iDAAwC,uBAEtD,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,kCACZqG,CArRf,CAAE9R,GAAI,OAAQ2C,KAAM,OAAQoP,QAAS,IAAK,EAC1C,CAAE/R,GAAI,OAAQ2C,KAAM,kBAAmBoP,QAAS,IAAK,EACrD,CAAE/R,GAAI,SAAU2C,KAAM,gBAAiBoP,QAAS,yBAA0B,EAC1E,CAAE/R,GAAI,SAAU2C,KAAM,eAAgBoP,QAAS,yBAA0B,EACzE,CAAE/R,GAAI,WAAY2C,KAAM,gBAAiBoP,QAAS,2BAA4B,EAC9E,CAAE/R,GAAI,WAAY2C,KAAM,kBAAmBoP,QAAS,2BAA4B,EACjF,CA+QkC/E,GAAG,CAACgF,GACrB,GAAA1G,EAAAC,IAAA,EAAC+E,SAAAA,CAECC,QAAS,IAAMR,EAAsBiC,EAAGhS,EAAE,EAC1CyL,UAAW,0CAIVxC,MAAA,CAHCmF,IAAuB4D,EAAGhS,EAAE,CACxB,qCACA,+DAGN,GAAAsL,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,oHACZuG,SAAAA,EAAGhS,EAAE,CAAc,GAAAsL,EAAAI,GAAA,EAACc,EAAAA,CAAOA,CAAAA,CAACf,UAAU,uBAA0B,GAAAH,EAAAI,GAAA,EAACiF,EAAAA,CAAKA,CAAAA,CAAClF,UAAU,yBAEpF,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,0CAAkCuG,EAAGrP,IAAI,KAXnDqP,EAAGhS,EAAE,QAkBnBuN,aAAAA,GACC,GAAAjC,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,sBACb,GAAAH,EAAAI,GAAA,EAACoF,KAAAA,CAAGrF,UAAU,iDAAwC,sBAEtD,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,sBACb,GAAAH,EAAAC,IAAA,EAACgE,QAAAA,CAAM9D,UAAU,8CACf,GAAAH,EAAAC,IAAA,EAACC,MAAAA,WACC,GAAAF,EAAAI,GAAA,EAACK,OAAAA,CAAKN,UAAU,kCAAyB,0BACzC,GAAAH,EAAAI,GAAA,EAACQ,IAAAA,CAAET,UAAU,iCAAwB,2CAEvC,GAAAH,EAAAI,GAAA,EAAC+F,QAAAA,CACC1I,KAAK,WACL2I,QAASnT,EACToT,SAAU,GAAO9C,EAAqB+C,EAAEC,MAAM,CAACH,OAAO,EACtDjG,UAAU,mDAId,GAAAH,EAAAC,IAAA,EAACgE,QAAAA,CAAM9D,UAAU,8CACf,GAAAH,EAAAC,IAAA,EAACC,MAAAA,WACC,GAAAF,EAAAI,GAAA,EAACK,OAAAA,CAAKN,UAAU,kCAAyB,yBACzC,GAAAH,EAAAI,GAAA,EAACQ,IAAAA,CAAET,UAAU,iCAAwB,wCAEvC,GAAAH,EAAAI,GAAA,EAAC+F,QAAAA,CACC1I,KAAK,WACL2I,QAASlT,EACTmT,SAAU,GAAO7C,EAAmB8C,EAAEC,MAAM,CAACH,OAAO,EACpDjG,UAAU,yDAOnB8B,YAAAA,GACC,GAAAjC,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,sBACb,GAAAH,EAAAI,GAAA,EAACoF,KAAAA,CAAGrF,UAAU,iDAAwC,qBAEtD,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,sBACb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,WACC,GAAAF,EAAAI,GAAA,EAAC6D,QAAAA,CAAM9D,UAAU,qDAA4C,iBAC7D,GAAAH,EAAAI,GAAA,EAAC+F,QAAAA,CACC1I,KAAK,OACLkJ,MAAOvU,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAaiF,IAAI,GAAI,GAC5B8I,UAAU,qBACVyG,YAAY,yBAIhB,GAAA5G,EAAAC,IAAA,EAACC,MAAAA,WACC,GAAAF,EAAAI,GAAA,EAAC6D,QAAAA,CAAM9D,UAAU,qDAA4C,kBAC7D,GAAAH,EAAAC,IAAA,EAAC4G,SAAAA,CAAO1G,UAAU,+BAChB,GAAAH,EAAAI,GAAA,EAAC2F,SAAAA,CAAOY,MAAM,gBAAO,eACrB,GAAA3G,EAAAI,GAAA,EAAC2F,SAAAA,CAAOY,MAAM,iBAAQ,gBACtB,GAAA3G,EAAAI,GAAA,EAAC2F,SAAAA,CAAOY,MAAM,gBAAO,2BAUnC,GAAA3G,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,6EACb,GAAAH,EAAAI,GAAA,EAAC4E,SAAAA,CACCC,QAASjD,EACT7B,UAAU,mCACX,WAGD,GAAAH,EAAAI,GAAA,EAAC4E,SAAAA,CACCC,QAASjD,EACT7B,UAAU,iCACX,wBAOX,mCCrZO,SAAS2G,IACd,GAAM,CAACC,EAAsBC,EAAmB,CAAG7E,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IACtD,CAAC8E,EAAcC,EAAgB,CAAG/E,CAAAA,EAAAA,EAAAA,QAAAA,EAAwB,MAC1D,CAACsB,EAAoBC,EAAsB,CAAGvB,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IACvD,CAACwB,EAAiBC,EAAmB,CAAGzB,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IACjD,CAACgF,EAAmBC,EAAqB,CAAGjF,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IACrD,CAACC,EAASC,EAAW,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,EAAwB,EAAE,EAClD,CAACG,EAAgBC,EAAkB,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IAC/C,CAACK,EAAoBC,EAAsB,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IAEvD,CACJlN,YAAAA,CAAW,CACXK,YAAAA,CAAW,CACX9C,aAAAA,CAAY,CACZC,aAAAA,CAAY,CAEZuC,eAAAA,CAAc,CACf,CAAGhB,IAGE6P,EAAcwD,CAAAA,EAAAA,EAAAA,WAAAA,EAAY,UAC9B,GAAI,CAEF,MAAMtM,UAAUC,YAAY,CAACH,YAAY,CAAC,CAAEV,MAAO,GAAMP,MAAO,EAAK,GAGrE,IAAMoB,EAA8B8I,CAFjB,MAAM/I,UAAUC,YAAY,CAAC+I,gBAAgB,IAG7Dc,MAAM,CAACb,GAAUA,eAAAA,EAAOnH,IAAI,EAAqBmH,eAAAA,EAAOnH,IAAI,EAC5D6E,GAAG,CAACsC,GAAW,EACd1I,SAAU0I,EAAO1I,QAAQ,CACzB2I,MAAOD,EAAOC,KAAK,EAAI,GAAkBD,MAAAA,CAAfA,EAAOnH,IAAI,CAAC,KAA+Bc,MAAA,CAA5BqG,EAAO1I,QAAQ,CAAC4I,KAAK,CAAC,EAAG,IAClErH,KAAMmH,EAAOnH,IAAI,CACnB,GAKF,GAHAwF,EAAWrH,GAGP,CAACsH,EAAgB,CACnB,IAAM6B,EAAgBnJ,EAAa2B,IAAI,CAACyH,GAAKA,eAAAA,EAAEvH,IAAI,EAC/CsH,GAAe5B,EAAkB4B,EAAc7I,QAAQ,CAC7D,CAEA,GAAI,CAACkH,EAAoB,CACvB,IAAM6B,EAAarJ,EAAa2B,IAAI,CAACyH,GAAKA,eAAAA,EAAEvH,IAAI,EAC5CwH,GAAY5B,EAAsB4B,EAAW/I,QAAQ,CAC3D,CACF,CAAE,MAAOlD,EAAO,CACdF,QAAQE,KAAK,CAAC,yBAA0BA,EAC1C,CAEF,EAAG,CAACkK,EAAgBE,EAAmB,EAEvC7C,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,KACRkE,IAGA9I,UAAUC,YAAY,CAACL,gBAAgB,CAAC,eAAgBkJ,GAEjD,KACL9I,UAAUC,YAAY,CAACsM,mBAAmB,CAAC,eAAgBzD,EAC7D,GACC,CAACA,EAAY,EAEhB,IAAM0D,EAAmB,IACvBL,EAAgBpR,GAChB0R,WAAW,IAAMN,EAAgB,MAAO,IAC1C,EAgEMO,EAA0B,UAC9B,GAAI,CACF,GAAKV,EAeHW,QAfyB,CAEzB,IAAMhM,EAAe,MAAM0D,EAAW3D,eAAe,EAGrD,OAAM2D,EAAWT,iBAAiB,CAACjD,GAGnCA,EAAanG,cAAc,EAAE,CAAC,EAAE,CAACoG,OAAO,CAAG,KACzC+L,GACF,EAEAV,EAAmB,IACnBO,EAAiB,yBACnB,CAGF,CAAE,MAAOnP,EAAO,CACdF,QAAQE,KAAK,CAAC,+BAAgCA,GAC9CmP,EAAiB,gCACnB,CACF,EAEMG,EAAwB,UAC5B,GAAI,CAEF,IAAMzM,EAAe,MAAMmE,EAAWvE,YAAY,GAGlDuE,EAAWT,iBAAiB,CAAC1D,GAE7B+L,EAAmB,IACnBO,EAAiB,yBACnB,CAAE,MAAOnP,EAAO,CACdF,QAAQE,KAAK,CAAC,+BAAgCA,GAC9CmP,EAAiB,8BACnB,CACF,EAEMI,EAAqBN,CAAAA,EAAAA,EAAAA,WAAAA,EAAY,MAAO/L,IAC5C,GAAIA,IAAagH,EAAgB,CAC/BoB,EAAsB,IACtB,MACF,CAEA,GAAI,CACF,MAAMtE,EAAWrC,YAAY,CAACzB,GAC9BiH,EAAkBjH,GAClBoI,EAAsB,IACtB6D,EAAiB,iBACnB,CAAE,MAAOnP,EAAO,CACdF,QAAQE,KAAK,CAAC,0BAA2BA,GACzCmP,EAAiB,0BACnB,CACF,EAAG,CAACjF,EAAe,EAEbsF,EAAyBP,CAAAA,EAAAA,EAAAA,WAAAA,EAAY,MAAO/L,IAChD,GAAIA,IAAakH,EAAoB,CACnCoB,EAAmB,IACnB,MACF,CAEA,GAAI,CAGFnB,EAAsBnH,GACtBsI,EAAmB,IACnB2D,EAAiB,qBACnB,CAAE,MAAOnP,EAAO,CACdF,QAAQE,KAAK,CAAC,8BAA+BA,GAC7CmP,EAAiB,8BACnB,CACF,EAAG,CAAC/E,EAAmB,EAEjBX,EAAUO,EAAQyC,MAAM,CAAC,GAAoBT,eAAAA,EAAEvH,IAAI,EACnDiF,EAAcM,EAAQyC,MAAM,CAAC,GAAoBT,eAAAA,EAAEvH,IAAI,EAE7D,MACE,GAAAmD,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,gBACb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,2BAEb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,qBACb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,iBACb,GAAAH,EAAAI,GAAA,EAAC4E,SAAAA,CACCC,QAhGc,KACxBhQ,IACAsS,EAAiB/U,EAAe,qBAAuB,mBACzD,EA8FY2N,UAAW,eAAoDxC,MAAA,CAArCnL,EAAe,SAAW,WAAW,mBAC/DqV,MAAOrV,EAAe,oBAAsB,2BAE3CA,EACC,GAAAwN,EAAAI,GAAA,EAACW,EAAAA,CAAMA,CAAAA,CAACZ,UAAU,YAElB,GAAAH,EAAAI,GAAA,EAACU,EAAAA,CAAGA,CAAAA,CAACX,UAAU,cAGlB2B,EAAY3G,MAAM,CAAG,GACpB,GAAA6E,EAAAI,GAAA,EAAC4E,SAAAA,CACCC,QAAS,IAAMrB,EAAmB,CAACD,GACnCxD,UAAU,mEACV0H,MAAM,6BAEN,GAAA7H,EAAAI,GAAA,EAAC0H,EAAAA,CAASA,CAAAA,CAAC3H,UAAU,iBAK1BwD,GAAmB7B,EAAY3G,MAAM,CAAG,GACvC,GAAA6E,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,uGACb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,gBACb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,wEACb,GAAAH,EAAAI,GAAA,EAAC2H,EAAAA,CAAUA,CAAAA,CAAC5H,UAAU,YAAY,uBAGnC2B,EAAYJ,GAAG,CAAC,GACf,GAAA1B,EAAAI,GAAA,EAACF,MAAAA,CAECC,UAAU,0EACV8E,QAAS,IAAM2C,EAAuB3B,EAAI3K,QAAQ,WAElD,GAAA0E,EAAAI,GAAA,EAACK,OAAAA,CAAKN,UAAWqC,IAAuByD,EAAI3K,QAAQ,CAAG,gBAAkB,YACtE2K,EAAIhC,KAAK,IALPgC,EAAI3K,QAAQ,WAe7B,GAAA0E,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,qBACb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,iBACb,GAAAH,EAAAI,GAAA,EAAC4E,SAAAA,CACCC,QA3Ic,KACxB3P,IACAiS,EAAiB9U,EAAe,mBAAqB,oBACvD,EAyIY0N,UAAW,eAAoDxC,MAAA,CAArClL,EAAe,SAAW,WAAW,mBAC/DoV,MAAOpV,EAAe,iBAAmB,2BAExCA,EACC,GAAAuN,EAAAI,GAAA,EAACa,EAAAA,CAAQA,CAAAA,CAACd,UAAU,YAEpB,GAAAH,EAAAI,GAAA,EAACY,EAAAA,CAAKA,CAAAA,CAACb,UAAU,cAGpB0B,EAAQ1G,MAAM,CAAG,GAChB,GAAA6E,EAAAI,GAAA,EAAC4E,SAAAA,CACCC,QAAS,IAAMvB,EAAsB,CAACD,GACtCtD,UAAU,mEACV0H,MAAM,yBAEN,GAAA7H,EAAAI,GAAA,EAAC0H,EAAAA,CAASA,CAAAA,CAAC3H,UAAU,iBAK1BsD,GAAsB5B,EAAQ1G,MAAM,CAAG,GACtC,GAAA6E,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,uGACb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,gBACb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,wEACb,GAAAH,EAAAI,GAAA,EAACgF,EAAAA,CAAMA,CAAAA,CAACjF,UAAU,YAAY,mBAG/B0B,EAAQH,GAAG,CAAC,GACX,GAAA1B,EAAAI,GAAA,EAACF,MAAAA,CAECC,UAAU,0EACV8E,QAAS,IAAM0C,EAAmBhC,EAAOrK,QAAQ,WAEjD,GAAA0E,EAAAI,GAAA,EAACK,OAAAA,CAAKN,UAAWmC,IAAmBqD,EAAOrK,QAAQ,CAAG,gBAAkB,YACrEqK,EAAO1B,KAAK,IALV0B,EAAOrK,QAAQ,WAehC,GAAA0E,EAAAI,GAAA,EAAC4E,SAAAA,CACCC,QAASwC,EACTtH,UAAW,eAA4DxC,MAAA,CAA7CoJ,EAAuB,SAAW,YAC5Dc,MAAOd,EAAuB,sBAAwB,wBAErDA,EACC,GAAA/G,EAAAI,GAAA,EAAC4H,EAAAA,CAAUA,CAAAA,CAAC7H,UAAU,YAEtB,GAAAH,EAAAI,GAAA,EAACc,EAAAA,CAAOA,CAAAA,CAACf,UAAU,cAKvB,GAAAH,EAAAI,GAAA,EAAC4E,SAAAA,CACCC,QAAS,IAAMmC,EAAqB,IACpCjH,UAAU,uDACV8H,aAAW,oBAEX,GAAAjI,EAAAI,GAAA,EAAC8H,EAAAA,CAAYA,CAAAA,CAAC/H,UAAU,iBAK5B,GAAAH,EAAAI,GAAA,EAACwB,EAAaA,CACZG,OAAQoF,EACRnF,QAAS,IAAMoF,EAAqB,MAIrCH,GACC,GAAAjH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,uHACZ8G,MAKX,wBC3WO,SAASkB,IACd,GAAM,CAACrS,EAASsS,EAAW,CAAGjG,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IACjCkG,EAAiB3I,CAAAA,EAAAA,EAAAA,MAAAA,EAAuB,MAExC,CACJ/M,SAAAA,CAAQ,CACRP,YAAAA,CAAW,CACXgE,WAAAA,CAAU,CACVD,iBAAAA,CAAgB,CAChBnD,iBAAAA,CAAgB,CAEhB6D,UAAAA,CAAS,CACV,CAAG7C,IAEEsU,EAAUlW,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAauE,IAAI,IAAK,QAAUvE,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAauE,IAAI,IAAK,UAGtEgJ,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,SACR0I,CAAsB,QAAtBA,CAAAA,EAAAA,EAAezI,OAAO,GAAtByI,KAAAA,IAAAA,GAAAA,EAAwBE,cAAc,CAAC,CAAEC,SAAU,QAAS,EAC9D,EAAG,CAAC7V,EAAS,EAGbgN,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,KACRxJ,GACF,EAAG,CAACA,EAAiB,EAerB,IAAMsS,EAAkB,IAClBH,GACFzR,EAAUd,EAEd,EAEM2S,EAAa,GACVC,EAAKC,kBAAkB,CAAC,EAAE,CAAE,CAAEC,KAAM,UAAWC,OAAQ,SAAU,GAG1E,MACE,GAAA9I,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,gDAEb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,2EACb,GAAAH,EAAAC,IAAA,EAACuF,KAAAA,CAAGrF,UAAU,qEACZ,GAAAH,EAAAI,GAAA,EAAC2I,EAAAA,CAAaA,CAAAA,CAAC5I,UAAU,YAAY,UAGvC,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,mCACZxN,EAASwI,MAAM,CAAG,GACjB,GAAA6E,EAAAC,IAAA,EAACQ,OAAAA,CAAKN,UAAU,kCACbxN,EAASwI,MAAM,CAAC,oBAOzB,GAAA6E,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,iDACZxN,IAAAA,EAASwI,MAAM,CACd,GAAA6E,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,2CACb,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,6HACb,GAAAH,EAAAI,GAAA,EAAC4I,EAAAA,CAAIA,CAAAA,CAAC7I,UAAU,yBAElB,GAAAH,EAAAI,GAAA,EAACQ,IAAAA,CAAET,UAAU,oCAA2B,oBACxC,GAAAH,EAAAI,GAAA,EAACQ,IAAAA,CAAET,UAAU,mBAAU,+BAGzBxN,EACGkS,MAAM,CAACoE,GAAO,CAACA,EAAIC,SAAS,EAC5BxH,GAAG,CAAC,GACL,GAAA1B,EAAAC,IAAA,EAACC,MAAAA,CAECC,UAAW,iBAEP8I,MAAAA,CADFA,EAAIlT,MAAM,GAAK3D,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAasC,EAAE,EAAG,YAAc,cAChD,KAAkCiJ,MAAA,CAA/BsL,EAAI/S,MAAM,CAAG,aAAe,cAEhC,GAAA8J,EAAAC,IAAA,EAACC,MAAAA,CACCC,UAAW,yCAMVxC,MAAA,CALCsL,EAAIlT,MAAM,GAAK3D,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAasC,EAAE,EAC1B,+CACAuU,EAAI/S,MAAM,CACR,yCACA,yBAGR,GAAA8J,EAAAI,GAAA,EAACQ,IAAAA,CAAET,UAAU,8CAAsC8I,EAAInT,OAAO,GAC7DmT,EAAI/S,MAAM,EACT,GAAA8J,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,gFAAuE,YAK1F,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,mEACb,GAAAH,EAAAI,GAAA,EAACK,OAAAA,CAAKN,UAAU,+BAAuB8I,EAAI1Q,QAAQ,GACnD,GAAAyH,EAAAI,GAAA,EAACK,OAAAA,UAAK,MACN,GAAAT,EAAAI,GAAA,EAACK,OAAAA,CAAKN,UAAU,mBAAWuI,EAAWO,EAAInQ,SAAS,IAClDwP,GAAWW,EAAIlT,MAAM,GAAK3D,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAasC,EAAE,GACxC,GAAAsL,EAAAI,GAAA,EAAC4E,SAAAA,CACCC,QAAS,IAAMwD,EAAgBQ,EAAIlT,MAAM,EACzCoK,UAAU,wDACX,eA7BA8I,EAAIvU,EAAE,GAqCjB,GAAAsL,EAAAI,GAAA,EAACF,MAAAA,CAAIG,IAAKgI,OAIZ,GAAArI,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,wCACb,GAAAH,EAAAC,IAAA,EAACkJ,OAAAA,CAAKC,SAjGc,IACxB9C,EAAE+C,cAAc,GAEXvT,EAAQwT,IAAI,IAAOlX,IAKxBgH,EAAcP,eAAe,CAAC/C,EAAQwT,IAAI,GAAIlX,EAAYiF,IAAI,EAE9D+Q,EAAW,IACb,EAsFyCjI,UAAU,2BAC3C,GAAAH,EAAAI,GAAA,EAAC+F,QAAAA,CACC1I,KAAK,OACLkJ,MAAO7Q,EACPuQ,SAAU,GAAO+B,EAAW9B,EAAEC,MAAM,CAACI,KAAK,EAC1CC,YAAY,oBACZzG,UAAU,kCACVoJ,UAAW,IACXC,aAAa,QAEf,GAAAxJ,EAAAI,GAAA,EAAC4E,SAAAA,CACCvH,KAAK,SACLgM,SAAU,CAAC3T,EAAQwT,IAAI,GACvBnJ,UAAW,iCAIVxC,MAAA,CAHC7H,EAAQwT,IAAI,GACR,kGACA,yDAGN,GAAAtJ,EAAAI,GAAA,EAAC4I,EAAAA,CAAIA,CAAAA,CAAC7I,UAAU,qBAM5B,2ECrIO,SAASuJ,IACd,GAAM,CAACC,EAAeC,EAAiB,CAAGzH,CAAAA,EAAAA,EAAAA,QAAAA,EAAwB,MAE5D,CACJ/P,YAAAA,CAAW,CACXC,aAAAA,CAAY,CACZF,WAAAA,CAAU,CACVmE,gBAAAA,CAAe,CACfC,oBAAAA,CAAmB,CACnB5B,kBAAAA,CAAiB,CACjB+B,gBAAAA,CAAe,CACfE,iBAAAA,CAAgB,CAChBI,SAAAA,CAAQ,CACRC,WAAAA,CAAU,CACVJ,UAAAA,CAAS,CACV,CAAG7C,IAEEoN,EAAmBC,MAAMC,IAAI,CAACjP,EAAakP,MAAM,IACjDsI,EAAkBzX,EAAc,CAACA,KAAgBgP,EAAiB,CAAGA,EAErE0I,EAAgB,IACpB,IAAMC,EAAQ,EAAE,CAkBhB,OAhBItV,EAAYjC,YAAY,CAC1BuX,EAAMC,IAAI,CAAC,GAAAhK,EAAAI,GAAA,EAACW,EAAAA,CAAMA,CAAAA,CAAWZ,UAAU,wBAAhB,QAEvB4J,EAAMC,IAAI,CAAC,GAAAhK,EAAAI,GAAA,EAACU,EAAAA,CAAGA,CAAAA,CAAWX,UAAU,0BAAhB,QAGlB1L,EAAYhC,YAAY,CAC1BsX,EAAMC,IAAI,CAAC,GAAAhK,EAAAI,GAAA,EAACa,EAAAA,CAAQA,CAAAA,CAAad,UAAU,wBAAlB,UAEzB4J,EAAMC,IAAI,CAAC,GAAAhK,EAAAI,GAAA,EAACY,EAAAA,CAAKA,CAAAA,CAAab,UAAU,0BAAlB,UAGpB1L,EAAY/B,eAAe,EAC7BqX,EAAMC,IAAI,CAAC,GAAAhK,EAAAI,GAAA,EAACc,EAAAA,CAAOA,CAAAA,CAAcf,UAAU,yBAAnB,WAGnB4J,CACT,EAEMzB,EAAUlW,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAauE,IAAI,IAAK,QAAUvE,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAauE,IAAI,IAAK,UAChEsT,EAAS7X,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAauE,IAAI,IAAK,OAE/BuT,EAAoB,CAACC,EAAgBtV,KACzC,OAAQsV,GACN,IAAK,OACH7T,EAAgBzB,GAChB,KACF,KAAK,SACHF,EAAkBE,GAClB,KACF,KAAK,UACH6B,EAAgB7B,GAChB,KACF,KAAK,SACH+B,EAAiB/B,GACjB,KACF,KAAK,QACHgC,EAAUhC,EAEd,CACA+U,EAAiB,KACnB,EAEA,MACE,GAAA5J,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,gDAEb,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,wCACb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,8CACb,GAAAH,EAAAC,IAAA,EAACuF,KAAAA,CAAGrF,UAAU,qEACZ,GAAAH,EAAAI,GAAA,EAACgK,EAAAA,CAAKA,CAAAA,CAACjK,UAAU,YAAY,iBACd0J,EAAgB1O,MAAM,CAAC,IACrChJ,GAAc,GAAA6N,EAAAI,GAAA,EAACiK,EAAAA,CAAIA,CAAAA,CAAClK,UAAU,+BAGhCmI,GACC,GAAAtI,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,oCACb,GAAAH,EAAAI,GAAA,EAAC4E,SAAAA,CACCC,QAAS1O,EACT4J,UAAU,kDACV0H,MAAM,iCAEN,GAAA7H,EAAAI,GAAA,EAACW,EAAAA,CAAMA,CAAAA,CAACZ,UAAU,cAGnB8J,GACC,GAAAjK,EAAAI,GAAA,EAAC4E,SAAAA,CACCC,QAAS,IAAM9S,EAAa8E,IAAeD,IAC3CmJ,UAAU,kDACV0H,MAAO1V,EAAa,cAAgB,qBAEnCA,EAAa,GAAA6N,EAAAI,GAAA,EAACkK,EAAAA,CAAMA,CAAAA,CAACnK,UAAU,YAAe,GAAAH,EAAAI,GAAA,EAACiK,EAAAA,CAAIA,CAAAA,CAAClK,UAAU,sBAS3E,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,gDACZ0J,EAAgBnI,GAAG,CAAC,IACnB,IAAM6I,EAAgB9V,EAAYC,EAAE,GAAKtC,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAasC,EAAE,EAExD,MACE,GAAAsL,EAAAI,GAAA,EAACF,MAAAA,CAECC,UAAU,sEAEV,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,8CACb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,wCAEb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,wHACb,GAAAH,EAAAI,GAAA,EAACK,OAAAA,CAAKN,UAAU,wCACb1L,EAAY4C,IAAI,CAACqJ,MAAM,CAAC,GAAGC,WAAW,KAExClM,EAAY+V,YAAY,EACvB,GAAAxK,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,wGACb,GAAAH,EAAAI,GAAA,EAACqK,EAAAA,CAAIA,CAAAA,CAACtK,UAAU,4BAMtB,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,yCACb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,wCACb,GAAAH,EAAAC,IAAA,EAACQ,OAAAA,CAAKN,UAAU,oDACb1L,EAAY4C,IAAI,CAChBkT,GAAiB,YAInB9V,SAAAA,EAAYkC,IAAI,EACf,GAAAqJ,EAAAI,GAAA,EAACS,EAAAA,CAAKA,CAAAA,CAACV,UAAU,0CAElB1L,YAAAA,EAAYkC,IAAI,EACf,GAAAqJ,EAAAI,GAAA,EAACkF,EAAAA,CAAMA,CAAAA,CAACnF,UAAU,2CAGtB,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,uCACZ2J,EAAcrV,WAMpB,CAAC8V,GAAiBjC,GACjB,GAAAtI,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,qBACb,GAAAH,EAAAI,GAAA,EAAC4E,SAAAA,CACCC,QAAS,IAAM2E,EAAiBD,IAAkBlV,EAAYC,EAAE,CAAG,KAAOD,EAAYC,EAAE,EACxFyL,UAAU,2DAEV,GAAAH,EAAAI,GAAA,EAACsK,EAAAA,CAAYA,CAAAA,CAACvK,UAAU,cAIzBwJ,IAAkBlV,EAAYC,EAAE,EAC/B,GAAAsL,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,qGACb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,gBACb,GAAAH,EAAAC,IAAA,EAAC+E,SAAAA,CACCC,QAAS,IAAMiF,EAAkB,OAAQzV,EAAYC,EAAE,EACvDyL,UAAU,sGAEV,GAAAH,EAAAI,GAAA,EAACW,EAAAA,CAAMA,CAAAA,CAACZ,UAAU,YAAY,UAI/B8J,GAAUxV,gBAAAA,EAAYkC,IAAI,EACzB,GAAAqJ,EAAAC,IAAA,EAAC+E,SAAAA,CACCC,QAAS,IAAMiF,EAAkB,UAAWzV,EAAYC,EAAE,EAC1DyL,UAAU,sGAEV,GAAAH,EAAAI,GAAA,EAACkF,EAAAA,CAAMA,CAAAA,CAACnF,UAAU,YAAY,kBAKjC8J,GAAUxV,YAAAA,EAAYkC,IAAI,EACzB,GAAAqJ,EAAAC,IAAA,EAAC+E,SAAAA,CACCC,QAAS,IAAMiF,EAAkB,SAAUzV,EAAYC,EAAE,EACzDyL,UAAU,sGAEV,GAAAH,EAAAI,GAAA,EAACuK,EAAAA,CAASA,CAAAA,CAACxK,UAAU,YAAY,oBAKrC,GAAAH,EAAAC,IAAA,EAAC+E,SAAAA,CACCC,QAAS,IAAMiF,EAAkB,QAASzV,EAAYC,EAAE,EACxDyL,UAAU,wGAEV,GAAAH,EAAAI,GAAA,EAACwK,EAAAA,CAASA,CAAAA,CAACzK,UAAU,YAAY,gBAInC,GAAAH,EAAAC,IAAA,EAAC+E,SAAAA,CACCC,QAAS,IAAMiF,EAAkB,SAAUzV,EAAYC,EAAE,EACzDyL,UAAU,wGAEV,GAAAH,EAAAI,GAAA,EAACwK,EAAAA,CAASA,CAAAA,CAACzK,UAAU,YAAY,wBA7F1C1L,EAAYC,EAAE,CAwGzB,OAIR,qDCpNO,SAASmW,GAAcvL,CAA8B,KAA9B,CAAErN,OAAAA,CAAM,CAAsB,CAA9BqN,EACtBwL,EAASC,CAAAA,EAAAA,EAAAA,SAAAA,IACT,CAACC,EAAeC,EAAiB,CAAG9I,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IAE7C,CAAC+I,EAAcC,EAAgB,CAAGhJ,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IAC3C,CAACiJ,EAAcC,EAAgB,CAAGlJ,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IAC3C,CAAC8E,EAAcC,EAAgB,CAAG/E,CAAAA,EAAAA,EAAAA,QAAAA,EAAwB,MAE1D,CACJ/P,YAAAA,CAAW,CACXC,aAAAA,CAAY,CACZiC,aAAAA,CAAY,CACZE,eAAAA,CAAc,CACdG,kBAAAA,CAAiB,CACjBG,kBAAAA,CAAiB,CACjBE,eAAAA,CAAc,CACdU,WAAAA,CAAU,CACV0B,MAAAA,CAAK,CACN,CAAGpD,IAGEuT,EAAmB,IACvBL,EAAgBpR,GAChB0R,WAAW,IAAMN,EAAgB,MAAO,IAC1C,EAGAvH,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,KACR,IAAM2L,EAAiB,UACrB,GAAI,CAIF,GAAI,CAFWlS,EAAc7B,OAAO,GAGlC,MAAM,MAAU,+BAIlB,IAAMuE,EAAS,MAAMsD,EAAWvE,YAAY,GAC5C7F,EAAe8G,GAGfsD,EAAWL,qBAAqB,CAAC,CAAChJ,EAAQ6C,KACxCQ,EAAcV,UAAU,CAAC3C,EAAQ6C,EACnC,GAGAwG,EAAWP,QAAQ,CAAC,CAAC9I,EAAQ+F,KAC3BhH,EAAkBiB,EAAQ,CAAE+F,OAAAA,CAAO,EACrC,GAGAsD,EAAWN,kBAAkB,CAAC,IAC5BnK,EAAkBoB,GAClBwR,EAAiB,qCACnB,GAGInV,GACFgH,EAAcd,QAAQ,CAACrG,EAAQG,EAAYsC,EAAE,CAAEtC,EAAYiF,IAAI,EAGjE4T,EAAiB,IACjB3W,EAAa,IACbiT,EAAiB,oCAEnB,CAAE,MAAOnP,EAAO,CACdF,QAAQE,KAAK,CAAC,6BAA8BA,GAC5CmP,EAAiB,yDACnB,CACF,EAMA,OAJInV,GAAe,CAAC4Y,GAClBM,IAGK,KAEDN,IACF5R,EAAcX,SAAS,GACvB2G,EAAWxE,OAAO,GAClBxD,IAEJ,CAEF,EAAG,CAAChF,EAAaH,EAAQ+Y,EAAc,EAGvCrL,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,KACR,GAAI,CAACqL,EAAe,OAEpB,IAAMvT,EAAS2B,EAAcD,SAAS,GACtC,GAAI,CAAC1B,EAAQ,OAGb,IAAM8T,EAAmB,MAAOC,IAC9BtT,QAAQC,GAAG,CAAC,eAAgBqT,GAE5BhX,EAAe,CACbE,GAAI8W,EAAKzV,MAAM,CACfsB,KAAMmU,EAAKjT,QAAQ,CACnB/F,aAAc,GACdC,aAAc,GACdC,gBAAiB,GACjBiE,KAAM,cACN6T,aAAc,GACdiB,SAAU,IAAI7V,KACd8V,aAAc,IAAI9V,IACpB,GAGA,GAAI,CACF,IAAMoI,EAAQ,MAAMoB,EAAWrB,WAAW,CAACyN,EAAKzV,MAAM,EACtDqD,EAAcV,UAAU,CAAC8S,EAAKzV,MAAM,CAAE,CACpC0H,KAAM,QACNO,MAAAA,CACF,EACF,CAAE,MAAO5F,EAAO,CACdF,QAAQE,KAAK,CAAC,wBAAyBA,EACzC,CAEAmP,EAAiB,GAAiB5J,MAAA,CAAd6N,EAAKjT,QAAQ,CAAC,uBACpC,EAGMoT,EAAiB,IACrBzT,QAAQC,GAAG,CAAC,aAAcqT,GAC1B7W,EAAkB6W,EAAKzV,MAAM,EAC7BqJ,EAAWvB,oBAAoB,CAAC2N,EAAKzV,MAAM,EAC3CwR,EAAiB,GAAiB5J,MAAA,CAAd6N,EAAKjT,QAAQ,CAAC,qBACpC,EAGMqT,EAAe,MAAOJ,IAC1BtT,QAAQC,GAAG,CAAC,mBAAoBqT,GAEhC,GAAI,CACF,GAAM,CAAEK,WAAAA,CAAU,CAAEjT,OAAAA,CAAM,CAAE,CAAG4S,EAE/B,OAAQ5S,EAAO6E,IAAI,EACjB,IAAK,QACH,IAAMa,EAAS,MAAMc,EAAWhB,YAAY,CAACyN,EAAYjT,EAAOoF,KAAK,EACrE5E,EAAcV,UAAU,CAACmT,EAAY,CACnCpO,KAAM,SACNa,OAAAA,CACF,GACA,KAEF,KAAK,SACH,MAAMc,EAAWb,YAAY,CAACsN,EAAYjT,EAAO0F,MAAM,EACvD,KAEF,KAAK,gBACH,MAAMc,EAAWZ,kBAAkB,CAACqN,EAAYjT,EAAO4E,SAAS,CAEpE,CACF,CAAE,MAAOpF,EAAO,CACdF,QAAQE,KAAK,CAAC,yBAA0BA,EAC1C,CACF,EAGM0T,EAAoB,IACxBpW,EAAW,CACThB,GAAIqX,KAAKC,MAAM,GAAGC,QAAQ,CAAC,IAAIC,SAAS,CAAC,EAAG,IAC5CnW,OAAQyV,EAAKzV,MAAM,CACnBwC,SAAUiT,EAAKjT,QAAQ,CACvBzC,QAAS0V,EAAK1V,OAAO,CACrBgD,UAAW,IAAIlD,KAAK4V,EAAK1S,SAAS,CACpC,EACF,EAQA,OALArB,EAAOQ,EAAE,CAAC,cAAesT,GACzB9T,EAAOQ,EAAE,CAAC,YAAa0T,GACvBlU,EAAOQ,EAAE,CAAC,SAAU2T,GACpBnU,EAAOQ,EAAE,CAAC,eAAgB6T,GAEnB,KACLrU,EAAOyB,GAAG,CAAC,cAAeqS,GAC1B9T,EAAOyB,GAAG,CAAC,YAAayS,GACxBlU,EAAOyB,GAAG,CAAC,SAAU0S,GACrBnU,EAAOyB,GAAG,CAAC,eAAgB4S,EAC7B,CAEF,EAAG,CAACd,EAAc,EAElB,GAAM,CAACmB,EAAkBC,EAAoB,CAAGjK,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,WAsCzD,GAAuB/P,EAgBrB,GAAA4N,EAAAC,IAAA,EAAAD,EAAAqM,QAAA,YAEE,GAAArM,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,gBAEf,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,2DAEb,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,0DACb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,8CACb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,wCACb,GAAAH,EAAAI,GAAA,EAACkM,KAAAA,CAAGnM,UAAU,4CAAmC,yBAGjD,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,sDACb,GAAAH,EAAAI,GAAA,EAACgK,EAAAA,CAAKA,CAAAA,CAACjK,UAAU,YACjB,GAAAH,EAAAC,IAAA,EAACQ,OAAAA,WAAMpO,EAAaka,IAAI,CAAG,EAAE,sBAE/B,GAAAvM,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,kCAAwB,OAChClO,QAIT,GAAA+N,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,wCACb,GAAAH,EAAAI,GAAA,EAAC4E,SAAAA,CACCC,QA5DU,KACtB,IAAMuH,EAAc,GAAkCva,MAAAA,CAA/B2F,OAAOC,QAAQ,CAACC,MAAM,CAAC,UAAe6F,MAAA,CAAP1L,GACtD8I,UAAU0R,SAAS,CAACC,SAAS,CAACF,GAC9BjF,EAAiB,oCACnB,EAyDcpH,UAAU,oDACV0H,MAAM,6BAEN,GAAA7H,EAAAI,GAAA,EAACuM,EAAAA,CAAKA,CAAAA,CAACxM,UAAU,cAGnB,GAAAH,EAAAI,GAAA,EAAC4E,SAAAA,CACCC,QA9DS,KACrBlK,UAAU0R,SAAS,CAACC,SAAS,CAACza,GAC9BsV,EAAiB,kCACnB,EA4DcpH,UAAU,oDACV0H,MAAM,2BAEN,GAAA7H,EAAAI,GAAA,EAACwM,EAAAA,CAAIA,CAAAA,CAACzM,UAAU,cAGlB,GAAAH,EAAAI,GAAA,EAAC4E,SAAAA,CACCC,QAAS,IAAMkG,EAAgB,CAACD,GAChC/K,UAAW,qDAA4FxC,MAAA,CAAvCuN,EAAe,mBAAqB,IACpGrD,MAAM,oBAEN,GAAA7H,EAAAI,GAAA,EAAC2E,EAAAA,CAAQA,CAAAA,CAAC5E,UAAU,cAGtB,GAAAH,EAAAI,GAAA,EAAC4E,SAAAA,CACCC,QAzEW,KAClB4H,SAASC,iBAAiB,EAI7BD,SAASE,cAAc,GACvB1B,EAAgB,MAJhBwB,SAASG,eAAe,CAACC,iBAAiB,GAC1C5B,EAAgB,IAKpB,EAkEclL,UAAU,oDACV0H,MAAM,6BAELuD,EAAe,GAAApL,EAAAI,GAAA,EAAC8M,EAAAA,CAASA,CAAAA,CAAC/M,UAAU,YAAe,GAAAH,EAAAI,GAAA,EAAC+M,EAAAA,CAASA,CAAAA,CAAChN,UAAU,cAG3E,GAAAH,EAAAI,GAAA,EAAC4E,SAAAA,CACCC,QA3GU,KACtBmH,EAAoB,GACtB,EA0GcjM,UAAU,gFACV0H,MAAM,yBAEN,GAAA7H,EAAAI,GAAA,EAACgN,EAAAA,CAAQA,CAAAA,CAACjN,UAAU,sBAO5B,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,6CAEb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,gCACb,GAAAH,EAAAI,GAAA,EAACe,EAASA,CAAAA,GACV,GAAAnB,EAAAI,GAAA,EAAC0G,EAAaA,CAAAA,MAIhB,GAAA9G,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,sCAEb,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,iBACb,GAAAH,EAAAI,GAAA,EAACsJ,EAAgBA,CAAAA,KAInB,GAAA1J,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,iBACb,GAAAH,EAAAI,GAAA,EAAC+H,EAAIA,CAAAA,WAMX,GAAAnI,EAAAI,GAAA,EAACwB,EAAaA,CACZG,OAAQmJ,EACRlJ,QAAS,IAAMmJ,EAAgB,MAIhCgB,GACC,GAAAnM,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,+EACb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,iDACb,GAAAH,EAAAI,GAAA,EAACoF,KAAAA,CAAGrF,UAAU,iDAAwC,mBACtD,GAAAH,EAAAI,GAAA,EAACQ,IAAAA,CAAET,UAAU,8BAAqB,gDAClC,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,uCACb,GAAAH,EAAAI,GAAA,EAAC4E,SAAAA,CACCC,QA9II,KAClBmH,EAAoB,GACtB,EA6IgBjM,UAAU,2FACX,WAGD,GAAAH,EAAAI,GAAA,EAAC4E,SAAAA,CACCC,QA3JK,KACnB7L,EAAcX,SAAS,GACvB2G,EAAWxE,OAAO,GAClBxD,IACA0T,EAAOd,IAAI,CAAC,IACd,EAuJgB7J,UAAU,yFACX,kBASR8G,GACC,GAAAjH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,uFACZ8G,UAtIP,GAAAjH,EAAAC,IAAA,EAAAD,EAAAqM,QAAA,YACE,GAAArM,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,gBACf,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,uEACb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,kCACb,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,6FACf,GAAAH,EAAAI,GAAA,EAAC0E,KAAAA,CAAG3E,UAAU,kDAAyC,0BACvD,GAAAH,EAAAI,GAAA,EAACQ,IAAAA,CAAET,UAAU,yBAAgB,0DAsIzC,CCjYe,SAASkN,KACtB,IAAMC,EAASC,CAAAA,EAAAA,EAAAA,SAAAA,IACTzC,EAASC,CAAAA,EAAAA,EAAAA,SAAAA,IACT9Y,EAASqb,EAAO5Y,EAAE,CAClB,CAAC6D,EAAUiV,EAAY,CAAGrL,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IACnC,CAACsL,EAAWC,EAAa,CAAGvL,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IACrC,CAACwL,EAAWC,EAAa,CAAGzL,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IAErC,CAAE9N,UAAAA,CAAS,CAAEE,eAAAA,CAAc,CAAE,CAAGP,IAEhC6Z,EAAiBxG,CAAAA,EAAAA,EAAAA,WAAAA,EAAY,MAAOhQ,IACxC,GAAI,CAACA,EAAKiS,IAAI,GAAI,CAChBwE,MAAM,0BACN,MACF,CAEAJ,EAAa,IAEb,GAAI,CAEFrZ,EAAUpC,GAGV,IAAM8b,EAAO,CACXrZ,GAAIqX,KAAKC,MAAM,GAAGC,QAAQ,CAAC,IAAIC,SAAS,CAAC,EAAG,IAC5C7U,KAAMA,EAAKiS,IAAI,GACf9W,aAAc,GACdC,aAAc,GACdC,gBAAiB,GACjBiE,KAAM,OACN6T,aAAc,GACdiB,SAAU,IAAI7V,KACd8V,aAAc,IAAI9V,IACpB,EAGArB,EAAewZ,GAGfC,aAAaC,OAAO,CAAC,WAAY5W,EAAKiS,IAAI,IAE1CsE,EAAa,GACf,CAAE,MAAOxV,EAAO,CACdF,QAAQE,KAAK,CAAC,sBAAuBA,GACrC0V,MAAM,yCACR,QAAU,CACRJ,EAAa,GACf,CACF,EAAG,CAACzb,EAAQsC,EAAgBF,EAAU,SAEtCsL,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,KAER,IAAMuO,EAAaF,aAAaG,OAAO,CAAC,YACpCD,IACFV,EAAYU,GACZL,EAAeK,GAEnB,EAAG,CAACL,EAAe,EAOdF,GAgDE,GAAA3N,EAAAI,GAAA,EAACyK,GAAaA,CAAC5Y,OAAQA,IA9C1B,GAAA+N,EAAAC,IAAA,EAAAD,EAAAqM,QAAA,YACE,GAAArM,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,gBACf,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,2EACb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,sCACb,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,6BACb,GAAAH,EAAAI,GAAA,EAAC0E,KAAAA,CAAG3E,UAAU,8CAAqC,iBACnD,GAAAH,EAAAC,IAAA,EAACW,IAAAA,CAAET,UAAU,0BAAgB,eACf,GAAAH,EAAAI,GAAA,EAACK,OAAAA,CAAKN,UAAU,mDAA2ClO,UAI3E,GAAA+N,EAAAC,IAAA,EAACkJ,OAAAA,CAAKC,SAlBS,IACvB9C,EAAE+C,cAAc,GAChBwE,EAAetV,EACjB,EAe4C4H,UAAU,sBAC1C,GAAAH,EAAAI,GAAA,EAAC+F,QAAAA,CACC1I,KAAK,OACLmJ,YAAY,kBACZD,MAAOpO,EACP8N,SAAU,GAAOmH,EAAYlH,EAAEC,MAAM,CAACI,KAAK,EAC3C8C,SAAUgE,EACVW,UAAS,GACTjO,UAAU,uBAGZ,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,uBACb,GAAAH,EAAAI,GAAA,EAAC4E,SAAAA,CACCvH,KAAK,SACLwH,QAAS,IAAM6F,EAAOd,IAAI,CAAC,KAC3BP,SAAUgE,EACVtN,UAAU,gCACX,SAGD,GAAAH,EAAAI,GAAA,EAAC4E,SAAAA,CACCvH,KAAK,SACLgM,SAAUgE,GAAa,CAAClV,EAAS+Q,IAAI,GACrCnJ,UAAU,8BAETsN,EAAY,aAAe,8BAW9C", "sources": ["webpack://_N_E/", "webpack://_N_E/./lib/store.ts", "webpack://_N_E/./lib/socket.ts", "webpack://_N_E/./lib/rtc.ts", "webpack://_N_E/./components/VideoCall/VideoTile.tsx", "webpack://_N_E/./components/VideoCall/VideoGrid.tsx", "webpack://_N_E/./components/VideoCall/SettingsModal.tsx", "webpack://_N_E/./components/VideoCall/VideoControls.tsx", "webpack://_N_E/./components/VideoCall/Chat.tsx", "webpack://_N_E/./components/VideoCall/ParticipantsList.tsx", "webpack://_N_E/./components/VideoCall/VideoCallRoom.tsx", "webpack://_N_E/./app/room/[id]/page.tsx"], "sourcesContent": ["import(/* webpackMode: \"eager\" */ \"/Volumes/Apps/Websites/streamit-main/app/room/[id]/page.tsx\");\n", "import { create } from 'zustand'\nimport { devtools } from 'zustand/middleware'\n\nexport interface Participant {\n  id: string\n  name: string\n  stream?: MediaStream\n  isAudioMuted: boolean\n  isVideoMuted: boolean\n  isScreenSharing: boolean\n  role: 'host' | 'co-host' | 'participant'\n  isHandRaised: boolean\n  joinedAt: Date\n  lastActivity: Date\n}\n\nexport interface ChatMessage {\n  id: string\n  userId: string\n  userName: string\n  message: string\n  timestamp: Date\n  isSpam?: boolean\n  isBlocked?: boolean\n}\n\nexport interface SecuritySettings {\n  encryptionEnabled: boolean\n  antiSpamEnabled: boolean\n  maxMessagesPerMinute: number\n  allowScreenShare: boolean\n  allowFileSharing: boolean\n  requireApprovalToJoin: boolean\n}\n\nexport interface AdminControls {\n  canMuteAll: boolean\n  canMuteParticipant: boolean\n  canRemoveParticipant: boolean\n  canControlCamera: boolean\n  canManageRoles: boolean\n}\n\nexport interface VideoCallState {\n  // Room state\n  roomId: string | null\n  isConnected: boolean\n  roomLocked: boolean\n\n  // User state\n  currentUser: Participant | null\n  participants: Map<string, Participant>\n\n  // Media state\n  localStream: MediaStream | null\n  isAudioMuted: boolean\n  isVideoMuted: boolean\n  isScreenSharing: boolean\n\n  // Chat state\n  messages: ChatMessage[]\n  unreadCount: number\n  messageHistory: Map<string, number>\n\n  // UI state\n  isChatOpen: boolean\n  isSettingsOpen: boolean\n\n  // Security & Admin\n  securitySettings: SecuritySettings\n  adminControls: AdminControls\n  blockedUsers: Set<string>\n  spamDetection: Map<string, { count: number, lastReset: number }>\n\n  // Actions\n  setRoomId: (roomId: string) => void\n  setConnected: (connected: boolean) => void\n  setCurrentUser: (user: Participant) => void\n  addParticipant: (participant: Participant) => void\n  removeParticipant: (participantId: string) => void\n  updateParticipant: (participantId: string, updates: Partial<Participant>) => void\n  setLocalStream: (stream: MediaStream | null) => void\n  toggleAudio: () => void\n  toggleVideo: () => void\n  toggleScreenShare: () => void\n  addMessage: (message: ChatMessage) => void\n  clearUnreadCount: () => void\n  toggleChat: () => void\n  toggleSettings: () => void\n\n  // Admin Actions\n  muteParticipant: (participantId: string) => void\n  muteAllParticipants: () => void\n  unmuteAllParticipants: () => void\n  promoteToCoHost: (participantId: string) => void\n  demoteFromCoHost: (participantId: string) => void\n  blockUser: (participantId: string) => void\n  unblockUser: (participantId: string) => void\n  lockRoom: () => void\n  unlockRoom: () => void\n  updateSecuritySettings: (settings: Partial<SecuritySettings>) => void\n\n  // Utility Actions\n  reset: () => void\n}\n\nconst initialState = {\n  roomId: null,\n  isConnected: false,\n  roomLocked: false,\n  currentUser: null,\n  participants: new Map<string, Participant>(),\n  localStream: null,\n  isAudioMuted: false,\n  isVideoMuted: false,\n  isScreenSharing: false,\n  messages: [],\n  unreadCount: 0,\n  messageHistory: new Map<string, number>(),\n  isChatOpen: false,\n  isSettingsOpen: false,\n  securitySettings: {\n    encryptionEnabled: true,\n    antiSpamEnabled: true,\n    maxMessagesPerMinute: 10,\n    allowScreenShare: true,\n    allowFileSharing: false,\n    requireApprovalToJoin: false,\n  },\n  adminControls: {\n    canMuteAll: true,\n    canMuteParticipant: true,\n    canRemoveParticipant: true,\n    canControlCamera: true,\n    canManageRoles: true,\n  },\n  blockedUsers: new Set<string>(),\n  spamDetection: new Map<string, { count: number, lastReset: number }>(),\n}\n\nexport const useVideoCallStore = create<VideoCallState>()(\n  devtools(\n    (set, get) => ({\n      ...initialState,\n\n      // Room actions\n      setRoomId: (roomId: string) => set({ roomId }),\n      setConnected: (isConnected: boolean) => set({ isConnected }),\n\n      // User actions\n      setCurrentUser: (currentUser: Participant) => set({ currentUser }),\n\n      addParticipant: (participant: Participant) => {\n        const participants = new Map(get().participants)\n        participants.set(participant.id, participant)\n        set({ participants })\n      },\n\n      removeParticipant: (participantId: string) => {\n        const participants = new Map(get().participants)\n        participants.delete(participantId)\n        set({ participants })\n      },\n\n      updateParticipant: (participantId: string, updates: Partial<Participant>) => {\n        const participants = new Map(get().participants)\n        const participant = participants.get(participantId)\n        if (participant) {\n          participants.set(participantId, { ...participant, ...updates })\n          set({ participants })\n        }\n      },\n\n      // Media actions\n      setLocalStream: (localStream: MediaStream | null) => set({ localStream }),\n\n      toggleAudio: () => {\n        const { isAudioMuted, localStream } = get()\n        if (localStream) {\n          localStream.getAudioTracks().forEach(track => {\n            track.enabled = isAudioMuted\n          })\n        }\n        set({ isAudioMuted: !isAudioMuted })\n      },\n\n      toggleVideo: () => {\n        const { isVideoMuted, localStream } = get()\n        if (localStream) {\n          localStream.getVideoTracks().forEach(track => {\n            track.enabled = isVideoMuted\n          })\n        }\n        set({ isVideoMuted: !isVideoMuted })\n      },\n\n      toggleScreenShare: () => {\n        set(state => ({ isScreenSharing: !state.isScreenSharing }))\n      },\n\n      // Chat actions\n      addMessage: (message: ChatMessage) => {\n        const { messages, securitySettings, spamDetection } = get()\n\n        // Anti-spam check\n        if (securitySettings.antiSpamEnabled) {\n          const now = Date.now()\n          const userSpam = spamDetection.get(message.userId) || { count: 0, lastReset: now }\n\n          // Reset count if more than a minute has passed\n          if (now - userSpam.lastReset > 60000) {\n            userSpam.count = 0\n            userSpam.lastReset = now\n          }\n\n          userSpam.count++\n          spamDetection.set(message.userId, userSpam)\n\n          // Mark as spam if exceeding limit\n          if (userSpam.count > securitySettings.maxMessagesPerMinute) {\n            message.isSpam = true\n          }\n        }\n\n        set({\n          messages: [...messages, message],\n          unreadCount: get().isChatOpen ? 0 : get().unreadCount + 1,\n          spamDetection: new Map(spamDetection)\n        })\n      },\n\n      clearUnreadCount: () => set({ unreadCount: 0 }),\n\n      // UI actions\n      toggleChat: () => {\n        const isChatOpen = !get().isChatOpen\n        set({\n          isChatOpen,\n          unreadCount: isChatOpen ? 0 : get().unreadCount\n        })\n      },\n\n      toggleSettings: () => set(state => ({ isSettingsOpen: !state.isSettingsOpen })),\n\n      // Admin actions\n      muteParticipant: (participantId: string) => {\n        get().updateParticipant(participantId, { isAudioMuted: true })\n      },\n\n      muteAllParticipants: () => {\n        const { participants } = get()\n        participants.forEach((_, id) => {\n          get().updateParticipant(id, { isAudioMuted: true })\n        })\n      },\n\n      unmuteAllParticipants: () => {\n        const { participants } = get()\n        participants.forEach((_, id) => {\n          get().updateParticipant(id, { isAudioMuted: false })\n        })\n      },\n\n      promoteToCoHost: (participantId: string) => {\n        get().updateParticipant(participantId, { role: 'co-host' })\n      },\n\n      demoteFromCoHost: (participantId: string) => {\n        get().updateParticipant(participantId, { role: 'participant' })\n      },\n\n      blockUser: (participantId: string) => {\n        const blockedUsers = new Set(get().blockedUsers)\n        blockedUsers.add(participantId)\n        set({ blockedUsers })\n        get().removeParticipant(participantId)\n      },\n\n      unblockUser: (participantId: string) => {\n        const blockedUsers = new Set(get().blockedUsers)\n        blockedUsers.delete(participantId)\n        set({ blockedUsers })\n      },\n\n      lockRoom: () => set({ roomLocked: true }),\n      unlockRoom: () => set({ roomLocked: false }),\n\n      updateSecuritySettings: (settings: Partial<SecuritySettings>) => {\n        set(state => ({\n          securitySettings: { ...state.securitySettings, ...settings }\n        }))\n      },\n\n      // Utility actions\n      reset: () => set(initialState),\n    }),\n    {\n      name: 'video-call-store',\n    }\n  )\n)\n\nexport default useVideoCallStore", "import { io, Socket } from 'socket.io-client'\n\nclass SocketManager {\n  private socket: Socket | null = null\n  private roomId: string | null = null\n\n  connect() {\n    if (this.socket?.connected) return this.socket\n\n    this.socket = io(process.env.NODE_ENV === 'production'\n      ? window.location.origin\n      : 'http://localhost:3002', {\n      transports: ['websocket', 'polling'],\n      upgrade: true,\n    })\n\n    this.socket.on('connect', () => {\n      console.log('Connected to server:', this.socket?.id)\n    })\n\n    this.socket.on('disconnect', () => {\n      console.log('Disconnected from server')\n    })\n\n    this.socket.on('connect_error', (error) => {\n      console.error('Connection error:', error)\n    })\n\n    return this.socket\n  }\n\n  disconnect() {\n    if (this.socket) {\n      this.socket.disconnect()\n      this.socket = null\n    }\n  }\n\n  joinRoom(roomId: string, userId: string, userName: string) {\n    if (!this.socket) return\n\n    this.roomId = roomId\n    this.socket.emit('join-room', { roomId, userId, userName })\n  }\n\n  leaveRoom() {\n    if (!this.socket || !this.roomId) return\n\n    this.socket.emit('leave-room', { roomId: this.roomId })\n    this.roomId = null\n  }\n\n  sendSignal(targetUserId: string, signal: any) {\n    if (!this.socket || !this.roomId) return\n\n    this.socket.emit('signal', {\n      roomId: this.roomId,\n      targetUserId,\n      signal\n    })\n  }\n\n  sendChatMessage(message: string, userName: string) {\n    if (!this.socket || !this.roomId) return\n\n    this.socket.emit('chat-message', {\n      roomId: this.roomId,\n      message,\n      userName,\n      timestamp: new Date().toISOString()\n    })\n  }\n\n  on(event: string, callback: (...args: any[]) => void) {\n    if (!this.socket) return\n\n    this.socket.on(event, callback)\n  }\n\n  off(event: string, callback?: (...args: any[]) => void) {\n    if (!this.socket) return\n\n    this.socket.off(event, callback)\n  }\n\n  getSocket() {\n    return this.socket\n  }\n\n  isConnected() {\n    return this.socket?.connected || false\n  }\n}\n\nexport const socketManager = new SocketManager()\n", "import { streamEncryption } from './encryption'\n\n// WebRTC configuration with enhanced security\nconst RTC_CONFIG: RTCConfiguration = {\n  iceServers: [\n    { urls: 'stun:stun.l.google.com:19302' },\n    { urls: 'stun:stun1.l.google.com:19302' },\n  ],\n  iceCandidatePoolSize: 10,\n  bundlePolicy: 'max-bundle',\n  rtcpMuxPolicy: 'require',\n}\n\n// High-quality video constraints\nexport const VIDEO_CONSTRAINTS: MediaStreamConstraints = {\n  video: {\n    width: { ideal: 1280, max: 1920 },\n    height: { ideal: 720, max: 1080 },\n    frameRate: { ideal: 30, max: 60 },\n    facingMode: 'user'\n  },\n  audio: {\n    echoCancellation: true,\n    noiseSuppression: true,\n    autoGainControl: true,\n    sampleRate: 48000\n  }\n}\n\nexport const SCREEN_SHARE_CONSTRAINTS = {\n  video: {\n    width: { ideal: 1920, max: 3840 },\n    height: { ideal: 1080, max: 2160 },\n    frameRate: { ideal: 30, max: 60 }\n  },\n  audio: true\n}\n\nexport class RTCManager {\n  private peerConnections: Map<string, RTCPeerConnection> = new Map()\n  private localStream: MediaStream | null = null\n  private screenStream: MediaStream | null = null\n  private cameraStream: MediaStream | null = null\n  private currentVideoDeviceId: string | null = null\n  private currentAudioDeviceId: string | null = null\n  private onStreamCallback?: (userId: string, stream: MediaStream) => void\n  private onUserDisconnectedCallback?: (userId: string) => void\n\n  constructor() {\n    this.setupEventHandlers()\n  }\n\n  private setupEventHandlers() {\n    // Handle page unload (only in browser)\n    if (typeof window !== 'undefined') {\n      window.addEventListener('beforeunload', () => {\n        this.cleanup()\n      })\n    }\n  }\n\n  async getUserMedia(constraints: MediaStreamConstraints = VIDEO_CONSTRAINTS): Promise<MediaStream> {\n    try {\n      this.localStream = await navigator.mediaDevices.getUserMedia(constraints)\n      this.cameraStream = this.localStream\n      \n      // Store device IDs if available\n      const videoTracks = this.localStream.getVideoTracks()\n      if (videoTracks.length > 0) {\n        this.currentVideoDeviceId = videoTracks[0].getSettings().deviceId || null\n      }\n      \n      const audioTracks = this.localStream.getAudioTracks()\n      if (audioTracks.length > 0) {\n        this.currentAudioDeviceId = audioTracks[0].getSettings().deviceId || null\n      }\n      \n      return this.localStream\n    } catch (error) {\n      console.error('Error accessing media devices:', error)\n      throw error\n    }\n  }\n  \n  // Screen sharing and media device methods\n  async getDisplayMedia(constraints: MediaStreamConstraints = SCREEN_SHARE_CONSTRAINTS): Promise<MediaStream> {\n    try {\n      this.screenStream = await navigator.mediaDevices.getDisplayMedia(constraints)\n      \n      // Handle when user stops screen sharing\n      this.screenStream.getVideoTracks()[0].onended = () => {\n        this.restoreVideoTrack()\n      }\n      \n      return this.screenStream\n    } catch (error) {\n      console.error('Error accessing screen share:', error)\n      throw error\n    }\n  }\n  \n  // replaceVideoTrack is implemented below with a more comprehensive version\n  \n  replaceAudioTrack(stream: MediaStream) {\n    if (!this.localStream) return\n    \n    // Stop existing audio tracks\n    this.localStream.getAudioTracks().forEach(track => track.stop())\n    \n    // Add new audio track\n    const audioTrack = stream.getAudioTracks()[0]\n    if (audioTrack) {\n      this.localStream.addTrack(audioTrack)\n    }\n    \n    // Update all peer connections\n    this.updateAudioTracks()\n  }\n  \n  restoreVideoTrack() {\n    if (!this.localStream || !this.cameraStream) return\n    \n    // Stop existing video tracks\n    this.localStream.getVideoTracks().forEach(track => track.stop())\n    \n    // Add camera video track back\n    const videoTrack = this.cameraStream.getVideoTracks()[0]\n    if (videoTrack) {\n      this.localStream.addTrack(videoTrack.clone())\n    }\n    \n    // Update all peer connections\n    this.updateVideoTracks()\n    \n    // Stop and clear screen stream\n    if (this.screenStream) {\n      this.screenStream.getTracks().forEach(track => track.stop())\n      this.screenStream = null\n    }\n  }\n  \n  private updateVideoTracks() {\n    if (!this.localStream) return\n    \n    const videoTrack = this.localStream.getVideoTracks()[0]\n    if (!videoTrack) return\n    \n    this.peerConnections.forEach(pc => {\n      const sender = pc.getSenders().find(s => s.track?.kind === 'video')\n      if (sender) {\n        sender.replaceTrack(videoTrack)\n      }\n    })\n  }\n  \n  private updateAudioTracks() {\n    if (!this.localStream) return\n    \n    const audioTrack = this.localStream.getAudioTracks()[0]\n    if (!audioTrack) return\n    \n    this.peerConnections.forEach(pc => {\n      const sender = pc.getSenders().find(s => s.track?.kind === 'audio')\n      if (sender) {\n        sender.replaceTrack(audioTrack)\n      }\n    })\n  }\n  \n  async switchCamera(deviceId: string) {\n    if (!this.localStream) return\n    \n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({\n        video: { deviceId: { exact: deviceId } },\n        audio: this.currentAudioDeviceId ? { deviceId: { exact: this.currentAudioDeviceId } } : true\n      })\n      \n      // Stop old tracks\n      this.localStream.getTracks().forEach(track => track.stop())\n      \n      // Replace with new stream\n      this.localStream = stream\n      this.cameraStream = stream\n      this.currentVideoDeviceId = deviceId\n      \n      // Update all peer connections\n      this.updateVideoTracks()\n      \n      return stream\n    } catch (error) {\n      console.error('Error switching camera:', error)\n      throw new Error('Failed to switch camera')\n    }\n  }\n\n  createPeerConnection(userId: string): RTCPeerConnection {\n    const pc = new RTCPeerConnection(RTC_CONFIG)\n\n    // Add local stream tracks\n    if (this.localStream) {\n      this.localStream.getTracks().forEach(track => {\n        pc.addTrack(track, this.localStream!)\n      })\n    }\n\n    // Handle incoming stream\n    pc.ontrack = (event) => {\n      const [remoteStream] = event.streams\n      if (this.onStreamCallback) {\n        this.onStreamCallback(userId, remoteStream)\n      }\n    }\n\n    // Handle ICE candidates\n    pc.onicecandidate = (event) => {\n      if (event.candidate) {\n        // Send ICE candidate to remote peer via signaling\n        this.sendSignal(userId, {\n          type: 'ice-candidate',\n          candidate: event.candidate\n        })\n      }\n    }\n\n    // Handle connection state changes\n    pc.onconnectionstatechange = () => {\n      console.log(`Connection state for ${userId}:`, pc.connectionState)\n      \n      if (pc.connectionState === 'disconnected' || pc.connectionState === 'failed') {\n        this.removePeerConnection(userId)\n        if (this.onUserDisconnectedCallback) {\n          this.onUserDisconnectedCallback(userId)\n        }\n      }\n    }\n\n    this.peerConnections.set(userId, pc)\n    return pc\n  }\n\n  async createOffer(userId: string): Promise<RTCSessionDescriptionInit> {\n    const pc = this.peerConnections.get(userId) || this.createPeerConnection(userId)\n    \n    const offer = await pc.createOffer({\n      offerToReceiveAudio: true,\n      offerToReceiveVideo: true\n    })\n    \n    await pc.setLocalDescription(offer)\n    return offer\n  }\n\n  async createAnswer(userId: string, offer: RTCSessionDescriptionInit): Promise<RTCSessionDescriptionInit> {\n    const pc = this.peerConnections.get(userId) || this.createPeerConnection(userId)\n    \n    await pc.setRemoteDescription(offer)\n    const answer = await pc.createAnswer()\n    await pc.setLocalDescription(answer)\n    \n    return answer\n  }\n\n  async handleAnswer(userId: string, answer: RTCSessionDescriptionInit): Promise<void> {\n    const pc = this.peerConnections.get(userId)\n    if (pc) {\n      await pc.setRemoteDescription(answer)\n    }\n  }\n\n  async handleIceCandidate(userId: string, candidate: RTCIceCandidateInit): Promise<void> {\n    const pc = this.peerConnections.get(userId)\n    if (pc) {\n      await pc.addIceCandidate(candidate)\n    }\n  }\n\n  removePeerConnection(userId: string): void {\n    const pc = this.peerConnections.get(userId)\n    if (pc) {\n      pc.close()\n      this.peerConnections.delete(userId)\n    }\n  }\n\n  replaceVideoTrack(newStream: MediaStream): void {\n    const videoTrack = newStream.getVideoTracks()[0]\n    \n    this.peerConnections.forEach(async (pc) => {\n      const sender = pc.getSenders().find(s => \n        s.track && s.track.kind === 'video'\n      )\n      \n      if (sender && videoTrack) {\n        await sender.replaceTrack(videoTrack)\n      }\n    })\n  }\n\n  onStream(callback: (userId: string, stream: MediaStream) => void): void {\n    this.onStreamCallback = callback\n  }\n\n  onUserDisconnected(callback: (userId: string) => void): void {\n    this.onUserDisconnectedCallback = callback\n  }\n\n  private sendSignal(userId: string, signal: any): void {\n    // This will be implemented by the component using RTCManager\n    // to send signals via Socket.IO\n  }\n\n  setSendSignalCallback(callback: (userId: string, signal: any) => void): void {\n    this.sendSignal = callback\n  }\n\n  cleanup(): void {\n    // Close all peer connections\n    this.peerConnections.forEach(pc => pc.close())\n    this.peerConnections.clear()\n\n    // Stop local stream\n    if (this.localStream) {\n      this.localStream.getTracks().forEach(track => track.stop())\n      this.localStream = null\n    }\n  }\n\n  getLocalStream(): MediaStream | null {\n    return this.localStream\n  }\n\n  getPeerConnection(userId: string): RTCPeerConnection | undefined {\n    return this.peerConnections.get(userId)\n  }\n}\n\nexport const rtcManager = new RTCManager()\n", "'use client'\n\nimport { useEffect, useRef } from 'react'\nimport { Participant } from '@/lib/store'\nimport { Mi<PERSON>, MicOff, Video, VideoOff, Monitor, Crown } from 'lucide-react'\n\ninterface VideoTileProps {\n  participant: Participant\n  stream?: MediaStream | null\n  isLocal: boolean\n  isFeatured?: boolean\n}\n\nexport function VideoTile({ participant, stream, isLocal, isFeatured = false }: VideoTileProps) {\n  const videoRef = useRef<HTMLVideoElement>(null)\n\n  useEffect(() => {\n    if (videoRef.current && stream) {\n      videoRef.current.srcObject = stream\n    }\n  }, [stream])\n\n  const hasVideo = stream && stream.getVideoTracks().length > 0 && !participant.isVideoMuted\n  const hasAudio = stream && stream.getAudioTracks().length > 0 && !participant.isAudioMuted\n\n  const tileSize = isFeatured ? 'w-full h-full' : 'w-full h-full max-w-sm max-h-64'\n  const avatarSize = isFeatured ? 'w-24 h-24' : 'w-16 h-16'\n  const textSize = isFeatured ? 'text-xl' : 'text-lg'\n  const nameSize = isFeatured ? 'text-lg' : 'text-sm'\n\n  return (\n    <div className={`video-container relative overflow-hidden ${tileSize}`}>\n      {hasVideo ? (\n        <video\n          ref={videoRef}\n          autoPlay\n          playsInline\n          muted={isLocal} // Mute local video to prevent feedback\n          className=\"w-full h-full object-cover rounded-lg\"\n        />\n      ) : (\n        <div className=\"w-full h-full bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center rounded-lg\">\n          <div className=\"text-center\">\n            <div className={`${avatarSize} bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-3 shadow-lg`}>\n              <span className={`text-white ${textSize} font-bold`}>\n                {participant.name.charAt(0).toUpperCase()}\n              </span>\n            </div>\n            <p className={`text-white ${nameSize} font-medium`}>{participant.name}</p>\n            <p className=\"text-white/60 text-xs\">Camera is off</p>\n          </div>\n        </div>\n      )}\n\n      {/* Participant info overlay */}\n      <div className=\"absolute bottom-2 left-2 right-2 flex items-center justify-between\">\n        <div className=\"glass-dark px-2 py-1 rounded-lg\">\n          <div className=\"flex items-center gap-1\">\n            {isLocal && <Crown className=\"h-3 w-3 text-yellow-400\" />}\n            <span className=\"text-white text-xs font-medium\">\n              {isLocal ? 'You' : participant.name}\n            </span>\n          </div>\n        </div>\n\n        <div className=\"flex items-center space-x-1\">\n          {/* Audio indicator */}\n          <div className={`p-1 rounded-full ${hasAudio ? 'bg-green-500/80' : 'bg-red-500/80'} backdrop-blur-sm`}>\n            {hasAudio ? (\n              <Mic className=\"h-3 w-3 text-white\" />\n            ) : (\n              <MicOff className=\"h-3 w-3 text-white\" />\n            )}\n          </div>\n\n          {/* Video indicator */}\n          <div className={`p-1 rounded-full ${hasVideo ? 'bg-green-500/80' : 'bg-red-500/80'} backdrop-blur-sm`}>\n            {hasVideo ? (\n              <Video className=\"h-3 w-3 text-white\" />\n            ) : (\n              <VideoOff className=\"h-3 w-3 text-white\" />\n            )}\n          </div>\n\n          {/* Screen sharing indicator */}\n          {participant.isScreenSharing && (\n            <div className=\"p-1 rounded-full bg-blue-500/80 backdrop-blur-sm\">\n              <Monitor className=\"h-3 w-3 text-white\" />\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Local user indicator */}\n      {isLocal && (\n        <div className=\"absolute top-2 left-2\">\n          <div className=\"glass-dark px-2 py-1 rounded-lg\">\n            <span className=\"text-white text-xs font-medium flex items-center gap-1\">\n              <Crown className=\"h-2 w-2 text-yellow-400\" />\n              Host\n            </span>\n          </div>\n        </div>\n      )}\n\n      {/* Connection quality indicator */}\n      <div className=\"absolute top-2 right-2\">\n        <div className=\"flex space-x-0.5\">\n          <div className=\"w-0.5 h-2 bg-green-400 rounded-full\"></div>\n          <div className=\"w-0.5 h-3 bg-green-400 rounded-full\"></div>\n          <div className=\"w-0.5 h-4 bg-green-400 rounded-full\"></div>\n        </div>\n      </div>\n    </div>\n  )\n}\n", "'use client'\n\nimport { useVideoCallStore } from '@/lib/store'\nimport { VideoTile } from './VideoTile'\n\nexport function VideoGrid() {\n  const { currentUser, participants, localStream } = useVideoCallStore()\n\n  const participantsList = Array.from(participants.values())\n  const totalParticipants = participantsList.length + 1 // +1 for current user\n\n  // Calculate grid layout for smaller, more organized tiles\n  const getGridClass = (count: number) => {\n    if (count === 1) return 'grid-cols-1'\n    if (count === 2) return 'grid-cols-1'\n    if (count <= 4) return 'grid-cols-2'\n    if (count <= 6) return 'grid-cols-2'\n    return 'grid-cols-3'\n  }\n\n  const getGridRows = (count: number) => {\n    if (count === 1) return 'grid-rows-1'\n    if (count === 2) return 'grid-rows-2'\n    if (count <= 4) return 'grid-rows-2'\n    if (count <= 6) return 'grid-rows-3'\n    return 'grid-rows-3'\n  }\n\n  return (\n    <div className=\"flex-1 p-4\">\n      <div className=\"glass h-full p-4 overflow-hidden\">\n        <div className={`\n          grid gap-3 h-full\n          ${getGridClass(totalParticipants)}\n          ${getGridRows(totalParticipants)}\n          place-items-center\n        `}>\n          {/* Local user video - Featured if alone */}\n          {currentUser && (\n            <VideoTile\n              key={currentUser.id}\n              participant={currentUser}\n              stream={localStream}\n              isLocal={true}\n              isFeatured={totalParticipants === 1}\n            />\n          )}\n\n          {/* Remote participants */}\n          {participantsList.map((participant, index) => (\n            <VideoTile\n              key={participant.id}\n              participant={participant}\n              stream={participant.stream}\n              isLocal={false}\n              isFeatured={totalParticipants === 2 && index === 0}\n            />\n          ))}\n        </div>\n      </div>\n    </div>\n  )\n}\n", "'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useVideoCallStore } from '@/lib/store'\nimport { rtcManager } from '@/lib/rtc'\nimport { \n  X, \n  Camera, \n  Mic, \n  Monitor, \n  Volume2, \n  Settings, \n  Shield,\n\n  Image,\n  ChevronDown,\n  Check\n} from 'lucide-react'\n\ninterface MediaDevice {\n  deviceId: string\n  label: string\n  kind: MediaDeviceKind\n}\n\ninterface SettingsModalProps {\n  isOpen: boolean\n  onClose: () => void\n}\n\nexport function SettingsModal({ isOpen, onClose }: SettingsModalProps) {\n  const [activeTab, setActiveTab] = useState('video')\n  const [devices, setDevices] = useState<MediaDevice[]>([])\n  const [selectedCamera, setSelectedCamera] = useState('')\n  const [selectedMicrophone, setSelectedMicrophone] = useState('')\n  const [selectedSpeaker, setSelectedSpeaker] = useState('')\n  const [backgroundBlur, setBackgroundBlur] = useState(false)\n  const [selectedBackground, setSelectedBackground] = useState('none')\n  const [videoQuality, setVideoQuality] = useState('hd')\n  const [audioQuality, setAudioQuality] = useState('high')\n  const [noiseSuppression, setNoiseSuppression] = useState(true)\n  const [echoCancellation, setEchoCancellation] = useState(true)\n  const [autoGainControl, setAutoGainControl] = useState(true)\n  const [encryptionEnabled, setEncryptionEnabled] = useState(true)\n  const [antiSpamEnabled, setAntiSpamEnabled] = useState(true)\n  const [showCameraDropdown, setShowCameraDropdown] = useState(false)\n  const [showMicDropdown, setShowMicDropdown] = useState(false)\n\n  const { currentUser } = useVideoCallStore()\n\n  // Background options\n  const backgroundOptions = [\n    { id: 'none', name: 'None', preview: null },\n    { id: 'blur', name: 'Blur Background', preview: null },\n    { id: 'office', name: 'Modern Office', preview: '/backgrounds/office.jpg' },\n    { id: 'nature', name: 'Nature Scene', preview: '/backgrounds/nature.jpg' },\n    { id: 'abstract', name: 'Abstract Blue', preview: '/backgrounds/abstract.jpg' },\n    { id: 'gradient', name: 'Purple Gradient', preview: '/backgrounds/gradient.jpg' }\n  ]\n\n  // Video quality options\n  const videoQualityOptions = [\n    { id: 'sd', name: 'SD (480p)', resolution: '640x480' },\n    { id: 'hd', name: 'HD (720p)', resolution: '1280x720' },\n    { id: 'fhd', name: 'Full HD (1080p)', resolution: '1920x1080' },\n    { id: '4k', name: '4K (2160p)', resolution: '3840x2160' }\n  ]\n\n  // Load available devices\n  useEffect(() => {\n    const loadDevices = async () => {\n      try {\n        const deviceList = await navigator.mediaDevices.enumerateDevices()\n        const mediaDevices: MediaDevice[] = deviceList.map(device => ({\n          deviceId: device.deviceId,\n          label: device.label || `${device.kind} ${device.deviceId.slice(0, 8)}`,\n          kind: device.kind\n        }))\n        setDevices(mediaDevices)\n\n        // Set default devices\n        const defaultCamera = mediaDevices.find(d => d.kind === 'videoinput')\n        const defaultMic = mediaDevices.find(d => d.kind === 'audioinput')\n        const defaultSpeaker = mediaDevices.find(d => d.kind === 'audiooutput')\n\n        if (defaultCamera) setSelectedCamera(defaultCamera.deviceId)\n        if (defaultMic) setSelectedMicrophone(defaultMic.deviceId)\n        if (defaultSpeaker) setSelectedSpeaker(defaultSpeaker.deviceId)\n      } catch (error) {\n        console.error('Error loading devices:', error)\n      }\n    }\n\n    if (isOpen) {\n      loadDevices()\n    }\n  }, [isOpen])\n\n  const handleCameraChange = async (deviceId: string) => {\n    try {\n      setSelectedCamera(deviceId)\n      const constraints = {\n        video: { \n          deviceId: { exact: deviceId },\n          width: { ideal: 1280 },\n          height: { ideal: 720 }\n        },\n        audio: false\n      }\n      const stream = await navigator.mediaDevices.getUserMedia(constraints)\n      rtcManager.replaceVideoTrack(stream)\n      setShowCameraDropdown(false)\n    } catch (error) {\n      console.error('Error changing camera:', error)\n    }\n  }\n\n  const handleMicrophoneChange = async (deviceId: string) => {\n    try {\n      setSelectedMicrophone(deviceId)\n      const constraints = {\n        video: false,\n        audio: { \n          deviceId: { exact: deviceId },\n          echoCancellation,\n          noiseSuppression,\n          autoGainControl\n        }\n      }\n      const stream = await navigator.mediaDevices.getUserMedia(constraints)\n      // Replace audio track logic would go here\n      setShowMicDropdown(false)\n    } catch (error) {\n      console.error('Error changing microphone:', error)\n    }\n  }\n\n  const applyBackgroundEffect = (backgroundId: string) => {\n    setSelectedBackground(backgroundId)\n    // Background effect logic would be implemented here\n    // This would typically involve canvas manipulation or WebGL shaders\n  }\n\n  const applyVideoQuality = (quality: string) => {\n    setVideoQuality(quality)\n    // Video quality change logic would go here\n  }\n\n  if (!isOpen) return null\n\n  const cameras = devices.filter(d => d.kind === 'videoinput')\n  const microphones = devices.filter(d => d.kind === 'audioinput')\n  const speakers = devices.filter(d => d.kind === 'audiooutput')\n\n  return (\n    <div className=\"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50\">\n      <div className=\"glass max-w-4xl w-full max-h-[90vh] overflow-hidden\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-6 border-b border-white/10\">\n          <h2 className=\"text-2xl font-bold text-white flex items-center gap-3\">\n            <Settings className=\"h-6 w-6\" />\n            Meeting Settings\n          </h2>\n          <button\n            onClick={onClose}\n            className=\"glass-button p-2 text-white hover:text-purple-300\"\n          >\n            <X className=\"h-6 w-6\" />\n          </button>\n        </div>\n\n        <div className=\"flex h-[600px]\">\n          {/* Sidebar */}\n          <div className=\"w-64 p-4 border-r border-white/10\">\n            <div className=\"space-y-2\">\n              {[\n                { id: 'video', name: 'Video', icon: Camera },\n                { id: 'audio', name: 'Audio', icon: Mic },\n                { id: 'background', name: 'Background', icon: Image },\n                { id: 'security', name: 'Security', icon: Shield },\n                { id: 'general', name: 'General', icon: Settings }\n              ].map(tab => (\n                <button\n                  key={tab.id}\n                  onClick={() => setActiveTab(tab.id)}\n                  className={`w-full flex items-center gap-3 p-3 rounded-lg transition-all ${\n                    activeTab === tab.id \n                      ? 'bg-purple-500/30 text-white' \n                      : 'text-white/70 hover:bg-white/10 hover:text-white'\n                  }`}\n                >\n                  <tab.icon className=\"h-5 w-5\" />\n                  {tab.name}\n                </button>\n              ))}\n            </div>\n          </div>\n\n          {/* Content */}\n          <div className=\"flex-1 p-6 overflow-y-auto\">\n            {activeTab === 'video' && (\n              <div className=\"space-y-6\">\n                <h3 className=\"text-xl font-semibold text-white mb-4\">Video Settings</h3>\n                \n                {/* Camera Selection */}\n                <div>\n                  <label className=\"block text-white text-sm font-medium mb-2\">Camera</label>\n                  <div className=\"relative\">\n                    <button\n                      onClick={() => setShowCameraDropdown(!showCameraDropdown)}\n                      className=\"glass-input w-full flex items-center justify-between\"\n                    >\n                      <span>{cameras.find(c => c.deviceId === selectedCamera)?.label || 'Select Camera'}</span>\n                      <ChevronDown className=\"h-4 w-4\" />\n                    </button>\n                    {showCameraDropdown && (\n                      <div className=\"absolute top-full left-0 right-0 mt-1 glass-dark rounded-lg border border-white/20 z-10\">\n                        {cameras.map(camera => (\n                          <button\n                            key={camera.deviceId}\n                            onClick={() => handleCameraChange(camera.deviceId)}\n                            className=\"w-full p-3 text-left text-white hover:bg-white/10 flex items-center justify-between\"\n                          >\n                            {camera.label}\n                            {selectedCamera === camera.deviceId && <Check className=\"h-4 w-4\" />}\n                          </button>\n                        ))}\n                      </div>\n                    )}\n                  </div>\n                </div>\n\n                {/* Video Quality */}\n                <div>\n                  <label className=\"block text-white text-sm font-medium mb-2\">Video Quality</label>\n                  <div className=\"grid grid-cols-2 gap-2\">\n                    {videoQualityOptions.map(option => (\n                      <button\n                        key={option.id}\n                        onClick={() => applyVideoQuality(option.id)}\n                        className={`p-3 rounded-lg border transition-all ${\n                          videoQuality === option.id\n                            ? 'bg-purple-500/30 border-purple-500 text-white'\n                            : 'bg-white/10 border-white/20 text-white/70 hover:bg-white/20'\n                        }`}\n                      >\n                        <div className=\"font-medium\">{option.name}</div>\n                        <div className=\"text-xs opacity-70\">{option.resolution}</div>\n                      </button>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {activeTab === 'audio' && (\n              <div className=\"space-y-6\">\n                <h3 className=\"text-xl font-semibold text-white mb-4\">Audio Settings</h3>\n                \n                {/* Microphone Selection */}\n                <div>\n                  <label className=\"block text-white text-sm font-medium mb-2\">Microphone</label>\n                  <div className=\"relative\">\n                    <button\n                      onClick={() => setShowMicDropdown(!showMicDropdown)}\n                      className=\"glass-input w-full flex items-center justify-between\"\n                    >\n                      <span>{microphones.find(m => m.deviceId === selectedMicrophone)?.label || 'Select Microphone'}</span>\n                      <ChevronDown className=\"h-4 w-4\" />\n                    </button>\n                    {showMicDropdown && (\n                      <div className=\"absolute top-full left-0 right-0 mt-1 glass-dark rounded-lg border border-white/20 z-10\">\n                        {microphones.map(mic => (\n                          <button\n                            key={mic.deviceId}\n                            onClick={() => handleMicrophoneChange(mic.deviceId)}\n                            className=\"w-full p-3 text-left text-white hover:bg-white/10 flex items-center justify-between\"\n                          >\n                            {mic.label}\n                            {selectedMicrophone === mic.deviceId && <Check className=\"h-4 w-4\" />}\n                          </button>\n                        ))}\n                      </div>\n                    )}\n                  </div>\n                </div>\n\n                {/* Audio Enhancement */}\n                <div className=\"space-y-4\">\n                  <h4 className=\"text-white font-medium\">Audio Enhancement</h4>\n                  \n                  <label className=\"flex items-center justify-between\">\n                    <span className=\"text-white\">Noise Suppression</span>\n                    <input\n                      type=\"checkbox\"\n                      checked={noiseSuppression}\n                      onChange={(e) => setNoiseSuppression(e.target.checked)}\n                      className=\"w-5 h-5 rounded bg-white/10 border-white/20\"\n                    />\n                  </label>\n                  \n                  <label className=\"flex items-center justify-between\">\n                    <span className=\"text-white\">Echo Cancellation</span>\n                    <input\n                      type=\"checkbox\"\n                      checked={echoCancellation}\n                      onChange={(e) => setEchoCancellation(e.target.checked)}\n                      className=\"w-5 h-5 rounded bg-white/10 border-white/20\"\n                    />\n                  </label>\n                  \n                  <label className=\"flex items-center justify-between\">\n                    <span className=\"text-white\">Auto Gain Control</span>\n                    <input\n                      type=\"checkbox\"\n                      checked={autoGainControl}\n                      onChange={(e) => setAutoGainControl(e.target.checked)}\n                      className=\"w-5 h-5 rounded bg-white/10 border-white/20\"\n                    />\n                  </label>\n                </div>\n              </div>\n            )}\n\n            {activeTab === 'background' && (\n              <div className=\"space-y-6\">\n                <h3 className=\"text-xl font-semibold text-white mb-4\">Background Effects</h3>\n                \n                <div className=\"grid grid-cols-3 gap-4\">\n                  {backgroundOptions.map(bg => (\n                    <button\n                      key={bg.id}\n                      onClick={() => applyBackgroundEffect(bg.id)}\n                      className={`p-4 rounded-lg border-2 transition-all ${\n                        selectedBackground === bg.id\n                          ? 'border-purple-500 bg-purple-500/20'\n                          : 'border-white/20 bg-white/10 hover:border-white/40'\n                      }`}\n                    >\n                      <div className=\"w-full h-20 bg-gradient-to-br from-gray-600 to-gray-800 rounded-lg mb-2 flex items-center justify-center\">\n                        {bg.id === 'blur' ? <Monitor className=\"h-8 w-8 text-white\" /> : <Image className=\"h-8 w-8 text-white\" />}\n                      </div>\n                      <div className=\"text-white text-sm font-medium\">{bg.name}</div>\n                    </button>\n                  ))}\n                </div>\n              </div>\n            )}\n\n            {activeTab === 'security' && (\n              <div className=\"space-y-6\">\n                <h3 className=\"text-xl font-semibold text-white mb-4\">Security Settings</h3>\n                \n                <div className=\"space-y-4\">\n                  <label className=\"flex items-center justify-between\">\n                    <div>\n                      <span className=\"text-white font-medium\">End-to-End Encryption</span>\n                      <p className=\"text-white/60 text-sm\">Encrypt all video and audio streams</p>\n                    </div>\n                    <input\n                      type=\"checkbox\"\n                      checked={encryptionEnabled}\n                      onChange={(e) => setEncryptionEnabled(e.target.checked)}\n                      className=\"w-5 h-5 rounded bg-white/10 border-white/20\"\n                    />\n                  </label>\n                  \n                  <label className=\"flex items-center justify-between\">\n                    <div>\n                      <span className=\"text-white font-medium\">Anti-Spam Protection</span>\n                      <p className=\"text-white/60 text-sm\">Prevent message flooding in chat</p>\n                    </div>\n                    <input\n                      type=\"checkbox\"\n                      checked={antiSpamEnabled}\n                      onChange={(e) => setAntiSpamEnabled(e.target.checked)}\n                      className=\"w-5 h-5 rounded bg-white/10 border-white/20\"\n                    />\n                  </label>\n                </div>\n              </div>\n            )}\n\n            {activeTab === 'general' && (\n              <div className=\"space-y-6\">\n                <h3 className=\"text-xl font-semibold text-white mb-4\">General Settings</h3>\n                \n                <div className=\"space-y-4\">\n                  <div>\n                    <label className=\"block text-white text-sm font-medium mb-2\">Display Name</label>\n                    <input\n                      type=\"text\"\n                      value={currentUser?.name || ''}\n                      className=\"glass-input w-full\"\n                      placeholder=\"Your display name\"\n                    />\n                  </div>\n                  \n                  <div>\n                    <label className=\"block text-white text-sm font-medium mb-2\">Meeting Theme</label>\n                    <select className=\"glass-input w-full\">\n                      <option value=\"dark\">Dark Theme</option>\n                      <option value=\"light\">Light Theme</option>\n                      <option value=\"auto\">Auto</option>\n                    </select>\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Footer */}\n        <div className=\"flex items-center justify-end gap-3 p-6 border-t border-white/10\">\n          <button\n            onClick={onClose}\n            className=\"btn-secondary px-6 py-2\"\n          >\n            Cancel\n          </button>\n          <button\n            onClick={onClose}\n            className=\"btn-primary px-6 py-2\"\n          >\n            Save Changes\n          </button>\n        </div>\n      </div>\n    </div>\n  )\n}\n", "'use client'\n\nimport { useState, useEffect, useCallback } from 'react'\nimport { useVideoCallStore } from '@/lib/store'\nimport { rtcManager } from '@/lib/rtc'\nimport { SettingsModal } from './SettingsModal'\nimport {\n  Mic,\n  MicOff,\n  Video,\n  VideoOff,\n  Monitor,\n  MonitorOff,\n  Settings as SettingsIcon,\n  ChevronUp,\n  Camera,\n  Headphones\n} from 'lucide-react'\n\ninterface MediaDevice {\n  deviceId: string\n  label: string\n  kind: MediaDeviceKind\n}\n\nexport function VideoControls() {\n  const [isScreenSharingState, setIsScreenSharing] = useState(false)\n  const [notification, setNotification] = useState<string | null>(null)\n  const [showCameraDropdown, setShowCameraDropdown] = useState(false)\n  const [showMicDropdown, setShowMicDropdown] = useState(false)\n  const [showSettingsModal, setShowSettingsModal] = useState(false)\n  const [devices, setDevices] = useState<MediaDevice[]>([])\n  const [selectedCamera, setSelectedCamera] = useState('')\n  const [selectedMicrophone, setSelectedMicrophone] = useState('')\n\n  const {\n    toggleAudio,\n    toggleVideo,\n    isAudioMuted,\n    isVideoMuted,\n\n    setLocalStream\n  } = useVideoCallStore()\n\n  // Load available devices\n  const loadDevices = useCallback(async () => {\n    try {\n      // Request permissions first to get non-empty device labels\n      await navigator.mediaDevices.getUserMedia({ audio: true, video: true })\n      const deviceList = await navigator.mediaDevices.enumerateDevices()\n      \n      const mediaDevices: MediaDevice[] = deviceList\n        .filter(device => device.kind === 'audioinput' || device.kind === 'videoinput')\n        .map(device => ({\n          deviceId: device.deviceId,\n          label: device.label || `${device.kind} ${device.deviceId.slice(0, 8)}`,\n          kind: device.kind as MediaDeviceKind\n        }))\n        \n      setDevices(mediaDevices)\n\n      // Set default devices if not already set\n      if (!selectedCamera) {\n        const defaultCamera = mediaDevices.find(d => d.kind === 'videoinput')\n        if (defaultCamera) setSelectedCamera(defaultCamera.deviceId)\n      }\n      \n      if (!selectedMicrophone) {\n        const defaultMic = mediaDevices.find(d => d.kind === 'audioinput')\n        if (defaultMic) setSelectedMicrophone(defaultMic.deviceId)\n      }\n    } catch (error) {\n      console.error('Error loading devices:', error)\n    }\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [selectedCamera, selectedMicrophone])\n\n  useEffect(() => {\n    loadDevices()\n    \n    // Add event listener for device changes\n    navigator.mediaDevices.addEventListener('devicechange', loadDevices)\n    \n    return () => {\n      navigator.mediaDevices.removeEventListener('devicechange', loadDevices)\n    }\n  }, [loadDevices])\n\n  const showNotification = (message: string) => {\n    setNotification(message)\n    setTimeout(() => setNotification(null), 2000)\n  }\n\n  const handleCameraChange = async (deviceId: string) => {\n    try {\n      setSelectedCamera(deviceId)\n      const constraints = {\n        video: {\n          deviceId: { exact: deviceId },\n          width: { ideal: 1280 },\n          height: { ideal: 720 }\n        },\n        audio: false\n      }\n      const stream = await navigator.mediaDevices.getUserMedia(constraints)\n\n      // Update the local stream in the store\n      setLocalStream(stream)\n\n      // Replace the video track in RTC connections\n      await rtcManager.replaceVideoTrack(stream)\n\n      setShowCameraDropdown(false)\n      showNotification('Camera changed successfully')\n    } catch (error) {\n      console.error('Error changing camera:', error)\n      showNotification('Failed to change camera')\n    }\n  }\n\n  const handleMicrophoneChange = async (deviceId: string) => {\n    try {\n      setSelectedMicrophone(deviceId)\n      const constraints = {\n        video: false,\n        audio: {\n          deviceId: { exact: deviceId },\n          echoCancellation: true,\n          noiseSuppression: true,\n          autoGainControl: true\n        }\n      }\n      const stream = await navigator.mediaDevices.getUserMedia(constraints)\n\n      // Replace audio track logic would go here\n      // For now, just update the selected device\n\n      setShowMicDropdown(false)\n      showNotification('Microphone changed successfully')\n    } catch (error) {\n      console.error('Error changing microphone:', error)\n      showNotification('Failed to change microphone')\n    }\n  }\n\n  const handleToggleAudio = () => {\n    toggleAudio()\n    showNotification(isAudioMuted ? 'Microphone unmuted' : 'Microphone muted')\n  }\n\n  const handleToggleVideo = () => {\n    toggleVideo()\n    showNotification(isVideoMuted ? 'Camera turned on' : 'Camera turned off')\n  }\n\n  const handleToggleScreenShare = async () => {\n    try {\n      if (!isScreenSharingState) {\n        // Start screen sharing\n        const screenStream = await rtcManager.getDisplayMedia()\n\n        // Replace video track with screen share\n        await rtcManager.replaceVideoTrack(screenStream)\n\n        // Listen for screen share end\n        screenStream.getVideoTracks()[0].onended = () => {\n          handleStopScreenShare()\n        }\n\n        setIsScreenSharing(true)\n        showNotification('Screen sharing started')\n      } else {\n        handleStopScreenShare()\n      }\n    } catch (error) {\n      console.error('Error toggling screen share:', error)\n      showNotification('Failed to toggle screen share')\n    }\n  }\n\n  const handleStopScreenShare = async () => {\n    try {\n      // Get camera stream back\n      const cameraStream = await rtcManager.getUserMedia()\n\n      // Replace screen share with camera\n      rtcManager.replaceVideoTrack(cameraStream)\n\n      setIsScreenSharing(false)\n      showNotification('Screen sharing stopped')\n    } catch (error) {\n      console.error('Error stopping screen share:', error)\n      showNotification('Failed to stop screen share')\n    }\n  }\n\n  const handleCameraSelect = useCallback(async (deviceId: string) => {\n    if (deviceId === selectedCamera) {\n      setShowCameraDropdown(false)\n      return\n    }\n    \n    try {\n      await rtcManager.switchCamera(deviceId)\n      setSelectedCamera(deviceId)\n      setShowCameraDropdown(false)\n      showNotification('Camera changed')\n    } catch (error) {\n      console.error('Error switching camera:', error)\n      showNotification('Failed to switch camera')\n    }\n  }, [selectedCamera])\n\n  const handleMicrophoneSelect = useCallback(async (deviceId: string) => {\n    if (deviceId === selectedMicrophone) {\n      setShowMicDropdown(false)\n      return\n    }\n    \n    try {\n      // For now, we'll just update the selected microphone\n      // The actual device switching will be handled by the RTC manager\n      setSelectedMicrophone(deviceId)\n      setShowMicDropdown(false)\n      showNotification('Microphone changed')\n    } catch (error) {\n      console.error('Error switching microphone:', error)\n      showNotification('Failed to switch microphone')\n    }\n  }, [selectedMicrophone])\n\n  const cameras = devices.filter((d: MediaDevice) => d.kind === 'videoinput')\n  const microphones = devices.filter((d: MediaDevice) => d.kind === 'audioinput')\n\n  return (\n    <div className=\"p-4\">\n      <div className=\"video-controls\">\n        {/* Audio controls with dropdown */}\n        <div className=\"relative\">\n          <div className=\"flex\">\n            <button\n              onClick={handleToggleAudio}\n              className={`control-btn ${isAudioMuted ? 'active' : 'inactive'} rounded-r-none`}\n              title={isAudioMuted ? 'Unmute microphone' : 'Mute microphone'}\n            >\n              {isAudioMuted ? (\n                <MicOff className=\"h-6 w-6\" />\n              ) : (\n                <Mic className=\"h-6 w-6\" />\n              )}\n            </button>\n            {microphones.length > 1 && (\n              <button\n                onClick={() => setShowMicDropdown(!showMicDropdown)}\n                className=\"control-btn inactive rounded-l-none border-l border-white/20 w-8\"\n                title=\"Select microphone\"\n              >\n                <ChevronUp className=\"h-4 w-4\" />\n              </button>\n            )}\n          </div>\n\n          {showMicDropdown && microphones.length > 1 && (\n            <div className=\"absolute bottom-full left-0 mb-2 glass-dark rounded-lg border border-white/20 min-w-48 z-10\">\n              <div className=\"p-2\">\n                <div className=\"text-white text-xs font-medium mb-2 flex items-center gap-2\">\n                  <Headphones className=\"h-3 w-3\" />\n                  Select Microphone\n                </div>\n                {microphones.map((mic: MediaDevice) => (\n                  <div \n                    key={mic.deviceId}\n                    className=\"px-4 py-2 text-white hover:bg-white/10 cursor-pointer flex items-center\"\n                    onClick={() => handleMicrophoneSelect(mic.deviceId)}\n                  >\n                    <span className={selectedMicrophone === mic.deviceId ? 'font-semibold' : ''}>\n                      {mic.label}\n                    </span>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Video controls with dropdown */}\n        <div className=\"relative\">\n          <div className=\"flex\">\n            <button\n              onClick={handleToggleVideo}\n              className={`control-btn ${isVideoMuted ? 'active' : 'inactive'} rounded-r-none`}\n              title={isVideoMuted ? 'Turn on camera' : 'Turn off camera'}\n            >\n              {isVideoMuted ? (\n                <VideoOff className=\"h-6 w-6\" />\n              ) : (\n                <Video className=\"h-6 w-6\" />\n              )}\n            </button>\n            {cameras.length > 1 && (\n              <button\n                onClick={() => setShowCameraDropdown(!showCameraDropdown)}\n                className=\"control-btn inactive rounded-l-none border-l border-white/20 w-8\"\n                title=\"Select camera\"\n              >\n                <ChevronUp className=\"h-4 w-4\" />\n              </button>\n            )}\n          </div>\n\n          {showCameraDropdown && cameras.length > 1 && (\n            <div className=\"absolute bottom-full left-0 mb-2 glass-dark rounded-lg border border-white/20 min-w-48 z-10\">\n              <div className=\"p-2\">\n                <div className=\"text-white text-xs font-medium mb-2 flex items-center gap-2\">\n                  <Camera className=\"h-3 w-3\" />\n                  Select Camera\n                </div>\n                {cameras.map((camera: MediaDevice) => (\n                  <div \n                    key={camera.deviceId}\n                    className=\"px-4 py-2 text-white hover:bg-white/10 cursor-pointer flex items-center\"\n                    onClick={() => handleCameraSelect(camera.deviceId)}\n                  >\n                    <span className={selectedCamera === camera.deviceId ? 'font-semibold' : ''}>\n                      {camera.label}\n                    </span>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Screen share toggle */}\n        <button\n          onClick={handleToggleScreenShare}\n          className={`control-btn ${isScreenSharingState ? 'active' : 'inactive'}`}\n          title={isScreenSharingState ? 'Stop screen sharing' : 'Share screen'}\n        >\n          {isScreenSharingState ? (\n            <MonitorOff className=\"h-6 w-6\" />\n          ) : (\n            <Monitor className=\"h-6 w-6\" />\n          )}\n        </button>\n\n        {/* Settings */}\n        <button\n          onClick={() => setShowSettingsModal(true)}\n          className=\"p-2 rounded-full hover:bg-gray-200 transition-colors\"\n          aria-label=\"Settings\"\n        >\n          <SettingsIcon className=\"w-6 h-6\" />\n        </button>\n      </div>\n\n      {/* Settings Modal */}\n      <SettingsModal\n        isOpen={showSettingsModal}\n        onClose={() => setShowSettingsModal(false)}\n      />\n\n      {/* Notification */}\n      {notification && (\n        <div className=\"fixed bottom-20 left-1/2 transform -translate-x-1/2 glass-dark px-4 py-2 rounded-lg text-white z-50 fade-in\">\n          {notification}\n        </div>\n      )}\n    </div>\n  )\n}\n", "'use client'\n\nimport { useState, useRef, useEffect } from 'react'\nimport { useVideoCallStore } from '@/lib/store'\nimport { socketManager } from '@/lib/socket'\nimport { Send, X, MessageCircle } from 'lucide-react'\n\nexport function Chat() {\n  const [message, setMessage] = useState('')\n  const messagesEndRef = useRef<HTMLDivElement>(null)\n\n  const {\n    messages,\n    currentUser,\n    toggleChat,\n    clearUnreadCount,\n    securitySettings,\n\n    blockUser\n  } = useVideoCallStore()\n\n  const isAdmin = currentUser?.role === 'host' || currentUser?.role === 'co-host'\n\n  // Auto-scroll to bottom when new messages arrive\n  useEffect(() => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })\n  }, [messages])\n\n  // Clear unread count when chat is opened\n  useEffect(() => {\n    clearUnreadCount()\n  }, [clearUnreadCount])\n\n  const handleSendMessage = (e: React.FormEvent) => {\n    e.preventDefault()\n\n    if (!message.trim() || !currentUser) return\n\n    // Spam detection is handled in the store when adding messages\n\n    // Send message via Socket.IO\n    socketManager.sendChatMessage(message.trim(), currentUser.name)\n\n    setMessage('')\n  }\n\n  const handleBlockUser = (userId: string) => {\n    if (isAdmin) {\n      blockUser(userId)\n    }\n  }\n\n  const formatTime = (date: Date) => {\n    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })\n  }\n\n  return (\n    <div className=\"chat-container flex flex-col h-full\">\n      {/* Chat header */}\n      <div className=\"flex items-center justify-between p-3 border-b border-white/10\">\n        <h3 className=\"text-white font-semibold text-sm flex items-center gap-2\">\n          <MessageCircle className=\"h-4 w-4\" />\n          Chat\n        </h3>\n        <div className=\"flex items-center gap-2\">\n          {messages.length > 0 && (\n            <span className=\"text-white/60 text-xs\">\n              {messages.length} messages\n            </span>\n          )}\n        </div>\n      </div>\n\n      {/* Messages */}\n      <div className=\"flex-1 overflow-y-auto p-3 space-y-3\">\n        {messages.length === 0 ? (\n          <div className=\"text-center text-white/60 py-8\">\n            <div className=\"w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-3\">\n              <Send className=\"h-6 w-6 text-white\" />\n            </div>\n            <p className=\"text-sm font-medium mb-1\">No messages yet</p>\n            <p className=\"text-xs\">Start the conversation!</p>\n          </div>\n        ) : (\n          messages\n            .filter(msg => !msg.isBlocked) // Filter out blocked messages\n            .map((msg) => (\n            <div\n              key={msg.id}\n              className={`flex flex-col ${\n                msg.userId === currentUser?.id ? 'items-end' : 'items-start'\n              } ${msg.isSpam ? 'opacity-50' : ''}`}\n            >\n              <div\n                className={`chat-message max-w-[90%] p-2 relative ${\n                  msg.userId === currentUser?.id\n                    ? 'bg-gradient-to-r from-purple-500 to-pink-500'\n                    : msg.isSpam\n                      ? 'bg-red-500/20 border border-red-500/50'\n                      : 'bg-white/10'\n                }`}\n              >\n                <p className=\"text-white text-xs leading-relaxed\">{msg.message}</p>\n                {msg.isSpam && (\n                  <div className=\"absolute top-0 right-0 bg-red-500 text-white text-xs px-1 rounded-bl\">\n                    SPAM\n                  </div>\n                )}\n              </div>\n              <div className=\"flex items-center space-x-1 mt-1 text-xs text-white/50\">\n                <span className=\"font-medium text-xs\">{msg.userName}</span>\n                <span>•</span>\n                <span className=\"text-xs\">{formatTime(msg.timestamp)}</span>\n                {isAdmin && msg.userId !== currentUser?.id && (\n                  <button\n                    onClick={() => handleBlockUser(msg.userId)}\n                    className=\"text-red-400 hover:text-red-300 text-xs ml-2\"\n                  >\n                    Block\n                  </button>\n                )}\n              </div>\n            </div>\n          ))\n        )}\n        <div ref={messagesEndRef} />\n      </div>\n\n      {/* Message input */}\n      <div className=\"p-3 border-t border-white/10\">\n        <form onSubmit={handleSendMessage} className=\"flex space-x-2\">\n          <input\n            type=\"text\"\n            value={message}\n            onChange={(e) => setMessage(e.target.value)}\n            placeholder=\"Type a message...\"\n            className=\"glass-input flex-1 text-sm py-2\"\n            maxLength={500}\n            autoComplete=\"off\"\n          />\n          <button\n            type=\"submit\"\n            disabled={!message.trim()}\n            className={`p-2 rounded-lg transition-all ${\n              message.trim()\n                ? 'bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white'\n                : 'bg-white/10 text-white/50 cursor-not-allowed'\n            }`}\n          >\n            <Send className=\"h-4 w-4\" />\n          </button>\n        </form>\n      </div>\n    </div>\n  )\n}\n", "'use client'\n\nimport { useState } from 'react'\nimport { useVideoCallStore } from '@/lib/store'\nimport {\n  Mic,\n  MicOff,\n  Video,\n  VideoOff,\n  Monitor,\n  Crown,\n  MoreVertical,\n  Users,\n  UserMinus,\n  UserCheck,\n  Shield,\n  ShieldOff,\n  Hand,\n  Lock,\n  Unlock\n} from 'lucide-react'\n\nexport function ParticipantsList() {\n  const [showAdminMenu, setShowAdminMenu] = useState<string | null>(null)\n\n  const {\n    currentUser,\n    participants,\n    roomLocked,\n    muteParticipant,\n    muteAllParticipants,\n    removeParticipant,\n    promoteToCoHost,\n    demoteFromCoHost,\n    lockRoom,\n    unlockRoom,\n    blockUser\n  } = useVideoCallStore()\n\n  const participantsList = Array.from(participants.values())\n  const allParticipants = currentUser ? [currentUser, ...participantsList] : participantsList\n\n  const getStatusIcon = (participant: any) => {\n    const icons = []\n\n    if (participant.isAudioMuted) {\n      icons.push(<MicOff key=\"mic\" className=\"h-3 w-3 text-red-400\" />)\n    } else {\n      icons.push(<Mic key=\"mic\" className=\"h-3 w-3 text-green-400\" />)\n    }\n\n    if (participant.isVideoMuted) {\n      icons.push(<VideoOff key=\"video\" className=\"h-3 w-3 text-red-400\" />)\n    } else {\n      icons.push(<Video key=\"video\" className=\"h-3 w-3 text-green-400\" />)\n    }\n\n    if (participant.isScreenSharing) {\n      icons.push(<Monitor key=\"screen\" className=\"h-3 w-3 text-blue-400\" />)\n    }\n\n    return icons\n  }\n\n  const isAdmin = currentUser?.role === 'host' || currentUser?.role === 'co-host'\n  const isHost = currentUser?.role === 'host'\n\n  const handleAdminAction = (action: string, participantId: string) => {\n    switch (action) {\n      case 'mute':\n        muteParticipant(participantId)\n        break\n      case 'remove':\n        removeParticipant(participantId)\n        break\n      case 'promote':\n        promoteToCoHost(participantId)\n        break\n      case 'demote':\n        demoteFromCoHost(participantId)\n        break\n      case 'block':\n        blockUser(participantId)\n        break\n    }\n    setShowAdminMenu(null)\n  }\n\n  return (\n    <div className=\"chat-container flex flex-col h-full\">\n      {/* Header */}\n      <div className=\"p-3 border-b border-white/10\">\n        <div className=\"flex items-center justify-between\">\n          <h3 className=\"text-white font-semibold text-sm flex items-center gap-2\">\n            <Users className=\"h-4 w-4\" />\n            Participants ({allParticipants.length})\n            {roomLocked && <Lock className=\"h-3 w-3 text-yellow-400\" />}\n          </h3>\n\n          {isAdmin && (\n            <div className=\"flex items-center gap-1\">\n              <button\n                onClick={muteAllParticipants}\n                className=\"glass-button p-1 text-white/60 hover:text-white\"\n                title=\"Mute all participants\"\n              >\n                <MicOff className=\"h-3 w-3\" />\n              </button>\n\n              {isHost && (\n                <button\n                  onClick={() => roomLocked ? unlockRoom() : lockRoom()}\n                  className=\"glass-button p-1 text-white/60 hover:text-white\"\n                  title={roomLocked ? 'Unlock room' : 'Lock room'}\n                >\n                  {roomLocked ? <Unlock className=\"h-3 w-3\" /> : <Lock className=\"h-3 w-3\" />}\n                </button>\n              )}\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Participants list */}\n      <div className=\"flex-1 overflow-y-auto p-2 space-y-2\">\n        {allParticipants.map((participant) => {\n          const isCurrentUser = participant.id === currentUser?.id\n\n          return (\n            <div\n              key={participant.id}\n              className=\"glass-button p-2 hover:bg-white/20 transition-all relative\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center space-x-2\">\n                  {/* Avatar */}\n                  <div className=\"w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center relative\">\n                    <span className=\"text-white text-xs font-bold\">\n                      {participant.name.charAt(0).toUpperCase()}\n                    </span>\n                    {participant.isHandRaised && (\n                      <div className=\"absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 rounded-full flex items-center justify-center\">\n                        <Hand className=\"h-2 w-2 text-black\" />\n                      </div>\n                    )}\n                  </div>\n\n                  {/* Name and status */}\n                  <div className=\"flex flex-col flex-1 min-w-0\">\n                    <div className=\"flex items-center space-x-1\">\n                      <span className=\"text-white text-xs font-medium truncate\">\n                        {participant.name}\n                        {isCurrentUser && ' (You)'}\n                      </span>\n\n                      {/* Role indicators */}\n                      {participant.role === 'host' && (\n                        <Crown className=\"h-3 w-3 text-yellow-400 flex-shrink-0\" />\n                      )}\n                      {participant.role === 'co-host' && (\n                        <Shield className=\"h-3 w-3 text-blue-400 flex-shrink-0\" />\n                      )}\n                    </div>\n                    <div className=\"flex items-center space-x-1\">\n                      {getStatusIcon(participant)}\n                    </div>\n                  </div>\n                </div>\n\n                {/* Actions */}\n                {!isCurrentUser && isAdmin && (\n                  <div className=\"relative\">\n                    <button\n                      onClick={() => setShowAdminMenu(showAdminMenu === participant.id ? null : participant.id)}\n                      className=\"glass-button p-1 text-white/60 hover:text-white\"\n                    >\n                      <MoreVertical className=\"h-3 w-3\" />\n                    </button>\n\n                    {/* Admin Menu */}\n                    {showAdminMenu === participant.id && (\n                      <div className=\"absolute right-0 top-full mt-1 glass-dark rounded-lg border border-white/20 min-w-32 z-10\">\n                        <div className=\"p-1\">\n                          <button\n                            onClick={() => handleAdminAction('mute', participant.id)}\n                            className=\"w-full p-2 text-left text-white text-xs hover:bg-white/10 rounded flex items-center gap-2\"\n                          >\n                            <MicOff className=\"h-3 w-3\" />\n                            Mute\n                          </button>\n\n                          {isHost && participant.role === 'participant' && (\n                            <button\n                              onClick={() => handleAdminAction('promote', participant.id)}\n                              className=\"w-full p-2 text-left text-white text-xs hover:bg-white/10 rounded flex items-center gap-2\"\n                            >\n                              <Shield className=\"h-3 w-3\" />\n                              Make Co-Host\n                            </button>\n                          )}\n\n                          {isHost && participant.role === 'co-host' && (\n                            <button\n                              onClick={() => handleAdminAction('demote', participant.id)}\n                              className=\"w-full p-2 text-left text-white text-xs hover:bg-white/10 rounded flex items-center gap-2\"\n                            >\n                              <ShieldOff className=\"h-3 w-3\" />\n                              Remove Co-Host\n                            </button>\n                          )}\n\n                          <button\n                            onClick={() => handleAdminAction('block', participant.id)}\n                            className=\"w-full p-2 text-left text-red-400 text-xs hover:bg-white/10 rounded flex items-center gap-2\"\n                          >\n                            <UserMinus className=\"h-3 w-3\" />\n                            Block User\n                          </button>\n\n                          <button\n                            onClick={() => handleAdminAction('remove', participant.id)}\n                            className=\"w-full p-2 text-left text-red-400 text-xs hover:bg-white/10 rounded flex items-center gap-2\"\n                          >\n                            <UserMinus className=\"h-3 w-3\" />\n                            Remove\n                          </button>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                )}\n              </div>\n            </div>\n          )\n        })}\n      </div>\n    </div>\n  )\n}\n", "'use client'\n\nimport { useEffect, useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useVideoCallStore } from '@/lib/store'\nimport { socketManager } from '@/lib/socket'\nimport { rtcManager } from '@/lib/rtc'\nimport { VideoGrid } from './VideoGrid'\nimport { VideoControls } from './VideoControls'\nimport { Chat } from './Chat'\nimport { ParticipantsList } from './ParticipantsList'\nimport { SettingsModal } from './SettingsModal'\nimport {\n  Users,\n  Settings,\n  PhoneOff,\n  Copy,\n  Share,\n  Maximize2,\n  Minimize2\n} from 'lucide-react'\n\ninterface VideoCallRoomProps {\n  roomId: string\n}\n\nexport function VideoCallRoom({ roomId }: VideoCallRoomProps) {\n  const router = useRouter()\n  const [isInitialized, setIsInitialized] = useState(false)\n\n  const [showSettings, setShowSettings] = useState(false)\n  const [isFullscreen, setIsFullscreen] = useState(false)\n  const [notification, setNotification] = useState<string | null>(null)\n\n  const {\n    currentUser,\n    participants,\n    setConnected,\n    addParticipant,\n    removeParticipant,\n    updateParticipant,\n    setLocalStream,\n    addMessage,\n    reset\n  } = useVideoCallStore()\n\n  // Show notification\n  const showNotification = (message: string) => {\n    setNotification(message)\n    setTimeout(() => setNotification(null), 3000)\n  }\n\n  // Initialize WebRTC and Socket.IO\n  useEffect(() => {\n    const initializeCall = async () => {\n      try {\n        // Connect to Socket.IO server\n        const socket = socketManager.connect()\n\n        if (!socket) {\n          throw new Error('Failed to connect to server')\n        }\n\n        // Get user media\n        const stream = await rtcManager.getUserMedia()\n        setLocalStream(stream)\n\n        // Set up RTC signal callback\n        rtcManager.setSendSignalCallback((userId, signal) => {\n          socketManager.sendSignal(userId, signal)\n        })\n\n        // Set up RTC stream callback\n        rtcManager.onStream((userId, stream) => {\n          updateParticipant(userId, { stream })\n        })\n\n        // Set up RTC disconnect callback\n        rtcManager.onUserDisconnected((userId) => {\n          removeParticipant(userId)\n          showNotification('A participant has left the meeting')\n        })\n\n        // Join the room\n        if (currentUser) {\n          socketManager.joinRoom(roomId, currentUser.id, currentUser.name)\n        }\n\n        setIsInitialized(true)\n        setConnected(true)\n        showNotification('Successfully connected to meeting')\n\n      } catch (error) {\n        console.error('Failed to initialize call:', error)\n        showNotification('Failed to join meeting. Please check your permissions.')\n      }\n    }\n\n    if (currentUser && !isInitialized) {\n      initializeCall()\n    }\n\n    return () => {\n      // Cleanup on unmount\n      if (isInitialized) {\n        socketManager.leaveRoom()\n        rtcManager.cleanup()\n        reset()\n      }\n    }\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [currentUser, roomId, isInitialized])\n\n  // Set up Socket.IO event listeners\n  useEffect(() => {\n    if (!isInitialized) return\n\n    const socket = socketManager.getSocket()\n    if (!socket) return\n\n    // Handle new user joined\n    const handleUserJoined = async (data: { userId: string, userName: string }) => {\n      console.log('User joined:', data)\n\n      addParticipant({\n        id: data.userId,\n        name: data.userName,\n        isAudioMuted: false,\n        isVideoMuted: false,\n        isScreenSharing: false,\n        role: 'participant',\n        isHandRaised: false,\n        joinedAt: new Date(),\n        lastActivity: new Date()\n      })\n\n      // Create offer for new user\n      try {\n        const offer = await rtcManager.createOffer(data.userId)\n        socketManager.sendSignal(data.userId, {\n          type: 'offer',\n          offer\n        })\n      } catch (error) {\n        console.error('Error creating offer:', error)\n      }\n\n      showNotification(`${data.userName} joined the meeting`)\n    }\n\n    // Handle user left\n    const handleUserLeft = (data: { userId: string, userName: string }) => {\n      console.log('User left:', data)\n      removeParticipant(data.userId)\n      rtcManager.removePeerConnection(data.userId)\n      showNotification(`${data.userName} left the meeting`)\n    }\n\n    // Handle WebRTC signals\n    const handleSignal = async (data: { fromUserId: string, signal: any }) => {\n      console.log('Received signal:', data)\n\n      try {\n        const { fromUserId, signal } = data\n\n        switch (signal.type) {\n          case 'offer':\n            const answer = await rtcManager.createAnswer(fromUserId, signal.offer)\n            socketManager.sendSignal(fromUserId, {\n              type: 'answer',\n              answer\n            })\n            break\n\n          case 'answer':\n            await rtcManager.handleAnswer(fromUserId, signal.answer)\n            break\n\n          case 'ice-candidate':\n            await rtcManager.handleIceCandidate(fromUserId, signal.candidate)\n            break\n        }\n      } catch (error) {\n        console.error('Error handling signal:', error)\n      }\n    }\n\n    // Handle chat messages\n    const handleChatMessage = (data: { userId: string, userName: string, message: string, timestamp: string }) => {\n      addMessage({\n        id: Math.random().toString(36).substring(2, 15),\n        userId: data.userId,\n        userName: data.userName,\n        message: data.message,\n        timestamp: new Date(data.timestamp)\n      })\n    }\n\n    // Register event listeners\n    socket.on('user-joined', handleUserJoined)\n    socket.on('user-left', handleUserLeft)\n    socket.on('signal', handleSignal)\n    socket.on('chat-message', handleChatMessage)\n\n    return () => {\n      socket.off('user-joined', handleUserJoined)\n      socket.off('user-left', handleUserLeft)\n      socket.off('signal', handleSignal)\n      socket.off('chat-message', handleChatMessage)\n    }\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [isInitialized])\n\n  const [showLeaveConfirm, setShowLeaveConfirm] = useState(false)\n\n  const handleLeaveCall = () => {\n    setShowLeaveConfirm(true)\n  }\n\n  const confirmLeave = () => {\n    socketManager.leaveRoom()\n    rtcManager.cleanup()\n    reset()\n    router.push('/')\n  }\n\n  const cancelLeave = () => {\n    setShowLeaveConfirm(false)\n  }\n\n  const copyMeetingLink = () => {\n    const meetingLink = `${window.location.origin}/room/${roomId}`\n    navigator.clipboard.writeText(meetingLink)\n    showNotification('Meeting link copied to clipboard!')\n  }\n\n  const shareMeetingId = () => {\n    navigator.clipboard.writeText(roomId)\n    showNotification('Meeting ID copied to clipboard!')\n  }\n\n  const toggleFullscreen = () => {\n    if (!document.fullscreenElement) {\n      document.documentElement.requestFullscreen()\n      setIsFullscreen(true)\n    } else {\n      document.exitFullscreen()\n      setIsFullscreen(false)\n    }\n  }\n\n  if (!isInitialized || !currentUser) {\n    return (\n      <>\n        <div className=\"animated-bg\"></div>\n        <div className=\"min-h-screen flex items-center justify-center relative z-10\">\n          <div className=\"glass p-8 text-center\">\n            <div className=\"animate-spin rounded-full h-16 w-16 border-4 border-white/30 border-t-white mx-auto mb-6\"></div>\n            <h2 className=\"text-2xl font-semibold text-white mb-2\">Connecting to Meeting</h2>\n            <p className=\"text-white/70\">Please wait while we set up your video call...</p>\n          </div>\n        </div>\n      </>\n    )\n  }\n\n  return (\n    <>\n      {/* Animated Background */}\n      <div className=\"animated-bg\"></div>\n\n      <div className=\"min-h-screen flex flex-col relative z-10 pt-20\">\n        {/* Header - Fixed at top */}\n        <div className=\"glass-dark p-4 fixed top-0 left-0 right-0 z-50\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-4\">\n              <h1 className=\"text-white text-xl font-semibold\">\n                StreamIt Pro Meeting\n              </h1>\n              <div className=\"flex items-center space-x-2 text-white/70\">\n                <Users className=\"h-4 w-4\" />\n                <span>{participants.size + 1} participants</span>\n              </div>\n              <div className=\"text-white/50 text-sm\">\n                ID: {roomId}\n              </div>\n            </div>\n\n            <div className=\"flex items-center space-x-2\">\n              <button\n                onClick={copyMeetingLink}\n                className=\"glass-button p-2 text-white hover:text-purple-300\"\n                title=\"Copy meeting link\"\n              >\n                <Share className=\"h-5 w-5\" />\n              </button>\n\n              <button\n                onClick={shareMeetingId}\n                className=\"glass-button p-2 text-white hover:text-purple-300\"\n                title=\"Copy meeting ID\"\n              >\n                <Copy className=\"h-5 w-5\" />\n              </button>\n\n              <button\n                onClick={() => setShowSettings(!showSettings)}\n                className={`glass-button p-2 text-white hover:text-purple-300 ${showSettings ? 'bg-purple-500/30' : ''}`}\n                title=\"Settings\"\n              >\n                <Settings className=\"h-5 w-5\" />\n              </button>\n\n              <button\n                onClick={toggleFullscreen}\n                className=\"glass-button p-2 text-white hover:text-purple-300\"\n                title=\"Toggle fullscreen\"\n              >\n                {isFullscreen ? <Minimize2 className=\"h-5 w-5\" /> : <Maximize2 className=\"h-5 w-5\" />}\n              </button>\n\n              <button\n                onClick={handleLeaveCall}\n                className=\"bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors\"\n                title=\"Leave meeting\"\n              >\n                <PhoneOff className=\"h-5 w-5\" />\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Main content - Add padding top to account for fixed header */}\n        <div className=\"flex-1 flex p-4 pt-20 gap-4 mt-4\">\n          {/* Video area - Made smaller */}\n          <div className=\"w-2/3 flex flex-col\">\n            <VideoGrid />\n            <VideoControls />\n          </div>\n\n          {/* Right Sidebar - Always visible */}\n          <div className=\"w-1/3 flex flex-col gap-4\">\n            {/* Participants Panel */}\n            <div className=\"h-1/2\">\n              <ParticipantsList />\n            </div>\n\n            {/* Chat Panel */}\n            <div className=\"h-1/2\">\n              <Chat />\n            </div>\n          </div>\n        </div>\n\n        {/* Settings Modal */}\n        <SettingsModal\n          isOpen={showSettings}\n          onClose={() => setShowSettings(false)}\n        />\n\n        {/* Leave Meeting Confirmation Modal */}\n        {showLeaveConfirm && (\n          <div className=\"fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4\">\n            <div className=\"glass p-6 rounded-lg max-w-md w-full\">\n              <h3 className=\"text-xl font-semibold text-white mb-4\">Leave Meeting?</h3>\n              <p className=\"text-white/80 mb-6\">Are you sure you want to leave the meeting?</p>\n              <div className=\"flex justify-end space-x-3\">\n                <button\n                  onClick={cancelLeave}\n                  className=\"px-4 py-2 rounded-lg bg-gray-600 text-white hover:bg-gray-500 transition-colors\"\n                >\n                  Cancel\n                </button>\n                <button\n                  onClick={confirmLeave}\n                  className=\"px-4 py-2 rounded-lg bg-red-600 text-white hover:bg-red-500 transition-colors\"\n                >\n                  Leave\n                </button>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Notification */}\n        {notification && (\n          <div className=\"fixed top-20 right-4 glass p-4 text-white z-50 fade-in rounded-lg shadow-lg\">\n            {notification}\n          </div>\n        )}\n      </div>\n    </>\n  )\n}\n", "'use client'\n\nimport { useEffect, useState, useCallback } from 'react'\nimport { usePara<PERSON>, useRouter } from 'next/navigation'\nimport { VideoCallRoom } from '@/components/VideoCall/VideoCallRoom'\nimport { useVideoCallStore } from '@/lib/store'\n\n\nexport default function RoomPage() {\n  const params = useParams()\n  const router = useRouter()\n  const roomId = params.id as string\n  const [userName, setUserName] = useState('')\n  const [isJoining, setIsJoining] = useState(false)\n  const [hasJoined, setHasJoined] = useState(false)\n  \n  const { setRoomId, setCurrentUser } = useVideoCallStore()\n\n  const handleJoinRoom = useCallback(async (name: string) => {\n    if (!name.trim()) {\n      alert('Please enter your name')\n      return\n    }\n\n    setIsJoining(true)\n    \n    try {\n      // Set room ID in store\n      setRoomId(roomId)\n      \n      // Create user object\n      const user = {\n        id: Math.random().toString(36).substring(2, 15),\n        name: name.trim(),\n        isAudioMuted: false,\n        isVideoMuted: false,\n        isScreenSharing: false,\n        role: 'host' as const, // First user is always host\n        isHandRaised: false,\n        joinedAt: new Date(),\n        lastActivity: new Date()\n      }\n      \n      // Set current user in store\n      setCurrentUser(user)\n      \n      // Store user name for future sessions\n      localStorage.setItem('userName', name.trim())\n      \n      setHasJoined(true)\n    } catch (error) {\n      console.error('Error joining room:', error)\n      alert('Failed to join room. Please try again.')\n    } finally {\n      setIsJoining(false)\n    }\n  }, [roomId, setCurrentUser, setRoomId])\n\n  useEffect(() => {\n    // Check if user name is already stored\n    const storedName = localStorage.getItem('userName')\n    if (storedName) {\n      setUserName(storedName)\n      handleJoinRoom(storedName)\n    }\n  }, [handleJoinRoom])\n\n  const handleNameSubmit = (e: React.FormEvent) => {\n    e.preventDefault()\n    handleJoinRoom(userName)\n  }\n\n  if (!hasJoined) {\n    return (\n      <>\n        <div className=\"animated-bg\"></div>\n        <div className=\"min-h-screen flex items-center justify-center p-4 relative z-10\">\n          <div className=\"glass p-8 max-w-md w-full\">\n            <div className=\"text-center mb-6\">\n              <h2 className=\"text-2xl font-bold text-white mb-2\">Join Meeting</h2>\n              <p className=\"text-white/70\">\n                Meeting ID: <span className=\"font-mono font-semibold text-purple-300\">{roomId}</span>\n              </p>\n            </div>\n\n            <form onSubmit={handleNameSubmit} className=\"space-y-4\">\n              <input\n                type=\"text\"\n                placeholder=\"Enter your name\"\n                value={userName}\n                onChange={(e) => setUserName(e.target.value)}\n                disabled={isJoining}\n                autoFocus\n                className=\"glass-input w-full\"\n              />\n\n              <div className=\"flex gap-3\">\n                <button\n                  type=\"button\"\n                  onClick={() => router.push('/')}\n                  disabled={isJoining}\n                  className=\"btn-secondary flex-1\"\n                >\n                  Back\n                </button>\n                <button\n                  type=\"submit\"\n                  disabled={isJoining || !userName.trim()}\n                  className=\"btn-primary flex-1\"\n                >\n                  {isJoining ? 'Joining...' : 'Join Meeting'}\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      </>\n    )\n  }\n\n  return <VideoCallRoom roomId={roomId} />\n}\n"], "names": ["Promise", "resolve", "then", "__webpack_require__", "bind", "initialState", "roomId", "isConnected", "roomLocked", "currentUser", "participants", "Map", "localStream", "isAudioMuted", "isVideoMuted", "isScreenSharing", "messages", "unreadCount", "messageHistory", "isChatOpen", "isSettingsOpen", "securitySettings", "encryptionEnabled", "antiSpamEnabled", "maxMessagesPerMinute", "allowScreenShare", "allowFileSharing", "requireApprovalToJoin", "adminControls", "canMuteAll", "canMuteParticipant", "canRemoveParticipant", "canControlCamera", "canManageRoles", "blockedUsers", "Set", "spamDetection", "useVideoCallStore", "create", "devtools", "set", "get", "setRoomId", "setConnected", "setCurrentUser", "addParticipant", "participant", "id", "removeParticipant", "delete", "participantId", "updateParticipant", "updates", "setLocalStream", "toggleAudio", "getAudioTracks", "for<PERSON>ach", "track", "enabled", "toggleVideo", "getVideoTracks", "toggleScreenShare", "state", "addMessage", "now", "Date", "userSpam", "message", "userId", "count", "last<PERSON><PERSON>t", "isSpam", "clearUnreadCount", "toggleChat", "toggleSettings", "muteParticipant", "muteAllParticipants", "_", "unmuteAllParticipants", "promoteToCoHost", "role", "demoteFromCoHost", "blockUser", "add", "unblockUser", "lockRoom", "unlockRoom", "updateSecuritySettings", "settings", "reset", "name", "SocketManager", "connect", "_this_socket", "socket", "connected", "io", "window", "location", "origin", "transports", "upgrade", "on", "console", "log", "error", "disconnect", "joinRoom", "userName", "emit", "leaveRoom", "sendSignal", "targetUserId", "signal", "sendChatMessage", "timestamp", "toISOString", "event", "callback", "off", "getSocket", "socketManager", "RTC_CONFIG", "iceServers", "urls", "iceCandidatePoolSize", "bundlePolicy", "rtcpMuxPolicy", "VIDEO_CONSTRAINTS", "video", "width", "ideal", "max", "height", "frameRate", "facingMode", "audio", "echoCancellation", "noiseSuppression", "autoGainControl", "sampleRate", "SCREEN_SHARE_CONSTRAINTS", "RTCManager", "setupEventHandlers", "addEventListener", "cleanup", "getUserMedia", "constraints", "navigator", "mediaDevices", "cameraStream", "videoTracks", "length", "currentVideoDeviceId", "getSettings", "deviceId", "audioTracks", "currentAudioDeviceId", "getDisplayMedia", "screenStream", "onended", "restoreVideoTrack", "replaceAudioTrack", "stream", "stop", "audioTrack", "addTrack", "updateAudioTracks", "videoTrack", "clone", "updateVideoTracks", "getTracks", "peerConnections", "pc", "sender", "getSenders", "find", "s", "kind", "replaceTrack", "switchCamera", "exact", "createPeerConnection", "RTCPeerConnection", "ontrack", "remoteStream", "streams", "onStreamCallback", "onicecandidate", "candidate", "type", "onconnectionstatechange", "concat", "connectionState", "removePeerConnection", "onUserDisconnectedCallback", "createOffer", "offer", "offerToReceiveAudio", "offerToReceiveVideo", "setLocalDescription", "createAnswer", "setRemoteDescription", "answer", "handleAnswer", "handleIceCandidate", "addIceCandidate", "close", "replaceVideoTrack", "newStream", "onStream", "onUserDisconnected", "setSendSignalCallback", "clear", "getLocalStream", "getPeerConnection", "constructor", "rtcManager", "VideoTile", "param", "isLocal", "isFeatured", "videoRef", "useRef", "useEffect", "current", "srcObject", "hasVideo", "hasAudio", "jsx_runtime", "jsxs", "div", "className", "jsx", "ref", "autoPlay", "playsInline", "muted", "span", "char<PERSON>t", "toUpperCase", "p", "Crown", "Mic", "<PERSON><PERSON><PERSON><PERSON>", "Video", "VideoOff", "Monitor", "VideoGrid", "participantsList", "Array", "from", "values", "totalParticipants", "getGridRows", "map", "index", "SettingsModal", "cameras", "microphones", "isOpen", "onClose", "activeTab", "setActiveTab", "useState", "devices", "setDevices", "selectedCamera", "setSelectedCamera", "selectedMicrophone", "setSelectedMicrophone", "selectedSpeaker", "setSelectedSpeaker", "backgroundBlur", "setBackgroundBlur", "selectedBackground", "setSelectedBackground", "videoQuality", "setVideoQuality", "audioQuality", "setAudioQuality", "setNoiseSuppression", "setEchoCancellation", "setAutoGainControl", "setEncryptionEnabled", "setAntiSpamEnabled", "showCameraDropdown", "setShowCameraDropdown", "showMicDropdown", "setShowMicDropdown", "loadDevices", "deviceList", "enumerateDevices", "device", "label", "slice", "defaultCamera", "d", "defaultMic", "defaultSpeaker", "handleCameraChange", "handleMicrophoneChange", "applyBackgroundEffect", "backgroundId", "applyVideoQuality", "quality", "filter", "h2", "Settings", "button", "onClick", "X", "icon", "Camera", "Image", "Shield", "tab", "h3", "c", "ChevronDown", "camera", "Check", "videoQualityOptions", "resolution", "option", "m", "mic", "h4", "input", "checked", "onChange", "e", "target", "backgroundOptions", "preview", "bg", "value", "placeholder", "select", "VideoControls", "isScreenSharingState", "setIsScreenSharing", "notification", "setNotification", "showSettingsModal", "setShowSettingsModal", "useCallback", "removeEventListener", "showNotification", "setTimeout", "handleToggleScreenShare", "handleStopScreenShare", "handleCameraSelect", "handleMicrophoneSelect", "title", "ChevronUp", "Headphones", "MonitorOff", "aria-label", "SettingsIcon", "Cha<PERSON>", "setMessage", "messagesEndRef", "isAdmin", "scrollIntoView", "behavior", "handleBlockUser", "formatTime", "date", "toLocaleTimeString", "hour", "minute", "MessageCircle", "Send", "msg", "isBlocked", "form", "onSubmit", "preventDefault", "trim", "max<PERSON><PERSON><PERSON>", "autoComplete", "disabled", "ParticipantsList", "showAdminMenu", "setShowAdminMenu", "allParticipants", "getStatusIcon", "icons", "push", "isHost", "handleAdminAction", "action", "Users", "Lock", "Unlock", "isCurrentUser", "isHandRaised", "Hand", "MoreVertical", "ShieldOff", "UserMinus", "VideoCallRoom", "router", "useRouter", "isInitialized", "setIsInitialized", "showSettings", "setShowSettings", "isFullscreen", "setIsFullscreen", "initializeCall", "handleUserJoined", "data", "joinedAt", "lastActivity", "handleUserLeft", "handleSignal", "fromUserId", "handleChatMessage", "Math", "random", "toString", "substring", "showLeaveConfirm", "setShowLeaveConfirm", "Fragment", "h1", "size", "meetingLink", "clipboard", "writeText", "Share", "Copy", "document", "fullscreenElement", "exitFullscreen", "documentElement", "requestFullscreen", "Minimize2", "Maximize2", "PhoneOff", "RoomPage", "params", "useParams", "setUserName", "isJoining", "setIsJoining", "hasJoined", "setHasJoined", "handleJoinRoom", "alert", "user", "localStorage", "setItem", "storedName", "getItem", "autoFocus"], "sourceRoot": ""}