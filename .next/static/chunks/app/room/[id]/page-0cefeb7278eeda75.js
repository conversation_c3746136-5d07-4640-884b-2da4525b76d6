(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[786],{9256:function(e,t,s){Promise.resolve().then(s.bind(s,456))},456:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return X}});var a=s(7437),i=s(2265),l=s(6463),n=s(6936),r=s(5040);class c{connect(){var e;return(null===(e=this.socket)||void 0===e?void 0:e.connected)||(this.socket=(0,r.io)(window.location.origin,{transports:["websocket","polling"],upgrade:!0}),this.socket.on("connect",()=>{var e;console.log("Connected to server:",null===(e=this.socket)||void 0===e?void 0:e.id)}),this.socket.on("disconnect",()=>{console.log("Disconnected from server")}),this.socket.on("connect_error",e=>{console.error("Connection error:",e)})),this.socket}disconnect(){this.socket&&(this.socket.disconnect(),this.socket=null)}joinRoom(e,t,s){this.socket&&(this.roomId=e,this.socket.emit("join-room",{roomId:e,userId:t,userName:s}))}leaveRoom(){this.socket&&this.roomId&&(this.socket.emit("leave-room",{roomId:this.roomId}),this.roomId=null)}sendSignal(e,t){this.socket&&this.roomId&&this.socket.emit("signal",{roomId:this.roomId,targetUserId:e,signal:t})}sendChatMessage(e,t){this.socket&&this.roomId&&this.socket.emit("chat-message",{roomId:this.roomId,message:e,userName:t,timestamp:new Date().toISOString()})}on(e,t){this.socket&&this.socket.on(e,t)}off(e,t){this.socket&&this.socket.off(e,t)}getSocket(){return this.socket}isConnected(){var e;return(null===(e=this.socket)||void 0===e?void 0:e.connected)||!1}constructor(){this.socket=null,this.roomId=null}}let o=new c,d={iceServers:[{urls:"stun:stun.l.google.com:19302"},{urls:"stun:stun1.l.google.com:19302"}],iceCandidatePoolSize:10,bundlePolicy:"max-bundle",rtcpMuxPolicy:"require"},h={video:{width:{ideal:1280,max:1920},height:{ideal:720,max:1080},frameRate:{ideal:30,max:60},facingMode:"user"},audio:{echoCancellation:!0,noiseSuppression:!0,autoGainControl:!0,sampleRate:48e3}},m={video:{width:{ideal:1920,max:3840},height:{ideal:1080,max:2160},frameRate:{ideal:30,max:60}},audio:!0};class u{setupEventHandlers(){window.addEventListener("beforeunload",()=>{this.cleanup()})}async getUserMedia(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:h;try{this.localStream=await navigator.mediaDevices.getUserMedia(e),this.cameraStream=this.localStream;let t=this.localStream.getVideoTracks();t.length>0&&(this.currentVideoDeviceId=t[0].getSettings().deviceId||null);let s=this.localStream.getAudioTracks();return s.length>0&&(this.currentAudioDeviceId=s[0].getSettings().deviceId||null),this.localStream}catch(e){throw console.error("Error accessing media devices:",e),e}}async getDisplayMedia(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:m;try{return this.screenStream=await navigator.mediaDevices.getDisplayMedia(e),this.screenStream.getVideoTracks()[0].onended=()=>{this.restoreVideoTrack()},this.screenStream}catch(e){throw console.error("Error accessing screen share:",e),e}}replaceAudioTrack(e){if(!this.localStream)return;this.localStream.getAudioTracks().forEach(e=>e.stop());let t=e.getAudioTracks()[0];t&&this.localStream.addTrack(t),this.updateAudioTracks()}restoreVideoTrack(){if(!this.localStream||!this.cameraStream)return;this.localStream.getVideoTracks().forEach(e=>e.stop());let e=this.cameraStream.getVideoTracks()[0];e&&this.localStream.addTrack(e.clone()),this.updateVideoTracks(),this.screenStream&&(this.screenStream.getTracks().forEach(e=>e.stop()),this.screenStream=null)}updateVideoTracks(){if(!this.localStream)return;let e=this.localStream.getVideoTracks()[0];e&&this.peerConnections.forEach(t=>{let s=t.getSenders().find(e=>{var t;return(null===(t=e.track)||void 0===t?void 0:t.kind)==="video"});s&&s.replaceTrack(e)})}updateAudioTracks(){if(!this.localStream)return;let e=this.localStream.getAudioTracks()[0];e&&this.peerConnections.forEach(t=>{let s=t.getSenders().find(e=>{var t;return(null===(t=e.track)||void 0===t?void 0:t.kind)==="audio"});s&&s.replaceTrack(e)})}async switchCamera(e){if(this.localStream)try{let t=await navigator.mediaDevices.getUserMedia({video:{deviceId:{exact:e}},audio:!this.currentAudioDeviceId||{deviceId:{exact:this.currentAudioDeviceId}}});return this.localStream.getTracks().forEach(e=>e.stop()),this.localStream=t,this.cameraStream=t,this.currentVideoDeviceId=e,this.updateVideoTracks(),t}catch(e){throw console.error("Error switching camera:",e),Error("Failed to switch camera")}}createPeerConnection(e){let t=new RTCPeerConnection(d);return this.localStream&&this.localStream.getTracks().forEach(e=>{t.addTrack(e,this.localStream)}),t.ontrack=t=>{let[s]=t.streams;this.onStreamCallback&&this.onStreamCallback(e,s)},t.onicecandidate=t=>{t.candidate&&this.sendSignal(e,{type:"ice-candidate",candidate:t.candidate})},t.onconnectionstatechange=()=>{console.log("Connection state for ".concat(e,":"),t.connectionState),("disconnected"===t.connectionState||"failed"===t.connectionState)&&(this.removePeerConnection(e),this.onUserDisconnectedCallback&&this.onUserDisconnectedCallback(e))},this.peerConnections.set(e,t),t}async createOffer(e){let t=this.peerConnections.get(e)||this.createPeerConnection(e),s=await t.createOffer({offerToReceiveAudio:!0,offerToReceiveVideo:!0});return await t.setLocalDescription(s),s}async createAnswer(e,t){let s=this.peerConnections.get(e)||this.createPeerConnection(e);await s.setRemoteDescription(t);let a=await s.createAnswer();return await s.setLocalDescription(a),a}async handleAnswer(e,t){let s=this.peerConnections.get(e);s&&await s.setRemoteDescription(t)}async handleIceCandidate(e,t){let s=this.peerConnections.get(e);s&&await s.addIceCandidate(t)}removePeerConnection(e){let t=this.peerConnections.get(e);t&&(t.close(),this.peerConnections.delete(e))}replaceVideoTrack(e){let t=e.getVideoTracks()[0];this.peerConnections.forEach(async e=>{let s=e.getSenders().find(e=>e.track&&"video"===e.track.kind);s&&t&&await s.replaceTrack(t)})}onStream(e){this.onStreamCallback=e}onUserDisconnected(e){this.onUserDisconnectedCallback=e}sendSignal(e,t){}setSendSignalCallback(e){this.sendSignal=e}cleanup(){this.peerConnections.forEach(e=>e.close()),this.peerConnections.clear(),this.localStream&&(this.localStream.getTracks().forEach(e=>e.stop()),this.localStream=null)}getLocalStream(){return this.localStream}getPeerConnection(e){return this.peerConnections.get(e)}constructor(){this.peerConnections=new Map,this.localStream=null,this.screenStream=null,this.cameraStream=null,this.currentVideoDeviceId=null,this.currentAudioDeviceId=null,this.setupEventHandlers()}}let x=new u;var p=s(957),g=s(9333),f=s(3977),v=s(8954),w=s(8511),j=s(5302);function b(e){let{participant:t,stream:s,isLocal:l,isFeatured:n=!1}=e,r=(0,i.useRef)(null);(0,i.useEffect)(()=>{r.current&&s&&(r.current.srcObject=s)},[s]);let c=s&&s.getVideoTracks().length>0&&!t.isVideoMuted,o=s&&s.getAudioTracks().length>0&&!t.isAudioMuted;return(0,a.jsxs)("div",{className:"video-container relative overflow-hidden ".concat(n?"w-full h-full":"w-full h-full max-w-sm max-h-64"),children:[c?(0,a.jsx)("video",{ref:r,autoPlay:!0,playsInline:!0,muted:l,className:"w-full h-full object-cover rounded-lg"}):(0,a.jsx)("div",{className:"w-full h-full bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center rounded-lg",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"".concat(n?"w-24 h-24":"w-16 h-16"," bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-3 shadow-lg"),children:(0,a.jsx)("span",{className:"text-white ".concat(n?"text-xl":"text-lg"," font-bold"),children:t.name.charAt(0).toUpperCase()})}),(0,a.jsx)("p",{className:"text-white ".concat(n?"text-lg":"text-sm"," font-medium"),children:t.name}),(0,a.jsx)("p",{className:"text-white/60 text-xs",children:"Camera is off"})]})}),(0,a.jsxs)("div",{className:"absolute bottom-2 left-2 right-2 flex items-center justify-between",children:[(0,a.jsx)("div",{className:"glass-dark px-2 py-1 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[l&&(0,a.jsx)(p.Z,{className:"h-3 w-3 text-yellow-400"}),(0,a.jsx)("span",{className:"text-white text-xs font-medium",children:l?"You":t.name})]})}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)("div",{className:"p-1 rounded-full ".concat(o?"bg-green-500/80":"bg-red-500/80"," backdrop-blur-sm"),children:o?(0,a.jsx)(g.Z,{className:"h-3 w-3 text-white"}):(0,a.jsx)(f.Z,{className:"h-3 w-3 text-white"})}),(0,a.jsx)("div",{className:"p-1 rounded-full ".concat(c?"bg-green-500/80":"bg-red-500/80"," backdrop-blur-sm"),children:c?(0,a.jsx)(v.Z,{className:"h-3 w-3 text-white"}):(0,a.jsx)(w.Z,{className:"h-3 w-3 text-white"})}),t.isScreenSharing&&(0,a.jsx)("div",{className:"p-1 rounded-full bg-blue-500/80 backdrop-blur-sm",children:(0,a.jsx)(j.Z,{className:"h-3 w-3 text-white"})})]})]}),l&&(0,a.jsx)("div",{className:"absolute top-2 left-2",children:(0,a.jsx)("div",{className:"glass-dark px-2 py-1 rounded-lg",children:(0,a.jsxs)("span",{className:"text-white text-xs font-medium flex items-center gap-1",children:[(0,a.jsx)(p.Z,{className:"h-2 w-2 text-yellow-400"}),"Host"]})})}),(0,a.jsx)("div",{className:"absolute top-2 right-2",children:(0,a.jsxs)("div",{className:"flex space-x-0.5",children:[(0,a.jsx)("div",{className:"w-0.5 h-2 bg-green-400 rounded-full"}),(0,a.jsx)("div",{className:"w-0.5 h-3 bg-green-400 rounded-full"}),(0,a.jsx)("div",{className:"w-0.5 h-4 bg-green-400 rounded-full"})]})})]})}function N(){let{currentUser:e,participants:t,localStream:s}=(0,n.g)(),i=Array.from(t.values()),l=i.length+1;return(0,a.jsx)("div",{className:"flex-1 p-4",children:(0,a.jsx)("div",{className:"glass h-full p-4 overflow-hidden",children:(0,a.jsxs)("div",{className:"\n          grid gap-3 h-full\n          ".concat(1===l||2===l?"grid-cols-1":l<=4||l<=6?"grid-cols-2":"grid-cols-3","\n          ").concat(1===l?"grid-rows-1":2===l||l<=4?"grid-rows-2":"grid-rows-3","\n          place-items-center\n        "),children:[e&&(0,a.jsx)(b,{participant:e,stream:s,isLocal:!0,isFeatured:1===l},e.id),i.map((e,t)=>(0,a.jsx)(b,{participant:e,stream:e.stream,isLocal:!1,isFeatured:2===l&&0===t},e.id))]})})})}var k=s(4258),S=s(4697),y=s(233),C=s(8604),I=s(500),M=s(2421),Z=s(2468);function E(e){var t,s;let{isOpen:l,onClose:r}=e,[c,o]=(0,i.useState)("video"),[d,h]=(0,i.useState)([]),[m,u]=(0,i.useState)(""),[p,f]=(0,i.useState)(""),[v,w]=(0,i.useState)(""),[b,N]=(0,i.useState)(!1),[E,T]=(0,i.useState)("none"),[D,A]=(0,i.useState)("hd"),[P,V]=(0,i.useState)("high"),[R,U]=(0,i.useState)(!0),[F,L]=(0,i.useState)(!0),[z,O]=(0,i.useState)(!0),[H,B]=(0,i.useState)(!0),[_,G]=(0,i.useState)(!0),[J,q]=(0,i.useState)(!1),[Y,K]=(0,i.useState)(!1),{currentUser:Q}=(0,n.g)();(0,i.useEffect)(()=>{let e=async()=>{try{let e=(await navigator.mediaDevices.enumerateDevices()).map(e=>({deviceId:e.deviceId,label:e.label||"".concat(e.kind," ").concat(e.deviceId.slice(0,8)),kind:e.kind}));h(e);let t=e.find(e=>"videoinput"===e.kind),s=e.find(e=>"audioinput"===e.kind),a=e.find(e=>"audiooutput"===e.kind);t&&u(t.deviceId),s&&f(s.deviceId),a&&w(a.deviceId)}catch(e){console.error("Error loading devices:",e)}};l&&e()},[l]);let W=async e=>{try{u(e);let t=await navigator.mediaDevices.getUserMedia({video:{deviceId:{exact:e},width:{ideal:1280},height:{ideal:720}},audio:!1});x.replaceVideoTrack(t),q(!1)}catch(e){console.error("Error changing camera:",e)}},X=async e=>{try{f(e),await navigator.mediaDevices.getUserMedia({video:!1,audio:{deviceId:{exact:e},echoCancellation:F,noiseSuppression:R,autoGainControl:z}}),K(!1)}catch(e){console.error("Error changing microphone:",e)}},$=e=>{T(e)},ee=e=>{A(e)};if(!l)return null;let et=d.filter(e=>"videoinput"===e.kind),es=d.filter(e=>"audioinput"===e.kind);return d.filter(e=>"audiooutput"===e.kind),(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:(0,a.jsxs)("div",{className:"glass max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-white/10",children:[(0,a.jsxs)("h2",{className:"text-2xl font-bold text-white flex items-center gap-3",children:[(0,a.jsx)(k.Z,{className:"h-6 w-6"}),"Meeting Settings"]}),(0,a.jsx)("button",{onClick:r,className:"glass-button p-2 text-white hover:text-purple-300",children:(0,a.jsx)(S.Z,{className:"h-6 w-6"})})]}),(0,a.jsxs)("div",{className:"flex h-[600px]",children:[(0,a.jsx)("div",{className:"w-64 p-4 border-r border-white/10",children:(0,a.jsx)("div",{className:"space-y-2",children:[{id:"video",name:"Video",icon:y.Z},{id:"audio",name:"Audio",icon:g.Z},{id:"background",name:"Background",icon:C.Z},{id:"security",name:"Security",icon:I.Z},{id:"general",name:"General",icon:k.Z}].map(e=>(0,a.jsxs)("button",{onClick:()=>o(e.id),className:"w-full flex items-center gap-3 p-3 rounded-lg transition-all ".concat(c===e.id?"bg-purple-500/30 text-white":"text-white/70 hover:bg-white/10 hover:text-white"),children:[(0,a.jsx)(e.icon,{className:"h-5 w-5"}),e.name]},e.id))})}),(0,a.jsxs)("div",{className:"flex-1 p-6 overflow-y-auto",children:["video"===c&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-white mb-4",children:"Video Settings"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-white text-sm font-medium mb-2",children:"Camera"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("button",{onClick:()=>q(!J),className:"glass-input w-full flex items-center justify-between",children:[(0,a.jsx)("span",{children:(null===(t=et.find(e=>e.deviceId===m))||void 0===t?void 0:t.label)||"Select Camera"}),(0,a.jsx)(M.Z,{className:"h-4 w-4"})]}),J&&(0,a.jsx)("div",{className:"absolute top-full left-0 right-0 mt-1 glass-dark rounded-lg border border-white/20 z-10",children:et.map(e=>(0,a.jsxs)("button",{onClick:()=>W(e.deviceId),className:"w-full p-3 text-left text-white hover:bg-white/10 flex items-center justify-between",children:[e.label,m===e.deviceId&&(0,a.jsx)(Z.Z,{className:"h-4 w-4"})]},e.deviceId))})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-white text-sm font-medium mb-2",children:"Video Quality"}),(0,a.jsx)("div",{className:"grid grid-cols-2 gap-2",children:[{id:"sd",name:"SD (480p)",resolution:"640x480"},{id:"hd",name:"HD (720p)",resolution:"1280x720"},{id:"fhd",name:"Full HD (1080p)",resolution:"1920x1080"},{id:"4k",name:"4K (2160p)",resolution:"3840x2160"}].map(e=>(0,a.jsxs)("button",{onClick:()=>ee(e.id),className:"p-3 rounded-lg border transition-all ".concat(D===e.id?"bg-purple-500/30 border-purple-500 text-white":"bg-white/10 border-white/20 text-white/70 hover:bg-white/20"),children:[(0,a.jsx)("div",{className:"font-medium",children:e.name}),(0,a.jsx)("div",{className:"text-xs opacity-70",children:e.resolution})]},e.id))})]})]}),"audio"===c&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-white mb-4",children:"Audio Settings"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-white text-sm font-medium mb-2",children:"Microphone"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("button",{onClick:()=>K(!Y),className:"glass-input w-full flex items-center justify-between",children:[(0,a.jsx)("span",{children:(null===(s=es.find(e=>e.deviceId===p))||void 0===s?void 0:s.label)||"Select Microphone"}),(0,a.jsx)(M.Z,{className:"h-4 w-4"})]}),Y&&(0,a.jsx)("div",{className:"absolute top-full left-0 right-0 mt-1 glass-dark rounded-lg border border-white/20 z-10",children:es.map(e=>(0,a.jsxs)("button",{onClick:()=>X(e.deviceId),className:"w-full p-3 text-left text-white hover:bg-white/10 flex items-center justify-between",children:[e.label,p===e.deviceId&&(0,a.jsx)(Z.Z,{className:"h-4 w-4"})]},e.deviceId))})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h4",{className:"text-white font-medium",children:"Audio Enhancement"}),(0,a.jsxs)("label",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-white",children:"Noise Suppression"}),(0,a.jsx)("input",{type:"checkbox",checked:R,onChange:e=>U(e.target.checked),className:"w-5 h-5 rounded bg-white/10 border-white/20"})]}),(0,a.jsxs)("label",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-white",children:"Echo Cancellation"}),(0,a.jsx)("input",{type:"checkbox",checked:F,onChange:e=>L(e.target.checked),className:"w-5 h-5 rounded bg-white/10 border-white/20"})]}),(0,a.jsxs)("label",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-white",children:"Auto Gain Control"}),(0,a.jsx)("input",{type:"checkbox",checked:z,onChange:e=>O(e.target.checked),className:"w-5 h-5 rounded bg-white/10 border-white/20"})]})]})]}),"background"===c&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-white mb-4",children:"Background Effects"}),(0,a.jsx)("div",{className:"grid grid-cols-3 gap-4",children:[{id:"none",name:"None",preview:null},{id:"blur",name:"Blur Background",preview:null},{id:"office",name:"Modern Office",preview:"/backgrounds/office.jpg"},{id:"nature",name:"Nature Scene",preview:"/backgrounds/nature.jpg"},{id:"abstract",name:"Abstract Blue",preview:"/backgrounds/abstract.jpg"},{id:"gradient",name:"Purple Gradient",preview:"/backgrounds/gradient.jpg"}].map(e=>(0,a.jsxs)("button",{onClick:()=>$(e.id),className:"p-4 rounded-lg border-2 transition-all ".concat(E===e.id?"border-purple-500 bg-purple-500/20":"border-white/20 bg-white/10 hover:border-white/40"),children:[(0,a.jsx)("div",{className:"w-full h-20 bg-gradient-to-br from-gray-600 to-gray-800 rounded-lg mb-2 flex items-center justify-center",children:"blur"===e.id?(0,a.jsx)(j.Z,{className:"h-8 w-8 text-white"}):(0,a.jsx)(C.Z,{className:"h-8 w-8 text-white"})}),(0,a.jsx)("div",{className:"text-white text-sm font-medium",children:e.name})]},e.id))})]}),"security"===c&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-white mb-4",children:"Security Settings"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("label",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-white font-medium",children:"End-to-End Encryption"}),(0,a.jsx)("p",{className:"text-white/60 text-sm",children:"Encrypt all video and audio streams"})]}),(0,a.jsx)("input",{type:"checkbox",checked:H,onChange:e=>B(e.target.checked),className:"w-5 h-5 rounded bg-white/10 border-white/20"})]}),(0,a.jsxs)("label",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-white font-medium",children:"Anti-Spam Protection"}),(0,a.jsx)("p",{className:"text-white/60 text-sm",children:"Prevent message flooding in chat"})]}),(0,a.jsx)("input",{type:"checkbox",checked:_,onChange:e=>G(e.target.checked),className:"w-5 h-5 rounded bg-white/10 border-white/20"})]})]})]}),"general"===c&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-white mb-4",children:"General Settings"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-white text-sm font-medium mb-2",children:"Display Name"}),(0,a.jsx)("input",{type:"text",value:(null==Q?void 0:Q.name)||"",className:"glass-input w-full",placeholder:"Your display name"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-white text-sm font-medium mb-2",children:"Meeting Theme"}),(0,a.jsxs)("select",{className:"glass-input w-full",children:[(0,a.jsx)("option",{value:"dark",children:"Dark Theme"}),(0,a.jsx)("option",{value:"light",children:"Light Theme"}),(0,a.jsx)("option",{value:"auto",children:"Auto"})]})]})]})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-end gap-3 p-6 border-t border-white/10",children:[(0,a.jsx)("button",{onClick:r,className:"btn-secondary px-6 py-2",children:"Cancel"}),(0,a.jsx)("button",{onClick:r,className:"btn-primary px-6 py-2",children:"Save Changes"})]})]})})}var T=s(4392),D=s(1492),A=s(1683);function P(){let[e,t]=(0,i.useState)(!1),[s,l]=(0,i.useState)(null),[r,c]=(0,i.useState)(!1),[o,d]=(0,i.useState)(!1),[h,m]=(0,i.useState)(!1),[u,p]=(0,i.useState)([]),[b,N]=(0,i.useState)(""),[S,C]=(0,i.useState)(""),{toggleAudio:I,toggleVideo:M,isAudioMuted:Z,isVideoMuted:P,setLocalStream:V}=(0,n.g)(),R=(0,i.useCallback)(async()=>{try{await navigator.mediaDevices.getUserMedia({audio:!0,video:!0});let e=(await navigator.mediaDevices.enumerateDevices()).filter(e=>"audioinput"===e.kind||"videoinput"===e.kind).map(e=>({deviceId:e.deviceId,label:e.label||"".concat(e.kind," ").concat(e.deviceId.slice(0,8)),kind:e.kind}));if(p(e),!b){let t=e.find(e=>"videoinput"===e.kind);t&&N(t.deviceId)}if(!S){let t=e.find(e=>"audioinput"===e.kind);t&&C(t.deviceId)}}catch(e){console.error("Error loading devices:",e)}},[b,S]);(0,i.useEffect)(()=>(R(),navigator.mediaDevices.addEventListener("devicechange",R),()=>{navigator.mediaDevices.removeEventListener("devicechange",R)}),[R]);let U=e=>{l(e),setTimeout(()=>l(null),2e3)},F=async()=>{try{if(e)L();else{let e=await x.getDisplayMedia();await x.replaceVideoTrack(e),e.getVideoTracks()[0].onended=()=>{L()},t(!0),U("Screen sharing started")}}catch(e){console.error("Error toggling screen share:",e),U("Failed to toggle screen share")}},L=async()=>{try{let e=await x.getUserMedia();x.replaceVideoTrack(e),t(!1),U("Screen sharing stopped")}catch(e){console.error("Error stopping screen share:",e),U("Failed to stop screen share")}},z=(0,i.useCallback)(async e=>{if(e===b){c(!1);return}try{await x.switchCamera(e),N(e),c(!1),U("Camera changed")}catch(e){console.error("Error switching camera:",e),U("Failed to switch camera")}},[b]),O=(0,i.useCallback)(async e=>{if(e===S){d(!1);return}try{C(e),d(!1),U("Microphone changed")}catch(e){console.error("Error switching microphone:",e),U("Failed to switch microphone")}},[S]),H=u.filter(e=>"videoinput"===e.kind),B=u.filter(e=>"audioinput"===e.kind);return(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsxs)("div",{className:"video-controls",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("button",{onClick:()=>{I(),U(Z?"Microphone unmuted":"Microphone muted")},className:"control-btn ".concat(Z?"active":"inactive"," rounded-r-none"),title:Z?"Unmute microphone":"Mute microphone",children:Z?(0,a.jsx)(f.Z,{className:"h-6 w-6"}):(0,a.jsx)(g.Z,{className:"h-6 w-6"})}),B.length>1&&(0,a.jsx)("button",{onClick:()=>d(!o),className:"control-btn inactive rounded-l-none border-l border-white/20 w-8",title:"Select microphone",children:(0,a.jsx)(T.Z,{className:"h-4 w-4"})})]}),o&&B.length>1&&(0,a.jsx)("div",{className:"absolute bottom-full left-0 mb-2 glass-dark rounded-lg border border-white/20 min-w-48 z-10",children:(0,a.jsxs)("div",{className:"p-2",children:[(0,a.jsxs)("div",{className:"text-white text-xs font-medium mb-2 flex items-center gap-2",children:[(0,a.jsx)(D.Z,{className:"h-3 w-3"}),"Select Microphone"]}),B.map(e=>(0,a.jsx)("div",{className:"px-4 py-2 text-white hover:bg-white/10 cursor-pointer flex items-center",onClick:()=>O(e.deviceId),children:(0,a.jsx)("span",{className:S===e.deviceId?"font-semibold":"",children:e.label})},e.deviceId))]})})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("button",{onClick:()=>{M(),U(P?"Camera turned on":"Camera turned off")},className:"control-btn ".concat(P?"active":"inactive"," rounded-r-none"),title:P?"Turn on camera":"Turn off camera",children:P?(0,a.jsx)(w.Z,{className:"h-6 w-6"}):(0,a.jsx)(v.Z,{className:"h-6 w-6"})}),H.length>1&&(0,a.jsx)("button",{onClick:()=>c(!r),className:"control-btn inactive rounded-l-none border-l border-white/20 w-8",title:"Select camera",children:(0,a.jsx)(T.Z,{className:"h-4 w-4"})})]}),r&&H.length>1&&(0,a.jsx)("div",{className:"absolute bottom-full left-0 mb-2 glass-dark rounded-lg border border-white/20 min-w-48 z-10",children:(0,a.jsxs)("div",{className:"p-2",children:[(0,a.jsxs)("div",{className:"text-white text-xs font-medium mb-2 flex items-center gap-2",children:[(0,a.jsx)(y.Z,{className:"h-3 w-3"}),"Select Camera"]}),H.map(e=>(0,a.jsx)("div",{className:"px-4 py-2 text-white hover:bg-white/10 cursor-pointer flex items-center",onClick:()=>z(e.deviceId),children:(0,a.jsx)("span",{className:b===e.deviceId?"font-semibold":"",children:e.label})},e.deviceId))]})})]}),(0,a.jsx)("button",{onClick:F,className:"control-btn ".concat(e?"active":"inactive"),title:e?"Stop screen sharing":"Share screen",children:e?(0,a.jsx)(A.Z,{className:"h-6 w-6"}):(0,a.jsx)(j.Z,{className:"h-6 w-6"})}),(0,a.jsx)("button",{onClick:()=>m(!0),className:"p-2 rounded-full hover:bg-gray-200 transition-colors","aria-label":"Settings",children:(0,a.jsx)(k.Z,{className:"w-6 h-6"})})]}),(0,a.jsx)(E,{isOpen:h,onClose:()=>m(!1)}),s&&(0,a.jsx)("div",{className:"fixed bottom-20 left-1/2 transform -translate-x-1/2 glass-dark px-4 py-2 rounded-lg text-white z-50 fade-in",children:s})]})}var V=s(7583),R=s(994);function U(){let[e,t]=(0,i.useState)(""),s=(0,i.useRef)(null),{messages:l,currentUser:r,toggleChat:c,clearUnreadCount:d,securitySettings:h,blockUser:m}=(0,n.g)(),u=(null==r?void 0:r.role)==="host"||(null==r?void 0:r.role)==="co-host";(0,i.useEffect)(()=>{var e;null===(e=s.current)||void 0===e||e.scrollIntoView({behavior:"smooth"})},[l]),(0,i.useEffect)(()=>{d()},[d]);let x=e=>{u&&m(e)},p=e=>e.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"});return(0,a.jsxs)("div",{className:"chat-container flex flex-col h-full",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 border-b border-white/10",children:[(0,a.jsxs)("h3",{className:"text-white font-semibold text-sm flex items-center gap-2",children:[(0,a.jsx)(V.Z,{className:"h-4 w-4"}),"Chat"]}),(0,a.jsx)("div",{className:"flex items-center gap-2",children:l.length>0&&(0,a.jsxs)("span",{className:"text-white/60 text-xs",children:[l.length," messages"]})})]}),(0,a.jsxs)("div",{className:"flex-1 overflow-y-auto p-3 space-y-3",children:[0===l.length?(0,a.jsxs)("div",{className:"text-center text-white/60 py-8",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-3",children:(0,a.jsx)(R.Z,{className:"h-6 w-6 text-white"})}),(0,a.jsx)("p",{className:"text-sm font-medium mb-1",children:"No messages yet"}),(0,a.jsx)("p",{className:"text-xs",children:"Start the conversation!"})]}):l.filter(e=>!e.isBlocked).map(e=>(0,a.jsxs)("div",{className:"flex flex-col ".concat(e.userId===(null==r?void 0:r.id)?"items-end":"items-start"," ").concat(e.isSpam?"opacity-50":""),children:[(0,a.jsxs)("div",{className:"chat-message max-w-[90%] p-2 relative ".concat(e.userId===(null==r?void 0:r.id)?"bg-gradient-to-r from-purple-500 to-pink-500":e.isSpam?"bg-red-500/20 border border-red-500/50":"bg-white/10"),children:[(0,a.jsx)("p",{className:"text-white text-xs leading-relaxed",children:e.message}),e.isSpam&&(0,a.jsx)("div",{className:"absolute top-0 right-0 bg-red-500 text-white text-xs px-1 rounded-bl",children:"SPAM"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1 mt-1 text-xs text-white/50",children:[(0,a.jsx)("span",{className:"font-medium text-xs",children:e.userName}),(0,a.jsx)("span",{children:"•"}),(0,a.jsx)("span",{className:"text-xs",children:p(e.timestamp)}),u&&e.userId!==(null==r?void 0:r.id)&&(0,a.jsx)("button",{onClick:()=>x(e.userId),className:"text-red-400 hover:text-red-300 text-xs ml-2",children:"Block"})]})]},e.id)),(0,a.jsx)("div",{ref:s})]}),(0,a.jsx)("div",{className:"p-3 border-t border-white/10",children:(0,a.jsxs)("form",{onSubmit:s=>{s.preventDefault(),e.trim()&&r&&(o.sendChatMessage(e.trim(),r.name),t(""))},className:"flex space-x-2",children:[(0,a.jsx)("input",{type:"text",value:e,onChange:e=>t(e.target.value),placeholder:"Type a message...",className:"glass-input flex-1 text-sm py-2",maxLength:500,autoComplete:"off"}),(0,a.jsx)("button",{type:"submit",disabled:!e.trim(),className:"p-2 rounded-lg transition-all ".concat(e.trim()?"bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white":"bg-white/10 text-white/50 cursor-not-allowed"),children:(0,a.jsx)(R.Z,{className:"h-4 w-4"})})]})})]})}var F=s(1240),L=s(7893),z=s(1909),O=s(6676),H=s(8137),B=s(6055),_=s(1106);function G(){let[e,t]=(0,i.useState)(null),{currentUser:s,participants:l,roomLocked:r,muteParticipant:c,muteAllParticipants:o,removeParticipant:d,promoteToCoHost:h,demoteFromCoHost:m,lockRoom:u,unlockRoom:x,blockUser:b}=(0,n.g)(),N=Array.from(l.values()),k=s?[s,...N]:N,S=e=>{let t=[];return e.isAudioMuted?t.push((0,a.jsx)(f.Z,{className:"h-3 w-3 text-red-400"},"mic")):t.push((0,a.jsx)(g.Z,{className:"h-3 w-3 text-green-400"},"mic")),e.isVideoMuted?t.push((0,a.jsx)(w.Z,{className:"h-3 w-3 text-red-400"},"video")):t.push((0,a.jsx)(v.Z,{className:"h-3 w-3 text-green-400"},"video")),e.isScreenSharing&&t.push((0,a.jsx)(j.Z,{className:"h-3 w-3 text-blue-400"},"screen")),t},y=(null==s?void 0:s.role)==="host"||(null==s?void 0:s.role)==="co-host",C=(null==s?void 0:s.role)==="host",M=(e,s)=>{switch(e){case"mute":c(s);break;case"remove":d(s);break;case"promote":h(s);break;case"demote":m(s);break;case"block":b(s)}t(null)};return(0,a.jsxs)("div",{className:"chat-container flex flex-col h-full",children:[(0,a.jsx)("div",{className:"p-3 border-b border-white/10",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("h3",{className:"text-white font-semibold text-sm flex items-center gap-2",children:[(0,a.jsx)(F.Z,{className:"h-4 w-4"}),"Participants (",k.length,")",r&&(0,a.jsx)(L.Z,{className:"h-3 w-3 text-yellow-400"})]}),y&&(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("button",{onClick:o,className:"glass-button p-1 text-white/60 hover:text-white",title:"Mute all participants",children:(0,a.jsx)(f.Z,{className:"h-3 w-3"})}),C&&(0,a.jsx)("button",{onClick:()=>r?x():u(),className:"glass-button p-1 text-white/60 hover:text-white",title:r?"Unlock room":"Lock room",children:r?(0,a.jsx)(z.Z,{className:"h-3 w-3"}):(0,a.jsx)(L.Z,{className:"h-3 w-3"})})]})]})}),(0,a.jsx)("div",{className:"flex-1 overflow-y-auto p-2 space-y-2",children:k.map(i=>{let l=i.id===(null==s?void 0:s.id);return(0,a.jsx)("div",{className:"glass-button p-2 hover:bg-white/20 transition-all relative",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("div",{className:"w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center relative",children:[(0,a.jsx)("span",{className:"text-white text-xs font-bold",children:i.name.charAt(0).toUpperCase()}),i.isHandRaised&&(0,a.jsx)("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 rounded-full flex items-center justify-center",children:(0,a.jsx)(O.Z,{className:"h-2 w-2 text-black"})})]}),(0,a.jsxs)("div",{className:"flex flex-col flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsxs)("span",{className:"text-white text-xs font-medium truncate",children:[i.name,l&&" (You)"]}),"host"===i.role&&(0,a.jsx)(p.Z,{className:"h-3 w-3 text-yellow-400 flex-shrink-0"}),"co-host"===i.role&&(0,a.jsx)(I.Z,{className:"h-3 w-3 text-blue-400 flex-shrink-0"})]}),(0,a.jsx)("div",{className:"flex items-center space-x-1",children:S(i)})]})]}),!l&&y&&(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("button",{onClick:()=>t(e===i.id?null:i.id),className:"glass-button p-1 text-white/60 hover:text-white",children:(0,a.jsx)(H.Z,{className:"h-3 w-3"})}),e===i.id&&(0,a.jsx)("div",{className:"absolute right-0 top-full mt-1 glass-dark rounded-lg border border-white/20 min-w-32 z-10",children:(0,a.jsxs)("div",{className:"p-1",children:[(0,a.jsxs)("button",{onClick:()=>M("mute",i.id),className:"w-full p-2 text-left text-white text-xs hover:bg-white/10 rounded flex items-center gap-2",children:[(0,a.jsx)(f.Z,{className:"h-3 w-3"}),"Mute"]}),C&&"participant"===i.role&&(0,a.jsxs)("button",{onClick:()=>M("promote",i.id),className:"w-full p-2 text-left text-white text-xs hover:bg-white/10 rounded flex items-center gap-2",children:[(0,a.jsx)(I.Z,{className:"h-3 w-3"}),"Make Co-Host"]}),C&&"co-host"===i.role&&(0,a.jsxs)("button",{onClick:()=>M("demote",i.id),className:"w-full p-2 text-left text-white text-xs hover:bg-white/10 rounded flex items-center gap-2",children:[(0,a.jsx)(B.Z,{className:"h-3 w-3"}),"Remove Co-Host"]}),(0,a.jsxs)("button",{onClick:()=>M("block",i.id),className:"w-full p-2 text-left text-red-400 text-xs hover:bg-white/10 rounded flex items-center gap-2",children:[(0,a.jsx)(_.Z,{className:"h-3 w-3"}),"Block User"]}),(0,a.jsxs)("button",{onClick:()=>M("remove",i.id),className:"w-full p-2 text-left text-red-400 text-xs hover:bg-white/10 rounded flex items-center gap-2",children:[(0,a.jsx)(_.Z,{className:"h-3 w-3"}),"Remove"]})]})})]})]})},i.id)})})]})}var J=s(560),q=s(6884),Y=s(7063),K=s(5453),Q=s(523);function W(e){let{roomId:t}=e,s=(0,l.useRouter)(),[r,c]=(0,i.useState)(!1),[d,h]=(0,i.useState)(!1),[m,u]=(0,i.useState)(!1),[p,g]=(0,i.useState)(null),{currentUser:f,participants:v,setConnected:w,addParticipant:j,removeParticipant:b,updateParticipant:S,setLocalStream:y,addMessage:C,reset:I}=(0,n.g)(),M=e=>{g(e),setTimeout(()=>g(null),3e3)};(0,i.useEffect)(()=>{let e=async()=>{try{if(!o.connect())throw Error("Failed to connect to server");let e=await x.getUserMedia();y(e),x.setSendSignalCallback((e,t)=>{o.sendSignal(e,t)}),x.onStream((e,t)=>{S(e,{stream:t})}),x.onUserDisconnected(e=>{b(e),M("A participant has left the meeting")}),f&&o.joinRoom(t,f.id,f.name),c(!0),w(!0),M("Successfully connected to meeting")}catch(e){console.error("Failed to initialize call:",e),M("Failed to join meeting. Please check your permissions.")}};return f&&!r&&e(),()=>{r&&(o.leaveRoom(),x.cleanup(),I())}},[f,t,r]),(0,i.useEffect)(()=>{if(!r)return;let e=o.getSocket();if(!e)return;let t=async e=>{console.log("User joined:",e),j({id:e.userId,name:e.userName,isAudioMuted:!1,isVideoMuted:!1,isScreenSharing:!1,role:"participant",isHandRaised:!1,joinedAt:new Date,lastActivity:new Date});try{let t=await x.createOffer(e.userId);o.sendSignal(e.userId,{type:"offer",offer:t})}catch(e){console.error("Error creating offer:",e)}M("".concat(e.userName," joined the meeting"))},s=e=>{console.log("User left:",e),b(e.userId),x.removePeerConnection(e.userId),M("".concat(e.userName," left the meeting"))},a=async e=>{console.log("Received signal:",e);try{let{fromUserId:t,signal:s}=e;switch(s.type){case"offer":let a=await x.createAnswer(t,s.offer);o.sendSignal(t,{type:"answer",answer:a});break;case"answer":await x.handleAnswer(t,s.answer);break;case"ice-candidate":await x.handleIceCandidate(t,s.candidate)}}catch(e){console.error("Error handling signal:",e)}},i=e=>{C({id:Math.random().toString(36).substring(2,15),userId:e.userId,userName:e.userName,message:e.message,timestamp:new Date(e.timestamp)})};return e.on("user-joined",t),e.on("user-left",s),e.on("signal",a),e.on("chat-message",i),()=>{e.off("user-joined",t),e.off("user-left",s),e.off("signal",a),e.off("chat-message",i)}},[r]);let[Z,T]=(0,i.useState)(!1);return r&&f?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"animated-bg"}),(0,a.jsxs)("div",{className:"min-h-screen flex flex-col relative z-10 pt-20",children:[(0,a.jsx)("div",{className:"glass-dark p-4 fixed top-0 left-0 right-0 z-50",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("h1",{className:"text-white text-xl font-semibold",children:"StreamIt Pro Meeting"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-white/70",children:[(0,a.jsx)(F.Z,{className:"h-4 w-4"}),(0,a.jsxs)("span",{children:[v.size+1," participants"]})]}),(0,a.jsxs)("div",{className:"text-white/50 text-sm",children:["ID: ",t]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>{let e="".concat(window.location.origin,"/room/").concat(t);navigator.clipboard.writeText(e),M("Meeting link copied to clipboard!")},className:"glass-button p-2 text-white hover:text-purple-300",title:"Copy meeting link",children:(0,a.jsx)(J.Z,{className:"h-5 w-5"})}),(0,a.jsx)("button",{onClick:()=>{navigator.clipboard.writeText(t),M("Meeting ID copied to clipboard!")},className:"glass-button p-2 text-white hover:text-purple-300",title:"Copy meeting ID",children:(0,a.jsx)(q.Z,{className:"h-5 w-5"})}),(0,a.jsx)("button",{onClick:()=>h(!d),className:"glass-button p-2 text-white hover:text-purple-300 ".concat(d?"bg-purple-500/30":""),title:"Settings",children:(0,a.jsx)(k.Z,{className:"h-5 w-5"})}),(0,a.jsx)("button",{onClick:()=>{document.fullscreenElement?(document.exitFullscreen(),u(!1)):(document.documentElement.requestFullscreen(),u(!0))},className:"glass-button p-2 text-white hover:text-purple-300",title:"Toggle fullscreen",children:m?(0,a.jsx)(Y.Z,{className:"h-5 w-5"}):(0,a.jsx)(K.Z,{className:"h-5 w-5"})}),(0,a.jsx)("button",{onClick:()=>{T(!0)},className:"bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors",title:"Leave meeting",children:(0,a.jsx)(Q.Z,{className:"h-5 w-5"})})]})]})}),(0,a.jsxs)("div",{className:"flex-1 flex p-4 pt-20 gap-4 mt-4",children:[(0,a.jsxs)("div",{className:"w-2/3 flex flex-col",children:[(0,a.jsx)(N,{}),(0,a.jsx)(P,{})]}),(0,a.jsxs)("div",{className:"w-1/3 flex flex-col gap-4",children:[(0,a.jsx)("div",{className:"h-1/2",children:(0,a.jsx)(G,{})}),(0,a.jsx)("div",{className:"h-1/2",children:(0,a.jsx)(U,{})})]})]}),(0,a.jsx)(E,{isOpen:d,onClose:()=>h(!1)}),Z&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"glass p-6 rounded-lg max-w-md w-full",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-white mb-4",children:"Leave Meeting?"}),(0,a.jsx)("p",{className:"text-white/80 mb-6",children:"Are you sure you want to leave the meeting?"}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,a.jsx)("button",{onClick:()=>{T(!1)},className:"px-4 py-2 rounded-lg bg-gray-600 text-white hover:bg-gray-500 transition-colors",children:"Cancel"}),(0,a.jsx)("button",{onClick:()=>{o.leaveRoom(),x.cleanup(),I(),s.push("/")},className:"px-4 py-2 rounded-lg bg-red-600 text-white hover:bg-red-500 transition-colors",children:"Leave"})]})]})}),p&&(0,a.jsx)("div",{className:"fixed top-20 right-4 glass p-4 text-white z-50 fade-in rounded-lg shadow-lg",children:p})]})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"animated-bg"}),(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center relative z-10",children:(0,a.jsxs)("div",{className:"glass p-8 text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-16 w-16 border-4 border-white/30 border-t-white mx-auto mb-6"}),(0,a.jsx)("h2",{className:"text-2xl font-semibold text-white mb-2",children:"Connecting to Meeting"}),(0,a.jsx)("p",{className:"text-white/70",children:"Please wait while we set up your video call..."})]})})]})}function X(){let e=(0,l.useParams)(),t=(0,l.useRouter)(),s=e.id,[r,c]=(0,i.useState)(""),[o,d]=(0,i.useState)(!1),[h,m]=(0,i.useState)(!1),{setRoomId:u,setCurrentUser:x}=(0,n.g)(),p=(0,i.useCallback)(async e=>{if(!e.trim()){alert("Please enter your name");return}d(!0);try{u(s);let t={id:Math.random().toString(36).substring(2,15),name:e.trim(),isAudioMuted:!1,isVideoMuted:!1,isScreenSharing:!1,role:"host",isHandRaised:!1,joinedAt:new Date,lastActivity:new Date};x(t),localStorage.setItem("userName",e.trim()),m(!0)}catch(e){console.error("Error joining room:",e),alert("Failed to join room. Please try again.")}finally{d(!1)}},[s,x,u]);return((0,i.useEffect)(()=>{let e=localStorage.getItem("userName");e&&(c(e),p(e))},[p]),h)?(0,a.jsx)(W,{roomId:s}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"animated-bg"}),(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center p-4 relative z-10",children:(0,a.jsxs)("div",{className:"glass p-8 max-w-md w-full",children:[(0,a.jsxs)("div",{className:"text-center mb-6",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-white mb-2",children:"Join Meeting"}),(0,a.jsxs)("p",{className:"text-white/70",children:["Meeting ID: ",(0,a.jsx)("span",{className:"font-mono font-semibold text-purple-300",children:s})]})]}),(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),p(r)},className:"space-y-4",children:[(0,a.jsx)("input",{type:"text",placeholder:"Enter your name",value:r,onChange:e=>c(e.target.value),disabled:o,autoFocus:!0,className:"glass-input w-full"}),(0,a.jsxs)("div",{className:"flex gap-3",children:[(0,a.jsx)("button",{type:"button",onClick:()=>t.push("/"),disabled:o,className:"btn-secondary flex-1",children:"Back"}),(0,a.jsx)("button",{type:"submit",disabled:o||!r.trim(),className:"btn-primary flex-1",children:o?"Joining...":"Join Meeting"})]})]})]})})]})}},6936:function(e,t,s){"use strict";s.d(t,{g:function(){return n}});var a=s(9099),i=s(9291);let l={roomId:null,isConnected:!1,roomLocked:!1,currentUser:null,participants:new Map,localStream:null,isAudioMuted:!1,isVideoMuted:!1,isScreenSharing:!1,messages:[],unreadCount:0,messageHistory:new Map,isChatOpen:!1,isSettingsOpen:!1,securitySettings:{encryptionEnabled:!0,antiSpamEnabled:!0,maxMessagesPerMinute:10,allowScreenShare:!0,allowFileSharing:!1,requireApprovalToJoin:!1},adminControls:{canMuteAll:!0,canMuteParticipant:!0,canRemoveParticipant:!0,canControlCamera:!0,canManageRoles:!0},blockedUsers:new Set,spamDetection:new Map},n=(0,a.Ue)()((0,i.mW)((e,t)=>({...l,setRoomId:t=>e({roomId:t}),setConnected:t=>e({isConnected:t}),setCurrentUser:t=>e({currentUser:t}),addParticipant:s=>{let a=new Map(t().participants);a.set(s.id,s),e({participants:a})},removeParticipant:s=>{let a=new Map(t().participants);a.delete(s),e({participants:a})},updateParticipant:(s,a)=>{let i=new Map(t().participants),l=i.get(s);l&&(i.set(s,{...l,...a}),e({participants:i}))},setLocalStream:t=>e({localStream:t}),toggleAudio:()=>{let{isAudioMuted:s,localStream:a}=t();a&&a.getAudioTracks().forEach(e=>{e.enabled=s}),e({isAudioMuted:!s})},toggleVideo:()=>{let{isVideoMuted:s,localStream:a}=t();a&&a.getVideoTracks().forEach(e=>{e.enabled=s}),e({isVideoMuted:!s})},toggleScreenShare:()=>{e(e=>({isScreenSharing:!e.isScreenSharing}))},addMessage:s=>{let{messages:a,securitySettings:i,spamDetection:l}=t();if(i.antiSpamEnabled){let e=Date.now(),t=l.get(s.userId)||{count:0,lastReset:e};e-t.lastReset>6e4&&(t.count=0,t.lastReset=e),t.count++,l.set(s.userId,t),t.count>i.maxMessagesPerMinute&&(s.isSpam=!0)}e({messages:[...a,s],unreadCount:t().isChatOpen?0:t().unreadCount+1,spamDetection:new Map(l)})},clearUnreadCount:()=>e({unreadCount:0}),toggleChat:()=>{let s=!t().isChatOpen;e({isChatOpen:s,unreadCount:s?0:t().unreadCount})},toggleSettings:()=>e(e=>({isSettingsOpen:!e.isSettingsOpen})),muteParticipant:e=>{t().updateParticipant(e,{isAudioMuted:!0})},muteAllParticipants:()=>{let{participants:e}=t();e.forEach((e,s)=>{t().updateParticipant(s,{isAudioMuted:!0})})},unmuteAllParticipants:()=>{let{participants:e}=t();e.forEach((e,s)=>{t().updateParticipant(s,{isAudioMuted:!1})})},promoteToCoHost:e=>{t().updateParticipant(e,{role:"co-host"})},demoteFromCoHost:e=>{t().updateParticipant(e,{role:"participant"})},blockUser:s=>{let a=new Set(t().blockedUsers);a.add(s),e({blockedUsers:a}),t().removeParticipant(s)},unblockUser:s=>{let a=new Set(t().blockedUsers);a.delete(s),e({blockedUsers:a})},lockRoom:()=>e({roomLocked:!0}),unlockRoom:()=>e({roomLocked:!1}),updateSecuritySettings:t=>{e(e=>({securitySettings:{...e.securitySettings,...t}}))},reset:()=>e(l)}),{name:"video-call-store"}))}},function(e){e.O(0,[491,929,971,23,744],function(){return e(e.s=9256)}),_N_E=e.O()}]);
//# sourceMappingURL=page-0cefeb7278eeda75.js.map