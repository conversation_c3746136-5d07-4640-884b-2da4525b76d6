(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[786],{9256:function(e,t,s){Promise.resolve().then(s.bind(s,7994))},7994:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return X}});var a=s(7437),i=s(2265),r=s(9376),n=s(2097),l=s(8680);class c{connect(){var e;return(null===(e=this.socket)||void 0===e?void 0:e.connected)||(this.socket=(0,l.io)(window.location.origin,{transports:["websocket","polling"],upgrade:!0}),this.socket.on("connect",()=>{var e;console.log("Connected to server:",null===(e=this.socket)||void 0===e?void 0:e.id)}),this.socket.on("disconnect",()=>{console.log("Disconnected from server")}),this.socket.on("connect_error",e=>{console.error("Connection error:",e)})),this.socket}disconnect(){this.socket&&(this.socket.disconnect(),this.socket=null)}joinRoom(e,t,s){this.socket&&(this.roomId=e,this.socket.emit("join-room",{roomId:e,userId:t,userName:s}))}leaveRoom(){this.socket&&this.roomId&&(this.socket.emit("leave-room",{roomId:this.roomId}),this.roomId=null)}sendSignal(e,t){this.socket&&this.roomId&&this.socket.emit("signal",{roomId:this.roomId,targetUserId:e,signal:t})}sendChatMessage(e,t){this.socket&&this.roomId&&this.socket.emit("chat-message",{roomId:this.roomId,message:e,userName:t,timestamp:new Date().toISOString()})}on(e,t){this.socket&&this.socket.on(e,t)}off(e,t){this.socket&&this.socket.off(e,t)}getSocket(){return this.socket}isConnected(){var e;return(null===(e=this.socket)||void 0===e?void 0:e.connected)||!1}constructor(){this.socket=null,this.roomId=null}}let o=new c,d={iceServers:[{urls:"stun:stun.l.google.com:19302"},{urls:"stun:stun1.l.google.com:19302"}],iceCandidatePoolSize:10,bundlePolicy:"max-bundle",rtcpMuxPolicy:"require"},h={video:{width:{ideal:1280,max:1920},height:{ideal:720,max:1080},frameRate:{ideal:30,max:60},facingMode:"user"},audio:{echoCancellation:!0,noiseSuppression:!0,autoGainControl:!0,sampleRate:48e3}},m={video:{width:{ideal:1920,max:3840},height:{ideal:1080,max:2160},frameRate:{ideal:30,max:60}},audio:!0};class u{setupEventHandlers(){window.addEventListener("beforeunload",()=>{this.cleanup()})}async getUserMedia(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:h;try{this.localStream=await navigator.mediaDevices.getUserMedia(e),this.cameraStream=this.localStream;let t=this.localStream.getVideoTracks();t.length>0&&(this.currentVideoDeviceId=t[0].getSettings().deviceId||null);let s=this.localStream.getAudioTracks();return s.length>0&&(this.currentAudioDeviceId=s[0].getSettings().deviceId||null),this.localStream}catch(e){throw console.error("Error accessing media devices:",e),e}}async getDisplayMedia(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:m;try{return this.screenStream=await navigator.mediaDevices.getDisplayMedia(e),this.screenStream.getVideoTracks()[0].onended=()=>{this.restoreVideoTrack()},this.screenStream}catch(e){throw console.error("Error accessing screen share:",e),e}}replaceAudioTrack(e){if(!this.localStream)return;this.localStream.getAudioTracks().forEach(e=>e.stop());let t=e.getAudioTracks()[0];t&&this.localStream.addTrack(t),this.updateAudioTracks()}restoreVideoTrack(){if(!this.localStream||!this.cameraStream)return;this.localStream.getVideoTracks().forEach(e=>e.stop());let e=this.cameraStream.getVideoTracks()[0];e&&this.localStream.addTrack(e.clone()),this.updateVideoTracks(),this.screenStream&&(this.screenStream.getTracks().forEach(e=>e.stop()),this.screenStream=null)}updateVideoTracks(){if(!this.localStream)return;let e=this.localStream.getVideoTracks()[0];e&&this.peerConnections.forEach(t=>{let s=t.getSenders().find(e=>{var t;return(null===(t=e.track)||void 0===t?void 0:t.kind)==="video"});s&&s.replaceTrack(e)})}updateAudioTracks(){if(!this.localStream)return;let e=this.localStream.getAudioTracks()[0];e&&this.peerConnections.forEach(t=>{let s=t.getSenders().find(e=>{var t;return(null===(t=e.track)||void 0===t?void 0:t.kind)==="audio"});s&&s.replaceTrack(e)})}async switchCamera(e){if(this.localStream)try{let t=await navigator.mediaDevices.getUserMedia({video:{deviceId:{exact:e}},audio:!this.currentAudioDeviceId||{deviceId:{exact:this.currentAudioDeviceId}}});return this.localStream.getTracks().forEach(e=>e.stop()),this.localStream=t,this.cameraStream=t,this.currentVideoDeviceId=e,this.updateVideoTracks(),t}catch(e){throw console.error("Error switching camera:",e),Error("Failed to switch camera")}}createPeerConnection(e){let t=new RTCPeerConnection(d);return this.localStream&&this.localStream.getTracks().forEach(e=>{t.addTrack(e,this.localStream)}),t.ontrack=t=>{let[s]=t.streams;this.onStreamCallback&&this.onStreamCallback(e,s)},t.onicecandidate=t=>{t.candidate&&this.sendSignal(e,{type:"ice-candidate",candidate:t.candidate})},t.onconnectionstatechange=()=>{console.log("Connection state for ".concat(e,":"),t.connectionState),("disconnected"===t.connectionState||"failed"===t.connectionState)&&(this.removePeerConnection(e),this.onUserDisconnectedCallback&&this.onUserDisconnectedCallback(e))},this.peerConnections.set(e,t),t}async createOffer(e){let t=this.peerConnections.get(e)||this.createPeerConnection(e),s=await t.createOffer({offerToReceiveAudio:!0,offerToReceiveVideo:!0});return await t.setLocalDescription(s),s}async createAnswer(e,t){let s=this.peerConnections.get(e)||this.createPeerConnection(e);await s.setRemoteDescription(t);let a=await s.createAnswer();return await s.setLocalDescription(a),a}async handleAnswer(e,t){let s=this.peerConnections.get(e);s&&await s.setRemoteDescription(t)}async handleIceCandidate(e,t){let s=this.peerConnections.get(e);s&&await s.addIceCandidate(t)}removePeerConnection(e){let t=this.peerConnections.get(e);t&&(t.close(),this.peerConnections.delete(e))}replaceVideoTrack(e){let t=e.getVideoTracks()[0];this.peerConnections.forEach(async e=>{let s=e.getSenders().find(e=>e.track&&"video"===e.track.kind);s&&t&&await s.replaceTrack(t)})}onStream(e){this.onStreamCallback=e}onUserDisconnected(e){this.onUserDisconnectedCallback=e}sendSignal(e,t){}setSendSignalCallback(e){this.sendSignal=e}cleanup(){this.peerConnections.forEach(e=>e.close()),this.peerConnections.clear(),this.localStream&&(this.localStream.getTracks().forEach(e=>e.stop()),this.localStream=null)}getLocalStream(){return this.localStream}getPeerConnection(e){return this.peerConnections.get(e)}constructor(){this.peerConnections=new Map,this.localStream=null,this.screenStream=null,this.cameraStream=null,this.currentVideoDeviceId=null,this.currentAudioDeviceId=null,this.setupEventHandlers()}}let x=new u;var p=s(8617),g=s(8662),v=s(730),f=s(9374),w=s(8178),b=s(9897);function j(e){let{participant:t,stream:s,isLocal:r,isFeatured:n=!1}=e,l=(0,i.useRef)(null);(0,i.useEffect)(()=>{l.current&&s&&(l.current.srcObject=s)},[s]);let c=s&&s.getVideoTracks().length>0&&!t.isVideoMuted,o=s&&s.getAudioTracks().length>0&&!t.isAudioMuted;return(0,a.jsxs)("div",{className:"video-container relative overflow-hidden ".concat(n?"w-full h-full":"w-full h-full max-w-sm max-h-64"),children:[c?(0,a.jsx)("video",{ref:l,autoPlay:!0,playsInline:!0,muted:r,className:"w-full h-full object-cover rounded-lg"}):(0,a.jsx)("div",{className:"w-full h-full bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center rounded-lg",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"".concat(n?"w-24 h-24":"w-16 h-16"," bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-3 shadow-lg"),children:(0,a.jsx)("span",{className:"text-white ".concat(n?"text-xl":"text-lg"," font-bold"),children:t.name.charAt(0).toUpperCase()})}),(0,a.jsx)("p",{className:"text-white ".concat(n?"text-lg":"text-sm"," font-medium"),children:t.name}),(0,a.jsx)("p",{className:"text-white/60 text-xs",children:"Camera is off"})]})}),(0,a.jsxs)("div",{className:"absolute bottom-2 left-2 right-2 flex items-center justify-between",children:[(0,a.jsx)("div",{className:"glass-dark px-2 py-1 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[r&&(0,a.jsx)(p.Z,{className:"h-3 w-3 text-yellow-400"}),(0,a.jsx)("span",{className:"text-white text-xs font-medium",children:r?"You":t.name})]})}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)("div",{className:"p-1 rounded-full ".concat(o?"bg-green-500/80":"bg-red-500/80"," backdrop-blur-sm"),children:o?(0,a.jsx)(g.Z,{className:"h-3 w-3 text-white"}):(0,a.jsx)(v.Z,{className:"h-3 w-3 text-white"})}),(0,a.jsx)("div",{className:"p-1 rounded-full ".concat(c?"bg-green-500/80":"bg-red-500/80"," backdrop-blur-sm"),children:c?(0,a.jsx)(f.Z,{className:"h-3 w-3 text-white"}):(0,a.jsx)(w.Z,{className:"h-3 w-3 text-white"})}),t.isScreenSharing&&(0,a.jsx)("div",{className:"p-1 rounded-full bg-blue-500/80 backdrop-blur-sm",children:(0,a.jsx)(b.Z,{className:"h-3 w-3 text-white"})})]})]}),r&&(0,a.jsx)("div",{className:"absolute top-2 left-2",children:(0,a.jsx)("div",{className:"glass-dark px-2 py-1 rounded-lg",children:(0,a.jsxs)("span",{className:"text-white text-xs font-medium flex items-center gap-1",children:[(0,a.jsx)(p.Z,{className:"h-2 w-2 text-yellow-400"}),"Host"]})})}),(0,a.jsx)("div",{className:"absolute top-2 right-2",children:(0,a.jsxs)("div",{className:"flex space-x-0.5",children:[(0,a.jsx)("div",{className:"w-0.5 h-2 bg-green-400 rounded-full"}),(0,a.jsx)("div",{className:"w-0.5 h-3 bg-green-400 rounded-full"}),(0,a.jsx)("div",{className:"w-0.5 h-4 bg-green-400 rounded-full"})]})})]})}function N(){let{currentUser:e,participants:t,localStream:s}=(0,n.g)(),i=Array.from(t.values()),r=i.length+1;return(0,a.jsx)("div",{className:"flex-1 p-4",children:(0,a.jsx)("div",{className:"glass h-full p-4 overflow-hidden",children:(0,a.jsxs)("div",{className:"\n          grid gap-3 h-full\n          ".concat(1===r||2===r?"grid-cols-1":r<=4||r<=6?"grid-cols-2":"grid-cols-3","\n          ").concat(1===r?"grid-rows-1":2===r||r<=4?"grid-rows-2":"grid-rows-3","\n          place-items-center\n        "),children:[e&&(0,a.jsx)(j,{participant:e,stream:s,isLocal:!0,isFeatured:1===r},e.id),i.map((e,t)=>(0,a.jsx)(j,{participant:e,stream:e.stream,isLocal:!1,isFeatured:2===r&&0===t},e.id))]})})})}var k=s(8728),S=s(2489),y=s(3581),C=s(3113),I=s(8906),M=s(875),E=s(401);function Z(e){var t,s;let{isOpen:r,onClose:l}=e,[c,o]=(0,i.useState)("video"),[d,h]=(0,i.useState)([]),[m,u]=(0,i.useState)(""),[p,v]=(0,i.useState)(""),[f,w]=(0,i.useState)(""),[j,N]=(0,i.useState)(!1),[Z,D]=(0,i.useState)("none"),[T,A]=(0,i.useState)("hd"),[U,P]=(0,i.useState)("high"),[V,R]=(0,i.useState)(!0),[F,L]=(0,i.useState)(!0),[O,z]=(0,i.useState)(!0),[H,B]=(0,i.useState)(!0),[_,G]=(0,i.useState)(!0),[q,J]=(0,i.useState)(!1),[Y,K]=(0,i.useState)(!1),{currentUser:Q}=(0,n.g)();(0,i.useEffect)(()=>{let e=async()=>{try{let e=(await navigator.mediaDevices.enumerateDevices()).map(e=>({deviceId:e.deviceId,label:e.label||"".concat(e.kind," ").concat(e.deviceId.slice(0,8)),kind:e.kind}));h(e);let t=e.find(e=>"videoinput"===e.kind),s=e.find(e=>"audioinput"===e.kind),a=e.find(e=>"audiooutput"===e.kind);t&&u(t.deviceId),s&&v(s.deviceId),a&&w(a.deviceId)}catch(e){console.error("Error loading devices:",e)}};r&&e()},[r]);let W=async e=>{try{u(e);let t=await navigator.mediaDevices.getUserMedia({video:{deviceId:{exact:e},width:{ideal:1280},height:{ideal:720}},audio:!1});x.replaceVideoTrack(t),J(!1)}catch(e){console.error("Error changing camera:",e)}},X=async e=>{try{v(e),await navigator.mediaDevices.getUserMedia({video:!1,audio:{deviceId:{exact:e},echoCancellation:F,noiseSuppression:V,autoGainControl:O}}),K(!1)}catch(e){console.error("Error changing microphone:",e)}},$=e=>{D(e)},ee=e=>{A(e)};if(!r)return null;let et=d.filter(e=>"videoinput"===e.kind),es=d.filter(e=>"audioinput"===e.kind);return d.filter(e=>"audiooutput"===e.kind),(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:(0,a.jsxs)("div",{className:"glass max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-white/10",children:[(0,a.jsxs)("h2",{className:"text-2xl font-bold text-white flex items-center gap-3",children:[(0,a.jsx)(k.Z,{className:"h-6 w-6"}),"Meeting Settings"]}),(0,a.jsx)("button",{onClick:l,className:"glass-button p-2 text-white hover:text-purple-300",children:(0,a.jsx)(S.Z,{className:"h-6 w-6"})})]}),(0,a.jsxs)("div",{className:"flex h-[600px]",children:[(0,a.jsx)("div",{className:"w-64 p-4 border-r border-white/10",children:(0,a.jsx)("div",{className:"space-y-2",children:[{id:"video",name:"Video",icon:y.Z},{id:"audio",name:"Audio",icon:g.Z},{id:"background",name:"Background",icon:C.Z},{id:"security",name:"Security",icon:I.Z},{id:"general",name:"General",icon:k.Z}].map(e=>(0,a.jsxs)("button",{onClick:()=>o(e.id),className:"w-full flex items-center gap-3 p-3 rounded-lg transition-all ".concat(c===e.id?"bg-purple-500/30 text-white":"text-white/70 hover:bg-white/10 hover:text-white"),children:[(0,a.jsx)(e.icon,{className:"h-5 w-5"}),e.name]},e.id))})}),(0,a.jsxs)("div",{className:"flex-1 p-6 overflow-y-auto",children:["video"===c&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-white mb-4",children:"Video Settings"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-white text-sm font-medium mb-2",children:"Camera"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("button",{onClick:()=>J(!q),className:"glass-input w-full flex items-center justify-between",children:[(0,a.jsx)("span",{children:(null===(t=et.find(e=>e.deviceId===m))||void 0===t?void 0:t.label)||"Select Camera"}),(0,a.jsx)(M.Z,{className:"h-4 w-4"})]}),q&&(0,a.jsx)("div",{className:"absolute top-full left-0 right-0 mt-1 glass-dark rounded-lg border border-white/20 z-10",children:et.map(e=>(0,a.jsxs)("button",{onClick:()=>W(e.deviceId),className:"w-full p-3 text-left text-white hover:bg-white/10 flex items-center justify-between",children:[e.label,m===e.deviceId&&(0,a.jsx)(E.Z,{className:"h-4 w-4"})]},e.deviceId))})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-white text-sm font-medium mb-2",children:"Video Quality"}),(0,a.jsx)("div",{className:"grid grid-cols-2 gap-2",children:[{id:"sd",name:"SD (480p)",resolution:"640x480"},{id:"hd",name:"HD (720p)",resolution:"1280x720"},{id:"fhd",name:"Full HD (1080p)",resolution:"1920x1080"},{id:"4k",name:"4K (2160p)",resolution:"3840x2160"}].map(e=>(0,a.jsxs)("button",{onClick:()=>ee(e.id),className:"p-3 rounded-lg border transition-all ".concat(T===e.id?"bg-purple-500/30 border-purple-500 text-white":"bg-white/10 border-white/20 text-white/70 hover:bg-white/20"),children:[(0,a.jsx)("div",{className:"font-medium",children:e.name}),(0,a.jsx)("div",{className:"text-xs opacity-70",children:e.resolution})]},e.id))})]})]}),"audio"===c&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-white mb-4",children:"Audio Settings"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-white text-sm font-medium mb-2",children:"Microphone"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("button",{onClick:()=>K(!Y),className:"glass-input w-full flex items-center justify-between",children:[(0,a.jsx)("span",{children:(null===(s=es.find(e=>e.deviceId===p))||void 0===s?void 0:s.label)||"Select Microphone"}),(0,a.jsx)(M.Z,{className:"h-4 w-4"})]}),Y&&(0,a.jsx)("div",{className:"absolute top-full left-0 right-0 mt-1 glass-dark rounded-lg border border-white/20 z-10",children:es.map(e=>(0,a.jsxs)("button",{onClick:()=>X(e.deviceId),className:"w-full p-3 text-left text-white hover:bg-white/10 flex items-center justify-between",children:[e.label,p===e.deviceId&&(0,a.jsx)(E.Z,{className:"h-4 w-4"})]},e.deviceId))})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h4",{className:"text-white font-medium",children:"Audio Enhancement"}),(0,a.jsxs)("label",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-white",children:"Noise Suppression"}),(0,a.jsx)("input",{type:"checkbox",checked:V,onChange:e=>R(e.target.checked),className:"w-5 h-5 rounded bg-white/10 border-white/20"})]}),(0,a.jsxs)("label",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-white",children:"Echo Cancellation"}),(0,a.jsx)("input",{type:"checkbox",checked:F,onChange:e=>L(e.target.checked),className:"w-5 h-5 rounded bg-white/10 border-white/20"})]}),(0,a.jsxs)("label",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-white",children:"Auto Gain Control"}),(0,a.jsx)("input",{type:"checkbox",checked:O,onChange:e=>z(e.target.checked),className:"w-5 h-5 rounded bg-white/10 border-white/20"})]})]})]}),"background"===c&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-white mb-4",children:"Background Effects"}),(0,a.jsx)("div",{className:"grid grid-cols-3 gap-4",children:[{id:"none",name:"None",preview:null},{id:"blur",name:"Blur Background",preview:null},{id:"office",name:"Modern Office",preview:"/backgrounds/office.jpg"},{id:"nature",name:"Nature Scene",preview:"/backgrounds/nature.jpg"},{id:"abstract",name:"Abstract Blue",preview:"/backgrounds/abstract.jpg"},{id:"gradient",name:"Purple Gradient",preview:"/backgrounds/gradient.jpg"}].map(e=>(0,a.jsxs)("button",{onClick:()=>$(e.id),className:"p-4 rounded-lg border-2 transition-all ".concat(Z===e.id?"border-purple-500 bg-purple-500/20":"border-white/20 bg-white/10 hover:border-white/40"),children:[(0,a.jsx)("div",{className:"w-full h-20 bg-gradient-to-br from-gray-600 to-gray-800 rounded-lg mb-2 flex items-center justify-center",children:"blur"===e.id?(0,a.jsx)(b.Z,{className:"h-8 w-8 text-white"}):(0,a.jsx)(C.Z,{className:"h-8 w-8 text-white"})}),(0,a.jsx)("div",{className:"text-white text-sm font-medium",children:e.name})]},e.id))})]}),"security"===c&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-white mb-4",children:"Security Settings"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("label",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-white font-medium",children:"End-to-End Encryption"}),(0,a.jsx)("p",{className:"text-white/60 text-sm",children:"Encrypt all video and audio streams"})]}),(0,a.jsx)("input",{type:"checkbox",checked:H,onChange:e=>B(e.target.checked),className:"w-5 h-5 rounded bg-white/10 border-white/20"})]}),(0,a.jsxs)("label",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-white font-medium",children:"Anti-Spam Protection"}),(0,a.jsx)("p",{className:"text-white/60 text-sm",children:"Prevent message flooding in chat"})]}),(0,a.jsx)("input",{type:"checkbox",checked:_,onChange:e=>G(e.target.checked),className:"w-5 h-5 rounded bg-white/10 border-white/20"})]})]})]}),"general"===c&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-white mb-4",children:"General Settings"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-white text-sm font-medium mb-2",children:"Display Name"}),(0,a.jsx)("input",{type:"text",value:(null==Q?void 0:Q.name)||"",className:"glass-input w-full",placeholder:"Your display name"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-white text-sm font-medium mb-2",children:"Meeting Theme"}),(0,a.jsxs)("select",{className:"glass-input w-full",children:[(0,a.jsx)("option",{value:"dark",children:"Dark Theme"}),(0,a.jsx)("option",{value:"light",children:"Light Theme"}),(0,a.jsx)("option",{value:"auto",children:"Auto"})]})]})]})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-end gap-3 p-6 border-t border-white/10",children:[(0,a.jsx)("button",{onClick:l,className:"btn-secondary px-6 py-2",children:"Cancel"}),(0,a.jsx)("button",{onClick:l,className:"btn-primary px-6 py-2",children:"Save Changes"})]})]})})}var D=s(2135),T=s(5471),A=s(8549);function U(){let[e,t]=(0,i.useState)(null),[s,r]=(0,i.useState)(!1),[l,c]=(0,i.useState)(!1),[o,d]=(0,i.useState)(!1),[h,m]=(0,i.useState)([]),[u,p]=(0,i.useState)(""),[j,N]=(0,i.useState)(""),{toggleAudio:S,toggleVideo:C,toggleScreenShare:I,isAudioMuted:M,isVideoMuted:E,isScreenSharing:U}=(0,n.g)(),P=(0,i.useCallback)(async()=>{try{await navigator.mediaDevices.getUserMedia({audio:!0,video:!0});let e=(await navigator.mediaDevices.enumerateDevices()).filter(e=>"audioinput"===e.kind||"videoinput"===e.kind).map(e=>({deviceId:e.deviceId,label:e.label||"".concat(e.kind," ").concat(e.deviceId.slice(0,8)),kind:e.kind}));if(m(e),!u){let t=e.find(e=>"videoinput"===e.kind);t&&p(t.deviceId)}if(!j){let t=e.find(e=>"audioinput"===e.kind);t&&N(t.deviceId)}}catch(e){console.error("Error loading devices:",e)}},[u,j]);(0,i.useEffect)(()=>(P(),navigator.mediaDevices.addEventListener("devicechange",P),()=>{navigator.mediaDevices.removeEventListener("devicechange",P)}),[P]);let V=e=>{t(e),setTimeout(()=>t(null),2e3)},R=(0,i.useCallback)(async()=>{try{await x.restoreVideoTrack(),I(),V("Screen sharing stopped")}catch(e){console.error("Error stopping screen share:",e),V("Failed to stop screen share")}},[I]),F=(0,i.useCallback)(async()=>{try{if(U)R();else{let e=await x.getDisplayMedia();await x.replaceVideoTrack(e),e.getVideoTracks()[0].onended=()=>{R()},I(),V("Screen sharing started")}}catch(e){console.error("Error toggling screen share:",e),V("Failed to toggle screen share")}},[U,I,R]),L=(0,i.useCallback)(async e=>{if(e===u){r(!1);return}try{await x.switchCamera(e),p(e),r(!1),V("Camera changed")}catch(e){console.error("Error switching camera:",e),V("Failed to switch camera")}},[u]),O=(0,i.useCallback)(async e=>{if(e===j){c(!1);return}try{N(e),c(!1),V("Microphone changed")}catch(e){console.error("Error switching microphone:",e),V("Failed to switch microphone")}},[j]),z=h.filter(e=>"videoinput"===e.kind),H=h.filter(e=>"audioinput"===e.kind);return(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsxs)("div",{className:"video-controls",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("button",{onClick:()=>{S(),V(M?"Microphone unmuted":"Microphone muted")},className:"control-btn ".concat(M?"active":"inactive"," rounded-r-none"),title:M?"Unmute microphone":"Mute microphone",children:M?(0,a.jsx)(v.Z,{className:"h-6 w-6"}):(0,a.jsx)(g.Z,{className:"h-6 w-6"})}),H.length>1&&(0,a.jsx)("button",{onClick:()=>c(!l),className:"control-btn inactive rounded-l-none border-l border-white/20 w-8",title:"Select microphone",children:(0,a.jsx)(D.Z,{className:"h-4 w-4"})})]}),l&&H.length>1&&(0,a.jsx)("div",{className:"absolute bottom-full left-0 mb-2 glass-dark rounded-lg border border-white/20 min-w-48 z-10",children:(0,a.jsxs)("div",{className:"p-2",children:[(0,a.jsxs)("div",{className:"text-white text-xs font-medium mb-2 flex items-center gap-2",children:[(0,a.jsx)(T.Z,{className:"h-3 w-3"}),"Select Microphone"]}),H.map(e=>(0,a.jsx)("div",{className:"px-4 py-2 text-white hover:bg-white/10 cursor-pointer flex items-center",onClick:()=>O(e.deviceId),children:(0,a.jsx)("span",{className:j===e.deviceId?"font-semibold":"",children:e.label})},e.deviceId))]})})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("button",{onClick:()=>{C(),V(E?"Camera turned on":"Camera turned off")},className:"control-btn ".concat(E?"active":"inactive"," rounded-r-none"),title:E?"Turn on camera":"Turn off camera",children:E?(0,a.jsx)(w.Z,{className:"h-6 w-6"}):(0,a.jsx)(f.Z,{className:"h-6 w-6"})}),z.length>1&&(0,a.jsx)("button",{onClick:()=>r(!s),className:"control-btn inactive rounded-l-none border-l border-white/20 w-8",title:"Select camera",children:(0,a.jsx)(D.Z,{className:"h-4 w-4"})})]}),s&&z.length>1&&(0,a.jsx)("div",{className:"absolute bottom-full left-0 mb-2 glass-dark rounded-lg border border-white/20 min-w-48 z-10",children:(0,a.jsxs)("div",{className:"p-2",children:[(0,a.jsxs)("div",{className:"text-white text-xs font-medium mb-2 flex items-center gap-2",children:[(0,a.jsx)(y.Z,{className:"h-3 w-3"}),"Select Camera"]}),z.map(e=>(0,a.jsx)("div",{className:"px-4 py-2 text-white hover:bg-white/10 cursor-pointer flex items-center",onClick:()=>L(e.deviceId),children:(0,a.jsx)("span",{className:u===e.deviceId?"font-semibold":"",children:e.label})},e.deviceId))]})})]}),(0,a.jsx)("button",{onClick:F,className:"control-btn ".concat(U?"active":"inactive"),title:U?"Stop screen sharing":"Share screen",children:U?(0,a.jsx)(A.Z,{className:"h-6 w-6"}):(0,a.jsx)(b.Z,{className:"h-6 w-6"})}),(0,a.jsx)("button",{onClick:()=>d(!0),className:"p-2 rounded-full hover:bg-gray-200 transition-colors","aria-label":"Settings",children:(0,a.jsx)(k.Z,{className:"w-6 h-6"})})]}),(0,a.jsx)(Z,{isOpen:o,onClose:()=>d(!1)}),e&&(0,a.jsx)("div",{className:"fixed bottom-20 left-1/2 transform -translate-x-1/2 glass-dark px-4 py-2 rounded-lg text-white z-50 fade-in",children:e})]})}var P=s(2718),V=s(4743);function R(){let[e,t]=(0,i.useState)(""),s=(0,i.useRef)(null),{messages:r,currentUser:l,toggleChat:c,clearUnreadCount:d,securitySettings:h,detectSpam:m,blockUser:u}=(0,n.g)(),x=(null==l?void 0:l.role)==="host"||(null==l?void 0:l.role)==="co-host";(0,i.useEffect)(()=>{var e;null===(e=s.current)||void 0===e||e.scrollIntoView({behavior:"smooth"})},[r]),(0,i.useEffect)(()=>{d()},[d]);let p=e=>{x&&u(e)},g=e=>e.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"});return(0,a.jsxs)("div",{className:"chat-container flex flex-col h-full",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 border-b border-white/10",children:[(0,a.jsxs)("h3",{className:"text-white font-semibold text-sm flex items-center gap-2",children:[(0,a.jsx)(P.Z,{className:"h-4 w-4"}),"Chat"]}),(0,a.jsx)("div",{className:"flex items-center gap-2",children:r.length>0&&(0,a.jsxs)("span",{className:"text-white/60 text-xs",children:[r.length," messages"]})})]}),(0,a.jsxs)("div",{className:"flex-1 overflow-y-auto p-3 space-y-3",children:[0===r.length?(0,a.jsxs)("div",{className:"text-center text-white/60 py-8",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-3",children:(0,a.jsx)(V.Z,{className:"h-6 w-6 text-white"})}),(0,a.jsx)("p",{className:"text-sm font-medium mb-1",children:"No messages yet"}),(0,a.jsx)("p",{className:"text-xs",children:"Start the conversation!"})]}):r.filter(e=>!e.isBlocked).map(e=>(0,a.jsxs)("div",{className:"flex flex-col ".concat(e.userId===(null==l?void 0:l.id)?"items-end":"items-start"," ").concat(e.isSpam?"opacity-50":""),children:[(0,a.jsxs)("div",{className:"chat-message max-w-[90%] p-2 relative ".concat(e.userId===(null==l?void 0:l.id)?"bg-gradient-to-r from-purple-500 to-pink-500":e.isSpam?"bg-red-500/20 border border-red-500/50":"bg-white/10"),children:[(0,a.jsx)("p",{className:"text-white text-xs leading-relaxed",children:e.message}),e.isSpam&&(0,a.jsx)("div",{className:"absolute top-0 right-0 bg-red-500 text-white text-xs px-1 rounded-bl",children:"SPAM"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1 mt-1 text-xs text-white/50",children:[(0,a.jsx)("span",{className:"font-medium text-xs",children:e.userName}),(0,a.jsx)("span",{children:"•"}),(0,a.jsx)("span",{className:"text-xs",children:g(e.timestamp)}),x&&e.userId!==(null==l?void 0:l.id)&&(0,a.jsx)("button",{onClick:()=>p(e.userId),className:"text-red-400 hover:text-red-300 text-xs ml-2",children:"Block"})]})]},e.id)),(0,a.jsx)("div",{ref:s})]}),(0,a.jsx)("div",{className:"p-3 border-t border-white/10",children:(0,a.jsxs)("form",{onSubmit:s=>{if(s.preventDefault(),e.trim()&&l){if(h.antiSpamEnabled&&m(l.id)){alert("You are sending messages too quickly. Please slow down.");return}o.sendChatMessage(e.trim(),l.name),t("")}},className:"flex space-x-2",children:[(0,a.jsx)("input",{type:"text",value:e,onChange:e=>t(e.target.value),placeholder:"Type a message...",className:"glass-input flex-1 text-sm py-2",maxLength:500,autoComplete:"off"}),(0,a.jsx)("button",{type:"submit",disabled:!e.trim(),className:"p-2 rounded-lg transition-all ".concat(e.trim()?"bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white":"bg-white/10 text-white/50 cursor-not-allowed"),children:(0,a.jsx)(V.Z,{className:"h-4 w-4"})})]})})]})}var F=s(5805),L=s(6337),O=s(9711),z=s(512),H=s(7684),B=s(4315),_=s(8837);function G(){let[e,t]=(0,i.useState)(null),{currentUser:s,participants:r,roomLocked:l,muteParticipant:c,muteAllParticipants:o,removeParticipantAsAdmin:d,promoteToCoHost:h,demoteFromCoHost:m,toggleRoomLock:u,blockUser:x}=(0,n.g)(),j=Array.from(r.values()),N=s?[s,...j]:j,k=e=>{let t=[];return e.isAudioMuted?t.push((0,a.jsx)(v.Z,{className:"h-3 w-3 text-red-400"},"mic")):t.push((0,a.jsx)(g.Z,{className:"h-3 w-3 text-green-400"},"mic")),e.isVideoMuted?t.push((0,a.jsx)(w.Z,{className:"h-3 w-3 text-red-400"},"video")):t.push((0,a.jsx)(f.Z,{className:"h-3 w-3 text-green-400"},"video")),e.isScreenSharing&&t.push((0,a.jsx)(b.Z,{className:"h-3 w-3 text-blue-400"},"screen")),t},S=(null==s?void 0:s.role)==="host"||(null==s?void 0:s.role)==="co-host",y=(null==s?void 0:s.role)==="host",C=(e,s)=>{switch(e){case"mute":c(s);break;case"remove":d(s);break;case"promote":h(s);break;case"demote":m(s);break;case"block":x(s)}t(null)};return(0,a.jsxs)("div",{className:"chat-container flex flex-col h-full",children:[(0,a.jsx)("div",{className:"p-3 border-b border-white/10",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("h3",{className:"text-white font-semibold text-sm flex items-center gap-2",children:[(0,a.jsx)(F.Z,{className:"h-4 w-4"}),"Participants (",N.length,")",l&&(0,a.jsx)(L.Z,{className:"h-3 w-3 text-yellow-400"})]}),S&&(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("button",{onClick:o,className:"glass-button p-1 text-white/60 hover:text-white",title:"Mute all participants",children:(0,a.jsx)(v.Z,{className:"h-3 w-3"})}),y&&(0,a.jsx)("button",{onClick:u,className:"glass-button p-1 text-white/60 hover:text-white",title:l?"Unlock room":"Lock room",children:l?(0,a.jsx)(O.Z,{className:"h-3 w-3"}):(0,a.jsx)(L.Z,{className:"h-3 w-3"})})]})]})}),(0,a.jsx)("div",{className:"flex-1 overflow-y-auto p-2 space-y-2",children:N.map(i=>{let r=i.id===(null==s?void 0:s.id);return(0,a.jsx)("div",{className:"glass-button p-2 hover:bg-white/20 transition-all relative",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("div",{className:"w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center relative",children:[(0,a.jsx)("span",{className:"text-white text-xs font-bold",children:i.name.charAt(0).toUpperCase()}),i.isHandRaised&&(0,a.jsx)("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 rounded-full flex items-center justify-center",children:(0,a.jsx)(z.Z,{className:"h-2 w-2 text-black"})})]}),(0,a.jsxs)("div",{className:"flex flex-col flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsxs)("span",{className:"text-white text-xs font-medium truncate",children:[i.name,r&&" (You)"]}),"host"===i.role&&(0,a.jsx)(p.Z,{className:"h-3 w-3 text-yellow-400 flex-shrink-0"}),"co-host"===i.role&&(0,a.jsx)(I.Z,{className:"h-3 w-3 text-blue-400 flex-shrink-0"})]}),(0,a.jsx)("div",{className:"flex items-center space-x-1",children:k(i)})]})]}),!r&&S&&(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("button",{onClick:()=>t(e===i.id?null:i.id),className:"glass-button p-1 text-white/60 hover:text-white",children:(0,a.jsx)(H.Z,{className:"h-3 w-3"})}),e===i.id&&(0,a.jsx)("div",{className:"absolute right-0 top-full mt-1 glass-dark rounded-lg border border-white/20 min-w-32 z-10",children:(0,a.jsxs)("div",{className:"p-1",children:[(0,a.jsxs)("button",{onClick:()=>C("mute",i.id),className:"w-full p-2 text-left text-white text-xs hover:bg-white/10 rounded flex items-center gap-2",children:[(0,a.jsx)(v.Z,{className:"h-3 w-3"}),"Mute"]}),y&&"participant"===i.role&&(0,a.jsxs)("button",{onClick:()=>C("promote",i.id),className:"w-full p-2 text-left text-white text-xs hover:bg-white/10 rounded flex items-center gap-2",children:[(0,a.jsx)(I.Z,{className:"h-3 w-3"}),"Make Co-Host"]}),y&&"co-host"===i.role&&(0,a.jsxs)("button",{onClick:()=>C("demote",i.id),className:"w-full p-2 text-left text-white text-xs hover:bg-white/10 rounded flex items-center gap-2",children:[(0,a.jsx)(B.Z,{className:"h-3 w-3"}),"Remove Co-Host"]}),(0,a.jsxs)("button",{onClick:()=>C("block",i.id),className:"w-full p-2 text-left text-red-400 text-xs hover:bg-white/10 rounded flex items-center gap-2",children:[(0,a.jsx)(_.Z,{className:"h-3 w-3"}),"Block User"]}),(0,a.jsxs)("button",{onClick:()=>C("remove",i.id),className:"w-full p-2 text-left text-red-400 text-xs hover:bg-white/10 rounded flex items-center gap-2",children:[(0,a.jsx)(_.Z,{className:"h-3 w-3"}),"Remove"]})]})})]})]})},i.id)})})]})}var q=s(4394),J=s(8867),Y=s(4440),K=s(7416),Q=s(7133);function W(e){let{roomId:t}=e,s=(0,r.useRouter)(),[l,c]=(0,i.useState)(!1),[d,h]=(0,i.useState)(!1),[m,u]=(0,i.useState)(!1),[p,g]=(0,i.useState)(!1),[v,f]=(0,i.useState)(null),{currentUser:w,participants:b,localStream:j,isChatOpen:S,isConnected:y,setConnected:C,addParticipant:I,removeParticipant:M,updateParticipant:E,setLocalStream:D,toggleChat:T,addMessage:A,reset:P,toggleVideo:V,toggleAudio:L,toggleScreenShare:O}=(0,n.g)(),z=e=>{f(e),setTimeout(()=>f(null),3e3)};return((0,i.useEffect)(()=>{let e=async()=>{try{if(!o.connect())throw Error("Failed to connect to server");let e=await x.getUserMedia();D(e),x.setSendSignalCallback((e,t)=>{o.sendSignal(e,t)}),x.onStream((e,t)=>{E(e,{stream:t})}),x.onUserDisconnected(e=>{M(e),z("A participant has left the meeting")}),w&&o.joinRoom(t,w.id,w.name),c(!0),C(!0),z("Successfully connected to meeting")}catch(e){console.error("Failed to initialize call:",e),z("Failed to join meeting. Please check your permissions.")}};return w&&!l&&e(),()=>{l&&(o.leaveRoom(),x.cleanup(),P())}},[w,t,l]),(0,i.useEffect)(()=>{if(!l)return;let e=o.getSocket();if(!e)return;let t=async e=>{console.log("User joined:",e),I({id:e.userId,name:e.userName,isAudioMuted:!1,isVideoMuted:!1,isScreenSharing:!1,role:"participant",isHandRaised:!1,joinedAt:new Date,lastActivity:new Date});try{let t=await x.createOffer(e.userId);o.sendSignal(e.userId,{type:"offer",offer:t})}catch(e){console.error("Error creating offer:",e)}z("".concat(e.userName," joined the meeting"))},s=e=>{console.log("User left:",e),M(e.userId),x.removePeerConnection(e.userId),z("".concat(e.userName," left the meeting"))},a=async e=>{console.log("Received signal:",e);try{let{fromUserId:t,signal:s}=e;switch(s.type){case"offer":let a=await x.createAnswer(t,s.offer);o.sendSignal(t,{type:"answer",answer:a});break;case"answer":await x.handleAnswer(t,s.answer);break;case"ice-candidate":await x.handleIceCandidate(t,s.candidate)}}catch(e){console.error("Error handling signal:",e)}},i=e=>{A({id:Math.random().toString(36).substring(2,15),userId:e.userId,userName:e.userName,message:e.message,timestamp:new Date(e.timestamp)})};return e.on("user-joined",t),e.on("user-left",s),e.on("signal",a),e.on("chat-message",i),()=>{e.off("user-joined",t),e.off("user-left",s),e.off("signal",a),e.off("chat-message",i)}},[l]),l&&w)?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"animated-bg"}),(0,a.jsxs)("div",{className:"min-h-screen flex flex-col relative z-10",children:[(0,a.jsx)("div",{className:"glass-dark p-4 m-4 mb-0",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("h1",{className:"text-white text-xl font-semibold",children:"StreamIt Pro Meeting"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-white/70",children:[(0,a.jsx)(F.Z,{className:"h-4 w-4"}),(0,a.jsxs)("span",{children:[b.size+1," participants"]})]}),(0,a.jsxs)("div",{className:"text-white/50 text-sm",children:["ID: ",t]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>{let e="".concat(window.location.origin,"/room/").concat(t);navigator.clipboard.writeText(e),z("Meeting link copied to clipboard!")},className:"glass-button p-2 text-white hover:text-purple-300",title:"Copy meeting link",children:(0,a.jsx)(q.Z,{className:"h-5 w-5"})}),(0,a.jsx)("button",{onClick:()=>{navigator.clipboard.writeText(t),z("Meeting ID copied to clipboard!")},className:"glass-button p-2 text-white hover:text-purple-300",title:"Copy meeting ID",children:(0,a.jsx)(J.Z,{className:"h-5 w-5"})}),(0,a.jsx)("button",{onClick:()=>u(!m),className:"glass-button p-2 text-white hover:text-purple-300 ".concat(m?"bg-purple-500/30":""),title:"Settings",children:(0,a.jsx)(k.Z,{className:"h-5 w-5"})}),(0,a.jsx)("button",{onClick:()=>{document.fullscreenElement?(document.exitFullscreen(),g(!1)):(document.documentElement.requestFullscreen(),g(!0))},className:"glass-button p-2 text-white hover:text-purple-300",title:"Toggle fullscreen",children:p?(0,a.jsx)(Y.Z,{className:"h-5 w-5"}):(0,a.jsx)(K.Z,{className:"h-5 w-5"})}),(0,a.jsx)("button",{onClick:()=>{confirm("Are you sure you want to leave the meeting?")&&(o.leaveRoom(),x.cleanup(),P(),s.push("/"))},className:"bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors",title:"Leave meeting",children:(0,a.jsx)(Q.Z,{className:"h-5 w-5"})})]})]})}),(0,a.jsxs)("div",{className:"flex-1 flex p-4 pt-0 gap-4",children:[(0,a.jsxs)("div",{className:"w-2/3 flex flex-col",children:[(0,a.jsx)(N,{}),(0,a.jsx)(U,{})]}),(0,a.jsxs)("div",{className:"w-1/3 flex flex-col gap-4",children:[(0,a.jsx)("div",{className:"h-1/2",children:(0,a.jsx)(G,{})}),(0,a.jsx)("div",{className:"h-1/2",children:(0,a.jsx)(R,{})})]})]}),(0,a.jsx)(Z,{isOpen:m,onClose:()=>u(!1)}),v&&(0,a.jsx)("div",{className:"fixed top-4 right-4 glass p-4 text-white z-50 fade-in",children:v})]})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"animated-bg"}),(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center relative z-10",children:(0,a.jsxs)("div",{className:"glass p-8 text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-16 w-16 border-4 border-white/30 border-t-white mx-auto mb-6"}),(0,a.jsx)("h2",{className:"text-2xl font-semibold text-white mb-2",children:"Connecting to Meeting"}),(0,a.jsx)("p",{className:"text-white/70",children:"Please wait while we set up your video call..."})]})})]})}function X(){let e=(0,r.useParams)(),t=(0,r.useRouter)(),s=e.id,[l,c]=(0,i.useState)(""),[o,d]=(0,i.useState)(!1),[h,m]=(0,i.useState)(!1),{setRoomId:u,setCurrentUser:x}=(0,n.g)(),p=(0,i.useCallback)(async e=>{if(!e.trim()){alert("Please enter your name");return}d(!0);try{u(s);let t={id:Math.random().toString(36).substring(2,15),name:e.trim(),isAudioMuted:!1,isVideoMuted:!1,isScreenSharing:!1,role:"host",isHandRaised:!1,joinedAt:new Date,lastActivity:new Date};x(t),localStorage.setItem("userName",e.trim()),m(!0)}catch(e){console.error("Error joining room:",e),alert("Failed to join room. Please try again.")}finally{d(!1)}},[s,x,u]);return((0,i.useEffect)(()=>{let e=localStorage.getItem("userName");e&&(c(e),p(e))},[p]),h)?(0,a.jsx)(W,{roomId:s}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"animated-bg"}),(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center p-4 relative z-10",children:(0,a.jsxs)("div",{className:"glass p-8 max-w-md w-full",children:[(0,a.jsxs)("div",{className:"text-center mb-6",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-white mb-2",children:"Join Meeting"}),(0,a.jsxs)("p",{className:"text-white/70",children:["Meeting ID: ",(0,a.jsx)("span",{className:"font-mono font-semibold text-purple-300",children:s})]})]}),(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),p(l)},className:"space-y-4",children:[(0,a.jsx)("input",{type:"text",placeholder:"Enter your name",value:l,onChange:e=>c(e.target.value),disabled:o,autoFocus:!0,className:"glass-input w-full"}),(0,a.jsxs)("div",{className:"flex gap-3",children:[(0,a.jsx)("button",{type:"button",onClick:()=>t.push("/"),disabled:o,className:"btn-secondary flex-1",children:"Back"}),(0,a.jsx)("button",{type:"submit",disabled:o||!l.trim(),className:"btn-primary flex-1",children:o?"Joining...":"Join Meeting"})]})]})]})})]})}},2097:function(e,t,s){"use strict";s.d(t,{g:function(){return n}});var a=s(9625),i=s(6885);let r={roomId:null,isConnected:!1,roomLocked:!1,currentUser:null,participants:new Map,localStream:null,isAudioMuted:!1,isVideoMuted:!1,isScreenSharing:!1,messages:[],unreadCount:0,messageHistory:new Map,isChatOpen:!1,isSettingsOpen:!1,securitySettings:{encryptionEnabled:!0,antiSpamEnabled:!0,maxMessagesPerMinute:10,allowScreenShare:!0,allowFileSharing:!0,requireApprovalToJoin:!1},adminControls:{canMuteAll:!0,canMuteParticipant:!0,canRemoveParticipant:!0,canControlCamera:!0,canManageRoles:!0},blockedUsers:new Set,spamDetection:new Map},n=(0,a.Ue)()((0,i.mW)((e,t)=>({...r,setRoomId:t=>e({roomId:t}),setConnected:t=>e({isConnected:t}),setCurrentUser:t=>e({currentUser:t}),addParticipant:t=>e(e=>{let s=new Map(e.participants);return s.set(t.id,t),{participants:s}}),removeParticipant:t=>e(e=>{let s=new Map(e.participants);return s.delete(t),{participants:s}}),updateParticipant:(t,s)=>e(e=>{let a=new Map(e.participants),i=a.get(t);return i&&a.set(t,{...i,...s}),{participants:a}}),setLocalStream:t=>e({localStream:t}),toggleAudio:()=>e(e=>{let t=!e.isAudioMuted;return e.localStream&&e.localStream.getAudioTracks().forEach(e=>{e.enabled=!t}),{isAudioMuted:t}}),toggleVideo:()=>e(e=>{let t=!e.isVideoMuted;return e.localStream&&e.localStream.getVideoTracks().forEach(e=>{e.enabled=!t}),{isVideoMuted:t}}),toggleScreenShare:()=>e(e=>({isScreenSharing:!e.isScreenSharing})),addMessage:t=>e(e=>{if(e.securitySettings.antiSpamEnabled){let s=Date.now(),a=e.spamDetection.get(t.userId)||{count:0,lastReset:s};s-a.lastReset>6e4&&(a.count=0,a.lastReset=s),a.count++,e.spamDetection.set(t.userId,a),a.count>e.securitySettings.maxMessagesPerMinute&&(t.isSpam=!0)}return{messages:[...e.messages,t],unreadCount:e.isChatOpen?e.unreadCount:e.unreadCount+1}}),clearUnreadCount:()=>e({unreadCount:0}),toggleChat:()=>e(e=>({isChatOpen:!e.isChatOpen,unreadCount:e.isChatOpen?e.unreadCount:0})),toggleSettings:()=>e(e=>({isSettingsOpen:!e.isSettingsOpen})),muteParticipant:t=>e(e=>{var s,a;let i=e.participants.get(t);if(i&&((null===(s=e.currentUser)||void 0===s?void 0:s.role)==="host"||(null===(a=e.currentUser)||void 0===a?void 0:a.role)==="co-host")){let s=new Map(e.participants);return s.set(t,{...i,isAudioMuted:!0}),{participants:s}}return e}),muteAllParticipants:()=>e(e=>{var t,s;if((null===(t=e.currentUser)||void 0===t?void 0:t.role)==="host"||(null===(s=e.currentUser)||void 0===s?void 0:s.role)==="co-host"){let t=new Map;return e.participants.forEach((e,s)=>{t.set(s,{...e,isAudioMuted:!0})}),{participants:t}}return e}),removeParticipantAsAdmin:t=>e(e=>{var s,a;if((null===(s=e.currentUser)||void 0===s?void 0:s.role)==="host"||(null===(a=e.currentUser)||void 0===a?void 0:a.role)==="co-host"){let s=new Map(e.participants);return s.delete(t),{participants:s}}return e}),promoteToCoHost:t=>e(e=>{var s;let a=e.participants.get(t);if(a&&(null===(s=e.currentUser)||void 0===s?void 0:s.role)==="host"){let s=new Map(e.participants);return s.set(t,{...a,role:"co-host"}),{participants:s}}return e}),demoteFromCoHost:t=>e(e=>{var s;let a=e.participants.get(t);if(a&&(null===(s=e.currentUser)||void 0===s?void 0:s.role)==="host"){let s=new Map(e.participants);return s.set(t,{...a,role:"participant"}),{participants:s}}return e}),toggleRoomLock:()=>e(e=>{var t;return(null===(t=e.currentUser)||void 0===t?void 0:t.role)==="host"?{roomLocked:!e.roomLocked}:e}),blockUser:t=>e(e=>{var s,a;if((null===(s=e.currentUser)||void 0===s?void 0:s.role)==="host"||(null===(a=e.currentUser)||void 0===a?void 0:a.role)==="co-host"){let s=new Set(e.blockedUsers);return s.add(t),{blockedUsers:s}}return e}),unblockUser:t=>e(e=>{var s,a;if((null===(s=e.currentUser)||void 0===s?void 0:s.role)==="host"||(null===(a=e.currentUser)||void 0===a?void 0:a.role)==="co-host"){let s=new Set(e.blockedUsers);return s.delete(t),{blockedUsers:s}}return e}),updateSecuritySettings:t=>e(e=>{var s;return(null===(s=e.currentUser)||void 0===s?void 0:s.role)==="host"?{securitySettings:{...e.securitySettings,...t}}:e}),detectSpam:e=>{let s=t();if(!s.securitySettings.antiSpamEnabled)return!1;let a=s.spamDetection.get(e);return!(!a||Date.now()-a.lastReset>6e4)&&a.count>s.securitySettings.maxMessagesPerMinute},reset:()=>e(r)}),{name:"video-call-store"}))}},function(e){e.O(0,[74,85,971,117,744],function(){return e(e.s=9256)}),_N_E=e.O()}]);