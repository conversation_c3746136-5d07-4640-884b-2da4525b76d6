{"version": 3, "file": "static/chunks/app/about/page-7a3ace58ce01c812.js", "mappings": "oFAAAA,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,2LCKO,IAAME,EAAU,gBAER,SAASC,IACtB,MACE,GAAAC,EAAAC,IAAA,EAAAD,EAAAE,QAAA,YAEE,GAAAF,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,gBAEf,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,6CAEb,GAAAL,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,0CACb,GAAAL,EAAAG,GAAA,EAACI,KAAAA,CAAGF,UAAU,sDAA6C,uBAG3D,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,4EAAmE,6JAQpF,GAAAL,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,6BACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,oDACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,WACC,GAAAJ,EAAAG,GAAA,EAACM,KAAAA,CAAGJ,UAAU,8CAAqC,gBACnD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,sCAA6B,sQAK1C,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,8BAAqB,sPAKlC,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,mCACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,kCACb,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,8CAAqC,SACpD,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,iCAAwB,oBAEzC,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,kCACb,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,8CAAqC,SACpD,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,iCAAwB,uBAI7C,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sBACb,GAAAL,EAAAG,GAAA,EAACO,KAAAA,CAAGL,UAAU,8CAAqC,eACnD,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sBACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,mCACb,GAAAL,EAAAG,GAAA,EAACQ,EAAAA,CAAKA,CAAAA,CAACN,UAAU,iCACjB,GAAAL,EAAAC,IAAA,EAACG,MAAAA,WACC,GAAAJ,EAAAG,GAAA,EAACS,KAAAA,CAAGP,UAAU,yCAAgC,eAC9C,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,iCAAwB,2GAKzC,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,mCACb,GAAAL,EAAAG,GAAA,EAACU,EAAAA,CAAMA,CAAAA,CAACR,UAAU,gCAClB,GAAAL,EAAAC,IAAA,EAACG,MAAAA,WACC,GAAAJ,EAAAG,GAAA,EAACS,KAAAA,CAAGP,UAAU,yCAAgC,aAC9C,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,iCAAwB,yGAKzC,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,mCACb,GAAAL,EAAAG,GAAA,EAACW,EAAAA,CAAKA,CAAAA,CAACT,UAAU,8BACjB,GAAAL,EAAAC,IAAA,EAACG,MAAAA,WACC,GAAAJ,EAAAG,GAAA,EAACS,KAAAA,CAAGP,UAAU,yCAAgC,iBAC9C,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,iCAAwB,4GAYnD,GAAAL,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,8BACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,8BACb,GAAAL,EAAAG,GAAA,EAACM,KAAAA,CAAGJ,UAAU,8CAAqC,kBACnD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,mDAA0C,gIAKzD,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sCACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,kCACb,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,6HACb,GAAAL,EAAAG,GAAA,EAACY,OAAAA,CAAKV,UAAU,yCAAgC,SAElD,GAAAL,EAAAG,GAAA,EAACO,KAAAA,CAAGL,UAAU,iDAAwC,eACtD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,gCAAuB,qBACpC,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,iCAAwB,iHAKvC,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,kCACb,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,2HACb,GAAAL,EAAAG,GAAA,EAACY,OAAAA,CAAKV,UAAU,yCAAgC,SAElD,GAAAL,EAAAG,GAAA,EAACO,KAAAA,CAAGL,UAAU,iDAAwC,kBACtD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,8BAAqB,qBAClC,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,iCAAwB,iGAKvC,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,kCACb,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,+HACb,GAAAL,EAAAG,GAAA,EAACY,OAAAA,CAAKV,UAAU,yCAAgC,SAElD,GAAAL,EAAAG,GAAA,EAACO,KAAAA,CAAGL,UAAU,iDAAwC,sBACtD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,+BAAsB,mBACnC,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,iCAAwB,oGAS7C,GAAAL,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,8BACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,8BACb,GAAAL,EAAAG,GAAA,EAACM,KAAAA,CAAGJ,UAAU,8CAAqC,mBACnD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,mDAA0C,4FAKzD,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,qDACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,kCACb,GAAAL,EAAAG,GAAA,EAACa,EAAAA,CAAKA,CAAAA,CAACX,UAAU,2CACjB,GAAAL,EAAAG,GAAA,EAACS,KAAAA,CAAGP,UAAU,yCAAgC,WAC9C,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,iCAAwB,4CAEvC,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,kCACb,GAAAL,EAAAG,GAAA,EAACU,EAAAA,CAAMA,CAAAA,CAACR,UAAU,0CAClB,GAAAL,EAAAG,GAAA,EAACS,KAAAA,CAAGP,UAAU,yCAAgC,0BAC9C,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,iCAAwB,yCAEvC,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,kCACb,GAAAL,EAAAG,GAAA,EAACc,EAAAA,CAAGA,CAAAA,CAACZ,UAAU,2CACf,GAAAL,EAAAG,GAAA,EAACS,KAAAA,CAAGP,UAAU,yCAAgC,mBAC9C,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,iCAAwB,sCAEvC,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,kCACb,GAAAL,EAAAG,GAAA,EAACe,EAAAA,CAAUA,CAAAA,CAACb,UAAU,yCACtB,GAAAL,EAAAG,GAAA,EAACS,KAAAA,CAAGP,UAAU,yCAAgC,wBAC9C,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,iCAAwB,2CAO7C,GAAAL,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,yCACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,uBACb,GAAAL,EAAAG,GAAA,EAACM,KAAAA,CAAGJ,UAAU,8CAAqC,0BACnD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,sCAA6B,qFAG1C,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,2DACb,GAAAL,EAAAG,GAAA,EAACgB,SAAAA,CAAOd,UAAU,iCAAwB,qBAC1C,GAAAL,EAAAG,GAAA,EAACgB,SAAAA,CAAOd,UAAU,mCAA0B,iCAQ5D,mFC9Lee,EAAA,CACbC,MAAO,6BACPC,MAAO,GACPC,OAAQ,GACRC,QAAS,YACTC,KAAM,OACNC,OAAQ,eACRC,YAAa,EACbC,cAAe,QACfC,eAAgB,OAClB;;;;;GCmBa,IAAAC,EAAc,GACzBC,EACGC,OAAA,CAAQ,qBAAsB,SAC9BC,WAAY,GACZC,IAAK,GAEJC,EAAmB,CAACC,EAAkBC,KAC1C,IAAMC,EAAYC,CAAAA,EAAAA,EAAAA,UAAAA,EAChB,CAAAC,EAAiHC,QAAhH,CAAEC,MAAAA,EAAQ,eAAgBC,KAAAA,EAAO,EAAI,CAAAhB,YAAAA,EAAc,CAAG,CAAAiB,oBAAAA,CAAA,CAAqBvC,UAAAA,EAAY,GAAIwC,SAAAA,CAAa,IAAAC,EAAA,CAAAN,QACvGO,CAAAA,EAAAA,EAAAA,aAAAA,EACE,MACA,CACEN,IAAAA,EACA,GAAGrB,CAAA,CACHE,MAAOqB,EACPpB,OAAQoB,EACRjB,OAAQgB,EACRf,YAAaiB,EAAsBI,GAAAA,OAAOrB,GAAoBqB,OAAOL,GAAQhB,EAC7EtB,UAAW,CAAC,SAAoB,UAAyB4C,MAAA,CAAzBnB,EAAYM,IAAa/B,EAAW,CAAA6C,IAAA,CAAK,KACzE,GAAGJ,CAAA,EAEL,IACKT,EAASc,GAAA,CAAI,OAAC,CAACC,EAAKC,EAAW,CAAAb,QAAAO,CAAAA,EAAAA,EAAAA,aAAAA,EAAcK,EAAKC,QACjDC,MAAMC,OAAA,CAAQV,GAAYA,EAAW,CAACA,EAAQ,CACpD,IAMC,OAFPP,EAAUkB,WAAA,CAAc,GAAGP,MAAA,CAAAb,GAEpBE,CACT;;;;;GC/CM,IAAA3B,EAAQwB,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,QAAS,CACtC,CAAC,SAAU,CAAEsB,GAAI,KAAMC,GAAI,IAAKC,EAAG,IAAKC,IAAK,UAAU,CACvD,CAAC,OAAQ,CAAEC,EAAG,0CAA2CD,IAAK,UAAU,CACzE;;;;;GCHK,IAAA5C,EAAQmB,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,QAAS,CACtC,CAAC,SAAU,CAAEsB,GAAI,KAAMC,GAAI,KAAMC,EAAG,KAAMC,IAAK,UAAU,CACzD,CAAC,OAAQ,CAAEC,EAAG,kDAAmDD,IAAK,UAAU,CAChF,CAAC,OAAQ,CAAEC,EAAG,WAAYD,IAAK,UAAU,CAC1C;;;;;GCJK,IAAA1C,EAAaiB,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,aAAc,CAChD,CACE,OACA,CACE0B,EAAG,8HACHD,IAAK,QACP,EACF,CACD;;;;;GCRK,IAAA9C,EAAQqB,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,QAAS,CACtC,CACE,OACA,CACE0B,EAAG,2IACHD,IAAK,QACP,EACF,CACD;;;;;GCRK,IAAA/C,EAASsB,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,SAAU,CACxC,CAAC,OAAQ,CAAE0B,EAAG,6CAA8CD,IAAK,UAAU,CAC5E;;;;;GCFK,IAAA3C,EAAMkB,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,MAAO,CAClC,CAAC,UAAW,CAAE2B,OAAQ,yCAA0CF,IAAK,UAAU,CAChF", "sources": ["webpack://_N_E/?7c81", "webpack://_N_E/./app/about/page.tsx", "webpack://_N_E/../../src/defaultAttributes.ts", "webpack://_N_E/../../src/createLucideIcon.ts", "webpack://_N_E/../../../src/icons/award.ts", "webpack://_N_E/../../../src/icons/globe.ts", "webpack://_N_E/../../../src/icons/headphones.ts", "webpack://_N_E/../../../src/icons/heart.ts", "webpack://_N_E/../../../src/icons/shield.ts", "webpack://_N_E/../../../src/icons/zap.ts"], "sourcesContent": ["import(/* webpackMode: \"eager\" */ \"/Volumes/Apps/Websites/streamit-main/app/about/page.tsx\");\n", "'use client'\n\nimport { Video, Users, Shield, Zap, Award, TrendingUp, Heart, Globe, Clock, Headphones } from 'lucide-react'\n\n// Force dynamic rendering\nexport const dynamic = 'force-dynamic'\n\nexport default function AboutPage() {\n  return (\n    <>\n      {/* Animated Background */}\n      <div className=\"animated-bg\"></div>\n\n      <div className=\"min-h-screen pt-20 relative z-10\">\n        {/* Hero Section */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-7xl mx-auto text-center\">\n            <h1 className=\"text-5xl font-bold text-white mb-6 fade-in\">\n              About StreamIt Pro\n            </h1>\n            <p className=\"text-xl text-white/80 max-w-3xl mx-auto leading-relaxed slide-up\">\n              We're on a mission to revolutionize remote communication and make video conferencing \n              accessible, secure, and delightful for teams around the world.\n            </p>\n          </div>\n        </section>\n\n        {/* Mission Section */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-7xl mx-auto\">\n            <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n              <div>\n                <h2 className=\"text-4xl font-bold text-white mb-6\">Our Mission</h2>\n                <p className=\"text-xl text-white/80 mb-6\">\n                  At StreamIt Pro, we believe that distance should never be a barrier to meaningful \n                  collaboration. Our platform is designed to bring teams together with crystal-clear \n                  video, advanced audio processing, and intuitive features that make remote work feel natural.\n                </p>\n                <p className=\"text-white/70 mb-8\">\n                  Founded in 2019 by a team of passionate engineers and designers, we've grown from a \n                  small startup to a trusted platform serving millions of users worldwide. Our commitment \n                  to innovation, security, and user experience drives everything we do.\n                </p>\n                <div className=\"grid grid-cols-2 gap-6\">\n                  <div className=\"glass p-6 text-center\">\n                    <div className=\"text-3xl font-bold text-white mb-2\">10M+</div>\n                    <div className=\"text-white/60 text-sm\">Active Users</div>\n                  </div>\n                  <div className=\"glass p-6 text-center\">\n                    <div className=\"text-3xl font-bold text-white mb-2\">150+</div>\n                    <div className=\"text-white/60 text-sm\">Countries</div>\n                  </div>\n                </div>\n              </div>\n              <div className=\"glass p-8\">\n                <h3 className=\"text-2xl font-bold text-white mb-6\">Our Values</h3>\n                <div className=\"space-y-6\">\n                  <div className=\"flex items-start gap-4\">\n                    <Award className=\"h-6 w-6 text-purple-400 mt-1\" />\n                    <div>\n                      <h4 className=\"text-white font-semibold mb-2\">Excellence</h4>\n                      <p className=\"text-white/70 text-sm\">\n                        We strive for excellence in every aspect of our platform, from video quality to user experience.\n                      </p>\n                    </div>\n                  </div>\n                  <div className=\"flex items-start gap-4\">\n                    <Shield className=\"h-6 w-6 text-green-400 mt-1\" />\n                    <div>\n                      <h4 className=\"text-white font-semibold mb-2\">Security</h4>\n                      <p className=\"text-white/70 text-sm\">\n                        Your privacy and data security are paramount. We implement industry-leading security measures.\n                      </p>\n                    </div>\n                  </div>\n                  <div className=\"flex items-start gap-4\">\n                    <Heart className=\"h-6 w-6 text-red-400 mt-1\" />\n                    <div>\n                      <h4 className=\"text-white font-semibold mb-2\">User-Centric</h4>\n                      <p className=\"text-white/70 text-sm\">\n                        Every feature we build is designed with our users' needs and feedback at the center.\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* Team Section */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-7xl mx-auto\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-4xl font-bold text-white mb-4\">Meet Our Team</h2>\n              <p className=\"text-xl text-white/70 max-w-3xl mx-auto\">\n                Our diverse team of engineers, designers, and innovators work together to create the best video conferencing experience.\n              </p>\n            </div>\n\n            <div className=\"grid md:grid-cols-3 gap-8\">\n              <div className=\"glass p-8 text-center\">\n                <div className=\"w-24 h-24 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <span className=\"text-white text-2xl font-bold\">JS</span>\n                </div>\n                <h3 className=\"text-xl font-semibold text-white mb-2\">John Smith</h3>\n                <p className=\"text-purple-400 mb-3\">CEO & Co-Founder</p>\n                <p className=\"text-white/70 text-sm\">\n                  Former VP of Engineering at major tech companies. Passionate about building products that connect people.\n                </p>\n              </div>\n\n              <div className=\"glass p-8 text-center\">\n                <div className=\"w-24 h-24 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <span className=\"text-white text-2xl font-bold\">EJ</span>\n                </div>\n                <h3 className=\"text-xl font-semibold text-white mb-2\">Emily Johnson</h3>\n                <p className=\"text-blue-400 mb-3\">CTO & Co-Founder</p>\n                <p className=\"text-white/70 text-sm\">\n                  WebRTC expert with 15+ years in real-time communications. Leads our technical innovation.\n                </p>\n              </div>\n\n              <div className=\"glass p-8 text-center\">\n                <div className=\"w-24 h-24 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <span className=\"text-white text-2xl font-bold\">MR</span>\n                </div>\n                <h3 className=\"text-xl font-semibold text-white mb-2\">Michael Rodriguez</h3>\n                <p className=\"text-green-400 mb-3\">Head of Design</p>\n                <p className=\"text-white/70 text-sm\">\n                  Award-winning designer focused on creating intuitive and beautiful user experiences.\n                </p>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* Technology Section */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-7xl mx-auto\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-4xl font-bold text-white mb-4\">Our Technology</h2>\n              <p className=\"text-xl text-white/70 max-w-3xl mx-auto\">\n                Built on cutting-edge technologies to deliver the best video conferencing experience\n              </p>\n            </div>\n\n            <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-6\">\n              <div className=\"glass p-6 text-center\">\n                <Globe className=\"h-12 w-12 text-purple-400 mx-auto mb-3\" />\n                <h4 className=\"text-white font-semibold mb-2\">WebRTC</h4>\n                <p className=\"text-white/60 text-sm\">Real-time peer-to-peer communication</p>\n              </div>\n              <div className=\"glass p-6 text-center\">\n                <Shield className=\"h-12 w-12 text-green-400 mx-auto mb-3\" />\n                <h4 className=\"text-white font-semibold mb-2\">End-to-End Encryption</h4>\n                <p className=\"text-white/60 text-sm\">Military-grade security protocols</p>\n              </div>\n              <div className=\"glass p-6 text-center\">\n                <Zap className=\"h-12 w-12 text-yellow-400 mx-auto mb-3\" />\n                <h4 className=\"text-white font-semibold mb-2\">Edge Computing</h4>\n                <p className=\"text-white/60 text-sm\">Global CDN for minimal latency</p>\n              </div>\n              <div className=\"glass p-6 text-center\">\n                <Headphones className=\"h-12 w-12 text-blue-400 mx-auto mb-3\" />\n                <h4 className=\"text-white font-semibold mb-2\">AI Audio Processing</h4>\n                <p className=\"text-white/60 text-sm\">Advanced noise cancellation</p>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* Contact CTA */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-4xl mx-auto text-center\">\n            <div className=\"glass p-12\">\n              <h2 className=\"text-3xl font-bold text-white mb-4\">Ready to Get Started?</h2>\n              <p className=\"text-xl text-white/70 mb-8\">\n                Join millions of users who trust StreamIt Pro for their video conferencing needs\n              </p>\n              <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n                <button className=\"btn-primary px-8 py-3\">Start Free Trial</button>\n                <button className=\"btn-secondary px-8 py-3\">Contact Sales</button>\n              </div>\n            </div>\n          </div>\n        </section>\n      </div>\n    </>\n  )\n}\n", "export default {\n  xmlns: 'http://www.w3.org/2000/svg',\n  width: 24,\n  height: 24,\n  viewBox: '0 0 24 24',\n  fill: 'none',\n  stroke: 'currentColor',\n  strokeWidth: 2,\n  strokeLinecap: 'round',\n  strokeLinejoin: 'round',\n};\n", "import {\n  forwardRef,\n  createElement,\n  ReactSVG,\n  SVGProps,\n  ForwardRefExoticComponent,\n  RefAttributes,\n} from 'react';\nimport defaultAttributes from './defaultAttributes';\n\nexport type IconNode = [elementName: keyof ReactSVG, attrs: Record<string, string>][];\n\nexport type SVGAttributes = Partial<SVGProps<SVGSVGElement>>;\ntype ComponentAttributes = RefAttributes<SVGSVGElement> & SVGAttributes;\n\nexport interface LucideProps extends ComponentAttributes {\n  size?: string | number;\n  absoluteStrokeWidth?: boolean;\n}\n\nexport type LucideIcon = ForwardRefExoticComponent<LucideProps>;\n/**\n * Converts string to KebabCase\n * Copied from scripts/helper. If anyone knows how to properly import it here\n * then please fix it.\n *\n * @param {string} string\n * @returns {string} A kebabized string\n */\nexport const toKebabCase = (string: string) =>\n  string\n    .replace(/([a-z0-9])([A-Z])/g, '$1-$2')\n    .toLowerCase()\n    .trim();\n\nconst createLucideIcon = (iconName: string, iconNode: IconNode): LucideIcon => {\n  const Component = forwardRef<SVGSVGElement, LucideProps>(\n    ({ color = 'currentColor', size = 24, strokeWidth = 2, absoluteStrokeWidth, className = '', children, ...rest }, ref) =>\n      createElement(\n        'svg',\n        {\n          ref,\n          ...defaultAttributes,\n          width: size,\n          height: size,\n          stroke: color,\n          strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n          className: ['lucide', `lucide-${toKebabCase(iconName)}`, className].join(' '),\n          ...rest,\n        },\n        [\n          ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n          ...(Array.isArray(children) ? children : [children]),\n        ]\n      )\n  );\n\n  Component.displayName = `${iconName}`;\n\n  return Component;\n};\n\nexport default createLucideIcon;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Award\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjgiIHI9IjYiIC8+CiAgPHBhdGggZD0iTTE1LjQ3NyAxMi44OSAxNyAyMmwtNS0zLTUgMyAxLjUyMy05LjExIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/award\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Award = createLucideIcon('Award', [\n  ['circle', { cx: '12', cy: '8', r: '6', key: '1vp47v' }],\n  ['path', { d: 'M15.477 12.89 17 22l-5-3-5 3 1.523-9.11', key: 'em7aur' }],\n]);\n\nexport default Award;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Globe\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8cGF0aCBkPSJNMTIgMmExNC41IDE0LjUgMCAwIDAgMCAyMCAxNC41IDE0LjUgMCAwIDAgMC0yMCIgLz4KICA8cGF0aCBkPSJNMiAxMmgyMCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/globe\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Globe = createLucideIcon('Globe', [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['path', { d: 'M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20', key: '13o1zl' }],\n  ['path', { d: 'M2 12h20', key: '9i4pu4' }],\n]);\n\nexport default Globe;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Headphones\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyAxNGgzYTIgMiAwIDAgMSAyIDJ2M2EyIDIgMCAwIDEtMiAySDVhMiAyIDAgMCAxLTItMnYtN2E5IDkgMCAwIDEgMTggMHY3YTIgMiAwIDAgMS0yIDJoLTFhMiAyIDAgMCAxLTItMnYtM2EyIDIgMCAwIDEgMi0yaDMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/headphones\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Headphones = createLucideIcon('Headphones', [\n  [\n    'path',\n    {\n      d: 'M3 14h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7a9 9 0 0 1 18 0v7a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3',\n      key: '1xhozi',\n    },\n  ],\n]);\n\nexport default Headphones;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Heart\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTkgMTRjMS40OS0xLjQ2IDMtMy4yMSAzLTUuNUE1LjUgNS41IDAgMCAwIDE2LjUgM2MtMS43NiAwLTMgLjUtNC41IDItMS41LTEuNS0yLjc0LTItNC41LTJBNS41IDUuNSAwIDAgMCAyIDguNWMwIDIuMyAxLjUgNC4wNSAzIDUuNWw3IDdaIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/heart\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Heart = createLucideIcon('Heart', [\n  [\n    'path',\n    {\n      d: 'M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z',\n      key: 'c3ymky',\n    },\n  ],\n]);\n\nexport default Heart;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Shield\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMjJzOC00IDgtMTBWNWwtOC0zLTggM3Y3YzAgNiA4IDEwIDggMTAiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/shield\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Shield = createLucideIcon('Shield', [\n  ['path', { d: 'M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10', key: '1irkt0' }],\n]);\n\nexport default Shield;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Zap\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWdvbiBwb2ludHM9IjEzIDIgMyAxNCAxMiAxNCAxMSAyMiAyMSAxMCAxMiAxMCAxMyAyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/zap\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Zap = createLucideIcon('Zap', [\n  ['polygon', { points: '13 2 3 14 12 14 11 22 21 10 12 10 13 2', key: '45s27k' }],\n]);\n\nexport default Zap;\n"], "names": ["Promise", "resolve", "then", "__webpack_require__", "bind", "dynamic", "AboutPage", "react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__", "jsxs", "Fragment", "jsx", "div", "className", "section", "h1", "p", "h2", "h3", "Award", "h4", "Shield", "Heart", "span", "Globe", "Zap", "Headphones", "button", "defaultAttributes", "xmlns", "width", "height", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "toKebabCase", "string", "replace", "toLowerCase", "trim", "createLucideIcon", "iconName", "iconNode", "Component", "forwardRef", "param", "ref", "color", "size", "absoluteStrokeWidth", "children", "rest", "createElement", "Number", "concat", "join", "map", "tag", "attrs", "Array", "isArray", "displayName", "cx", "cy", "r", "key", "d", "points"], "sourceRoot": ""}