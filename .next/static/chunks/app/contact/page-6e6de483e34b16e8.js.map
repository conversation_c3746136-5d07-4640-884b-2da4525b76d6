{"version": 3, "file": "static/chunks/app/contact/page-6e6de483e34b16e8.js", "mappings": "oFAAAA,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,sMCKO,IAAME,EAAU,gBAGR,SAASC,IACtB,GAAM,CAACC,EAAUC,EAAY,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,CACvCC,KAAM,GACNC,MAAO,GACPC,QAAS,GACTC,QAAS,GACTC,QAAS,EACX,GAUMC,EAAe,IACnBP,EAAY,CACV,GAAGD,CAAQ,CACX,CAACS,EAAEC,MAAM,CAACP,IAAI,CAAC,CAAEM,EAAEC,MAAM,CAACC,KAAK,EAEnC,EAEA,MACE,GAAAC,EAAAC,IAAA,EAAAD,EAAAE,QAAA,YAEE,GAAAF,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,gBAEf,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,6CAEb,GAAAL,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,0CACb,GAAAL,EAAAG,GAAA,EAACI,KAAAA,CAAGF,UAAU,sDAA6C,eAG3D,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,4EAAmE,kIAOpF,GAAAL,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,8BACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,4CACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,kCACb,GAAAL,EAAAG,GAAA,EAACM,EAAAA,CAAaA,CAAAA,CAACJ,UAAU,2CACzB,GAAAL,EAAAG,GAAA,EAACO,KAAAA,CAAGL,UAAU,iDAAwC,cACtD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,8BAAqB,2CAGlC,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,iCAAwB,sBAGvC,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,kCACb,GAAAL,EAAAG,GAAA,EAACQ,EAAAA,CAAIA,CAAAA,CAACN,UAAU,yCAChB,GAAAL,EAAAG,GAAA,EAACO,KAAAA,CAAGL,UAAU,iDAAwC,UACtD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,8BAAqB,uDAGlC,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,iCAAwB,+BAGvC,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,kCACb,GAAAL,EAAAG,GAAA,EAACS,EAAAA,CAAKA,CAAAA,CAACP,UAAU,0CACjB,GAAAL,EAAAG,GAAA,EAACO,KAAAA,CAAGL,UAAU,iDAAwC,UACtD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,8BAAqB,qCAGlC,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,iCAAwB,yBAGvC,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,kCACb,GAAAL,EAAAG,GAAA,EAACU,EAAAA,CAAMA,CAAAA,CAACR,UAAU,2CAClB,GAAAL,EAAAG,GAAA,EAACO,KAAAA,CAAGL,UAAU,iDAAwC,WACtD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,8BAAqB,2BAGlC,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,iCAAwB,4BAKzC,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,uCAEb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sBACb,GAAAL,EAAAG,GAAA,EAACW,KAAAA,CAAGT,UAAU,8CAAqC,sBACnD,GAAAL,EAAAC,IAAA,EAACc,OAAAA,CAAKC,SA/EC,IACnBnB,EAAEoB,cAAc,GAEhBC,QAAQC,GAAG,CAAC,kBAAmB/B,GAC/BgC,MAAM,2DACN/B,EAAY,CAAEE,KAAM,GAAIC,MAAO,GAAIC,QAAS,GAAIC,QAAS,GAAIC,QAAS,EAAG,EAC3E,EAyE4CU,UAAU,sBACtC,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sCACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,WACC,GAAAJ,EAAAG,GAAA,EAACkB,QAAAA,CAAMC,QAAQ,OAAOjB,UAAU,wDAA+C,gBAG/E,GAAAL,EAAAG,GAAA,EAACoB,QAAAA,CACCC,KAAK,OACLC,GAAG,OACHlC,KAAK,OACLQ,MAAOX,EAASG,IAAI,CACpBmC,SAAU9B,EACV+B,SAAQ,GACRtB,UAAU,qBACVuB,YAAY,sBAGhB,GAAA5B,EAAAC,IAAA,EAACG,MAAAA,WACC,GAAAJ,EAAAG,GAAA,EAACkB,QAAAA,CAAMC,QAAQ,QAAQjB,UAAU,wDAA+C,oBAGhF,GAAAL,EAAAG,GAAA,EAACoB,QAAAA,CACCC,KAAK,QACLC,GAAG,QACHlC,KAAK,QACLQ,MAAOX,EAASI,KAAK,CACrBkC,SAAU9B,EACV+B,SAAQ,GACRtB,UAAU,qBACVuB,YAAY,yBAKlB,GAAA5B,EAAAC,IAAA,EAACG,MAAAA,WACC,GAAAJ,EAAAG,GAAA,EAACkB,QAAAA,CAAMC,QAAQ,UAAUjB,UAAU,wDAA+C,YAGlF,GAAAL,EAAAG,GAAA,EAACoB,QAAAA,CACCC,KAAK,OACLC,GAAG,UACHlC,KAAK,UACLQ,MAAOX,EAASK,OAAO,CACvBiC,SAAU9B,EACVS,UAAU,qBACVuB,YAAY,yBAIhB,GAAA5B,EAAAC,IAAA,EAACG,MAAAA,WACC,GAAAJ,EAAAG,GAAA,EAACkB,QAAAA,CAAMC,QAAQ,UAAUjB,UAAU,wDAA+C,cAGlF,GAAAL,EAAAC,IAAA,EAAC4B,SAAAA,CACCJ,GAAG,UACHlC,KAAK,UACLQ,MAAOX,EAASM,OAAO,CACvBgC,SAAU9B,EACV+B,SAAQ,GACRtB,UAAU,+BAEV,GAAAL,EAAAG,GAAA,EAAC2B,SAAAA,CAAO/B,MAAM,YAAG,qBACjB,GAAAC,EAAAG,GAAA,EAAC2B,SAAAA,CAAO/B,MAAM,mBAAU,oBACxB,GAAAC,EAAAG,GAAA,EAAC2B,SAAAA,CAAO/B,MAAM,mBAAU,sBACxB,GAAAC,EAAAG,GAAA,EAAC2B,SAAAA,CAAO/B,MAAM,iBAAQ,mBACtB,GAAAC,EAAAG,GAAA,EAAC2B,SAAAA,CAAO/B,MAAM,uBAAc,gBAC5B,GAAAC,EAAAG,GAAA,EAAC2B,SAAAA,CAAO/B,MAAM,oBAAW,mBAI7B,GAAAC,EAAAC,IAAA,EAACG,MAAAA,WACC,GAAAJ,EAAAG,GAAA,EAACkB,QAAAA,CAAMC,QAAQ,UAAUjB,UAAU,wDAA+C,cAGlF,GAAAL,EAAAG,GAAA,EAAC4B,WAAAA,CACCN,GAAG,UACHlC,KAAK,UACLQ,MAAOX,EAASO,OAAO,CACvB+B,SAAU9B,EACV+B,SAAQ,GACRK,KAAM,EACN3B,UAAU,iCACVuB,YAAY,sCAIhB,GAAA5B,EAAAC,IAAA,EAACgC,SAAAA,CACCT,KAAK,SACLnB,UAAU,sEAEV,GAAAL,EAAAG,GAAA,EAAC+B,EAAAA,CAAIA,CAAAA,CAAC7B,UAAU,YAAY,wBAOlC,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sBACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sBACb,GAAAL,EAAAG,GAAA,EAACO,KAAAA,CAAGL,UAAU,iDAAwC,iBACtD,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sBACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,oCACb,GAAAL,EAAAG,GAAA,EAACgC,EAAAA,CAAKA,CAAAA,CAAC9B,UAAU,4BACjB,GAAAL,EAAAC,IAAA,EAACG,MAAAA,WACC,GAAAJ,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,kCAAyB,oBACxC,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,iCAAwB,gCAG3C,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,oCACb,GAAAL,EAAAG,GAAA,EAACgC,EAAAA,CAAKA,CAAAA,CAAC9B,UAAU,4BACjB,GAAAL,EAAAC,IAAA,EAACG,MAAAA,WACC,GAAAJ,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,kCAAyB,aACxC,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,iCAAwB,iCAG3C,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,oCACb,GAAAL,EAAAG,GAAA,EAACgC,EAAAA,CAAKA,CAAAA,CAAC9B,UAAU,4BACjB,GAAAL,EAAAC,IAAA,EAACG,MAAAA,WACC,GAAAJ,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,kCAAyB,WACxC,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,iCAAwB,uBAM/C,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sBACb,GAAAL,EAAAG,GAAA,EAACO,KAAAA,CAAGL,UAAU,iDAAwC,gBACtD,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sBACb,GAAAL,EAAAG,GAAA,EAACiC,IAAAA,CAAEC,KAAK,IAAIhC,UAAU,kEAAyD,gCAG/E,GAAAL,EAAAG,GAAA,EAACiC,IAAAA,CAAEC,KAAK,IAAIhC,UAAU,kEAAyD,kBAG/E,GAAAL,EAAAG,GAAA,EAACiC,IAAAA,CAAEC,KAAK,IAAIhC,UAAU,kEAAyD,qBAG/E,GAAAL,EAAAG,GAAA,EAACiC,IAAAA,CAAEC,KAAK,IAAIhC,UAAU,kEAAyD,gBAG/E,GAAAL,EAAAG,GAAA,EAACiC,IAAAA,CAAEC,KAAK,IAAIhC,UAAU,kEAAyD,0BAMnF,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sBACb,GAAAL,EAAAG,GAAA,EAACO,KAAAA,CAAGL,UAAU,iDAAwC,qBACtD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,8BAAqB,+FAGlC,GAAAL,EAAAG,GAAA,EAAC8B,SAAAA,CAAO5B,UAAU,gCAAuB,2CAQnD,GAAAL,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,8BACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,8BACb,GAAAL,EAAAG,GAAA,EAACW,KAAAA,CAAGT,UAAU,8CAAqC,+BAGnD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,yBAAgB,oFAI/B,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,uBACb,GAAAL,EAAAG,GAAA,EAAC8B,SAAAA,CAAO5B,UAAU,uBAAc,8BAO9C,mFCjReiC,EAAA,CACbC,MAAO,6BACPC,MAAO,GACPC,OAAQ,GACRC,QAAS,YACTC,KAAM,OACNC,OAAQ,eACRC,YAAa,EACbC,cAAe,QACfC,eAAgB,OAClB;;;;;GCmBa,IAAAC,EAAc,GACzBC,EACGC,OAAA,CAAQ,qBAAsB,SAC9BC,WAAY,GACZC,IAAK,GAEJC,EAAmB,CAACC,EAAkBC,KAC1C,IAAMC,EAAYC,CAAAA,EAAAA,EAAAA,UAAAA,EAChB,CAAAC,EAAiHC,QAAhH,CAAEC,MAAAA,EAAQ,eAAgBC,KAAAA,EAAO,EAAI,CAAAhB,YAAAA,EAAc,CAAG,CAAAiB,oBAAAA,CAAA,CAAqBzD,UAAAA,EAAY,GAAI0D,SAAAA,CAAa,IAAAC,EAAA,CAAAN,QACvGO,CAAAA,EAAAA,EAAAA,aAAAA,EACE,MACA,CACEN,IAAAA,EACA,GAAGrB,CAAA,CACHE,MAAOqB,EACPpB,OAAQoB,EACRjB,OAAQgB,EACRf,YAAaiB,EAAsBI,GAAAA,OAAOrB,GAAoBqB,OAAOL,GAAQhB,EAC7ExC,UAAW,CAAC,SAAoB,UAAyB8D,MAAA,CAAzBnB,EAAYM,IAAajD,EAAW,CAAA+D,IAAA,CAAK,KACzE,GAAGJ,CAAA,EAEL,IACKT,EAASc,GAAA,CAAI,OAAC,CAACC,EAAKC,EAAW,CAAAb,QAAAO,CAAAA,EAAAA,EAAAA,aAAAA,EAAcK,EAAKC,QACjDC,MAAMC,OAAA,CAAQV,GAAYA,EAAW,CAACA,EAAQ,CACpD,IAMC,OAFPP,EAAUkB,WAAA,CAAc,GAAGP,MAAA,CAAAb,GAEpBE,CACT;;;;;GC/CM,IAAArB,EAAQkB,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,QAAS,CACtC,CAAC,SAAU,CAAEsB,GAAI,KAAMC,GAAI,KAAMC,EAAG,KAAMC,IAAK,UAAU,CACzD,CAAC,WAAY,CAAEC,OAAQ,mBAAoBD,IAAK,UAAU,CAC3D;;;;;GCHK,IAAAnE,EAAO0C,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,OAAQ,CACpC,CAAC,OAAQ,CAAEb,MAAO,KAAMC,OAAQ,KAAMuC,EAAG,IAAKC,EAAG,IAAKC,GAAI,IAAKJ,IAAK,UAAU,CAC9E,CAAC,OAAQ,CAAEK,EAAG,4CAA6CL,IAAK,UAAU,CAC3E;;;;;GCHK,IAAAjE,EAASwC,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,SAAU,CACxC,CAAC,OAAQ,CAAE8B,EAAG,iDAAkDL,IAAK,UAAU,CAC/E,CAAC,SAAU,CAAEH,GAAI,KAAMC,GAAI,KAAMC,EAAG,IAAKC,IAAK,UAAU,CACzD;;;;;GCHK,IAAArE,EAAgB4C,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,gBAAiB,CACtD,CAAC,OAAQ,CAAE8B,EAAG,uCAAwCL,IAAK,UAAU,CACtE;;;;;GCFK,IAAAlE,EAAQyC,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,QAAS,CACtC,CACE,OACA,CACE8B,EAAG,gSACHL,IAAK,QACP,EACF,CACD;;;;;GCRK,IAAA5C,EAAOmB,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,OAAQ,CACpC,CAAC,OAAQ,CAAE8B,EAAG,sBAAuBL,IAAK,UAAU,CACpD,CAAC,OAAQ,CAAEK,EAAG,cAAeL,IAAK,UAAU,CAC7C", "sources": ["webpack://_N_E/?b597", "webpack://_N_E/./app/contact/page.tsx", "webpack://_N_E/../../src/defaultAttributes.ts", "webpack://_N_E/../../src/createLucideIcon.ts", "webpack://_N_E/../../../src/icons/clock.ts", "webpack://_N_E/../../../src/icons/mail.ts", "webpack://_N_E/../../../src/icons/map-pin.ts", "webpack://_N_E/../../../src/icons/message-circle.ts", "webpack://_N_E/../../../src/icons/phone.ts", "webpack://_N_E/../../../src/icons/send.ts"], "sourcesContent": ["import(/* webpackMode: \"eager\" */ \"/Volumes/Apps/Websites/streamit-main/app/contact/page.tsx\");\n", "'use client'\n\nimport { useState } from 'react'\n\n// Force dynamic rendering\nexport const dynamic = 'force-dynamic'\nimport { MessageCircle, Mail, Phone, MapPin, Clock, Send } from 'lucide-react'\n\nexport default function ContactPage() {\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    company: '',\n    subject: '',\n    message: ''\n  })\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault()\n    // Handle form submission here\n    console.log('Form submitted:', formData)\n    alert('Thank you for your message! We\\'ll get back to you soon.')\n    setFormData({ name: '', email: '', company: '', subject: '', message: '' })\n  }\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    })\n  }\n\n  return (\n    <>\n      {/* Animated Background */}\n      <div className=\"animated-bg\"></div>\n\n      <div className=\"min-h-screen pt-20 relative z-10\">\n        {/* Hero Section */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-7xl mx-auto text-center\">\n            <h1 className=\"text-5xl font-bold text-white mb-6 fade-in\">\n              Contact Us\n            </h1>\n            <p className=\"text-xl text-white/80 max-w-3xl mx-auto leading-relaxed slide-up\">\n              Have questions or need support? We're here to help. Reach out to our team and we'll get back to you as soon as possible.\n            </p>\n          </div>\n        </section>\n\n        {/* Contact Methods */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-7xl mx-auto\">\n            <div className=\"grid md:grid-cols-4 gap-8 mb-16\">\n              <div className=\"glass p-8 text-center\">\n                <MessageCircle className=\"h-12 w-12 text-purple-400 mx-auto mb-4\" />\n                <h3 className=\"text-xl font-semibold text-white mb-3\">Live Chat</h3>\n                <p className=\"text-white/70 mb-4\">\n                  Get instant help from our support team\n                </p>\n                <p className=\"text-white/60 text-sm\">Available 24/7</p>\n              </div>\n\n              <div className=\"glass p-8 text-center\">\n                <Mail className=\"h-12 w-12 text-blue-400 mx-auto mb-4\" />\n                <h3 className=\"text-xl font-semibold text-white mb-3\">Email</h3>\n                <p className=\"text-white/70 mb-4\">\n                  Send us an email and we'll respond within 24 hours\n                </p>\n                <p className=\"text-white/60 text-sm\"><EMAIL></p>\n              </div>\n\n              <div className=\"glass p-8 text-center\">\n                <Phone className=\"h-12 w-12 text-green-400 mx-auto mb-4\" />\n                <h3 className=\"text-xl font-semibold text-white mb-3\">Phone</h3>\n                <p className=\"text-white/70 mb-4\">\n                  Call us for immediate assistance\n                </p>\n                <p className=\"text-white/60 text-sm\">+****************</p>\n              </div>\n\n              <div className=\"glass p-8 text-center\">\n                <MapPin className=\"h-12 w-12 text-orange-400 mx-auto mb-4\" />\n                <h3 className=\"text-xl font-semibold text-white mb-3\">Office</h3>\n                <p className=\"text-white/70 mb-4\">\n                  Visit our headquarters\n                </p>\n                <p className=\"text-white/60 text-sm\">San Francisco, CA</p>\n              </div>\n            </div>\n\n            {/* Contact Form and Info */}\n            <div className=\"grid lg:grid-cols-2 gap-12\">\n              {/* Contact Form */}\n              <div className=\"glass p-8\">\n                <h2 className=\"text-2xl font-bold text-white mb-6\">Send us a Message</h2>\n                <form onSubmit={handleSubmit} className=\"space-y-6\">\n                  <div className=\"grid md:grid-cols-2 gap-4\">\n                    <div>\n                      <label htmlFor=\"name\" className=\"block text-white/80 text-sm font-medium mb-2\">\n                        Full Name *\n                      </label>\n                      <input\n                        type=\"text\"\n                        id=\"name\"\n                        name=\"name\"\n                        value={formData.name}\n                        onChange={handleChange}\n                        required\n                        className=\"glass-input w-full\"\n                        placeholder=\"Your full name\"\n                      />\n                    </div>\n                    <div>\n                      <label htmlFor=\"email\" className=\"block text-white/80 text-sm font-medium mb-2\">\n                        Email Address *\n                      </label>\n                      <input\n                        type=\"email\"\n                        id=\"email\"\n                        name=\"email\"\n                        value={formData.email}\n                        onChange={handleChange}\n                        required\n                        className=\"glass-input w-full\"\n                        placeholder=\"<EMAIL>\"\n                      />\n                    </div>\n                  </div>\n\n                  <div>\n                    <label htmlFor=\"company\" className=\"block text-white/80 text-sm font-medium mb-2\">\n                      Company\n                    </label>\n                    <input\n                      type=\"text\"\n                      id=\"company\"\n                      name=\"company\"\n                      value={formData.company}\n                      onChange={handleChange}\n                      className=\"glass-input w-full\"\n                      placeholder=\"Your company name\"\n                    />\n                  </div>\n\n                  <div>\n                    <label htmlFor=\"subject\" className=\"block text-white/80 text-sm font-medium mb-2\">\n                      Subject *\n                    </label>\n                    <select\n                      id=\"subject\"\n                      name=\"subject\"\n                      value={formData.subject}\n                      onChange={handleChange}\n                      required\n                      className=\"glass-input w-full\"\n                    >\n                      <option value=\"\">Select a subject</option>\n                      <option value=\"general\">General Inquiry</option>\n                      <option value=\"support\">Technical Support</option>\n                      <option value=\"sales\">Sales Question</option>\n                      <option value=\"partnership\">Partnership</option>\n                      <option value=\"feedback\">Feedback</option>\n                    </select>\n                  </div>\n\n                  <div>\n                    <label htmlFor=\"message\" className=\"block text-white/80 text-sm font-medium mb-2\">\n                      Message *\n                    </label>\n                    <textarea\n                      id=\"message\"\n                      name=\"message\"\n                      value={formData.message}\n                      onChange={handleChange}\n                      required\n                      rows={6}\n                      className=\"glass-input w-full resize-none\"\n                      placeholder=\"Tell us how we can help you...\"\n                    />\n                  </div>\n\n                  <button\n                    type=\"submit\"\n                    className=\"btn-primary w-full flex items-center justify-center gap-2\"\n                  >\n                    <Send className=\"h-4 w-4\" />\n                    Send Message\n                  </button>\n                </form>\n              </div>\n\n              {/* Contact Information */}\n              <div className=\"space-y-8\">\n                <div className=\"glass p-8\">\n                  <h3 className=\"text-xl font-semibold text-white mb-6\">Office Hours</h3>\n                  <div className=\"space-y-4\">\n                    <div className=\"flex items-center gap-3\">\n                      <Clock className=\"h-5 w-5 text-purple-400\" />\n                      <div>\n                        <div className=\"text-white font-medium\">Monday - Friday</div>\n                        <div className=\"text-white/60 text-sm\">9:00 AM - 6:00 PM PST</div>\n                      </div>\n                    </div>\n                    <div className=\"flex items-center gap-3\">\n                      <Clock className=\"h-5 w-5 text-purple-400\" />\n                      <div>\n                        <div className=\"text-white font-medium\">Saturday</div>\n                        <div className=\"text-white/60 text-sm\">10:00 AM - 4:00 PM PST</div>\n                      </div>\n                    </div>\n                    <div className=\"flex items-center gap-3\">\n                      <Clock className=\"h-5 w-5 text-purple-400\" />\n                      <div>\n                        <div className=\"text-white font-medium\">Sunday</div>\n                        <div className=\"text-white/60 text-sm\">Closed</div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"glass p-8\">\n                  <h3 className=\"text-xl font-semibold text-white mb-6\">Quick Links</h3>\n                  <div className=\"space-y-3\">\n                    <a href=\"#\" className=\"block text-white/70 hover:text-white transition-colors\">\n                      Help Center & Documentation\n                    </a>\n                    <a href=\"#\" className=\"block text-white/70 hover:text-white transition-colors\">\n                      System Status\n                    </a>\n                    <a href=\"#\" className=\"block text-white/70 hover:text-white transition-colors\">\n                      Feature Requests\n                    </a>\n                    <a href=\"#\" className=\"block text-white/70 hover:text-white transition-colors\">\n                      Bug Reports\n                    </a>\n                    <a href=\"#\" className=\"block text-white/70 hover:text-white transition-colors\">\n                      Community Forum\n                    </a>\n                  </div>\n                </div>\n\n                <div className=\"glass p-8\">\n                  <h3 className=\"text-xl font-semibold text-white mb-6\">Enterprise Sales</h3>\n                  <p className=\"text-white/70 mb-4\">\n                    Looking for a custom solution for your organization? Our enterprise team is ready to help.\n                  </p>\n                  <button className=\"btn-secondary w-full\">Contact Enterprise Sales</button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* FAQ Section */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"text-center mb-12\">\n              <h2 className=\"text-3xl font-bold text-white mb-4\">\n                Frequently Asked Questions\n              </h2>\n              <p className=\"text-white/70\">\n                Can't find what you're looking for? Check out our comprehensive FAQ section.\n              </p>\n            </div>\n            <div className=\"text-center\">\n              <button className=\"btn-primary\">View All FAQs</button>\n            </div>\n          </div>\n        </section>\n      </div>\n    </>\n  )\n}\n", "export default {\n  xmlns: 'http://www.w3.org/2000/svg',\n  width: 24,\n  height: 24,\n  viewBox: '0 0 24 24',\n  fill: 'none',\n  stroke: 'currentColor',\n  strokeWidth: 2,\n  strokeLinecap: 'round',\n  strokeLinejoin: 'round',\n};\n", "import {\n  forwardRef,\n  createElement,\n  ReactSVG,\n  SVGProps,\n  ForwardRefExoticComponent,\n  RefAttributes,\n} from 'react';\nimport defaultAttributes from './defaultAttributes';\n\nexport type IconNode = [elementName: keyof ReactSVG, attrs: Record<string, string>][];\n\nexport type SVGAttributes = Partial<SVGProps<SVGSVGElement>>;\ntype ComponentAttributes = RefAttributes<SVGSVGElement> & SVGAttributes;\n\nexport interface LucideProps extends ComponentAttributes {\n  size?: string | number;\n  absoluteStrokeWidth?: boolean;\n}\n\nexport type LucideIcon = ForwardRefExoticComponent<LucideProps>;\n/**\n * Converts string to KebabCase\n * Copied from scripts/helper. If anyone knows how to properly import it here\n * then please fix it.\n *\n * @param {string} string\n * @returns {string} A kebabized string\n */\nexport const toKebabCase = (string: string) =>\n  string\n    .replace(/([a-z0-9])([A-Z])/g, '$1-$2')\n    .toLowerCase()\n    .trim();\n\nconst createLucideIcon = (iconName: string, iconNode: IconNode): LucideIcon => {\n  const Component = forwardRef<SVGSVGElement, LucideProps>(\n    ({ color = 'currentColor', size = 24, strokeWidth = 2, absoluteStrokeWidth, className = '', children, ...rest }, ref) =>\n      createElement(\n        'svg',\n        {\n          ref,\n          ...defaultAttributes,\n          width: size,\n          height: size,\n          stroke: color,\n          strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n          className: ['lucide', `lucide-${toKebabCase(iconName)}`, className].join(' '),\n          ...rest,\n        },\n        [\n          ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n          ...(Array.isArray(children) ? children : [children]),\n        ]\n      )\n  );\n\n  Component.displayName = `${iconName}`;\n\n  return Component;\n};\n\nexport default createLucideIcon;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Clock\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8cG9seWxpbmUgcG9pbnRzPSIxMiA2IDEyIDEyIDE2IDE0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/clock\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Clock = createLucideIcon('Clock', [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['polyline', { points: '12 6 12 12 16 14', key: '68esgv' }],\n]);\n\nexport default Clock;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Mail\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMjAiIGhlaWdodD0iMTYiIHg9IjIiIHk9IjQiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Im0yMiA3LTguOTcgNS43YTEuOTQgMS45NCAwIDAgMS0yLjA2IDBMMiA3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/mail\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Mail = createLucideIcon('Mail', [\n  ['rect', { width: '20', height: '16', x: '2', y: '4', rx: '2', key: '18n3k1' }],\n  ['path', { d: 'm22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7', key: '1ocrg3' }],\n]);\n\nexport default Mail;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name MapPin\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjAgMTBjMCA2LTggMTItOCAxMnMtOC02LTgtMTJhOCA4IDAgMCAxIDE2IDBaIiAvPgogIDxjaXJjbGUgY3g9IjEyIiBjeT0iMTAiIHI9IjMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/map-pin\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MapPin = createLucideIcon('MapPin', [\n  ['path', { d: 'M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z', key: '2oe9fu' }],\n  ['circle', { cx: '12', cy: '10', r: '3', key: 'ilqhr7' }],\n]);\n\nexport default MapPin;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name MessageCircle\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMyAyMSAxLjktNS43YTguNSA4LjUgMCAxIDEgMy44IDMuOHoiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/message-circle\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MessageCircle = createLucideIcon('MessageCircle', [\n  ['path', { d: 'm3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z', key: 'v2veuj' }],\n]);\n\nexport default MessageCircle;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Phone\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjIgMTYuOTJ2M2EyIDIgMCAwIDEtMi4xOCAyIDE5Ljc5IDE5Ljc5IDAgMCAxLTguNjMtMy4wNyAxOS41IDE5LjUgMCAwIDEtNi02IDE5Ljc5IDE5Ljc5IDAgMCAxLTMuMDctOC42N0EyIDIgMCAwIDEgNC4xMSAyaDNhMiAyIDAgMCAxIDIgMS43MiAxMi44NCAxMi44NCAwIDAgMCAuNyAyLjgxIDIgMiAwIDAgMS0uNDUgMi4xMUw4LjA5IDkuOTFhMTYgMTYgMCAwIDAgNiA2bDEuMjctMS4yN2EyIDIgMCAwIDEgMi4xMS0uNDUgMTIuODQgMTIuODQgMCAwIDAgMi44MS43QTIgMiAwIDAgMSAyMiAxNi45MnoiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/phone\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Phone = createLucideIcon('Phone', [\n  [\n    'path',\n    {\n      d: 'M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z',\n      key: 'foiqr5',\n    },\n  ],\n]);\n\nexport default Phone;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Send\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMjIgMi03IDIwLTQtOS05LTRaIiAvPgogIDxwYXRoIGQ9Ik0yMiAyIDExIDEzIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/send\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Send = createLucideIcon('Send', [\n  ['path', { d: 'm22 2-7 20-4-9-9-4Z', key: '1q3vgg' }],\n  ['path', { d: 'M22 2 11 13', key: 'nzbqef' }],\n]);\n\nexport default Send;\n"], "names": ["Promise", "resolve", "then", "__webpack_require__", "bind", "dynamic", "ContactPage", "formData", "setFormData", "useState", "name", "email", "company", "subject", "message", "handleChange", "e", "target", "value", "react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__", "jsxs", "Fragment", "jsx", "div", "className", "section", "h1", "p", "MessageCircle", "h3", "Mail", "Phone", "MapPin", "h2", "form", "onSubmit", "preventDefault", "console", "log", "alert", "label", "htmlFor", "input", "type", "id", "onChange", "required", "placeholder", "select", "option", "textarea", "rows", "button", "Send", "Clock", "a", "href", "defaultAttributes", "xmlns", "width", "height", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "toKebabCase", "string", "replace", "toLowerCase", "trim", "createLucideIcon", "iconName", "iconNode", "Component", "forwardRef", "param", "ref", "color", "size", "absoluteStrokeWidth", "children", "rest", "createElement", "Number", "concat", "join", "map", "tag", "attrs", "Array", "isArray", "displayName", "cx", "cy", "r", "key", "points", "x", "y", "rx", "d"], "sourceRoot": ""}