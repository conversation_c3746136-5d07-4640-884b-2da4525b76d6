{"version": 3, "file": "static/chunks/app/global-error-c11f75afe3374c0e.js", "mappings": "oFAAAA,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,qGCEe,SAASE,EAAYC,CAMnC,KANmC,CAClCC,MAAAA,CAAK,CACLC,MAAAA,CAAK,CAIN,CANmCF,EAOlC,MACE,GAAAG,EAAAC,GAAA,EAACC,OAAAA,UACC,GAAAF,EAAAC,GAAA,EAACE,OAAAA,UACC,GAAAH,EAAAC,GAAA,EAACG,MAAAA,CAAIC,UAAU,0HACb,GAAAL,EAAAM,IAAA,EAACF,MAAAA,CAAIC,UAAU,wBACb,GAAAL,EAAAM,IAAA,EAACF,MAAAA,CAAIC,UAAU,iBACb,GAAAL,EAAAC,GAAA,EAACM,KAAAA,CAAGF,UAAU,iDAAwC,QACtD,GAAAL,EAAAC,GAAA,EAACO,KAAAA,CAAGH,UAAU,8CAAqC,0BACnD,GAAAL,EAAAC,GAAA,EAACQ,IAAAA,CAAEJ,UAAU,uDAA8C,uDAK7D,GAAAL,EAAAM,IAAA,EAACF,MAAAA,CAAIC,UAAU,2DACb,GAAAL,EAAAC,GAAA,EAACS,SAAAA,CACCC,QAAS,IAAMZ,IACfM,UAAU,wHACX,cAID,GAAAL,EAAAC,GAAA,EAACW,IAAAA,CACCC,KAAK,IACLR,UAAU,oHACX,uBASf", "sources": ["webpack://_N_E/?6263", "webpack://_N_E/./app/global-error.tsx"], "sourcesContent": ["import(/* webpackMode: \"eager\" */ \"/Volumes/Apps/Websites/streamit-main/app/global-error.tsx\");\n", "'use client'\n\nexport default function GlobalError({\n  error,\n  reset,\n}: {\n  error: Error & { digest?: string }\n  reset: () => void\n}) {\n  return (\n    <html>\n      <body>\n        <div className=\"min-h-screen bg-gradient-to-br from-red-900 via-purple-900 to-indigo-900 flex items-center justify-center px-4\">\n          <div className=\"text-center\">\n            <div className=\"mb-8\">\n              <h1 className=\"text-9xl font-bold text-white/20 mb-4\">500</h1>\n              <h2 className=\"text-3xl font-bold text-white mb-4\">Something went wrong!</h2>\n              <p className=\"text-white/80 text-lg mb-8 max-w-md mx-auto\">\n                An unexpected error occurred. Please try again.\n              </p>\n            </div>\n            \n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <button\n                onClick={() => reset()}\n                className=\"inline-flex items-center px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors\"\n              >\n                Try again\n              </button>\n              \n              <a\n                href=\"/\"\n                className=\"inline-flex items-center px-6 py-3 bg-white/10 hover:bg-white/20 text-white rounded-lg transition-colors\"\n              >\n                Go Home\n              </a>\n            </div>\n          </div>\n        </div>\n      </body>\n    </html>\n  )\n}\n"], "names": ["Promise", "resolve", "then", "__webpack_require__", "bind", "GlobalError", "param", "error", "reset", "react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__", "jsx", "html", "body", "div", "className", "jsxs", "h1", "h2", "p", "button", "onClick", "a", "href"], "sourceRoot": ""}