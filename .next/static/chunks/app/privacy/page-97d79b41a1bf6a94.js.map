{"version": 3, "file": "static/chunks/app/privacy/page-97d79b41a1bf6a94.js", "mappings": "oFAAAA,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,6LCKO,IAAME,EAAU,gBAER,SAASC,IACtB,IAAMC,EAAW,CACf,CACEC,GAAI,yBACJC,MAAO,yBACPC,KAAMC,EAAAA,CAAQA,EAEhB,CACEH,GAAI,kBACJC,MAAO,yBACPC,KAAME,EAAAA,CAAKA,EAEb,CACEJ,GAAI,sBACJC,MAAO,sBACPC,KAAMG,EAAAA,CAAKA,EAEb,CACEL,GAAI,gBACJC,MAAO,gBACPC,KAAMI,EAAAA,CAAMA,EAEd,CACEN,GAAI,cACJC,MAAO,cACPC,KAAMK,EAAAA,CAAGA,EAEX,CACEP,GAAI,UACJC,MAAO,aACPC,KAAMM,EAAAA,CAAIA,EAEb,CAED,MACE,GAAAC,EAAAC,IAAA,EAAAD,EAAAE,QAAA,YAEE,GAAAF,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,gBAEf,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,6CAEb,GAAAL,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,0CACb,GAAAL,EAAAG,GAAA,EAACI,KAAAA,CAAGF,UAAU,sDAA6C,mBAG3D,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,4EAAmE,uIAIhF,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,8BAAqB,uCAKtC,GAAAL,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,6BACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sBACb,GAAAL,EAAAG,GAAA,EAACM,KAAAA,CAAGJ,UAAU,8CAAqC,sBACnD,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,qCACZf,EAASoB,GAAG,CAAC,CAACJ,EAASK,IACtB,GAAAX,EAAAC,IAAA,EAACW,IAAAA,CAECC,KAAM,IAAeC,MAAA,CAAXR,EAAQf,EAAE,EACpBc,UAAU,sFAEV,GAAAL,EAAAG,GAAA,EAACG,EAAQb,IAAI,EAACY,UAAU,4BACxB,GAAAL,EAAAG,GAAA,EAACY,OAAAA,CAAKV,UAAU,4CAAoCC,EAAQd,KAAK,KAL5Dc,EAAQf,EAAE,YAc3B,GAAAS,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,yCAGb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIb,GAAG,yBAAyBc,UAAU,sBACzC,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,yCACb,GAAAL,EAAAG,GAAA,EAACT,EAAAA,CAAQA,CAAAA,CAACW,UAAU,4BACpB,GAAAL,EAAAG,GAAA,EAACM,KAAAA,CAAGJ,UAAU,yCAAgC,8BAEhD,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,oCACb,GAAAL,EAAAG,GAAA,EAACa,KAAAA,CAAGX,UAAU,4CAAmC,wBACjD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,UAAE,4HAEH,GAAAR,EAAAG,GAAA,EAACa,KAAAA,CAAGX,UAAU,4CAAmC,iBACjD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,UAAE,yNAEH,GAAAR,EAAAG,GAAA,EAACa,KAAAA,CAAGX,UAAU,4CAAmC,0BACjD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,UAAE,yJAEH,GAAAR,EAAAG,GAAA,EAACa,KAAAA,CAAGX,UAAU,4CAAmC,mBACjD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,UAAE,uHAKP,GAAAR,EAAAC,IAAA,EAACG,MAAAA,CAAIb,GAAG,kBAAkBc,UAAU,sBAClC,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,yCACb,GAAAL,EAAAG,GAAA,EAACR,EAAAA,CAAKA,CAAAA,CAACU,UAAU,0BACjB,GAAAL,EAAAG,GAAA,EAACM,KAAAA,CAAGJ,UAAU,yCAAgC,8BAEhD,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,oCACb,GAAAL,EAAAG,GAAA,EAACK,IAAAA,UAAE,0CACH,GAAAR,EAAAC,IAAA,EAACgB,KAAAA,CAAGZ,UAAU,iDACZ,GAAAL,EAAAG,GAAA,EAACe,KAAAA,UAAG,wDACJ,GAAAlB,EAAAG,GAAA,EAACe,KAAAA,UAAG,mEACJ,GAAAlB,EAAAG,GAAA,EAACe,KAAAA,UAAG,+DACJ,GAAAlB,EAAAG,GAAA,EAACe,KAAAA,UAAG,yDACJ,GAAAlB,EAAAG,GAAA,EAACe,KAAAA,UAAG,2DACJ,GAAAlB,EAAAG,GAAA,EAACe,KAAAA,UAAG,iDACJ,GAAAlB,EAAAG,GAAA,EAACe,KAAAA,UAAG,sDACJ,GAAAlB,EAAAG,GAAA,EAACe,KAAAA,UAAG,iEAMV,GAAAlB,EAAAC,IAAA,EAACG,MAAAA,CAAIb,GAAG,sBAAsBc,UAAU,sBACtC,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,yCACb,GAAAL,EAAAG,GAAA,EAACP,EAAAA,CAAKA,CAAAA,CAACS,UAAU,2BACjB,GAAAL,EAAAG,GAAA,EAACM,KAAAA,CAAGJ,UAAU,yCAAgC,2BAEhD,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,oCACb,GAAAL,EAAAG,GAAA,EAACK,IAAAA,UAAE,kJAEH,GAAAR,EAAAG,GAAA,EAACa,KAAAA,CAAGX,UAAU,4CAAmC,sBACjD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,UAAE,iKAEH,GAAAR,EAAAG,GAAA,EAACa,KAAAA,CAAGX,UAAU,4CAAmC,uBACjD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,UAAE,8IAEH,GAAAR,EAAAG,GAAA,EAACa,KAAAA,CAAGX,UAAU,4CAAmC,uBACjD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,UAAE,qIAKP,GAAAR,EAAAC,IAAA,EAACG,MAAAA,CAAIb,GAAG,gBAAgBc,UAAU,sBAChC,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,yCACb,GAAAL,EAAAG,GAAA,EAACN,EAAAA,CAAMA,CAAAA,CAACQ,UAAU,4BAClB,GAAAL,EAAAG,GAAA,EAACM,KAAAA,CAAGJ,UAAU,yCAAgC,qBAEhD,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,oCACb,GAAAL,EAAAG,GAAA,EAACK,IAAAA,UAAE,kFACH,GAAAR,EAAAC,IAAA,EAACgB,KAAAA,CAAGZ,UAAU,iDACZ,GAAAL,EAAAG,GAAA,EAACe,KAAAA,UAAG,yDACJ,GAAAlB,EAAAG,GAAA,EAACe,KAAAA,UAAG,wCACJ,GAAAlB,EAAAG,GAAA,EAACe,KAAAA,UAAG,oDACJ,GAAAlB,EAAAG,GAAA,EAACe,KAAAA,UAAG,6BACJ,GAAAlB,EAAAG,GAAA,EAACe,KAAAA,UAAG,sDACJ,GAAAlB,EAAAG,GAAA,EAACe,KAAAA,UAAG,wDAEN,GAAAlB,EAAAG,GAAA,EAACK,IAAAA,UAAE,oMAKP,GAAAR,EAAAC,IAAA,EAACG,MAAAA,CAAIb,GAAG,cAAcc,UAAU,sBAC9B,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,yCACb,GAAAL,EAAAG,GAAA,EAACL,EAAAA,CAAGA,CAAAA,CAACO,UAAU,4BACf,GAAAL,EAAAG,GAAA,EAACM,KAAAA,CAAGJ,UAAU,yCAAgC,mBAEhD,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,oCACb,GAAAL,EAAAG,GAAA,EAACK,IAAAA,UAAE,uEAEH,GAAAR,EAAAG,GAAA,EAACa,KAAAA,CAAGX,UAAU,4CAAmC,2BACjD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,UAAE,yFAEH,GAAAR,EAAAG,GAAA,EAACa,KAAAA,CAAGX,UAAU,4CAAmC,eACjD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,UAAE,0FAEH,GAAAR,EAAAG,GAAA,EAACa,KAAAA,CAAGX,UAAU,4CAAmC,aACjD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,UAAE,wIAEH,GAAAR,EAAAG,GAAA,EAACa,KAAAA,CAAGX,UAAU,4CAAmC,YACjD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,UAAE,qHAEH,GAAAR,EAAAG,GAAA,EAACa,KAAAA,CAAGX,UAAU,4CAAmC,gBACjD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,UAAE,gMAKP,GAAAR,EAAAC,IAAA,EAACG,MAAAA,CAAIb,GAAG,UAAUc,UAAU,sBAC1B,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,yCACb,GAAAL,EAAAG,GAAA,EAACJ,EAAAA,CAAIA,CAAAA,CAACM,UAAU,yBAChB,GAAAL,EAAAG,GAAA,EAACM,KAAAA,CAAGJ,UAAU,yCAAgC,kBAEhD,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,oCACb,GAAAL,EAAAG,GAAA,EAACK,IAAAA,UAAE,kGAEH,GAAAR,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sCACb,GAAAL,EAAAC,IAAA,EAACO,IAAAA,WAAE,GAAAR,EAAAG,GAAA,EAACgB,SAAAA,UAAO,WAAe,8BAC1B,GAAAnB,EAAAC,IAAA,EAACO,IAAAA,WAAE,GAAAR,EAAAG,GAAA,EAACgB,SAAAA,UAAO,aAAiB,sBAAmB,GAAAnB,EAAAG,GAAA,EAACiB,KAAAA,CAAAA,GAAK,qBACnC,GAAApB,EAAAG,GAAA,EAACiB,KAAAA,CAAAA,GAAK,0BACD,GAAApB,EAAAG,GAAA,EAACiB,KAAAA,CAAAA,GAAK,sBAI/B,GAAApB,EAAAG,GAAA,EAACK,IAAAA,UAAE,0DAKP,GAAAR,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sBACb,GAAAL,EAAAG,GAAA,EAACM,KAAAA,CAAGJ,UAAU,8CAAqC,mBACnD,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,oCACb,GAAAL,EAAAG,GAAA,EAACK,IAAAA,UAAE,wFACH,GAAAR,EAAAC,IAAA,EAACgB,KAAAA,CAAGZ,UAAU,iDACZ,GAAAL,EAAAG,GAAA,EAACe,KAAAA,UAAG,6DACJ,GAAAlB,EAAAG,GAAA,EAACe,KAAAA,UAAG,wDACJ,GAAAlB,EAAAG,GAAA,EAACe,KAAAA,UAAG,oCAEN,GAAAlB,EAAAG,GAAA,EAACK,IAAAA,UAAE,sHASnB,mFC1Oea,EAAA,CACbC,MAAO,6BACPC,MAAO,GACPC,OAAQ,GACRC,QAAS,YACTC,KAAM,OACNC,OAAQ,eACRC,YAAa,EACbC,cAAe,QACfC,eAAgB,OAClB;;;;;GCmBa,IAAAC,EAAc,GACzBC,EACGC,OAAA,CAAQ,qBAAsB,SAC9BC,WAAY,GACZC,IAAK,GAEJC,EAAmB,CAACC,EAAkBC,KAC1C,IAAMC,EAAYC,CAAAA,EAAAA,EAAAA,UAAAA,EAChB,CAAAC,EAAiHC,QAAhH,CAAEC,MAAAA,EAAQ,eAAgBC,KAAAA,EAAO,EAAI,CAAAhB,YAAAA,EAAc,CAAG,CAAAiB,oBAAAA,CAAA,CAAqBxC,UAAAA,EAAY,GAAIyC,SAAAA,CAAa,IAAAC,EAAA,CAAAN,QACvGO,CAAAA,EAAAA,EAAAA,aAAAA,EACE,MACA,CACEN,IAAAA,EACA,GAAGrB,CAAA,CACHE,MAAOqB,EACPpB,OAAQoB,EACRjB,OAAQgB,EACRf,YAAaiB,EAAsBI,GAAAA,OAAOrB,GAAoBqB,OAAOL,GAAQhB,EAC7EvB,UAAW,CAAC,SAAoB,UAAyBS,MAAA,CAAzBiB,EAAYM,IAAahC,EAAW,CAAA6C,IAAA,CAAK,KACzE,GAAGH,CAAA,EAEL,IACKT,EAAS5B,GAAA,CAAI,OAAC,CAACyC,EAAKC,EAAW,CAAAX,QAAAO,CAAAA,EAAAA,EAAAA,aAAAA,EAAcG,EAAKC,QACjDC,MAAMC,OAAA,CAAQR,GAAYA,EAAW,CAACA,EAAQ,CACpD,IAMC,OAFPP,EAAUgB,WAAA,CAAc,GAAGzC,MAAA,CAAAuB,GAEpBE,CACT;;;;;GC/CM,IAAAzC,EAAMsC,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,MAAO,CAClC,CAAC,OAAQ,CAAEoB,EAAG,+CAAgDC,IAAK,UAAU,CAC7E,CAAC,SAAU,CAAEC,GAAI,KAAMC,GAAI,KAAMC,EAAG,IAAKH,IAAK,UAAU,CACzD;;;;;GCHK,IAAA/D,EAAW0C,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,WAAY,CAC5C,CACE,OACA,CAAEoB,EAAG,wEAAyEC,IAAK,QAAS,EAC9F,CACA,CAAC,WAAY,CAAEI,OAAQ,iBAAkBJ,IAAK,UAAU,CACxD,CAAC,OAAQ,CAAEK,GAAI,KAAMC,GAAI,IAAKC,GAAI,KAAMC,GAAI,KAAMR,IAAK,UAAU,CACjE,CAAC,OAAQ,CAAEK,GAAI,KAAMC,GAAI,IAAKC,GAAI,KAAMC,GAAI,KAAMR,IAAK,UAAU,CACjE,CAAC,OAAQ,CAAEK,GAAI,KAAMC,GAAI,IAAKC,GAAI,IAAKC,GAAI,IAAKR,IAAK,UAAU,CAChE;;;;;GCTK,IAAA7D,EAAQwC,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,QAAS,CACtC,CAAC,SAAU,CAAEsB,GAAI,KAAMC,GAAI,KAAMC,EAAG,KAAMH,IAAK,UAAU,CACzD,CAAC,OAAQ,CAAED,EAAG,kDAAmDC,IAAK,UAAU,CAChF,CAAC,OAAQ,CAAED,EAAG,WAAYC,IAAK,UAAU,CAC1C;;;;;GCJK,IAAA1D,EAAOqC,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,OAAQ,CACpC,CAAC,OAAQ,CAAEb,MAAO,KAAMC,OAAQ,KAAM0C,EAAG,IAAKC,EAAG,KAAMC,GAAI,IAAKC,GAAI,IAAKZ,IAAK,UAAU,CACxF,CAAC,OAAQ,CAAED,EAAG,2BAA4BC,IAAK,UAAU,CAC1D;;;;;GCHK,IAAA5D,EAASuC,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,SAAU,CACxC,CAAC,OAAQ,CAAEoB,EAAG,6CAA8CC,IAAK,UAAU,CAC5E;;;;;GCFK,IAAA9D,EAAQyC,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,QAAS,CACtC,CAAC,OAAQ,CAAEoB,EAAG,4CAA6CC,IAAK,UAAU,CAC1E,CAAC,SAAU,CAAEC,GAAI,IAAKC,GAAI,IAAKC,EAAG,IAAKH,IAAK,SAAS,CACrD,CAAC,OAAQ,CAAED,EAAG,6BAA8BC,IAAK,UAAU,CAC3D,CAAC,OAAQ,CAAED,EAAG,4BAA6BC,IAAK,UAAU,CAC3D", "sources": ["webpack://_N_E/?189f", "webpack://_N_E/./app/privacy/page.tsx", "webpack://_N_E/../../src/defaultAttributes.ts", "webpack://_N_E/../../src/createLucideIcon.ts", "webpack://_N_E/../../../src/icons/eye.ts", "webpack://_N_E/../../../src/icons/file-text.ts", "webpack://_N_E/../../../src/icons/globe.ts", "webpack://_N_E/../../../src/icons/lock.ts", "webpack://_N_E/../../../src/icons/shield.ts", "webpack://_N_E/../../../src/icons/users.ts"], "sourcesContent": ["import(/* webpackMode: \"eager\" */ \"/Volumes/Apps/Websites/streamit-main/app/privacy/page.tsx\");\n", "'use client'\n\nimport { Shield, Eye, Lock, FileText, Users, Globe } from 'lucide-react'\n\n// Force dynamic rendering\nexport const dynamic = 'force-dynamic'\n\nexport default function PrivacyPage() {\n  const sections = [\n    {\n      id: 'information-collection',\n      title: 'Information We Collect',\n      icon: FileText\n    },\n    {\n      id: 'information-use',\n      title: 'How We Use Information',\n      icon: Users\n    },\n    {\n      id: 'information-sharing',\n      title: 'Information Sharing',\n      icon: Globe\n    },\n    {\n      id: 'data-security',\n      title: 'Data Security',\n      icon: Shield\n    },\n    {\n      id: 'your-rights',\n      title: 'Your Rights',\n      icon: Eye\n    },\n    {\n      id: 'contact',\n      title: 'Contact Us',\n      icon: Lock\n    }\n  ]\n\n  return (\n    <>\n      {/* Animated Background */}\n      <div className=\"animated-bg\"></div>\n\n      <div className=\"min-h-screen pt-20 relative z-10\">\n        {/* Hero Section */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-7xl mx-auto text-center\">\n            <h1 className=\"text-5xl font-bold text-white mb-6 fade-in\">\n              Privacy Policy\n            </h1>\n            <p className=\"text-xl text-white/80 max-w-3xl mx-auto leading-relaxed slide-up\">\n              Your privacy is important to us. This policy explains how we collect, \n              use, and protect your information when you use StreamIt Pro.\n            </p>\n            <p className=\"text-white/60 mt-4\">Last updated: January 1, 2024</p>\n          </div>\n        </section>\n\n        {/* Table of Contents */}\n        <section className=\"py-10 px-4\">\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"glass p-8\">\n              <h2 className=\"text-2xl font-bold text-white mb-6\">Table of Contents</h2>\n              <div className=\"grid md:grid-cols-2 gap-4\">\n                {sections.map((section, index) => (\n                  <a\n                    key={section.id}\n                    href={`#${section.id}`}\n                    className=\"flex items-center gap-3 p-3 hover:bg-white/5 rounded-lg transition-colors\"\n                  >\n                    <section.icon className=\"h-5 w-5 text-purple-400\" />\n                    <span className=\"text-white hover:text-purple-300\">{section.title}</span>\n                  </a>\n                ))}\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* Privacy Content */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-4xl mx-auto space-y-12\">\n            \n            {/* Information Collection */}\n            <div id=\"information-collection\" className=\"glass p-8\">\n              <div className=\"flex items-center gap-3 mb-6\">\n                <FileText className=\"h-6 w-6 text-purple-400\" />\n                <h2 className=\"text-2xl font-bold text-white\">Information We Collect</h2>\n              </div>\n              <div className=\"space-y-4 text-white/80\">\n                <h3 className=\"text-lg font-semibold text-white\">Account Information</h3>\n                <p>When you create an account, we collect your name, email address, and other information you provide during registration.</p>\n                \n                <h3 className=\"text-lg font-semibold text-white\">Meeting Data</h3>\n                <p>We collect information about your meetings, including participant lists, meeting duration, and usage statistics. We do not record or store the content of your meetings unless you explicitly choose to record them.</p>\n                \n                <h3 className=\"text-lg font-semibold text-white\">Technical Information</h3>\n                <p>We automatically collect technical information such as your IP address, browser type, device information, and usage patterns to improve our service.</p>\n                \n                <h3 className=\"text-lg font-semibold text-white\">Communications</h3>\n                <p>When you contact us for support or feedback, we collect the information you provide in those communications.</p>\n              </div>\n            </div>\n\n            {/* Information Use */}\n            <div id=\"information-use\" className=\"glass p-8\">\n              <div className=\"flex items-center gap-3 mb-6\">\n                <Users className=\"h-6 w-6 text-blue-400\" />\n                <h2 className=\"text-2xl font-bold text-white\">How We Use Information</h2>\n              </div>\n              <div className=\"space-y-4 text-white/80\">\n                <p>We use the information we collect to:</p>\n                <ul className=\"list-disc list-inside space-y-2 ml-4\">\n                  <li>Provide and maintain our video conferencing service</li>\n                  <li>Process your account registration and manage your subscription</li>\n                  <li>Facilitate meetings and communication between participants</li>\n                  <li>Improve our service through analytics and usage data</li>\n                  <li>Provide customer support and respond to your inquiries</li>\n                  <li>Send you important updates about our service</li>\n                  <li>Ensure the security and integrity of our platform</li>\n                  <li>Comply with legal obligations and enforce our terms</li>\n                </ul>\n              </div>\n            </div>\n\n            {/* Information Sharing */}\n            <div id=\"information-sharing\" className=\"glass p-8\">\n              <div className=\"flex items-center gap-3 mb-6\">\n                <Globe className=\"h-6 w-6 text-green-400\" />\n                <h2 className=\"text-2xl font-bold text-white\">Information Sharing</h2>\n              </div>\n              <div className=\"space-y-4 text-white/80\">\n                <p>We do not sell, trade, or rent your personal information to third parties. We may share your information only in the following circumstances:</p>\n                \n                <h3 className=\"text-lg font-semibold text-white\">Service Providers</h3>\n                <p>We may share information with trusted third-party service providers who help us operate our service, such as cloud hosting providers and analytics services.</p>\n                \n                <h3 className=\"text-lg font-semibold text-white\">Legal Requirements</h3>\n                <p>We may disclose information if required by law, court order, or government request, or to protect our rights and the safety of our users.</p>\n                \n                <h3 className=\"text-lg font-semibold text-white\">Business Transfers</h3>\n                <p>In the event of a merger, acquisition, or sale of assets, your information may be transferred as part of that transaction.</p>\n              </div>\n            </div>\n\n            {/* Data Security */}\n            <div id=\"data-security\" className=\"glass p-8\">\n              <div className=\"flex items-center gap-3 mb-6\">\n                <Shield className=\"h-6 w-6 text-orange-400\" />\n                <h2 className=\"text-2xl font-bold text-white\">Data Security</h2>\n              </div>\n              <div className=\"space-y-4 text-white/80\">\n                <p>We implement industry-standard security measures to protect your information:</p>\n                <ul className=\"list-disc list-inside space-y-2 ml-4\">\n                  <li>End-to-end encryption for all meeting communications</li>\n                  <li>AES-256 encryption for data at rest</li>\n                  <li>Regular security audits and penetration testing</li>\n                  <li>SOC 2 Type II compliance</li>\n                  <li>Secure data centers with physical access controls</li>\n                  <li>Employee background checks and security training</li>\n                </ul>\n                <p>While we strive to protect your information, no method of transmission over the internet is 100% secure. We cannot guarantee absolute security but are committed to protecting your data.</p>\n              </div>\n            </div>\n\n            {/* Your Rights */}\n            <div id=\"your-rights\" className=\"glass p-8\">\n              <div className=\"flex items-center gap-3 mb-6\">\n                <Eye className=\"h-6 w-6 text-purple-400\" />\n                <h2 className=\"text-2xl font-bold text-white\">Your Rights</h2>\n              </div>\n              <div className=\"space-y-4 text-white/80\">\n                <p>You have the following rights regarding your personal information:</p>\n                \n                <h3 className=\"text-lg font-semibold text-white\">Access and Portability</h3>\n                <p>You can access and download your personal information through your account settings.</p>\n                \n                <h3 className=\"text-lg font-semibold text-white\">Correction</h3>\n                <p>You can update or correct your personal information at any time through your account.</p>\n                \n                <h3 className=\"text-lg font-semibold text-white\">Deletion</h3>\n                <p>You can request deletion of your account and personal information. Some information may be retained for legal or business purposes.</p>\n                \n                <h3 className=\"text-lg font-semibold text-white\">Opt-out</h3>\n                <p>You can opt out of marketing communications at any time by following the unsubscribe instructions in our emails.</p>\n                \n                <h3 className=\"text-lg font-semibold text-white\">GDPR Rights</h3>\n                <p>If you are in the European Union, you have additional rights under GDPR, including the right to object to processing and the right to lodge a complaint with a supervisory authority.</p>\n              </div>\n            </div>\n\n            {/* Contact */}\n            <div id=\"contact\" className=\"glass p-8\">\n              <div className=\"flex items-center gap-3 mb-6\">\n                <Lock className=\"h-6 w-6 text-red-400\" />\n                <h2 className=\"text-2xl font-bold text-white\">Contact Us</h2>\n              </div>\n              <div className=\"space-y-4 text-white/80\">\n                <p>If you have any questions about this Privacy Policy or our data practices, please contact us:</p>\n                \n                <div className=\"bg-white/5 p-4 rounded-lg\">\n                  <p><strong>Email:</strong> <EMAIL></p>\n                  <p><strong>Address:</strong> StreamIt Pro, Inc.<br />\n                  123 Privacy Street<br />\n                  San Francisco, CA 94105<br />\n                  United States</p>\n                </div>\n                \n                <p>We will respond to your inquiry within 30 days.</p>\n              </div>\n            </div>\n\n            {/* Updates */}\n            <div className=\"glass p-8\">\n              <h2 className=\"text-2xl font-bold text-white mb-6\">Policy Updates</h2>\n              <div className=\"space-y-4 text-white/80\">\n                <p>We may update this Privacy Policy from time to time. When we make changes, we will:</p>\n                <ul className=\"list-disc list-inside space-y-2 ml-4\">\n                  <li>Update the \"Last updated\" date at the top of this policy</li>\n                  <li>Notify you via email if the changes are significant</li>\n                  <li>Post a notice on our website</li>\n                </ul>\n                <p>Your continued use of our service after any changes constitutes acceptance of the updated policy.</p>\n              </div>\n            </div>\n\n          </div>\n        </section>\n      </div>\n    </>\n  )\n}\n", "export default {\n  xmlns: 'http://www.w3.org/2000/svg',\n  width: 24,\n  height: 24,\n  viewBox: '0 0 24 24',\n  fill: 'none',\n  stroke: 'currentColor',\n  strokeWidth: 2,\n  strokeLinecap: 'round',\n  strokeLinejoin: 'round',\n};\n", "import {\n  forwardRef,\n  createElement,\n  ReactSVG,\n  SVGProps,\n  ForwardRefExoticComponent,\n  RefAttributes,\n} from 'react';\nimport defaultAttributes from './defaultAttributes';\n\nexport type IconNode = [elementName: keyof ReactSVG, attrs: Record<string, string>][];\n\nexport type SVGAttributes = Partial<SVGProps<SVGSVGElement>>;\ntype ComponentAttributes = RefAttributes<SVGSVGElement> & SVGAttributes;\n\nexport interface LucideProps extends ComponentAttributes {\n  size?: string | number;\n  absoluteStrokeWidth?: boolean;\n}\n\nexport type LucideIcon = ForwardRefExoticComponent<LucideProps>;\n/**\n * Converts string to KebabCase\n * Copied from scripts/helper. If anyone knows how to properly import it here\n * then please fix it.\n *\n * @param {string} string\n * @returns {string} A kebabized string\n */\nexport const toKebabCase = (string: string) =>\n  string\n    .replace(/([a-z0-9])([A-Z])/g, '$1-$2')\n    .toLowerCase()\n    .trim();\n\nconst createLucideIcon = (iconName: string, iconNode: IconNode): LucideIcon => {\n  const Component = forwardRef<SVGSVGElement, LucideProps>(\n    ({ color = 'currentColor', size = 24, strokeWidth = 2, absoluteStrokeWidth, className = '', children, ...rest }, ref) =>\n      createElement(\n        'svg',\n        {\n          ref,\n          ...defaultAttributes,\n          width: size,\n          height: size,\n          stroke: color,\n          strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n          className: ['lucide', `lucide-${toKebabCase(iconName)}`, className].join(' '),\n          ...rest,\n        },\n        [\n          ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n          ...(Array.isArray(children) ? children : [children]),\n        ]\n      )\n  );\n\n  Component.displayName = `${iconName}`;\n\n  return Component;\n};\n\nexport default createLucideIcon;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Eye\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMiAxMnMzLTcgMTAtNyAxMCA3IDEwIDctMyA3LTEwIDctMTAtNy0xMC03WiIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIzIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/eye\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Eye = createLucideIcon('Eye', [\n  ['path', { d: 'M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z', key: 'rwhkz3' }],\n  ['circle', { cx: '12', cy: '12', r: '3', key: '1v7zrd' }],\n]);\n\nexport default Eye;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name FileText\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTQuNSAySDZhMiAyIDAgMCAwLTIgMnYxNmEyIDIgMCAwIDAgMiAyaDEyYTIgMiAwIDAgMCAyLTJWNy41TDE0LjUgMnoiIC8+CiAgPHBvbHlsaW5lIHBvaW50cz0iMTQgMiAxNCA4IDIwIDgiIC8+CiAgPGxpbmUgeDE9IjE2IiB4Mj0iOCIgeTE9IjEzIiB5Mj0iMTMiIC8+CiAgPGxpbmUgeDE9IjE2IiB4Mj0iOCIgeTE9IjE3IiB5Mj0iMTciIC8+CiAgPGxpbmUgeDE9IjEwIiB4Mj0iOCIgeTE9IjkiIHkyPSI5IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/file-text\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FileText = createLucideIcon('FileText', [\n  [\n    'path',\n    { d: 'M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z', key: '1nnpy2' },\n  ],\n  ['polyline', { points: '14 2 14 8 20 8', key: '1ew0cm' }],\n  ['line', { x1: '16', x2: '8', y1: '13', y2: '13', key: '14keom' }],\n  ['line', { x1: '16', x2: '8', y1: '17', y2: '17', key: '17nazh' }],\n  ['line', { x1: '10', x2: '8', y1: '9', y2: '9', key: '1a5vjj' }],\n]);\n\nexport default FileText;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Globe\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8cGF0aCBkPSJNMTIgMmExNC41IDE0LjUgMCAwIDAgMCAyMCAxNC41IDE0LjUgMCAwIDAgMC0yMCIgLz4KICA8cGF0aCBkPSJNMiAxMmgyMCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/globe\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Globe = createLucideIcon('Globe', [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['path', { d: 'M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20', key: '13o1zl' }],\n  ['path', { d: 'M2 12h20', key: '9i4pu4' }],\n]);\n\nexport default Globe;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Lock\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTEiIHg9IjMiIHk9IjExIiByeD0iMiIgcnk9IjIiIC8+CiAgPHBhdGggZD0iTTcgMTFWN2E1IDUgMCAwIDEgMTAgMHY0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/lock\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Lock = createLucideIcon('Lock', [\n  ['rect', { width: '18', height: '11', x: '3', y: '11', rx: '2', ry: '2', key: '1w4ew1' }],\n  ['path', { d: 'M7 11V7a5 5 0 0 1 10 0v4', key: 'fwvmzm' }],\n]);\n\nexport default Lock;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Shield\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMjJzOC00IDgtMTBWNWwtOC0zLTggM3Y3YzAgNiA4IDEwIDggMTAiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/shield\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Shield = createLucideIcon('Shield', [\n  ['path', { d: 'M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10', key: '1irkt0' }],\n]);\n\nexport default Shield;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Users\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgMjF2LTJhNCA0IDAgMCAwLTQtNEg2YTQgNCAwIDAgMC00IDR2MiIgLz4KICA8Y2lyY2xlIGN4PSI5IiBjeT0iNyIgcj0iNCIgLz4KICA8cGF0aCBkPSJNMjIgMjF2LTJhNCA0IDAgMCAwLTMtMy44NyIgLz4KICA8cGF0aCBkPSJNMTYgMy4xM2E0IDQgMCAwIDEgMCA3Ljc1IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/users\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Users = createLucideIcon('Users', [\n  ['path', { d: 'M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2', key: '1yyitq' }],\n  ['circle', { cx: '9', cy: '7', r: '4', key: 'nufk8' }],\n  ['path', { d: 'M22 21v-2a4 4 0 0 0-3-3.87', key: 'kshegd' }],\n  ['path', { d: 'M16 3.13a4 4 0 0 1 0 7.75', key: '1da9ce' }],\n]);\n\nexport default Users;\n"], "names": ["Promise", "resolve", "then", "__webpack_require__", "bind", "dynamic", "PrivacyPage", "sections", "id", "title", "icon", "FileText", "Users", "Globe", "Shield", "Eye", "Lock", "react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__", "jsxs", "Fragment", "jsx", "div", "className", "section", "h1", "p", "h2", "map", "index", "a", "href", "concat", "span", "h3", "ul", "li", "strong", "br", "defaultAttributes", "xmlns", "width", "height", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "toKebabCase", "string", "replace", "toLowerCase", "trim", "createLucideIcon", "iconName", "iconNode", "Component", "forwardRef", "param", "ref", "color", "size", "absoluteStrokeWidth", "children", "rest", "createElement", "Number", "join", "tag", "attrs", "Array", "isArray", "displayName", "d", "key", "cx", "cy", "r", "points", "x1", "x2", "y1", "y2", "x", "y", "rx", "ry"], "sourceRoot": ""}