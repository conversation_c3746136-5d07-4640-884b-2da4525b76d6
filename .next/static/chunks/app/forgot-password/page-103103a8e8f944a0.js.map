{"version": 3, "file": "static/chunks/app/forgot-password/page-103103a8e8f944a0.js", "mappings": "oFAAAA,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,oLCKO,IAAME,EAAU,gBAIR,SAASC,IACtB,GAAM,CAACC,EAAOC,EAAS,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IAC7B,CAACC,EAAaC,EAAe,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IACzC,CAACG,EAAWC,EAAa,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IAErCK,EAAe,MAAOC,IAC1BA,EAAEC,cAAc,GAChBH,EAAa,IAGbI,WAAW,KACTJ,EAAa,IACbF,EAAe,GACjB,EAAG,IACL,SAEA,EAEI,GAAAO,EAAAC,IAAA,EAAAD,EAAAE,QAAA,YAEE,GAAAF,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,gBAEf,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,iFACb,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,2BACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,kCACb,GAAAL,EAAAG,GAAA,EAACG,EAAAA,CAAWA,CAAAA,CAACD,UAAU,0CACvB,GAAAL,EAAAG,GAAA,EAACI,KAAAA,CAAGF,UAAU,8CAAqC,qBACnD,GAAAL,EAAAC,IAAA,EAACO,IAAAA,CAAEH,UAAU,+BAAqB,uCACI,GAAAL,EAAAG,GAAA,EAACM,SAAAA,UAAQpB,IAAe,mFAG9D,GAAAW,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,sCAA6B,oFAG1C,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sBACb,GAAAL,EAAAG,GAAA,EAACO,EAAAA,OAAIA,CAAAA,CAACC,KAAK,SAASN,UAAU,uDAA8C,kBAG5E,GAAAL,EAAAG,GAAA,EAACS,SAAAA,CACCC,QAAS,IAAMpB,EAAe,IAC9BY,UAAU,gCACX,qCAYb,GAAAL,EAAAC,IAAA,EAAAD,EAAAE,QAAA,YAEE,GAAAF,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,gBAEf,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,iFACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,4BACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sBAEb,GAAAL,EAAAC,IAAA,EAACS,EAAAA,OAAIA,CAAAA,CACHC,KAAK,SACLN,UAAU,0FAEV,GAAAL,EAAAG,GAAA,EAACW,EAAAA,CAASA,CAAAA,CAACT,UAAU,YAAY,mBAKnC,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,6BACb,GAAAL,EAAAG,GAAA,EAACI,KAAAA,CAAGF,UAAU,8CAAqC,qBACnD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,yBAAgB,iFAM/B,GAAAL,EAAAC,IAAA,EAACc,OAAAA,CAAKC,SAAUpB,EAAcS,UAAU,sBACtC,GAAAL,EAAAC,IAAA,EAACG,MAAAA,WACC,GAAAJ,EAAAG,GAAA,EAACc,QAAAA,CAAMC,QAAQ,QAAQb,UAAU,wDAA+C,kBAGhF,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,qBACb,GAAAL,EAAAG,GAAA,EAACgB,EAAAA,CAAIA,CAAAA,CAACd,UAAU,6EAChB,GAAAL,EAAAG,GAAA,EAACiB,QAAAA,CACCC,KAAK,QACLC,GAAG,QACHC,MAAOlC,EACPmC,SAAU,GAAOlC,EAASO,EAAE4B,MAAM,CAACF,KAAK,EACxCG,SAAQ,GACRrB,UAAU,2BACVsB,YAAY,mCAKlB,GAAA3B,EAAAG,GAAA,EAACS,SAAAA,CACCS,KAAK,SACLO,SAAUlC,EACVW,UAAW,6DAEVwB,MAAA,CADCnC,EAAY,gCAAkC,aAG/CA,EACC,GAAAM,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,8EAEf,GAAAL,EAAAC,IAAA,EAAAD,EAAAE,QAAA,YACE,GAAAF,EAAAG,GAAA,EAACgB,EAAAA,CAAIA,CAAAA,CAACd,UAAU,YAAY,0BAQpC,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,4BACb,GAAAL,EAAAC,IAAA,EAACO,IAAAA,CAAEH,UAAU,kCAAwB,0BACX,IACxB,GAAAL,EAAAG,GAAA,EAACO,EAAAA,OAAIA,CAAAA,CAACC,KAAK,SAASN,UAAU,6DAAoD,oBAQxF,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,2BACb,GAAAL,EAAAG,GAAA,EAAC2B,KAAAA,CAAGzB,UAAU,yCAAgC,eAC9C,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,4CACb,GAAAL,EAAAG,GAAA,EAACK,IAAAA,UAAE,yEACH,GAAAR,EAAAG,GAAA,EAACK,IAAAA,UAAE,gEACH,GAAAR,EAAAG,GAAA,EAACK,IAAAA,UAAE,0DACH,GAAAR,EAAAG,GAAA,EAACK,IAAAA,UAAE,wDAEL,GAAAR,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,gBACb,GAAAL,EAAAG,GAAA,EAACO,EAAAA,OAAIA,CAAAA,CAACC,KAAK,WAAWN,UAAU,yDAAgD,kCAS9F;;;;;GC7IM,IAAAS,EAAYiB,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,YAAa,CAC9C,CAAC,OAAQ,CAAEC,EAAG,iBAAkBC,IAAK,UAAU,CAC/C,CAAC,OAAQ,CAAED,EAAG,WAAYC,IAAK,UAAU,CAC1C;;;;;GCHK,IAAA3B,EAAcyB,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,cAAe,CAClD,CAAC,OAAQ,CAAEC,EAAG,qCAAsCC,IAAK,UAAU,CACnE,CAAC,OAAQ,CAAED,EAAG,iBAAkBC,IAAK,UAAU,CAChD;;;;;GCHK,IAAAd,EAAOY,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,OAAQ,CACpC,CAAC,OAAQ,CAAEG,MAAO,KAAMC,OAAQ,KAAMC,EAAG,IAAKC,EAAG,IAAKC,GAAI,IAAKL,IAAK,UAAU,CAC9E,CAAC,OAAQ,CAAED,EAAG,4CAA6CC,IAAK,UAAU,CAC3E", "sources": ["webpack://_N_E/?c838", "webpack://_N_E/./app/forgot-password/page.tsx", "webpack://_N_E/../../../src/icons/arrow-left.ts", "webpack://_N_E/../../../src/icons/check-circle.ts", "webpack://_N_E/../../../src/icons/mail.ts"], "sourcesContent": ["import(/* webpackMode: \"eager\" */ \"/Volumes/Apps/Websites/streamit-main/app/forgot-password/page.tsx\");\n", "'use client'\n\nimport { useState } from 'react'\n\n// Force dynamic rendering\nexport const dynamic = 'force-dynamic'\nimport Link from 'next/link'\nimport { Mail, ArrowLeft, CheckCircle } from 'lucide-react'\n\nexport default function ForgotPasswordPage() {\n  const [email, setEmail] = useState('')\n  const [isSubmitted, setIsSubmitted] = useState(false)\n  const [isLoading, setIsLoading] = useState(false)\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setIsLoading(true)\n    \n    // Simulate API call\n    setTimeout(() => {\n      setIsLoading(false)\n      setIsSubmitted(true)\n    }, 2000)\n  }\n\n  if (isSubmitted) {\n    return (\n      <>\n        {/* Animated Background */}\n        <div className=\"animated-bg\"></div>\n\n        <div className=\"min-h-screen flex items-center justify-center p-4 relative z-10 pt-20\">\n          <div className=\"max-w-md w-full\">\n            <div className=\"glass p-8 text-center\">\n              <CheckCircle className=\"h-16 w-16 text-green-400 mx-auto mb-6\" />\n              <h1 className=\"text-3xl font-bold text-white mb-4\">Check Your Email</h1>\n              <p className=\"text-white/70 mb-6\">\n                We've sent a password reset link to <strong>{email}</strong>. \n                Please check your email and follow the instructions to reset your password.\n              </p>\n              <p className=\"text-white/60 text-sm mb-8\">\n                Didn't receive the email? Check your spam folder or try again in a few minutes.\n              </p>\n              <div className=\"space-y-4\">\n                <Link href=\"/login\" className=\"btn-primary w-full inline-block text-center\">\n                  Back to Login\n                </Link>\n                <button \n                  onClick={() => setIsSubmitted(false)}\n                  className=\"btn-secondary w-full\"\n                >\n                  Try Different Email\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </>\n    )\n  }\n\n  return (\n    <>\n      {/* Animated Background */}\n      <div className=\"animated-bg\"></div>\n\n      <div className=\"min-h-screen flex items-center justify-center p-4 relative z-10 pt-20\">\n        <div className=\"max-w-md w-full\">\n          <div className=\"glass p-8\">\n            {/* Back Link */}\n            <Link \n              href=\"/login\" \n              className=\"flex items-center gap-2 text-white/70 hover:text-white transition-colors mb-6\"\n            >\n              <ArrowLeft className=\"h-4 w-4\" />\n              Back to Login\n            </Link>\n\n            {/* Header */}\n            <div className=\"text-center mb-8\">\n              <h1 className=\"text-3xl font-bold text-white mb-2\">Forgot Password?</h1>\n              <p className=\"text-white/70\">\n                Enter your email address and we'll send you a link to reset your password\n              </p>\n            </div>\n\n            {/* Reset Form */}\n            <form onSubmit={handleSubmit} className=\"space-y-6\">\n              <div>\n                <label htmlFor=\"email\" className=\"block text-white/80 text-sm font-medium mb-2\">\n                  Email Address\n                </label>\n                <div className=\"relative\">\n                  <Mail className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-white/60\" />\n                  <input\n                    type=\"email\"\n                    id=\"email\"\n                    value={email}\n                    onChange={(e) => setEmail(e.target.value)}\n                    required\n                    className=\"glass-input w-full pl-10\"\n                    placeholder=\"Enter your email address\"\n                  />\n                </div>\n              </div>\n\n              <button\n                type=\"submit\"\n                disabled={isLoading}\n                className={`btn-primary w-full flex items-center justify-center gap-2 ${\n                  isLoading ? 'opacity-50 cursor-not-allowed' : ''\n                }`}\n              >\n                {isLoading ? (\n                  <div className=\"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin\" />\n                ) : (\n                  <>\n                    <Mail className=\"h-4 w-4\" />\n                    Send Reset Link\n                  </>\n                )}\n              </button>\n            </form>\n\n            {/* Additional Info */}\n            <div className=\"mt-8 text-center\">\n              <p className=\"text-white/60 text-sm\">\n                Remember your password?{' '}\n                <Link href=\"/login\" className=\"text-purple-400 hover:text-purple-300 font-medium\">\n                  Sign in\n                </Link>\n              </p>\n            </div>\n          </div>\n\n          {/* Help Text */}\n          <div className=\"mt-8 glass p-6\">\n            <h3 className=\"text-white font-semibold mb-3\">Need Help?</h3>\n            <div className=\"space-y-2 text-white/70 text-sm\">\n              <p>• Make sure you enter the email address associated with your account</p>\n              <p>• Check your spam or junk folder if you don't see the email</p>\n              <p>• The reset link will expire in 24 hours for security</p>\n              <p>• Contact support if you continue to have issues</p>\n            </div>\n            <div className=\"mt-4\">\n              <Link href=\"/contact\" className=\"text-purple-400 hover:text-purple-300 text-sm\">\n                Contact Support →\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n    </>\n  )\n}\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ArrowLeft\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTIgMTktNy03IDctNyIgLz4KICA8cGF0aCBkPSJNMTkgMTJINSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/arrow-left\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowLeft = createLucideIcon('ArrowLeft', [\n  ['path', { d: 'm12 19-7-7 7-7', key: '1l729n' }],\n  ['path', { d: 'M19 12H5', key: 'x3x0zl' }],\n]);\n\nexport default ArrowLeft;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name CheckCircle\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjIgMTEuMDhWMTJhMTAgMTAgMCAxIDEtNS45My05LjE0IiAvPgogIDxwYXRoIGQ9Im05IDExIDMgM0wyMiA0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/check-circle\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CheckCircle = createLucideIcon('CheckCircle', [\n  ['path', { d: 'M22 11.08V12a10 10 0 1 1-5.93-9.14', key: 'g774vq' }],\n  ['path', { d: 'm9 11 3 3L22 4', key: '1pflzl' }],\n]);\n\nexport default CheckCircle;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Mail\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMjAiIGhlaWdodD0iMTYiIHg9IjIiIHk9IjQiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Im0yMiA3LTguOTcgNS43YTEuOTQgMS45NCAwIDAgMS0yLjA2IDBMMiA3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/mail\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Mail = createLucideIcon('Mail', [\n  ['rect', { width: '20', height: '16', x: '2', y: '4', rx: '2', key: '18n3k1' }],\n  ['path', { d: 'm22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7', key: '1ocrg3' }],\n]);\n\nexport default Mail;\n"], "names": ["Promise", "resolve", "then", "__webpack_require__", "bind", "dynamic", "ForgotPasswordPage", "email", "setEmail", "useState", "isSubmitted", "setIsSubmitted", "isLoading", "setIsLoading", "handleSubmit", "e", "preventDefault", "setTimeout", "react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__", "jsxs", "Fragment", "jsx", "div", "className", "CheckCircle", "h1", "p", "strong", "Link", "href", "button", "onClick", "ArrowLeft", "form", "onSubmit", "label", "htmlFor", "Mail", "input", "type", "id", "value", "onChange", "target", "required", "placeholder", "disabled", "concat", "h3", "createLucideIcon", "d", "key", "width", "height", "x", "y", "rx"], "sourceRoot": ""}