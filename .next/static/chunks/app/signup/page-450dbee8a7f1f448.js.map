{"version": 3, "file": "static/chunks/app/signup/page-450dbee8a7f1f448.js", "mappings": "oFAAAA,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA;;;;;GCaM,IAAAE,EAAWC,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,WAAY,CAC5C,CAAC,OAAQ,CAAEC,MAAO,KAAMC,OAAQ,KAAMC,EAAG,IAAKC,EAAG,IAAKC,GAAI,IAAKC,GAAI,IAAKC,IAAK,UAAU,CACvF,CAAC,OAAQ,CAAEC,EAAG,eAAgBD,IAAK,UAAU,CAC7C,CAAC,OAAQ,CAAEC,EAAG,WAAYD,IAAK,UAAU,CACzC,CAAC,OAAQ,CAAEC,EAAG,YAAaD,IAAK,UAAU,CAC1C,CAAC,OAAQ,CAAEC,EAAG,YAAaD,IAAK,UAAU,CAC1C,CAAC,OAAQ,CAAEC,EAAG,aAAcD,IAAK,UAAU,CAC3C,CAAC,OAAQ,CAAEC,EAAG,aAAcD,IAAK,UAAU,CAC3C,CAAC,OAAQ,CAAEC,EAAG,aAAcD,IAAK,UAAU,CAC3C,CAAC,OAAQ,CAAEC,EAAG,aAAcD,IAAK,UAAU,CAC3C,CAAC,OAAQ,CAAEC,EAAG,YAAaD,IAAK,UAAU,CAC1C,CAAC,OAAQ,CAAEC,EAAG,YAAaD,IAAK,UAAU,CAC3C,wDClBc,SAASE,IACtB,GAAM,CAACC,EAAUC,EAAY,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,CACvCC,UAAW,GACXC,SAAU,GACVC,MAAO,GACPC,QAAS,GACTC,SAAU,GACVC,gBAAiB,GACjBC,aAAc,GACdC,oBAAqB,EACvB,GACM,CAACC,EAAcC,EAAgB,CAAGV,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IAC3C,CAACW,EAAqBC,EAAuB,CAAGZ,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IACzD,CAACa,EAAWC,EAAa,CAAGd,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IACrCe,EAASC,CAAAA,EAAAA,EAAAA,SAAAA,IAETC,EAAe,MAAOC,IAG1B,GAFAA,EAAEC,cAAc,GAEZrB,EAASO,QAAQ,GAAKP,EAASQ,eAAe,CAAE,CAClDc,MAAM,2BACN,MACF,CAEA,GAAI,CAACtB,EAASS,YAAY,CAAE,CAC1Ba,MAAM,2DACN,MACF,CAEAN,EAAa,IAGbO,WAAW,KACTP,EAAa,IACbM,MAAM,0DACNL,EAAOO,IAAI,CAAC,IACd,EAAG,IACL,EAEMC,EAAe,IACnB,GAAM,CAAEC,KAAAA,CAAI,CAAEC,MAAAA,CAAK,CAAEC,KAAAA,CAAI,CAAEC,QAAAA,CAAO,CAAE,CAAGT,EAAEU,MAAM,CAC/C7B,EAAY8B,GAAS,EACnB,GAAGA,CAAI,CACP,CAACL,EAAK,CAAEE,aAAAA,EAAsBC,EAAUF,CAC1C,GACF,EAEA,MACE,GAAAK,EAAAC,IAAA,EAAAD,EAAAE,QAAA,YAEE,GAAAF,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,gBAEf,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,uFACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,4BACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sBAEb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,6BACb,GAAAL,EAAAG,GAAA,EAACG,KAAAA,CAAGD,UAAU,8CAAqC,mBACnD,GAAAL,EAAAG,GAAA,EAACI,IAAAA,CAAEF,UAAU,yBAAgB,kDAI/B,GAAAL,EAAAC,IAAA,EAACO,OAAAA,CAAKC,SAAUtB,EAAckB,UAAU,sBAEtC,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,mCACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,WACC,GAAAJ,EAAAG,GAAA,EAACO,QAAAA,CAAMC,QAAQ,YAAYN,UAAU,wDAA+C,eAGpF,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,qBACb,GAAAL,EAAAG,GAAA,EAACS,EAAAA,CAAIA,CAAAA,CAACP,UAAU,6EAChB,GAAAL,EAAAG,GAAA,EAACU,QAAAA,CACCjB,KAAK,OACLkB,GAAG,YACHpB,KAAK,YACLC,MAAO3B,EAASG,SAAS,CACzB4C,SAAUtB,EACVuB,SAAQ,GACRX,UAAU,2BACVY,YAAY,eAIlB,GAAAjB,EAAAC,IAAA,EAACG,MAAAA,WACC,GAAAJ,EAAAG,GAAA,EAACO,QAAAA,CAAMC,QAAQ,WAAWN,UAAU,wDAA+C,cAGnF,GAAAL,EAAAG,GAAA,EAACU,QAAAA,CACCjB,KAAK,OACLkB,GAAG,WACHpB,KAAK,WACLC,MAAO3B,EAASI,QAAQ,CACxB2C,SAAUtB,EACVuB,SAAQ,GACRX,UAAU,qBACVY,YAAY,cAMlB,GAAAjB,EAAAC,IAAA,EAACG,MAAAA,WACC,GAAAJ,EAAAG,GAAA,EAACO,QAAAA,CAAMC,QAAQ,QAAQN,UAAU,wDAA+C,kBAGhF,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,qBACb,GAAAL,EAAAG,GAAA,EAACe,EAAAA,CAAIA,CAAAA,CAACb,UAAU,6EAChB,GAAAL,EAAAG,GAAA,EAACU,QAAAA,CACCjB,KAAK,QACLkB,GAAG,QACHpB,KAAK,QACLC,MAAO3B,EAASK,KAAK,CACrB0C,SAAUtB,EACVuB,SAAQ,GACRX,UAAU,2BACVY,YAAY,2BAMlB,GAAAjB,EAAAC,IAAA,EAACG,MAAAA,WACC,GAAAJ,EAAAG,GAAA,EAACO,QAAAA,CAAMC,QAAQ,UAAUN,UAAU,wDAA+C,uBAGlF,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,qBACb,GAAAL,EAAAG,GAAA,EAAC9C,EAAQA,CAACgD,UAAU,6EACpB,GAAAL,EAAAG,GAAA,EAACU,QAAAA,CACCjB,KAAK,OACLkB,GAAG,UACHpB,KAAK,UACLC,MAAO3B,EAASM,OAAO,CACvByC,SAAUtB,EACVY,UAAU,2BACVY,YAAY,4BAMlB,GAAAjB,EAAAC,IAAA,EAACG,MAAAA,WACC,GAAAJ,EAAAG,GAAA,EAACO,QAAAA,CAAMC,QAAQ,WAAWN,UAAU,wDAA+C,aAGnF,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,qBACb,GAAAL,EAAAG,GAAA,EAACgB,EAAAA,CAAIA,CAAAA,CAACd,UAAU,6EAChB,GAAAL,EAAAG,GAAA,EAACU,QAAAA,CACCjB,KAAMjB,EAAe,OAAS,WAC9BmC,GAAG,WACHpB,KAAK,WACLC,MAAO3B,EAASO,QAAQ,CACxBwC,SAAUtB,EACVuB,SAAQ,GACRX,UAAU,iCACVY,YAAY,6BAEd,GAAAjB,EAAAG,GAAA,EAACiB,SAAAA,CACCxB,KAAK,SACLyB,QAAS,IAAMzC,EAAgB,CAACD,GAChC0B,UAAU,8FAET1B,EAAe,GAAAqB,EAAAG,GAAA,EAACmB,EAAAA,CAAMA,CAAAA,CAACjB,UAAU,YAAe,GAAAL,EAAAG,GAAA,EAACoB,EAAAA,CAAGA,CAAAA,CAAClB,UAAU,oBAMtE,GAAAL,EAAAC,IAAA,EAACG,MAAAA,WACC,GAAAJ,EAAAG,GAAA,EAACO,QAAAA,CAAMC,QAAQ,kBAAkBN,UAAU,wDAA+C,qBAG1F,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,qBACb,GAAAL,EAAAG,GAAA,EAACgB,EAAAA,CAAIA,CAAAA,CAACd,UAAU,6EAChB,GAAAL,EAAAG,GAAA,EAACU,QAAAA,CACCjB,KAAMf,EAAsB,OAAS,WACrCiC,GAAG,kBACHpB,KAAK,kBACLC,MAAO3B,EAASQ,eAAe,CAC/BuC,SAAUtB,EACVuB,SAAQ,GACRX,UAAU,iCACVY,YAAY,0BAEd,GAAAjB,EAAAG,GAAA,EAACiB,SAAAA,CACCxB,KAAK,SACLyB,QAAS,IAAMvC,EAAuB,CAACD,GACvCwB,UAAU,8FAETxB,EAAsB,GAAAmB,EAAAG,GAAA,EAACmB,EAAAA,CAAMA,CAAAA,CAACjB,UAAU,YAAe,GAAAL,EAAAG,GAAA,EAACoB,EAAAA,CAAGA,CAAAA,CAAClB,UAAU,oBAM7E,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sBACb,GAAAL,EAAAC,IAAA,EAACS,QAAAA,CAAML,UAAU,mCACf,GAAAL,EAAAG,GAAA,EAACU,QAAAA,CACCjB,KAAK,WACLF,KAAK,eACLG,QAAS7B,EAASS,YAAY,CAC9BsC,SAAUtB,EACVuB,SAAQ,GACRX,UAAU,6GAEZ,GAAAL,EAAAC,IAAA,EAACuB,OAAAA,CAAKnB,UAAU,kCAAwB,iBACvB,IACf,GAAAL,EAAAG,GAAA,EAACsB,EAAAA,OAAIA,CAAAA,CAACC,KAAK,SAASrB,UAAU,iDAAwC,qBAE9D,IAAI,MACR,IACJ,GAAAL,EAAAG,GAAA,EAACsB,EAAAA,OAAIA,CAAAA,CAACC,KAAK,WAAWrB,UAAU,iDAAwC,yBAK5E,GAAAL,EAAAC,IAAA,EAACS,QAAAA,CAAML,UAAU,oCACf,GAAAL,EAAAG,GAAA,EAACU,QAAAA,CACCjB,KAAK,WACLF,KAAK,sBACLG,QAAS7B,EAASU,mBAAmB,CACrCqC,SAAUtB,EACVY,UAAU,sGAEZ,GAAAL,EAAAG,GAAA,EAACqB,OAAAA,CAAKnB,UAAU,iCAAwB,2DAO5C,GAAAL,EAAAG,GAAA,EAACiB,SAAAA,CACCxB,KAAK,SACL+B,SAAU5C,EACVsB,UAAW,6DAEVuB,MAAA,CADC7C,EAAY,gCAAkC,aAG/CA,EACC,GAAAiB,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,8EAEf,GAAAL,EAAAC,IAAA,EAAAD,EAAAE,QAAA,YAAE,iBAEA,GAAAF,EAAAG,GAAA,EAAC0B,EAAAA,CAAUA,CAAAA,CAACxB,UAAU,oBAO9B,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,mCACb,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,oCACf,GAAAL,EAAAG,GAAA,EAACqB,OAAAA,CAAKnB,UAAU,sCAA6B,OAC7C,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,uCAIjB,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sBACb,GAAAL,EAAAC,IAAA,EAACmB,SAAAA,CAAOf,UAAU,wEAChB,GAAAL,EAAAC,IAAA,EAAC6B,MAAAA,CAAIzB,UAAU,UAAU0B,QAAQ,sBAC/B,GAAA/B,EAAAG,GAAA,EAAC6B,OAAAA,CAAKC,KAAK,eAAenE,EAAE,4HAC5B,GAAAkC,EAAAG,GAAA,EAAC6B,OAAAA,CAAKC,KAAK,eAAenE,EAAE,0IAC5B,GAAAkC,EAAAG,GAAA,EAAC6B,OAAAA,CAAKC,KAAK,eAAenE,EAAE,kIAC5B,GAAAkC,EAAAG,GAAA,EAAC6B,OAAAA,CAAKC,KAAK,eAAenE,EAAE,2IACxB,yBAGR,GAAAkC,EAAAC,IAAA,EAACmB,SAAAA,CAAOf,UAAU,wEAChB,GAAAL,EAAAG,GAAA,EAAC2B,MAAAA,CAAIzB,UAAU,UAAU4B,KAAK,eAAeF,QAAQ,qBACnD,GAAA/B,EAAAG,GAAA,EAAC6B,OAAAA,CAAKlE,EAAE,qSACJ,8BAMV,GAAAkC,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,4BACb,GAAAL,EAAAC,IAAA,EAACM,IAAAA,CAAEF,UAAU,0BAAgB,2BACF,IACzB,GAAAL,EAAAG,GAAA,EAACsB,EAAAA,OAAIA,CAAAA,CAACC,KAAK,SAASrB,UAAU,6DAAoD,oBAQxF,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,2BACb,GAAAL,EAAAG,GAAA,EAAC+B,KAAAA,CAAG7B,UAAU,qDAA4C,oCAC1D,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sBACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,0DACb,GAAAL,EAAAG,GAAA,EAACgC,EAAAA,CAAKA,CAAAA,CAAC9B,UAAU,2BACjB,GAAAL,EAAAG,GAAA,EAACqB,OAAAA,UAAK,kDAER,GAAAxB,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,0DACb,GAAAL,EAAAG,GAAA,EAACgC,EAAAA,CAAKA,CAAAA,CAAC9B,UAAU,2BACjB,GAAAL,EAAAG,GAAA,EAACqB,OAAAA,UAAK,iDAER,GAAAxB,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,0DACb,GAAAL,EAAAG,GAAA,EAACgC,EAAAA,CAAKA,CAAAA,CAAC9B,UAAU,2BACjB,GAAAL,EAAAG,GAAA,EAACqB,OAAAA,UAAK,wCAER,GAAAxB,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,0DACb,GAAAL,EAAAG,GAAA,EAACgC,EAAAA,CAAKA,CAAAA,CAAC9B,UAAU,2BACjB,GAAAL,EAAAG,GAAA,EAACqB,OAAAA,UAAK,0CAQtB;;;;;GCjTM,IAAAK,EAAavE,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,aAAc,CAChD,CAAC,OAAQ,CAAEQ,EAAG,WAAYD,IAAK,UAAU,CACzC,CAAC,OAAQ,CAAEC,EAAG,gBAAiBD,IAAK,UAAU,CAC/C;;;;;GCHD,IAAMsE,EAAQ7E,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,QAAS,CAAC,CAAC,OAAQ,CAAEQ,EAAG,kBAAmBD,IAAK,QAAS,EAAE,CAAC;;;;;GCArF,IAAAyD,EAAShE,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,SAAU,CACxC,CAAC,OAAQ,CAAEQ,EAAG,iCAAkCD,IAAK,UAAU,CAC/D,CACE,OACA,CACEC,EAAG,+EACHD,IAAK,QACP,EACF,CACA,CACE,OACA,CAAEC,EAAG,yEAA0ED,IAAK,QAAS,EAC/F,CACA,CAAC,OAAQ,CAAEuE,GAAI,IAAKC,GAAI,KAAMC,GAAI,IAAKC,GAAI,KAAM1E,IAAK,UAAU,CACjE;;;;;GCdK,IAAA0D,EAAMjE,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,MAAO,CAClC,CAAC,OAAQ,CAAEQ,EAAG,+CAAgDD,IAAK,UAAU,CAC7E,CAAC,SAAU,CAAE2E,GAAI,KAAMC,GAAI,KAAMC,EAAG,IAAK7E,IAAK,UAAU,CACzD;;;;;GCHK,IAAAsD,EAAO7D,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,OAAQ,CACpC,CAAC,OAAQ,CAAEC,MAAO,KAAMC,OAAQ,KAAMC,EAAG,IAAKC,EAAG,KAAMC,GAAI,IAAKC,GAAI,IAAKC,IAAK,UAAU,CACxF,CAAC,OAAQ,CAAEC,EAAG,2BAA4BD,IAAK,UAAU,CAC1D;;;;;GCHK,IAAAqD,EAAO5D,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,OAAQ,CACpC,CAAC,OAAQ,CAAEC,MAAO,KAAMC,OAAQ,KAAMC,EAAG,IAAKC,EAAG,IAAKC,GAAI,IAAKE,IAAK,UAAU,CAC9E,CAAC,OAAQ,CAAEC,EAAG,4CAA6CD,IAAK,UAAU,CAC3E;;;;;GCHK,IAAA+C,EAAOtD,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,OAAQ,CACpC,CAAC,OAAQ,CAAEQ,EAAG,4CAA6CD,IAAK,UAAU,CAC1E,CAAC,SAAU,CAAE2E,GAAI,KAAMC,GAAI,IAAKC,EAAG,IAAK7E,IAAK,UAAU,CACxD", "sources": ["webpack://_N_E/?eed5", "webpack://_N_E/../../../src/icons/building.ts", "webpack://_N_E/./app/signup/page.tsx", "webpack://_N_E/../../../src/icons/arrow-right.ts", "webpack://_N_E/../../../src/icons/check.ts", "webpack://_N_E/../../../src/icons/eye-off.ts", "webpack://_N_E/../../../src/icons/eye.ts", "webpack://_N_E/../../../src/icons/lock.ts", "webpack://_N_E/../../../src/icons/mail.ts", "webpack://_N_E/../../../src/icons/user.ts", "webpack://_N_E/./node_modules/next/dist/api/navigation.js"], "sourcesContent": ["import(/* webpackMode: \"eager\" */ \"/Volumes/Apps/Websites/streamit-main/app/signup/page.tsx\");\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Building\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTYiIGhlaWdodD0iMjAiIHg9IjQiIHk9IjIiIHJ4PSIyIiByeT0iMiIgLz4KICA8cGF0aCBkPSJNOSAyMnYtNGg2djQiIC8+CiAgPHBhdGggZD0iTTggNmguMDEiIC8+CiAgPHBhdGggZD0iTTE2IDZoLjAxIiAvPgogIDxwYXRoIGQ9Ik0xMiA2aC4wMSIgLz4KICA8cGF0aCBkPSJNMTIgMTBoLjAxIiAvPgogIDxwYXRoIGQ9Ik0xMiAxNGguMDEiIC8+CiAgPHBhdGggZD0iTTE2IDEwaC4wMSIgLz4KICA8cGF0aCBkPSJNMTYgMTRoLjAxIiAvPgogIDxwYXRoIGQ9Ik04IDEwaC4wMSIgLz4KICA8cGF0aCBkPSJNOCAxNGguMDEiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/building\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Building = createLucideIcon('Building', [\n  ['rect', { width: '16', height: '20', x: '4', y: '2', rx: '2', ry: '2', key: '76otgf' }],\n  ['path', { d: 'M9 22v-4h6v4', key: 'r93iot' }],\n  ['path', { d: 'M8 6h.01', key: '1dz90k' }],\n  ['path', { d: 'M16 6h.01', key: '1x0f13' }],\n  ['path', { d: 'M12 6h.01', key: '1vi96p' }],\n  ['path', { d: 'M12 10h.01', key: '1nrarc' }],\n  ['path', { d: 'M12 14h.01', key: '1etili' }],\n  ['path', { d: 'M16 10h.01', key: '1m94wz' }],\n  ['path', { d: 'M16 14h.01', key: '1gbofw' }],\n  ['path', { d: 'M8 10h.01', key: '19clt8' }],\n  ['path', { d: 'M8 14h.01', key: '6423bh' }],\n]);\n\nexport default Building;\n", "'use client'\n\nimport { useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport Link from 'next/link'\nimport { Eye, EyeOff, Mail, Lock, User, Building, ArrowRight, Check } from 'lucide-react'\n\nexport default function SignupPage() {\n  const [formData, setFormData] = useState({\n    firstName: '',\n    lastName: '',\n    email: '',\n    company: '',\n    password: '',\n    confirmPassword: '',\n    agreeToTerms: false,\n    subscribeNewsletter: true\n  })\n  const [showPassword, setShowPassword] = useState(false)\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false)\n  const [isLoading, setIsLoading] = useState(false)\n  const router = useRouter()\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    if (formData.password !== formData.confirmPassword) {\n      alert('Passwords do not match!')\n      return\n    }\n    \n    if (!formData.agreeToTerms) {\n      alert('Please agree to the Terms of Service and Privacy Policy')\n      return\n    }\n\n    setIsLoading(true)\n    \n    // Simulate signup process\n    setTimeout(() => {\n      setIsLoading(false)\n      alert('Account created successfully! Welcome to StreamIt Pro!')\n      router.push('/')\n    }, 2000)\n  }\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value, type, checked } = e.target\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }))\n  }\n\n  return (\n    <>\n      {/* Animated Background */}\n      <div className=\"animated-bg\"></div>\n\n      <div className=\"min-h-screen flex items-center justify-center p-4 relative z-10 pt-20 pb-20\">\n        <div className=\"max-w-md w-full\">\n          <div className=\"glass p-8\">\n            {/* Header */}\n            <div className=\"text-center mb-8\">\n              <h1 className=\"text-3xl font-bold text-white mb-2\">Create Account</h1>\n              <p className=\"text-white/70\">Join thousands of teams using StreamIt Pro</p>\n            </div>\n\n            {/* Signup Form */}\n            <form onSubmit={handleSubmit} className=\"space-y-6\">\n              {/* Name Fields */}\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div>\n                  <label htmlFor=\"firstName\" className=\"block text-white/80 text-sm font-medium mb-2\">\n                    First Name\n                  </label>\n                  <div className=\"relative\">\n                    <User className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-white/60\" />\n                    <input\n                      type=\"text\"\n                      id=\"firstName\"\n                      name=\"firstName\"\n                      value={formData.firstName}\n                      onChange={handleChange}\n                      required\n                      className=\"glass-input w-full pl-10\"\n                      placeholder=\"John\"\n                    />\n                  </div>\n                </div>\n                <div>\n                  <label htmlFor=\"lastName\" className=\"block text-white/80 text-sm font-medium mb-2\">\n                    Last Name\n                  </label>\n                  <input\n                    type=\"text\"\n                    id=\"lastName\"\n                    name=\"lastName\"\n                    value={formData.lastName}\n                    onChange={handleChange}\n                    required\n                    className=\"glass-input w-full\"\n                    placeholder=\"Doe\"\n                  />\n                </div>\n              </div>\n\n              {/* Email Field */}\n              <div>\n                <label htmlFor=\"email\" className=\"block text-white/80 text-sm font-medium mb-2\">\n                  Email Address\n                </label>\n                <div className=\"relative\">\n                  <Mail className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-white/60\" />\n                  <input\n                    type=\"email\"\n                    id=\"email\"\n                    name=\"email\"\n                    value={formData.email}\n                    onChange={handleChange}\n                    required\n                    className=\"glass-input w-full pl-10\"\n                    placeholder=\"<EMAIL>\"\n                  />\n                </div>\n              </div>\n\n              {/* Company Field */}\n              <div>\n                <label htmlFor=\"company\" className=\"block text-white/80 text-sm font-medium mb-2\">\n                  Company (Optional)\n                </label>\n                <div className=\"relative\">\n                  <Building className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-white/60\" />\n                  <input\n                    type=\"text\"\n                    id=\"company\"\n                    name=\"company\"\n                    value={formData.company}\n                    onChange={handleChange}\n                    className=\"glass-input w-full pl-10\"\n                    placeholder=\"Your company name\"\n                  />\n                </div>\n              </div>\n\n              {/* Password Field */}\n              <div>\n                <label htmlFor=\"password\" className=\"block text-white/80 text-sm font-medium mb-2\">\n                  Password\n                </label>\n                <div className=\"relative\">\n                  <Lock className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-white/60\" />\n                  <input\n                    type={showPassword ? 'text' : 'password'}\n                    id=\"password\"\n                    name=\"password\"\n                    value={formData.password}\n                    onChange={handleChange}\n                    required\n                    className=\"glass-input w-full pl-10 pr-10\"\n                    placeholder=\"Create a strong password\"\n                  />\n                  <button\n                    type=\"button\"\n                    onClick={() => setShowPassword(!showPassword)}\n                    className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white\"\n                  >\n                    {showPassword ? <EyeOff className=\"h-5 w-5\" /> : <Eye className=\"h-5 w-5\" />}\n                  </button>\n                </div>\n              </div>\n\n              {/* Confirm Password Field */}\n              <div>\n                <label htmlFor=\"confirmPassword\" className=\"block text-white/80 text-sm font-medium mb-2\">\n                  Confirm Password\n                </label>\n                <div className=\"relative\">\n                  <Lock className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-white/60\" />\n                  <input\n                    type={showConfirmPassword ? 'text' : 'password'}\n                    id=\"confirmPassword\"\n                    name=\"confirmPassword\"\n                    value={formData.confirmPassword}\n                    onChange={handleChange}\n                    required\n                    className=\"glass-input w-full pl-10 pr-10\"\n                    placeholder=\"Confirm your password\"\n                  />\n                  <button\n                    type=\"button\"\n                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}\n                    className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white\"\n                  >\n                    {showConfirmPassword ? <EyeOff className=\"h-5 w-5\" /> : <Eye className=\"h-5 w-5\" />}\n                  </button>\n                </div>\n              </div>\n\n              {/* Checkboxes */}\n              <div className=\"space-y-3\">\n                <label className=\"flex items-start gap-3\">\n                  <input\n                    type=\"checkbox\"\n                    name=\"agreeToTerms\"\n                    checked={formData.agreeToTerms}\n                    onChange={handleChange}\n                    required\n                    className=\"w-4 h-4 text-purple-600 bg-transparent border-white/30 rounded focus:ring-purple-500 focus:ring-2 mt-0.5\"\n                  />\n                  <span className=\"text-sm text-white/70\">\n                    I agree to the{' '}\n                    <Link href=\"/terms\" className=\"text-purple-400 hover:text-purple-300\">\n                      Terms of Service\n                    </Link>{' '}\n                    and{' '}\n                    <Link href=\"/privacy\" className=\"text-purple-400 hover:text-purple-300\">\n                      Privacy Policy\n                    </Link>\n                  </span>\n                </label>\n                <label className=\"flex items-center gap-3\">\n                  <input\n                    type=\"checkbox\"\n                    name=\"subscribeNewsletter\"\n                    checked={formData.subscribeNewsletter}\n                    onChange={handleChange}\n                    className=\"w-4 h-4 text-purple-600 bg-transparent border-white/30 rounded focus:ring-purple-500 focus:ring-2\"\n                  />\n                  <span className=\"text-sm text-white/70\">\n                    Subscribe to our newsletter for updates and tips\n                  </span>\n                </label>\n              </div>\n\n              {/* Submit Button */}\n              <button\n                type=\"submit\"\n                disabled={isLoading}\n                className={`btn-primary w-full flex items-center justify-center gap-2 ${\n                  isLoading ? 'opacity-50 cursor-not-allowed' : ''\n                }`}\n              >\n                {isLoading ? (\n                  <div className=\"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin\" />\n                ) : (\n                  <>\n                    Create Account\n                    <ArrowRight className=\"h-4 w-4\" />\n                  </>\n                )}\n              </button>\n            </form>\n\n            {/* Divider */}\n            <div className=\"my-6 flex items-center\">\n              <div className=\"flex-1 border-t border-white/20\"></div>\n              <span className=\"px-4 text-white/60 text-sm\">or</span>\n              <div className=\"flex-1 border-t border-white/20\"></div>\n            </div>\n\n            {/* Social Signup */}\n            <div className=\"space-y-3\">\n              <button className=\"btn-secondary w-full flex items-center justify-center gap-2\">\n                <svg className=\"w-5 h-5\" viewBox=\"0 0 24 24\">\n                  <path fill=\"currentColor\" d=\"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"/>\n                  <path fill=\"currentColor\" d=\"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"/>\n                  <path fill=\"currentColor\" d=\"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"/>\n                  <path fill=\"currentColor\" d=\"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"/>\n                </svg>\n                Sign up with Google\n              </button>\n              <button className=\"btn-secondary w-full flex items-center justify-center gap-2\">\n                <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"/>\n                </svg>\n                Sign up with Facebook\n              </button>\n            </div>\n\n            {/* Login Link */}\n            <div className=\"mt-8 text-center\">\n              <p className=\"text-white/70\">\n                Already have an account?{' '}\n                <Link href=\"/login\" className=\"text-purple-400 hover:text-purple-300 font-medium\">\n                  Sign in\n                </Link>\n              </p>\n            </div>\n          </div>\n\n          {/* Features */}\n          <div className=\"mt-8 glass p-6\">\n            <h3 className=\"text-white font-semibold mb-4 text-center\">What you get with StreamIt Pro:</h3>\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center gap-2 text-white/70 text-sm\">\n                <Check className=\"h-4 w-4 text-green-400\" />\n                <span>14-day free trial, no credit card required</span>\n              </div>\n              <div className=\"flex items-center gap-2 text-white/70 text-sm\">\n                <Check className=\"h-4 w-4 text-green-400\" />\n                <span>HD video calls with up to 25 participants</span>\n              </div>\n              <div className=\"flex items-center gap-2 text-white/70 text-sm\">\n                <Check className=\"h-4 w-4 text-green-400\" />\n                <span>Advanced security and encryption</span>\n              </div>\n              <div className=\"flex items-center gap-2 text-white/70 text-sm\">\n                <Check className=\"h-4 w-4 text-green-400\" />\n                <span>24/7 customer support</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </>\n  )\n}\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ArrowRight\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSAxMmgxNCIgLz4KICA8cGF0aCBkPSJtMTIgNSA3IDctNyA3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/arrow-right\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowRight = createLucideIcon('ArrowRight', [\n  ['path', { d: 'M5 12h14', key: '1ays0h' }],\n  ['path', { d: 'm12 5 7 7-7 7', key: 'xquz4c' }],\n]);\n\nexport default ArrowRight;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Check\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjAgNiA5IDE3bC01LTUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/check\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Check = createLucideIcon('Check', [['path', { d: 'M20 6 9 17l-5-5', key: '1gmf2c' }]]);\n\nexport default Check;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name EyeOff\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOS44OCA5Ljg4YTMgMyAwIDEgMCA0LjI0IDQuMjQiIC8+CiAgPHBhdGggZD0iTTEwLjczIDUuMDhBMTAuNDMgMTAuNDMgMCAwIDEgMTIgNWM3IDAgMTAgNyAxMCA3YTEzLjE2IDEzLjE2IDAgMCAxLTEuNjcgMi42OCIgLz4KICA8cGF0aCBkPSJNNi42MSA2LjYxQTEzLjUyNiAxMy41MjYgMCAwIDAgMiAxMnMzIDcgMTAgN2E5Ljc0IDkuNzQgMCAwIDAgNS4zOS0xLjYxIiAvPgogIDxsaW5lIHgxPSIyIiB4Mj0iMjIiIHkxPSIyIiB5Mj0iMjIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/eye-off\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst EyeOff = createLucideIcon('EyeOff', [\n  ['path', { d: 'M9.88 9.88a3 3 0 1 0 4.24 4.24', key: '1jxqfv' }],\n  [\n    'path',\n    {\n      d: 'M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68',\n      key: '9wicm4',\n    },\n  ],\n  [\n    'path',\n    { d: 'M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61', key: '1jreej' },\n  ],\n  ['line', { x1: '2', x2: '22', y1: '2', y2: '22', key: 'a6p6uj' }],\n]);\n\nexport default EyeOff;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Eye\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMiAxMnMzLTcgMTAtNyAxMCA3IDEwIDctMyA3LTEwIDctMTAtNy0xMC03WiIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIzIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/eye\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Eye = createLucideIcon('Eye', [\n  ['path', { d: 'M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z', key: 'rwhkz3' }],\n  ['circle', { cx: '12', cy: '12', r: '3', key: '1v7zrd' }],\n]);\n\nexport default Eye;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Lock\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTEiIHg9IjMiIHk9IjExIiByeD0iMiIgcnk9IjIiIC8+CiAgPHBhdGggZD0iTTcgMTFWN2E1IDUgMCAwIDEgMTAgMHY0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/lock\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Lock = createLucideIcon('Lock', [\n  ['rect', { width: '18', height: '11', x: '3', y: '11', rx: '2', ry: '2', key: '1w4ew1' }],\n  ['path', { d: 'M7 11V7a5 5 0 0 1 10 0v4', key: 'fwvmzm' }],\n]);\n\nexport default Lock;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Mail\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMjAiIGhlaWdodD0iMTYiIHg9IjIiIHk9IjQiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Im0yMiA3LTguOTcgNS43YTEuOTQgMS45NCAwIDAgMS0yLjA2IDBMMiA3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/mail\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Mail = createLucideIcon('Mail', [\n  ['rect', { width: '20', height: '16', x: '2', y: '4', rx: '2', key: '18n3k1' }],\n  ['path', { d: 'm22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7', key: '1ocrg3' }],\n]);\n\nexport default Mail;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name User\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTkgMjF2LTJhNCA0IDAgMCAwLTQtNEg5YTQgNCAwIDAgMC00IDR2MiIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjciIHI9IjQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/user\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst User = createLucideIcon('User', [\n  ['path', { d: 'M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2', key: '975kel' }],\n  ['circle', { cx: '12', cy: '7', r: '4', key: '17ys0d' }],\n]);\n\nexport default User;\n", "export * from \"../client/components/navigation\";\n\n//# sourceMappingURL=navigation.js.map"], "names": ["Promise", "resolve", "then", "__webpack_require__", "bind", "Building", "createLucideIcon", "width", "height", "x", "y", "rx", "ry", "key", "d", "SignupPage", "formData", "setFormData", "useState", "firstName", "lastName", "email", "company", "password", "confirmPassword", "agreeToTerms", "subscribeNewsletter", "showPassword", "setShowPassword", "showConfirmPassword", "setShowConfirmPassword", "isLoading", "setIsLoading", "router", "useRouter", "handleSubmit", "e", "preventDefault", "alert", "setTimeout", "push", "handleChange", "name", "value", "type", "checked", "target", "prev", "jsx_runtime", "jsxs", "Fragment", "jsx", "div", "className", "h1", "p", "form", "onSubmit", "label", "htmlFor", "User", "input", "id", "onChange", "required", "placeholder", "Mail", "Lock", "button", "onClick", "Eye<PERSON>ff", "Eye", "span", "Link", "href", "disabled", "concat", "ArrowRight", "svg", "viewBox", "path", "fill", "h3", "Check", "x1", "x2", "y1", "y2", "cx", "cy", "r"], "sourceRoot": ""}