{"version": 3, "file": "static/chunks/app/layout-164734fc9c5f56ed.js", "mappings": "oFAAAA,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,CAAA,CAAAC,IAAA,CAAAF,EAAA,UAEAH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAE,IAAA,CAAAF,EAAA,OAEAH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAE,IAAA,CAAAF,EAAA,MAEAH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAE,IAAA,CAAAF,EAAA,MAEAH,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,CAAA,CAAAC,IAAA,CAAAF,EAAA,mJCHe,SAASG,IACtB,MACE,GAAAC,EAAAC,GAAA,EAACC,SAAAA,CAAOC,UAAU,uEAChB,GAAAH,EAAAI,IAAA,EAACC,MAAAA,CAAIF,UAAU,yDACb,GAAAH,EAAAI,IAAA,EAACC,MAAAA,CAAIF,UAAU,kDACb,GAAAH,EAAAI,IAAA,EAACC,MAAAA,WACC,GAAAL,EAAAI,IAAA,EAACC,MAAAA,CAAIF,UAAU,mCACb,GAAAH,EAAAC,GAAA,EAACI,MAAAA,CAAIF,UAAU,qHACb,GAAAH,EAAAC,GAAA,EAACK,EAAAA,CAAKA,CAAAA,CAACH,UAAU,yBAEnB,GAAAH,EAAAC,GAAA,EAACM,OAAAA,CAAKJ,UAAU,wCAA+B,oBAEjD,GAAAH,EAAAC,GAAA,EAACO,IAAAA,CAAEL,UAAU,sCAA6B,qEAG1C,GAAAH,EAAAI,IAAA,EAACC,MAAAA,CAAIF,UAAU,gCACb,GAAAH,EAAAC,GAAA,EAACQ,IAAAA,CAAEC,KAAK,IAAIP,UAAU,4DACpB,GAAAH,EAAAC,GAAA,EAACU,EAAAA,CAAOA,CAAAA,CAACR,UAAU,cAErB,GAAAH,EAAAC,GAAA,EAACQ,IAAAA,CAAEC,KAAK,IAAIP,UAAU,4DACpB,GAAAH,EAAAC,GAAA,EAACW,EAAAA,CAAMA,CAAAA,CAACT,UAAU,cAEpB,GAAAH,EAAAC,GAAA,EAACQ,IAAAA,CAAEC,KAAK,IAAIP,UAAU,4DACpB,GAAAH,EAAAC,GAAA,EAACY,EAAAA,CAAQA,CAAAA,CAACV,UAAU,oBAK1B,GAAAH,EAAAI,IAAA,EAACC,MAAAA,WACC,GAAAL,EAAAC,GAAA,EAACa,KAAAA,CAAGX,UAAU,yCAAgC,YAC9C,GAAAH,EAAAI,IAAA,EAACW,KAAAA,CAAGZ,UAAU,sBACZ,GAAAH,EAAAC,GAAA,EAACe,KAAAA,UAAG,GAAAhB,EAAAC,GAAA,EAACgB,EAAAA,OAAIA,CAAAA,CAACP,KAAK,YAAYP,UAAU,oEAA2D,eAChG,GAAAH,EAAAC,GAAA,EAACe,KAAAA,UAAG,GAAAhB,EAAAC,GAAA,EAACgB,EAAAA,OAAIA,CAAAA,CAACP,KAAK,WAAWP,UAAU,oEAA2D,cAC/F,GAAAH,EAAAC,GAAA,EAACe,KAAAA,UAAG,GAAAhB,EAAAC,GAAA,EAACgB,EAAAA,OAAIA,CAAAA,CAACP,KAAK,cAAcP,UAAU,oEAA2D,iBAClG,GAAAH,EAAAC,GAAA,EAACe,KAAAA,UAAG,GAAAhB,EAAAC,GAAA,EAACgB,EAAAA,OAAIA,CAAAA,CAACP,KAAK,YAAYP,UAAU,oEAA2D,qBAIpG,GAAAH,EAAAI,IAAA,EAACC,MAAAA,WACC,GAAAL,EAAAC,GAAA,EAACa,KAAAA,CAAGX,UAAU,yCAAgC,cAC9C,GAAAH,EAAAI,IAAA,EAACW,KAAAA,CAAGZ,UAAU,sBACZ,GAAAH,EAAAC,GAAA,EAACe,KAAAA,UAAG,GAAAhB,EAAAC,GAAA,EAACgB,EAAAA,OAAIA,CAAAA,CAACP,KAAK,QAAQP,UAAU,oEAA2D,WAC5F,GAAAH,EAAAC,GAAA,EAACe,KAAAA,UAAG,GAAAhB,EAAAC,GAAA,EAACgB,EAAAA,OAAIA,CAAAA,CAACP,KAAK,eAAeP,UAAU,oEAA2D,kBACnG,GAAAH,EAAAC,GAAA,EAACe,KAAAA,UAAG,GAAAhB,EAAAC,GAAA,EAACgB,EAAAA,OAAIA,CAAAA,CAACP,KAAK,aAAaP,UAAU,oEAA2D,gBACjG,GAAAH,EAAAC,GAAA,EAACe,KAAAA,UAAG,GAAAhB,EAAAC,GAAA,EAACgB,EAAAA,OAAIA,CAAAA,CAACP,KAAK,YAAYP,UAAU,oEAA2D,qBAIpG,GAAAH,EAAAI,IAAA,EAACC,MAAAA,WACC,GAAAL,EAAAC,GAAA,EAACa,KAAAA,CAAGX,UAAU,yCAAgC,YAC9C,GAAAH,EAAAI,IAAA,EAACW,KAAAA,CAAGZ,UAAU,sBACZ,GAAAH,EAAAC,GAAA,EAACe,KAAAA,UAAG,GAAAhB,EAAAC,GAAA,EAACgB,EAAAA,OAAIA,CAAAA,CAACP,KAAK,SAASP,UAAU,oEAA2D,eAC7F,GAAAH,EAAAC,GAAA,EAACe,KAAAA,UAAG,GAAAhB,EAAAC,GAAA,EAACgB,EAAAA,OAAIA,CAAAA,CAACP,KAAK,WAAWP,UAAU,oEAA2D,cAC/F,GAAAH,EAAAC,GAAA,EAACe,KAAAA,UAAG,GAAAhB,EAAAC,GAAA,EAACgB,EAAAA,OAAIA,CAAAA,CAACP,KAAK,WAAWP,UAAU,oEAA2D,iBAC/F,GAAAH,EAAAC,GAAA,EAACe,KAAAA,UAAG,GAAAhB,EAAAC,GAAA,EAACgB,EAAAA,OAAIA,CAAAA,CAACP,KAAK,WAAWP,UAAU,oEAA2D,8BAKrG,GAAAH,EAAAI,IAAA,EAACC,MAAAA,CAAIF,UAAU,uGACb,GAAAH,EAAAI,IAAA,EAACI,IAAAA,CAAEL,UAAU,kCAAwB,QAChC,IAAIe,OAAOC,WAAW,GAAG,yCAE9B,GAAAnB,EAAAI,IAAA,EAACC,MAAAA,CAAIF,UAAU,wCACb,GAAAH,EAAAC,GAAA,EAACgB,EAAAA,OAAIA,CAAAA,CAACP,KAAK,SAASP,UAAU,oEAA2D,qBAGzF,GAAAH,EAAAC,GAAA,EAACgB,EAAAA,OAAIA,CAAAA,CAACP,KAAK,WAAWP,UAAU,oEAA2D,mBAG3F,GAAAH,EAAAC,GAAA,EAACgB,EAAAA,OAAIA,CAAAA,CAACP,KAAK,WAAWP,UAAU,oEAA2D,sBAQvG,4GC9Ee,SAASiB,IACtB,MACE,GAAApB,EAAAC,GAAA,EAACoB,MAAAA,CAAIlB,UAAU,iGACb,GAAAH,EAAAC,GAAA,EAACI,MAAAA,CAAIF,UAAU,kDACb,GAAAH,EAAAI,IAAA,EAACC,MAAAA,CAAIF,UAAU,sCACb,GAAAH,EAAAC,GAAA,EAACI,MAAAA,CAAIF,UAAU,6BACb,GAAAH,EAAAI,IAAA,EAACa,EAAAA,OAAIA,CAAAA,CAACP,KAAK,IAAIP,UAAU,8BACvB,GAAAH,EAAAC,GAAA,EAACI,MAAAA,CAAIF,UAAU,qHACb,GAAAH,EAAAC,GAAA,EAACK,EAAAA,CAAKA,CAAAA,CAACH,UAAU,yBAEnB,GAAAH,EAAAC,GAAA,EAACM,OAAAA,CAAKJ,UAAU,wCAA+B,sBAGnD,GAAAH,EAAAI,IAAA,EAACC,MAAAA,CAAIF,UAAU,kDACb,GAAAH,EAAAC,GAAA,EAACQ,IAAAA,CACCC,KAAK,YACLP,UAAU,kEACVmB,QAAS,QAEPC,EADAC,EAAEC,cAAc,GACQ,OAAxBF,CAAAA,EAAAA,SAASG,cAAc,CAAC,cAAxBH,KAAAA,IAAAA,GAAAA,EAAqCI,cAAc,CAAC,CAAEC,SAAU,QAAS,EAC3E,WACD,aAGD,GAAA5B,EAAAC,GAAA,EAACQ,IAAAA,CACCC,KAAK,WACLP,UAAU,kEACVmB,QAAS,QAEPC,EADAC,EAAEC,cAAc,GACQ,OAAxBF,CAAAA,EAAAA,SAASG,cAAc,CAAC,aAAxBH,KAAAA,IAAAA,GAAAA,EAAoCI,cAAc,CAAC,CAAEC,SAAU,QAAS,EAC1E,WACD,YAGD,GAAA5B,EAAAC,GAAA,EAACgB,EAAAA,OAAIA,CAAAA,CAACP,KAAK,SAASP,UAAU,4DAAmD,UAGjF,GAAAH,EAAAC,GAAA,EAACgB,EAAAA,OAAIA,CAAAA,CAACP,KAAK,WAAWP,UAAU,4DAAmD,eAIrF,GAAAH,EAAAI,IAAA,EAACC,MAAAA,CAAIF,UAAU,8BACb,GAAAH,EAAAC,GAAA,EAACgB,EAAAA,OAAIA,CAAAA,CACHP,KAAK,SACLP,UAAU,mGACX,WAGD,GAAAH,EAAAC,GAAA,EAACgB,EAAAA,OAAIA,CAAAA,CACHP,KAAK,UACLP,UAAU,qJACX,0BAQb,iJC7DO,SAAS0B,IAAG,QAAAC,EAAAC,UAAAC,MAAA,CAAAC,EAAA,MAAAH,GAAAI,EAAA,EAAAA,EAAAJ,EAAAI,IAAGD,CAAAA,CAAHC,EAAA,CAAAH,SAAA,CAAAG,EAAuB,CACxC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,EAAQC,CAAAA,EAAAA,EAAAA,CAAAA,EAAKH,GACtB,CCEA,IAAMI,EAAgBC,EAAAA,EAAwB,CAExCC,EAAgBC,EAAAA,UAAgB,CAGpC,CAAAC,EAA0BC,QAAzB,CAAEvC,UAAAA,CAAS,CAAE,GAAGwC,EAAO,CAAAF,QACxB,GAAAG,EAAA3C,GAAA,EAACqC,EAAAA,EAAwB,EACvBI,IAAKA,EACLvC,UAAW0B,EACT,oIACA1B,GAED,GAAGwC,CAAK,IAGbJ,CAAAA,EAAcM,WAAW,CAAGP,EAAAA,EAAwB,CAACO,WAAW,CAEhE,IAAMC,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,EACpB,4lBACA,CACEC,SAAU,CACRC,QAAS,CACPC,QAAS,uCACTC,YACE,2EACJ,CACF,EACAC,gBAAiB,CACfH,QAAS,SACX,CACF,GAGII,EAAQb,EAAAA,UAAgB,CAI5B,CAAAC,EAAmCC,QAAlC,CAAEvC,UAAAA,CAAS,CAAE8C,QAAAA,CAAO,CAAE,GAAGN,EAAO,CAAAF,EACjC,MACE,GAAAG,EAAA3C,GAAA,EAACqC,EAAAA,EAAoB,EACnBI,IAAKA,EACLvC,UAAW0B,EAAGiB,EAAc,CAAEG,QAAAA,CAAQ,GAAI9C,GACzC,GAAGwC,CAAK,EAGf,EACAU,CAAAA,EAAMR,WAAW,CAAGP,EAAAA,EAAoB,CAACO,WAAW,CAepDS,EAboBd,UAAgB,CAGlC,CAAAC,EAA0BC,QAAzB,CAAEvC,UAAAA,CAAS,CAAE,GAAGwC,EAAO,CAAAF,QACxB,GAAAG,EAAA3C,GAAA,EAACqC,EAAAA,EAAsB,EACrBI,IAAKA,EACLvC,UAAW0B,EACT,qgBACA1B,GAED,GAAGwC,CAAK,KAGDE,WAAW,CAAGP,EAAAA,EAAsB,CAACO,WAAW,CAE5D,IAAMU,EAAaf,EAAAA,UAAgB,CAGjC,CAAAC,EAA0BC,QAAzB,CAAEvC,UAAAA,CAAS,CAAE,GAAGwC,EAAO,CAAAF,QACxB,GAAAG,EAAA3C,GAAA,EAACqC,EAAAA,EAAqB,EACpBI,IAAKA,EACLvC,UAAW0B,EACT,wVACA1B,GAEFqD,cAAY,GACX,GAAGb,CAAK,UAET,GAAAC,EAAA3C,GAAA,EAACwD,EAAAA,CAACA,CAAAA,CAACtD,UAAU,eAGjBoD,CAAAA,EAAWV,WAAW,CAAGP,EAAAA,EAAqB,CAACO,WAAW,CAE1D,IAAMa,EAAalB,EAAAA,UAAgB,CAGjC,CAAAC,EAA0BC,QAAzB,CAAEvC,UAAAA,CAAS,CAAE,GAAGwC,EAAO,CAAAF,QACxB,GAAAG,EAAA3C,GAAA,EAACqC,EAAAA,EAAqB,EACpBI,IAAKA,EACLvC,UAAW0B,EAAG,wBAAyB1B,GACtC,GAAGwC,CAAK,IAGbe,CAAAA,EAAWb,WAAW,CAAGP,EAAAA,EAAqB,CAACO,WAAW,CAE1D,IAAMc,EAAmBnB,EAAAA,UAAgB,CAGvC,CAAAC,EAA0BC,QAAzB,CAAEvC,UAAAA,CAAS,CAAE,GAAGwC,EAAO,CAAAF,QACxB,GAAAG,EAAA3C,GAAA,EAACqC,EAAAA,EAA2B,EAC1BI,IAAKA,EACLvC,UAAW0B,EAAG,qBAAsB1B,GACnC,GAAGwC,CAAK,IAGbgB,CAAAA,EAAiBd,WAAW,CAAGP,EAAAA,EAA2B,CAACO,WAAW,CCtFtE,IAAIe,EAAQ,EA+BNC,EAAgB,IAAIC,IAEpBC,EAAmB,IACvB,GAAIF,EAAcG,GAAG,CAACC,GACpB,OAGF,IAAMC,EAAUC,WAAW,KACzBN,EAAcO,MAAM,CAACH,GACrBI,EAAS,CACPC,KAAM,eACNL,QAASA,CACX,EACF,EA5DyB,KA8DzBJ,EAAcU,GAAG,CAACN,EAASC,EAC7B,EAEaM,EAAU,CAACC,EAAcC,KACpC,OAAQA,EAAOJ,IAAI,EACjB,IAAK,YACH,MAAO,CACL,GAAGG,CAAK,CACRE,OAAQ,CAACD,EAAOE,KAAK,IAAKH,EAAME,MAAM,CAAC,CAACE,KAAK,CAAC,EAvElC,EAwEd,CAEF,KAAK,eACH,MAAO,CACL,GAAGJ,CAAK,CACRE,OAAQF,EAAME,MAAM,CAACG,GAAG,CAAC,GACvBjF,EAAEkF,EAAE,GAAKL,EAAOE,KAAK,CAACG,EAAE,CAAG,CAAE,GAAGlF,CAAC,CAAE,GAAG6E,EAAOE,KAAK,EAAK/E,EAE3D,CAEF,KAAK,gBAAiB,CACpB,GAAM,CAAEoE,QAAAA,CAAO,CAAE,CAAGS,EAUpB,OARIT,EACFF,EAAiBE,GAEjBQ,EAAME,MAAM,CAACK,OAAO,CAAC,IACnBjB,EAAiBa,EAAMG,EAAE,CAC3B,GAGK,CACL,GAAGN,CAAK,CACRE,OAAQF,EAAME,MAAM,CAACG,GAAG,CAAC,GACvBjF,EAAEkF,EAAE,GAAKd,GAAWA,KAAYgB,IAAZhB,EAChB,CACE,GAAGpE,CAAC,CACJqF,KAAM,EACR,EACArF,EAER,CACF,CACA,IAAK,eACH,GAAI6E,KAAmBO,IAAnBP,EAAOT,OAAO,CAChB,MAAO,CACL,GAAGQ,CAAK,CACRE,OAAQ,EAAE,EAGd,MAAO,CACL,GAAGF,CAAK,CACRE,OAAQF,EAAME,MAAM,CAACQ,MAAM,CAAC,GAAOtF,EAAEkF,EAAE,GAAKL,EAAOT,OAAO,CAC5D,CACJ,CACF,EAEMmB,EAA2C,EAAE,CAE/CC,EAAqB,CAAEV,OAAQ,EAAE,EAErC,SAASN,EAASK,CAAc,EAC9BW,EAAcb,EAAQa,EAAaX,GACnCU,EAAUJ,OAAO,CAAC,IAChBM,EAASD,EACX,EACF,CAIA,SAAST,EAAMnC,CAAmB,KAAnB,CAAE,GAAGE,EAAc,CAAnBF,EACPsC,EAhHCnB,CADPA,EAAQ,CAACA,EAAQ,GAAK2B,OAAOC,gBAAgB,EAChCC,QAAQ,GAuHfC,EAAU,IAAMrB,EAAS,CAAEC,KAAM,gBAAiBL,QAASc,CAAG,GAcpE,OAZAV,EAAS,CACPC,KAAM,YACNM,MAAO,CACL,GAAGjC,CAAK,CACRoC,GAAAA,EACAG,KAAM,GACNS,aAAc,IACPT,GAAMQ,GACb,CACF,CACF,GAEO,CACLX,GAAIA,EACJW,QAAAA,EACAE,OAtBa,GACbvB,EAAS,CACPC,KAAM,eACNM,MAAO,CAAE,GAAGjC,CAAK,CAAEoC,GAAAA,CAAG,CACxB,EAmBF,CACF,CC1JO,SAASc,IACd,GAAM,CAAElB,OAAAA,CAAM,CAAE,CAAGmB,WD4JnB,GAAM,CAACrB,EAAOsB,EAAS,CAAGvD,EAAAA,QAAc,CAAQ6C,GAYhD,OAVA7C,EAAAA,SAAe,CAAC,KACd4C,EAAUY,IAAI,CAACD,GACR,KACL,IAAME,EAAQb,EAAUc,OAAO,CAACH,GAC5BE,EAAQ,IACVb,EAAUe,MAAM,CAACF,EAAO,EAE5B,GACC,CAACxB,EAAM,EAEH,CACL,GAAGA,CAAK,CACRG,MAAAA,EACAc,QAAS,GAAsBrB,EAAS,CAAEC,KAAM,gBAAiBL,QAAAA,CAAQ,EAC3E,CACF,IC3KE,MACE,GAAArB,EAAAxC,IAAA,EAACiC,EAAaA,WACXsC,EAAOG,GAAG,CAAC,SAAUrC,CAA4C,KAA5C,CAAEsC,GAAAA,CAAE,CAAEqB,MAAAA,CAAK,CAAEC,YAAAA,CAAW,CAAE3B,OAAAA,CAAM,CAAE,GAAG/B,EAAO,CAA5CF,EACpB,MACE,GAAAG,EAAAxC,IAAA,EAACiD,EAAKA,CAAW,GAAGV,CAAK,WACvB,GAAAC,EAAAxC,IAAA,EAACC,MAAAA,CAAIF,UAAU,uBACZiG,GAAS,GAAAxD,EAAA3C,GAAA,EAACyD,EAAUA,UAAE0C,IACtBC,GACC,GAAAzD,EAAA3C,GAAA,EAAC0D,EAAgBA,UAAE0C,OAGtB3B,EACD,GAAA9B,EAAA3C,GAAA,EAACsD,EAAUA,CAAAA,KARDwB,EAWhB,GACA,GAAAnC,EAAA3C,GAAA,EAACsC,EAAaA,CAAAA,KAGpB", "sources": ["webpack://_N_E/?d89e", "webpack://_N_E/./components/Footer.tsx", "webpack://_N_E/./components/Navbar.tsx", "webpack://_N_E/./lib/utils.ts", "webpack://_N_E/./components/ui/toast.tsx", "webpack://_N_E/./components/ui/use-toast.ts", "webpack://_N_E/./components/ui/toaster.tsx", "webpack://_N_E/./app/globals.css"], "sourcesContent": ["import(/* webpackMode: \"eager\" */ \"/Volumes/Apps/Websites/streamit-main/app/globals.css\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"/Volumes/Apps/Websites/streamit-main/components/Navbar.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"Toaster\"] */ \"/Volumes/Apps/Websites/streamit-main/components/ui/toaster.tsx\");\n;\nimport(/* webpackMode: \"eager\" */ \"/Volumes/Apps/Websites/streamit-main/node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n", "'use client';\n\nimport Link from 'next/link';\nimport { Video, Twitter, Github, Linkedin, Mail } from 'lucide-react';\n\nexport default function Footer() {\n  return (\n    <footer className=\"bg-black/50 backdrop-blur-md border-t border-white/10 mt-20\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          <div>\n            <div className=\"flex items-center mb-4\">\n              <div className=\"w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mr-3\">\n                <Video className=\"h-5 w-5 text-white\" />\n              </div>\n              <span className=\"text-xl font-bold text-white\">StreamIt Pro</span>\n            </div>\n            <p className=\"text-white/60 text-sm mt-2\">\n              Professional video conferencing solution for teams of all sizes.\n            </p>\n            <div className=\"flex space-x-4 mt-4\">\n              <a href=\"#\" className=\"text-white/60 hover:text-white transition-colors\">\n                <Twitter className=\"h-5 w-5\" />\n              </a>\n              <a href=\"#\" className=\"text-white/60 hover:text-white transition-colors\">\n                <Github className=\"h-5 w-5\" />\n              </a>\n              <a href=\"#\" className=\"text-white/60 hover:text-white transition-colors\">\n                <Linkedin className=\"h-5 w-5\" />\n              </a>\n            </div>\n          </div>\n          \n          <div>\n            <h3 className=\"text-white font-semibold mb-4\">Product</h3>\n            <ul className=\"space-y-2\">\n              <li><Link href=\"/features\" className=\"text-white/60 hover:text-white transition-colors text-sm\">Features</Link></li>\n              <li><Link href=\"/pricing\" className=\"text-white/60 hover:text-white transition-colors text-sm\">Pricing</Link></li>\n              <li><Link href=\"/enterprise\" className=\"text-white/60 hover:text-white transition-colors text-sm\">Enterprise</Link></li>\n              <li><Link href=\"/security\" className=\"text-white/60 hover:text-white transition-colors text-sm\">Security</Link></li>\n            </ul>\n          </div>\n          \n          <div>\n            <h3 className=\"text-white font-semibold mb-4\">Resources</h3>\n            <ul className=\"space-y-2\">\n              <li><Link href=\"/blog\" className=\"text-white/60 hover:text-white transition-colors text-sm\">Blog</Link></li>\n              <li><Link href=\"/help-center\" className=\"text-white/60 hover:text-white transition-colors text-sm\">Help Center</Link></li>\n              <li><Link href=\"/tutorials\" className=\"text-white/60 hover:text-white transition-colors text-sm\">Tutorials</Link></li>\n              <li><Link href=\"/webinars\" className=\"text-white/60 hover:text-white transition-colors text-sm\">Webinars</Link></li>\n            </ul>\n          </div>\n          \n          <div>\n            <h3 className=\"text-white font-semibold mb-4\">Company</h3>\n            <ul className=\"space-y-2\">\n              <li><Link href=\"/about\" className=\"text-white/60 hover:text-white transition-colors text-sm\">About Us</Link></li>\n              <li><Link href=\"/careers\" className=\"text-white/60 hover:text-white transition-colors text-sm\">Careers</Link></li>\n              <li><Link href=\"/contact\" className=\"text-white/60 hover:text-white transition-colors text-sm\">Contact Us</Link></li>\n              <li><Link href=\"/privacy\" className=\"text-white/60 hover:text-white transition-colors text-sm\">Privacy Policy</Link></li>\n            </ul>\n          </div>\n        </div>\n        \n        <div className=\"border-t border-white/10 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center\">\n          <p className=\"text-white/50 text-sm\">\n            © {new Date().getFullYear()} StreamIt Pro. All rights reserved.\n          </p>\n          <div className=\"flex space-x-6 mt-4 md:mt-0\">\n            <Link href=\"/terms\" className=\"text-white/60 hover:text-white transition-colors text-sm\">\n              Terms of Service\n            </Link>\n            <Link href=\"/privacy\" className=\"text-white/60 hover:text-white transition-colors text-sm\">\n              Privacy Policy\n            </Link>\n            <Link href=\"/cookies\" className=\"text-white/60 hover:text-white transition-colors text-sm\">\n              Cookies\n            </Link>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n", "'use client';\n\nimport Link from 'next/link';\nimport { Video } from 'lucide-react';\n\nexport default function Navbar() {\n  return (\n    <nav className=\"fixed top-0 left-0 right-0 z-50 bg-black/50 backdrop-blur-md border-b border-white/10\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center\">\n              <div className=\"w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mr-3\">\n                <Video className=\"h-5 w-5 text-white\" />\n              </div>\n              <span className=\"text-xl font-bold text-white\">StreamIt Pro</span>\n            </Link>\n          </div>\n          <div className=\"hidden md:flex items-center space-x-8\">\n            <a\n              href=\"#features\"\n              className=\"text-white/80 hover:text-white transition-colors cursor-pointer\"\n              onClick={(e) => {\n                e.preventDefault();\n                document.getElementById('features')?.scrollIntoView({ behavior: 'smooth' });\n              }}\n            >\n              Features\n            </a>\n            <a\n              href=\"#pricing\"\n              className=\"text-white/80 hover:text-white transition-colors cursor-pointer\"\n              onClick={(e) => {\n                e.preventDefault();\n                document.getElementById('pricing')?.scrollIntoView({ behavior: 'smooth' });\n              }}\n            >\n              Pricing\n            </a>\n            <Link href=\"/about\" className=\"text-white/80 hover:text-white transition-colors\">\n              About\n            </Link>\n            <Link href=\"/contact\" className=\"text-white/80 hover:text-white transition-colors\">\n              Contact\n            </Link>\n          </div>\n          <div className=\"flex items-center\">\n            <Link \n              href=\"/login\" \n              className=\"px-4 py-2 rounded-md text-sm font-medium text-white hover:bg-white/10 transition-colors\"\n            >\n              Log in\n            </Link>\n            <Link \n              href=\"/signup\" \n              className=\"ml-4 px-4 py-2 rounded-md text-sm font-medium text-white bg-gradient-to-r from-purple-600 to-pink-600 hover:opacity-90 transition-opacity\"\n            >\n              Sign up free\n            </Link>\n          </div>\n        </div>\n      </div>\n    </nav>\n  );\n}\n", "import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n", "import * as React from \"react\"\nimport * as ToastPrimitives from \"@radix-ui/react-toast\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst ToastProvider = ToastPrimitives.Provider\n\nconst ToastViewport = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Viewport>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Viewport>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Viewport\n    ref={ref}\n    className={cn(\n      \"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]\",\n      className\n    )}\n    {...props}\n  />\n))\nToastViewport.displayName = ToastPrimitives.Viewport.displayName\n\nconst toastVariants = cva(\n  \"group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full\",\n  {\n    variants: {\n      variant: {\n        default: \"border bg-background text-foreground\",\n        destructive:\n          \"destructive border-destructive bg-destructive text-destructive-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nconst Toast = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Root>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Root> &\n    VariantProps<typeof toastVariants>\n>(({ className, variant, ...props }, ref) => {\n  return (\n    <ToastPrimitives.Root\n      ref={ref}\n      className={cn(toastVariants({ variant }), className)}\n      {...props}\n    />\n  )\n})\nToast.displayName = ToastPrimitives.Root.displayName\n\nconst ToastAction = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Action>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Action>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Action\n    ref={ref}\n    className={cn(\n      \"inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive\",\n      className\n    )}\n    {...props}\n  />\n))\nToastAction.displayName = ToastPrimitives.Action.displayName\n\nconst ToastClose = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Close>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Close>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Close\n    ref={ref}\n    className={cn(\n      \"absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600\",\n      className\n    )}\n    toast-close=\"\"\n    {...props}\n  >\n    <X className=\"h-4 w-4\" />\n  </ToastPrimitives.Close>\n))\nToastClose.displayName = ToastPrimitives.Close.displayName\n\nconst ToastTitle = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Title>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Title>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Title\n    ref={ref}\n    className={cn(\"text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nToastTitle.displayName = ToastPrimitives.Title.displayName\n\nconst ToastDescription = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Description>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Description>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Description\n    ref={ref}\n    className={cn(\"text-sm opacity-90\", className)}\n    {...props}\n  />\n))\nToastDescription.displayName = ToastPrimitives.Description.displayName\n\ntype ToastProps = React.ComponentPropsWithoutRef<typeof Toast>\n\ntype ToastActionElement = React.ReactElement<typeof ToastAction>\n\nexport {\n  type ToastProps,\n  type ToastActionElement,\n  ToastProvider,\n  ToastViewport,\n  Toast,\n  ToastTitle,\n  ToastDescription,\n  ToastClose,\n  ToastAction,\n}\n", "import * as React from \"react\"\n\nimport type {\n  ToastActionElement,\n  ToastProps,\n} from \"@/components/ui/toast\"\n\nconst TOAST_LIMIT = 1\nconst TOAST_REMOVE_DELAY = 1000000\n\ntype ToasterToast = ToastProps & {\n  id: string\n  title?: React.ReactNode\n  description?: React.ReactNode\n  action?: ToastActionElement\n}\n\nconst actionTypes = {\n  ADD_TOAST: \"ADD_TOAST\",\n  UPDATE_TOAST: \"UPDATE_TOAST\",\n  DISMISS_TOAST: \"DISMISS_TOAST\",\n  REMOVE_TOAST: \"REMOVE_TOAST\",\n} as const\n\nlet count = 0\n\nfunction genId() {\n  count = (count + 1) % Number.MAX_SAFE_INTEGER\n  return count.toString()\n}\n\ntype ActionType = typeof actionTypes\n\ntype Action =\n  | {\n      type: ActionType[\"ADD_TOAST\"]\n      toast: ToasterToast\n    }\n  | {\n      type: ActionType[\"UPDATE_TOAST\"]\n      toast: Partial<ToasterToast>\n    }\n  | {\n      type: ActionType[\"DISMISS_TOAST\"]\n      toastId?: ToasterToast[\"id\"]\n    }\n  | {\n      type: ActionType[\"REMOVE_TOAST\"]\n      toastId?: ToasterToast[\"id\"]\n    }\n\ninterface State {\n  toasts: ToasterToast[]\n}\n\nconst toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>()\n\nconst addToRemoveQueue = (toastId: string) => {\n  if (toastTimeouts.has(toastId)) {\n    return\n  }\n\n  const timeout = setTimeout(() => {\n    toastTimeouts.delete(toastId)\n    dispatch({\n      type: \"REMOVE_TOAST\",\n      toastId: toastId,\n    })\n  }, TOAST_REMOVE_DELAY)\n\n  toastTimeouts.set(toastId, timeout)\n}\n\nexport const reducer = (state: State, action: Action): State => {\n  switch (action.type) {\n    case \"ADD_TOAST\":\n      return {\n        ...state,\n        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),\n      }\n\n    case \"UPDATE_TOAST\":\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === action.toast.id ? { ...t, ...action.toast } : t\n        ),\n      }\n\n    case \"DISMISS_TOAST\": {\n      const { toastId } = action\n\n      if (toastId) {\n        addToRemoveQueue(toastId)\n      } else {\n        state.toasts.forEach((toast) => {\n          addToRemoveQueue(toast.id)\n        })\n      }\n\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === toastId || toastId === undefined\n            ? {\n                ...t,\n                open: false,\n              }\n            : t\n        ),\n      }\n    }\n    case \"REMOVE_TOAST\":\n      if (action.toastId === undefined) {\n        return {\n          ...state,\n          toasts: [],\n        }\n      }\n      return {\n        ...state,\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\n      }\n  }\n}\n\nconst listeners: Array<(state: State) => void> = []\n\nlet memoryState: State = { toasts: [] }\n\nfunction dispatch(action: Action) {\n  memoryState = reducer(memoryState, action)\n  listeners.forEach((listener) => {\n    listener(memoryState)\n  })\n}\n\ntype Toast = Omit<ToasterToast, \"id\">\n\nfunction toast({ ...props }: Toast) {\n  const id = genId()\n\n  const update = (props: ToasterToast) =>\n    dispatch({\n      type: \"UPDATE_TOAST\",\n      toast: { ...props, id },\n    })\n  const dismiss = () => dispatch({ type: \"DISMISS_TOAST\", toastId: id })\n\n  dispatch({\n    type: \"ADD_TOAST\",\n    toast: {\n      ...props,\n      id,\n      open: true,\n      onOpenChange: (open) => {\n        if (!open) dismiss()\n      },\n    },\n  })\n\n  return {\n    id: id,\n    dismiss,\n    update,\n  }\n}\n\nfunction useToast() {\n  const [state, setState] = React.useState<State>(memoryState)\n\n  React.useEffect(() => {\n    listeners.push(setState)\n    return () => {\n      const index = listeners.indexOf(setState)\n      if (index > -1) {\n        listeners.splice(index, 1)\n      }\n    }\n  }, [state])\n\n  return {\n    ...state,\n    toast,\n    dismiss: (toastId?: string) => dispatch({ type: \"DISMISS_TOAST\", toastId }),\n  }\n}\n\nexport { useToast, toast }\n", "\"use client\"\n\nimport {\n  Toast,\n  ToastClose,\n  ToastDescription,\n  ToastProvider,\n  ToastTitle,\n  ToastViewport,\n} from \"@/components/ui/toast\"\nimport { useToast } from \"@/components/ui/use-toast\"\n\nexport function Toaster() {\n  const { toasts } = useToast()\n\n  return (\n    <ToastProvider>\n      {toasts.map(function ({ id, title, description, action, ...props }) {\n        return (\n          <Toast key={id} {...props}>\n            <div className=\"grid gap-1\">\n              {title && <ToastTitle>{title}</ToastTitle>}\n              {description && (\n                <ToastDescription>{description}</ToastDescription>\n              )}\n            </div>\n            {action}\n            <ToastClose />\n          </Toast>\n        )\n      })}\n      <ToastViewport />\n    </ToastProvider>\n  )\n}\n", "// extracted by mini-css-extract-plugin"], "names": ["Promise", "resolve", "then", "__webpack_require__", "t", "bind", "Footer", "react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__", "jsx", "footer", "className", "jsxs", "div", "Video", "span", "p", "a", "href", "Twitter", "<PERSON><PERSON><PERSON>", "Linkedin", "h3", "ul", "li", "Link", "Date", "getFullYear", "<PERSON><PERSON><PERSON>", "nav", "onClick", "document", "e", "preventDefault", "getElementById", "scrollIntoView", "behavior", "cn", "_len", "arguments", "length", "inputs", "_key", "twMerge", "clsx", "ToastProvider", "ToastPrimitives", "ToastViewport", "React", "param", "ref", "props", "jsx_runtime", "displayName", "toastVariants", "cva", "variants", "variant", "default", "destructive", "defaultVariants", "Toast", "ToastAction", "ToastClose", "toast-close", "X", "ToastTitle", "ToastDescription", "count", "toastTimeouts", "Map", "addToRemoveQueue", "has", "toastId", "timeout", "setTimeout", "delete", "dispatch", "type", "set", "reducer", "state", "action", "toasts", "toast", "slice", "map", "id", "for<PERSON>ach", "undefined", "open", "filter", "listeners", "memoryState", "listener", "Number", "MAX_SAFE_INTEGER", "toString", "dismiss", "onOpenChange", "update", "Toaster", "useToast", "setState", "push", "index", "indexOf", "splice", "title", "description"], "sourceRoot": ""}