{"version": 3, "file": "static/chunks/app/pricing/page-5f3b063daeba635b.js", "mappings": "oFAAAA,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA,gKCMO,IAAME,EAAU,gBAER,SAASC,IACtB,GAAM,CAACC,EAAUC,EAAY,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IAiEzC,MACE,GAAAC,EAAAC,IAAA,EAAAD,EAAAE,QAAA,YAEE,GAAAF,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,gBAEf,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,6CAEb,GAAAL,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,0CACb,GAAAL,EAAAG,GAAA,EAACI,KAAAA,CAAGF,UAAU,sDAA6C,gCAG3D,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,iFAAwE,oGAKrF,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,yDACb,GAAAL,EAAAG,GAAA,EAACM,OAAAA,CAAKJ,UAAW,iBAA6DK,MAAA,CAA5C,EAAyC,GAA7B,qCAAmC,YAGjF,GAAAV,EAAAG,GAAA,EAACQ,SAAAA,CACCC,QAAS,IAAMd,EAAY,CAACD,GAC5BQ,UAAW,oDAEVK,MAAA,CADCb,EAAW,gBAAkB,wBAG/B,GAAAG,EAAAG,GAAA,EAACC,MAAAA,CACCC,UAAW,qEAEVK,MAAA,CADCb,EAAW,gBAAkB,qBAInC,GAAAG,EAAAG,GAAA,EAACM,OAAAA,CAAKJ,UAAW,iBAA4DK,MAAA,CAA3Cb,EAAW,2BAA6B,aAAM,WAG/EA,GACC,GAAAG,EAAAG,GAAA,EAACM,OAAAA,CAAKJ,UAAU,kEAAyD,qBASjF,GAAAL,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,6BACb,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,qCACZQ,CA/GX,CACEC,KAAM,QACNC,YAAa,2CACbC,aAAc,EACdC,YAAa,EACbC,SAAU,CACR,CAAEJ,KAAM,uBAAwBK,SAAU,EAAK,EAC/C,CAAEL,KAAM,qBAAsBK,SAAU,EAAK,EAC7C,CAAEL,KAAM,mBAAoBK,SAAU,EAAK,EAC3C,CAAEL,KAAM,sBAAuBK,SAAU,EAAK,EAC9C,CAAEL,KAAM,iBAAkBK,SAAU,EAAK,EACzC,CAAEL,KAAM,cAAeK,SAAU,EAAK,EACtC,CAAEL,KAAM,qBAAsBK,SAAU,EAAM,EAC9C,CAAEL,KAAM,oBAAqBK,SAAU,EAAM,EAC7C,CAAEL,KAAM,mBAAoBK,SAAU,EAAM,EAC5C,CAAEL,KAAM,kBAAmBK,SAAU,EAAM,EAC5C,CACDC,IAAK,cACLC,QAAS,EACX,EACA,CACEP,KAAM,MACNC,YAAa,yCACbC,aAAc,GACdC,YAAa,IACbC,SAAU,CACR,CAAEJ,KAAM,wBAAyBK,SAAU,EAAK,EAChD,CAAEL,KAAM,yBAA0BK,SAAU,EAAK,EACjD,CAAEL,KAAM,mBAAoBK,SAAU,EAAK,EAC3C,CAAEL,KAAM,+BAAgCK,SAAU,EAAK,EACvD,CAAEL,KAAM,iBAAkBK,SAAU,EAAK,EACzC,CAAEL,KAAM,cAAeK,SAAU,EAAK,EACtC,CAAEL,KAAM,qBAAsBK,SAAU,EAAK,EAC7C,CAAEL,KAAM,oBAAqBK,SAAU,EAAK,EAC5C,CAAEL,KAAM,gBAAiBK,SAAU,EAAK,EACxC,CAAEL,KAAM,kBAAmBK,SAAU,EAAM,EAC5C,CACDC,IAAK,mBACLC,QAAS,EACX,EACA,CACEP,KAAM,aACNC,YAAa,8CACbC,aAAc,GACdC,YAAa,IACbC,SAAU,CACR,CAAEJ,KAAM,yBAA0BK,SAAU,EAAK,EACjD,CAAEL,KAAM,uBAAwBK,SAAU,EAAK,EAC/C,CAAEL,KAAM,mBAAoBK,SAAU,EAAK,EAC3C,CAAEL,KAAM,+BAAgCK,SAAU,EAAK,EACvD,CAAEL,KAAM,iBAAkBK,SAAU,EAAK,EACzC,CAAEL,KAAM,cAAeK,SAAU,EAAK,EACtC,CAAEL,KAAM,qBAAsBK,SAAU,EAAK,EAC7C,CAAEL,KAAM,sBAAuBK,SAAU,EAAK,EAC9C,CAAEL,KAAM,mBAAoBK,SAAU,EAAK,EAC3C,CAAEL,KAAM,kBAAmBK,SAAU,EAAK,EAC3C,CACDC,IAAK,gBACLC,QAAS,EACX,EACD,CAmDkBC,GAAG,CAAC,CAACC,EAAMC,IAChB,GAAAxB,EAAAC,IAAA,EAACG,MAAAA,CAECC,UAAW,kCAEVK,MAAA,CADCa,EAAKF,OAAO,CAAG,6BAA+B,cAG/CE,EAAKF,OAAO,EACX,GAAArB,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,oKAA2J,iBAK5K,GAAAL,EAAAG,GAAA,EAACsB,KAAAA,CAAGpB,UAAU,8CAAsCkB,EAAKT,IAAI,GAC7D,GAAAd,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,sCAA8BkB,EAAKR,WAAW,GAE3D,GAAAf,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,iBACb,GAAAL,EAAAC,IAAA,EAACQ,OAAAA,CAAKJ,UAAU,0CAAgC,IAC5CR,EAAW0B,EAAKN,WAAW,CAAGM,EAAKP,YAAY,IAEnD,GAAAhB,EAAAC,IAAA,EAACQ,OAAAA,CAAKJ,UAAU,0BAAgB,IAC5BR,EAAW,OAAS,cAI1B,GAAAG,EAAAG,GAAA,EAACuB,KAAAA,CAAGrB,UAAU,oCACXkB,EAAKL,QAAQ,CAACI,GAAG,CAAC,CAACK,EAASC,IAC3B,GAAA5B,EAAAC,IAAA,EAAC4B,KAAAA,CAAsBxB,UAAU,oCAC9BsB,EAAQR,QAAQ,CACf,GAAAnB,EAAAG,GAAA,EAAC2B,EAAAA,CAAKA,CAAAA,CAACzB,UAAU,yCAEjB,GAAAL,EAAAG,GAAA,EAAC4B,EAAAA,CAACA,CAAAA,CAAC1B,UAAU,uCAEf,GAAAL,EAAAG,GAAA,EAACM,OAAAA,CAAKJ,UAAW,WAEhBK,MAAA,CADCiB,EAAQR,QAAQ,CAAG,gBAAkB,0BAEpCQ,EAAQb,IAAI,KATRc,MAeb,GAAA5B,EAAAG,GAAA,EAACQ,SAAAA,CACCN,UAAW,UAEVK,MAAA,CADCa,EAAKF,OAAO,CAAG,cAAgB,0BAGhCE,EAAKH,GAAG,KA7CNG,EAAKT,IAAI,SAsDxB,GAAAd,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,8BACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,8BACb,GAAAL,EAAAG,GAAA,EAAC6B,KAAAA,CAAG3B,UAAU,8CAAqC,yBAGnD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,iCAAwB,gDAKvC,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,qCACb,GAAAL,EAAAC,IAAA,EAACgC,QAAAA,CAAM5B,UAAU,mBACf,GAAAL,EAAAG,GAAA,EAAC+B,QAAAA,UACC,GAAAlC,EAAAC,IAAA,EAACkC,KAAAA,CAAG9B,UAAU,qCACZ,GAAAL,EAAAG,GAAA,EAACiC,KAAAA,CAAG/B,UAAU,mDAA0C,aACxD,GAAAL,EAAAG,GAAA,EAACiC,KAAAA,CAAG/B,UAAU,qDAA4C,UAC1D,GAAAL,EAAAG,GAAA,EAACiC,KAAAA,CAAG/B,UAAU,qDAA4C,QAC1D,GAAAL,EAAAG,GAAA,EAACiC,KAAAA,CAAG/B,UAAU,qDAA4C,oBAG9D,GAAAL,EAAAG,GAAA,EAACkC,QAAAA,UACE,CACC,CAAEV,QAAS,eAAgBW,MAAO,IAAKC,IAAK,KAAMC,WAAY,KAAM,EACpE,CAAEb,QAAS,mBAAoBW,MAAO,SAAUC,IAAK,YAAaC,WAAY,WAAY,EAC1F,CAAEb,QAAS,gBAAiBW,MAAO,KAAMC,IAAK,KAAMC,WAAY,IAAK,EACrE,CAAEb,QAAS,YAAaW,MAAO,IAAKC,IAAK,IAAKC,WAAY,GAAI,EAC9D,CAAEb,QAAS,oBAAqBW,MAAO,IAAKC,IAAK,IAAKC,WAAY,GAAI,EACtE,CAAEb,QAAS,mBAAoBW,MAAO,IAAKC,IAAK,IAAKC,WAAY,GAAI,EACrE,CAAEb,QAAS,kBAAmBW,MAAO,IAAKC,IAAK,IAAKC,WAAY,GAAI,EACrE,CAAClB,GAAG,CAAC,CAACmB,EAAKjB,IACV,GAAAxB,EAAAC,IAAA,EAACkC,KAAAA,CAAe9B,UAAU,oCACxB,GAAAL,EAAAG,GAAA,EAACuC,KAAAA,CAAGrC,UAAU,8BAAsBoC,EAAId,OAAO,GAC/C,GAAA3B,EAAAG,GAAA,EAACuC,KAAAA,CAAGrC,UAAU,0CAAkCoC,EAAIH,KAAK,GACzD,GAAAtC,EAAAG,GAAA,EAACuC,KAAAA,CAAGrC,UAAU,0CAAkCoC,EAAIF,GAAG,GACvD,GAAAvC,EAAAG,GAAA,EAACuC,KAAAA,CAAGrC,UAAU,0CAAkCoC,EAAID,UAAU,KAJvDhB,gBAcrB,GAAAxB,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,8BACb,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,6BACb,GAAAL,EAAAG,GAAA,EAAC6B,KAAAA,CAAG3B,UAAU,8CAAqC,kBAKrD,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,qBACZ,CACC,CACEsC,SAAU,gCACVC,OAAQ,qIACV,EACA,CACED,SAAU,yBACVC,OAAQ,wIACV,EACA,CACED,SAAU,sCACVC,OAAQ,6HACV,EACA,CACED,SAAU,yCACVC,OAAQ,gJACV,EACD,CAACtB,GAAG,CAAC,CAACuB,EAAKrB,IACV,GAAAxB,EAAAC,IAAA,EAACG,MAAAA,CAAgBC,UAAU,sBACzB,GAAAL,EAAAG,GAAA,EAACsB,KAAAA,CAAGpB,UAAU,yCAAiCwC,EAAIF,QAAQ,GAC3D,GAAA3C,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,yBAAiBwC,EAAID,MAAM,KAFhCpB,WAUlB,GAAAxB,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,yCACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,uBACb,GAAAL,EAAAG,GAAA,EAAC6B,KAAAA,CAAG3B,UAAU,8CAAqC,0BAGnD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,sCAA6B,qFAG1C,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,2DACb,GAAAL,EAAAG,GAAA,EAACQ,SAAAA,CAAON,UAAU,iCAAwB,qBAC1C,GAAAL,EAAAG,GAAA,EAACQ,SAAAA,CAAON,UAAU,mCAA0B,iCAQ5D,mFCxReyC,EAAA,CACbC,MAAO,6BACPC,MAAO,GACPC,OAAQ,GACRC,QAAS,YACTC,KAAM,OACNC,OAAQ,eACRC,YAAa,EACbC,cAAe,QACfC,eAAgB,OAClB;;;;;GCmBa,IAAAC,EAAc,GACzBC,EACGC,OAAA,CAAQ,qBAAsB,SAC9BC,WAAY,GACZC,IAAK,GAEJC,EAAmB,CAACC,EAAkBC,KAC1C,IAAMC,EAAYC,CAAAA,EAAAA,EAAAA,UAAAA,EAChB,CAAAC,EAAiHC,QAAhH,CAAEC,MAAAA,EAAQ,eAAgBC,KAAAA,EAAO,EAAI,CAAAhB,YAAAA,EAAc,CAAG,CAAAiB,oBAAAA,CAAA,CAAqBjE,UAAAA,EAAY,GAAIkE,SAAAA,CAAa,IAAAC,EAAA,CAAAN,QACvGO,CAAAA,EAAAA,EAAAA,aAAAA,EACE,MACA,CACEN,IAAAA,EACA,GAAGrB,CAAA,CACHE,MAAOqB,EACPpB,OAAQoB,EACRjB,OAAQgB,EACRf,YAAaiB,EAAsBI,GAAAA,OAAOrB,GAAoBqB,OAAOL,GAAQhB,EAC7EhD,UAAW,CAAC,SAAoB,UAAyBK,MAAA,CAAzB8C,EAAYM,IAAazD,EAAW,CAAAsE,IAAA,CAAK,KACzE,GAAGH,CAAA,EAEL,IACKT,EAASzC,GAAA,CAAI,OAAC,CAACsD,EAAKC,EAAW,CAAAX,QAAAO,CAAAA,EAAAA,EAAAA,aAAAA,EAAcG,EAAKC,QACjDC,MAAMC,OAAA,CAAQR,GAAYA,EAAW,CAACA,EAAQ,CACpD,IAMC,OAFPP,EAAUgB,WAAA,CAAc,GAAGtE,MAAA,CAAAoD,GAEpBE,CACT;;;;;GC/CA,IAAMlC,EAAQ+B,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,QAAS,CAAC,CAAC,OAAQ,CAAEoB,EAAG,kBAAmBC,IAAK,QAAS,EAAE,CAAC;;;;;GCArF,IAAAnD,EAAI8B,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,IAAK,CAC9B,CAAC,OAAQ,CAAEoB,EAAG,aAAcC,IAAK,UAAU,CAC3C,CAAC,OAAQ,CAAED,EAAG,aAAcC,IAAK,UAAU,CAC5C", "sources": ["webpack://_N_E/?c896", "webpack://_N_E/./app/pricing/page.tsx", "webpack://_N_E/../../src/defaultAttributes.ts", "webpack://_N_E/../../src/createLucideIcon.ts", "webpack://_N_E/../../../src/icons/check.ts", "webpack://_N_E/../../../src/icons/x.ts"], "sourcesContent": ["import(/* webpackMode: \"eager\" */ \"/Volumes/Apps/Websites/streamit-main/app/pricing/page.tsx\");\n", "'use client'\n\nimport { useState } from 'react'\nimport { Check, X, Star, Users, Clock, Shield, Zap, Headphones, Globe } from 'lucide-react'\n\n// Force dynamic rendering\nexport const dynamic = 'force-dynamic'\n\nexport default function PricingPage() {\n  const [isAnnual, setIsAnnual] = useState(false)\n\n  const plans = [\n    {\n      name: 'Basic',\n      description: 'Perfect for small teams and personal use',\n      monthlyPrice: 0,\n      annualPrice: 0,\n      features: [\n        { name: 'Up to 5 participants', included: true },\n        { name: '40-minute meetings', included: true },\n        { name: 'HD video quality', included: true },\n        { name: 'Basic chat features', included: true },\n        { name: 'Screen sharing', included: true },\n        { name: 'Mobile apps', included: true },\n        { name: 'Meeting recordings', included: false },\n        { name: 'Advanced security', included: false },\n        { name: 'Priority support', included: false },\n        { name: 'Custom branding', included: false }\n      ],\n      cta: 'Get Started',\n      popular: false\n    },\n    {\n      name: 'Pro',\n      description: 'Ideal for growing teams and businesses',\n      monthlyPrice: 12,\n      annualPrice: 120,\n      features: [\n        { name: 'Up to 25 participants', included: true },\n        { name: 'Unlimited meeting time', included: true },\n        { name: '4K video quality', included: true },\n        { name: 'Advanced chat & file sharing', included: true },\n        { name: 'Screen sharing', included: true },\n        { name: 'Mobile apps', included: true },\n        { name: 'Meeting recordings', included: true },\n        { name: 'Advanced security', included: true },\n        { name: 'Email support', included: true },\n        { name: 'Custom branding', included: false }\n      ],\n      cta: 'Start Free Trial',\n      popular: true\n    },\n    {\n      name: 'Enterprise',\n      description: 'For large organizations with advanced needs',\n      monthlyPrice: 25,\n      annualPrice: 250,\n      features: [\n        { name: 'Up to 100 participants', included: true },\n        { name: 'Unlimited everything', included: true },\n        { name: '4K video quality', included: true },\n        { name: 'Advanced collaboration tools', included: true },\n        { name: 'Screen sharing', included: true },\n        { name: 'Mobile apps', included: true },\n        { name: 'Meeting recordings', included: true },\n        { name: 'Enterprise security', included: true },\n        { name: 'Priority support', included: true },\n        { name: 'Custom branding', included: true }\n      ],\n      cta: 'Contact Sales',\n      popular: false\n    }\n  ]\n\n  return (\n    <>\n      {/* Animated Background */}\n      <div className=\"animated-bg\"></div>\n\n      <div className=\"min-h-screen pt-20 relative z-10\">\n        {/* Hero Section */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-7xl mx-auto text-center\">\n            <h1 className=\"text-5xl font-bold text-white mb-6 fade-in\">\n              Simple, Transparent Pricing\n            </h1>\n            <p className=\"text-xl text-white/80 max-w-3xl mx-auto leading-relaxed slide-up mb-8\">\n              Choose the perfect plan for your team. All plans include our core features with no hidden fees.\n            </p>\n            \n            {/* Billing Toggle */}\n            <div className=\"flex items-center justify-center gap-4 mb-12\">\n              <span className={`text-white/70 ${!isAnnual ? 'text-white font-semibold' : ''}`}>\n                Monthly\n              </span>\n              <button\n                onClick={() => setIsAnnual(!isAnnual)}\n                className={`relative w-14 h-7 rounded-full transition-colors ${\n                  isAnnual ? 'bg-purple-500' : 'bg-white/20'\n                }`}\n              >\n                <div\n                  className={`absolute top-1 w-5 h-5 bg-white rounded-full transition-transform ${\n                    isAnnual ? 'translate-x-7' : 'translate-x-1'\n                  }`}\n                />\n              </button>\n              <span className={`text-white/70 ${isAnnual ? 'text-white font-semibold' : ''}`}>\n                Annual\n              </span>\n              {isAnnual && (\n                <span className=\"bg-green-500 text-white text-xs px-2 py-1 rounded-full\">\n                  Save 20%\n                </span>\n              )}\n            </div>\n          </div>\n        </section>\n\n        {/* Pricing Cards */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-7xl mx-auto\">\n            <div className=\"grid md:grid-cols-3 gap-8\">\n              {plans.map((plan, index) => (\n                <div\n                  key={plan.name}\n                  className={`glass p-8 text-center relative ${\n                    plan.popular ? 'border-2 border-purple-500' : ''\n                  }`}\n                >\n                  {plan.popular && (\n                    <div className=\"absolute -top-3 left-1/2 transform -translate-x-1/2 bg-gradient-to-r from-purple-500 to-pink-500 text-white px-4 py-1 rounded-full text-sm font-semibold\">\n                      Most Popular\n                    </div>\n                  )}\n                  \n                  <h3 className=\"text-2xl font-bold text-white mb-2\">{plan.name}</h3>\n                  <p className=\"text-white/70 text-sm mb-6\">{plan.description}</p>\n                  \n                  <div className=\"mb-8\">\n                    <span className=\"text-4xl font-bold text-white\">\n                      ${isAnnual ? plan.annualPrice : plan.monthlyPrice}\n                    </span>\n                    <span className=\"text-white/60\">\n                      /{isAnnual ? 'year' : 'month'}\n                    </span>\n                  </div>\n\n                  <ul className=\"space-y-3 mb-8 text-left\">\n                    {plan.features.map((feature, featureIndex) => (\n                      <li key={featureIndex} className=\"flex items-center gap-3\">\n                        {feature.included ? (\n                          <Check className=\"h-4 w-4 text-green-400 flex-shrink-0\" />\n                        ) : (\n                          <X className=\"h-4 w-4 text-red-400 flex-shrink-0\" />\n                        )}\n                        <span className={`text-sm ${\n                          feature.included ? 'text-white/80' : 'text-white/40'\n                        }`}>\n                          {feature.name}\n                        </span>\n                      </li>\n                    ))}\n                  </ul>\n\n                  <button\n                    className={`w-full ${\n                      plan.popular ? 'btn-primary' : 'btn-secondary'\n                    }`}\n                  >\n                    {plan.cta}\n                  </button>\n                </div>\n              ))}\n            </div>\n          </div>\n        </section>\n\n        {/* Features Comparison */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-7xl mx-auto\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-4xl font-bold text-white mb-4\">\n                Compare All Features\n              </h2>\n              <p className=\"text-xl text-white/70\">\n                See exactly what's included in each plan\n              </p>\n            </div>\n\n            <div className=\"glass p-8 overflow-x-auto\">\n              <table className=\"w-full\">\n                <thead>\n                  <tr className=\"border-b border-white/10\">\n                    <th className=\"text-left text-white font-semibold py-4\">Features</th>\n                    <th className=\"text-center text-white font-semibold py-4\">Basic</th>\n                    <th className=\"text-center text-white font-semibold py-4\">Pro</th>\n                    <th className=\"text-center text-white font-semibold py-4\">Enterprise</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  {[\n                    { feature: 'Participants', basic: '5', pro: '25', enterprise: '100' },\n                    { feature: 'Meeting Duration', basic: '40 min', pro: 'Unlimited', enterprise: 'Unlimited' },\n                    { feature: 'Video Quality', basic: 'HD', pro: '4K', enterprise: '4K' },\n                    { feature: 'Recording', basic: '✗', pro: '✓', enterprise: '✓' },\n                    { feature: 'Advanced Security', basic: '✗', pro: '✓', enterprise: '✓' },\n                    { feature: 'Priority Support', basic: '✗', pro: '✗', enterprise: '✓' },\n                    { feature: 'Custom Branding', basic: '✗', pro: '✗', enterprise: '✓' }\n                  ].map((row, index) => (\n                    <tr key={index} className=\"border-b border-white/5\">\n                      <td className=\"text-white/80 py-4\">{row.feature}</td>\n                      <td className=\"text-center text-white/70 py-4\">{row.basic}</td>\n                      <td className=\"text-center text-white/70 py-4\">{row.pro}</td>\n                      <td className=\"text-center text-white/70 py-4\">{row.enterprise}</td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n          </div>\n        </section>\n\n        {/* FAQ Section */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-4xl font-bold text-white mb-4\">\n                Pricing FAQ\n              </h2>\n            </div>\n\n            <div className=\"space-y-6\">\n              {[\n                {\n                  question: \"Can I change my plan anytime?\",\n                  answer: \"Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately and we'll prorate any billing differences.\"\n                },\n                {\n                  question: \"Is there a free trial?\",\n                  answer: \"Yes! We offer a 14-day free trial of our Pro plan with no credit card required. You can also use our Basic plan for free indefinitely.\"\n                },\n                {\n                  question: \"What payment methods do you accept?\",\n                  answer: \"We accept all major credit cards, PayPal, and bank transfers for Enterprise customers. All payments are processed securely.\"\n                },\n                {\n                  question: \"Do you offer discounts for nonprofits?\",\n                  answer: \"Yes, we offer special pricing for qualified nonprofit organizations and educational institutions. Contact our sales team for more information.\"\n                }\n              ].map((faq, index) => (\n                <div key={index} className=\"glass p-6\">\n                  <h3 className=\"text-white font-semibold mb-3\">{faq.question}</h3>\n                  <p className=\"text-white/70\">{faq.answer}</p>\n                </div>\n              ))}\n            </div>\n          </div>\n        </section>\n\n        {/* CTA Section */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-4xl mx-auto text-center\">\n            <div className=\"glass p-12\">\n              <h2 className=\"text-3xl font-bold text-white mb-4\">\n                Ready to Get Started?\n              </h2>\n              <p className=\"text-xl text-white/70 mb-8\">\n                Join millions of users who trust StreamIt Pro for their video conferencing needs\n              </p>\n              <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n                <button className=\"btn-primary px-8 py-3\">Start Free Trial</button>\n                <button className=\"btn-secondary px-8 py-3\">Contact Sales</button>\n              </div>\n            </div>\n          </div>\n        </section>\n      </div>\n    </>\n  )\n}\n", "export default {\n  xmlns: 'http://www.w3.org/2000/svg',\n  width: 24,\n  height: 24,\n  viewBox: '0 0 24 24',\n  fill: 'none',\n  stroke: 'currentColor',\n  strokeWidth: 2,\n  strokeLinecap: 'round',\n  strokeLinejoin: 'round',\n};\n", "import {\n  forwardRef,\n  createElement,\n  ReactSVG,\n  SVGProps,\n  ForwardRefExoticComponent,\n  RefAttributes,\n} from 'react';\nimport defaultAttributes from './defaultAttributes';\n\nexport type IconNode = [elementName: keyof ReactSVG, attrs: Record<string, string>][];\n\nexport type SVGAttributes = Partial<SVGProps<SVGSVGElement>>;\ntype ComponentAttributes = RefAttributes<SVGSVGElement> & SVGAttributes;\n\nexport interface LucideProps extends ComponentAttributes {\n  size?: string | number;\n  absoluteStrokeWidth?: boolean;\n}\n\nexport type LucideIcon = ForwardRefExoticComponent<LucideProps>;\n/**\n * Converts string to KebabCase\n * Copied from scripts/helper. If anyone knows how to properly import it here\n * then please fix it.\n *\n * @param {string} string\n * @returns {string} A kebabized string\n */\nexport const toKebabCase = (string: string) =>\n  string\n    .replace(/([a-z0-9])([A-Z])/g, '$1-$2')\n    .toLowerCase()\n    .trim();\n\nconst createLucideIcon = (iconName: string, iconNode: IconNode): LucideIcon => {\n  const Component = forwardRef<SVGSVGElement, LucideProps>(\n    ({ color = 'currentColor', size = 24, strokeWidth = 2, absoluteStrokeWidth, className = '', children, ...rest }, ref) =>\n      createElement(\n        'svg',\n        {\n          ref,\n          ...defaultAttributes,\n          width: size,\n          height: size,\n          stroke: color,\n          strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n          className: ['lucide', `lucide-${toKebabCase(iconName)}`, className].join(' '),\n          ...rest,\n        },\n        [\n          ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n          ...(Array.isArray(children) ? children : [children]),\n        ]\n      )\n  );\n\n  Component.displayName = `${iconName}`;\n\n  return Component;\n};\n\nexport default createLucideIcon;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Check\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjAgNiA5IDE3bC01LTUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/check\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Check = createLucideIcon('Check', [['path', { d: 'M20 6 9 17l-5-5', key: '1gmf2c' }]]);\n\nexport default Check;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name X\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTggNiA2IDE4IiAvPgogIDxwYXRoIGQ9Im02IDYgMTIgMTIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/x\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst X = createLucideIcon('X', [\n  ['path', { d: 'M18 6 6 18', key: '1bl5f8' }],\n  ['path', { d: 'm6 6 12 12', key: 'd8bk6v' }],\n]);\n\nexport default X;\n"], "names": ["Promise", "resolve", "then", "__webpack_require__", "bind", "dynamic", "PricingPage", "isAnnual", "setIsAnnual", "useState", "react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__", "jsxs", "Fragment", "jsx", "div", "className", "section", "h1", "p", "span", "concat", "button", "onClick", "plans", "name", "description", "monthlyPrice", "annualPrice", "features", "included", "cta", "popular", "map", "plan", "index", "h3", "ul", "feature", "featureIndex", "li", "Check", "X", "h2", "table", "thead", "tr", "th", "tbody", "basic", "pro", "enterprise", "row", "td", "question", "answer", "faq", "defaultAttributes", "xmlns", "width", "height", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "toKebabCase", "string", "replace", "toLowerCase", "trim", "createLucideIcon", "iconName", "iconNode", "Component", "forwardRef", "param", "ref", "color", "size", "absoluteStrokeWidth", "children", "rest", "createElement", "Number", "join", "tag", "attrs", "Array", "isArray", "displayName", "d", "key"], "sourceRoot": ""}