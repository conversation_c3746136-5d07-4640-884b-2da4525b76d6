{"version": 3, "file": "static/chunks/app/terms/page-6816ee3e947b603b.js", "mappings": "oFAAAA,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA;;;;;GCaM,IAAAE,EAAQC,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,QAAS,CACtC,CAAC,OAAQ,CAAEC,EAAG,mDAAoDC,IAAK,UAAU,CACjF,CAAC,OAAQ,CAAED,EAAG,kDAAmDC,IAAK,UAAU,CAChF,CAAC,OAAQ,CAAED,EAAG,WAAYC,IAAK,UAAU,CACzC,CAAC,OAAQ,CAAED,EAAG,WAAYC,IAAK,UAAU,CACzC,CAAC,OAAQ,CAAED,EAAG,mCAAoCC,IAAK,UAAU,CAClE,gBCdM,IAAMC,EAAU,gBAER,SAASC,IACtB,IAAMC,EAAW,CACf,CACEC,GAAI,aACJC,MAAO,sBACPC,KAAMC,EAAAA,CAAQA,EAEhB,CACEH,GAAI,sBACJC,MAAO,sBACPC,KAAME,EAAAA,CAAKA,EAEb,CACEJ,GAAI,wBACJC,MAAO,wBACPC,KAAMG,EAAAA,CAAMA,EAEd,CACEL,GAAI,kBACJC,MAAO,kBACPC,KAAMI,EAAAA,CAAaA,EAErB,CACEN,GAAI,uBACJC,MAAO,0BACPC,KAAMT,CACR,EACA,CACEO,GAAI,UACJC,MAAO,sBACPC,KAAMK,EAAAA,CAAIA,EAEb,CAED,MACE,GAAAC,EAAAC,IAAA,EAAAD,EAAAE,QAAA,YAEE,GAAAF,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,gBAEf,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,6CAEb,GAAAL,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,0CACb,GAAAL,EAAAG,GAAA,EAACI,KAAAA,CAAGF,UAAU,sDAA6C,qBAG3D,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,4EAAmE,yHAIhF,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,8BAAqB,uCAKtC,GAAAL,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,6BACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sBACb,GAAAL,EAAAG,GAAA,EAACM,KAAAA,CAAGJ,UAAU,8CAAqC,sBACnD,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,qCACZd,EAASmB,GAAG,CAAC,CAACJ,EAASK,IACtB,GAAAX,EAAAC,IAAA,EAACW,IAAAA,CAECC,KAAM,IAAeC,MAAA,CAAXR,EAAQd,EAAE,EACpBa,UAAU,sFAEV,GAAAL,EAAAG,GAAA,EAACG,EAAQZ,IAAI,EAACW,UAAU,4BACxB,GAAAL,EAAAG,GAAA,EAACY,OAAAA,CAAKV,UAAU,4CAAoCC,EAAQb,KAAK,KAL5Da,EAAQd,EAAE,YAc3B,GAAAQ,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,yCAGb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIZ,GAAG,aAAaa,UAAU,sBAC7B,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,yCACb,GAAAL,EAAAG,GAAA,EAACR,EAAAA,CAAQA,CAAAA,CAACU,UAAU,4BACpB,GAAAL,EAAAG,GAAA,EAACM,KAAAA,CAAGJ,UAAU,yCAAgC,2BAEhD,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,oCACb,GAAAL,EAAAG,GAAA,EAACK,IAAAA,UAAE,6LAEH,GAAAR,EAAAG,GAAA,EAACK,IAAAA,UAAE,yNAEH,GAAAR,EAAAG,GAAA,EAACK,IAAAA,UAAE,yPAKP,GAAAR,EAAAC,IAAA,EAACG,MAAAA,CAAIZ,GAAG,sBAAsBa,UAAU,sBACtC,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,yCACb,GAAAL,EAAAG,GAAA,EAACP,EAAAA,CAAKA,CAAAA,CAACS,UAAU,0BACjB,GAAAL,EAAAG,GAAA,EAACM,KAAAA,CAAGJ,UAAU,yCAAgC,2BAEhD,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,oCACb,GAAAL,EAAAG,GAAA,EAACK,IAAAA,UAAE,8JAEH,GAAAR,EAAAC,IAAA,EAACe,KAAAA,CAAGX,UAAU,iDACZ,GAAAL,EAAAG,GAAA,EAACc,KAAAA,UAAG,8CACJ,GAAAjB,EAAAG,GAAA,EAACc,KAAAA,UAAG,2CACJ,GAAAjB,EAAAG,GAAA,EAACc,KAAAA,UAAG,gCACJ,GAAAjB,EAAAG,GAAA,EAACc,KAAAA,UAAG,sDACJ,GAAAjB,EAAAG,GAAA,EAACc,KAAAA,UAAG,4CAGN,GAAAjB,EAAAG,GAAA,EAACK,IAAAA,UAAE,yMAEH,GAAAR,EAAAG,GAAA,EAACe,KAAAA,CAAGb,UAAU,4CAAmC,yBACjD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,UAAE,8NAKP,GAAAR,EAAAC,IAAA,EAACG,MAAAA,CAAIZ,GAAG,wBAAwBa,UAAU,sBACxC,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,yCACb,GAAAL,EAAAG,GAAA,EAACN,EAAAA,CAAMA,CAAAA,CAACQ,UAAU,2BAClB,GAAAL,EAAAG,GAAA,EAACM,KAAAA,CAAGJ,UAAU,yCAAgC,6BAEhD,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,oCACb,GAAAL,EAAAG,GAAA,EAACK,IAAAA,UAAE,6CAEH,GAAAR,EAAAC,IAAA,EAACe,KAAAA,CAAGX,UAAU,iDACZ,GAAAL,EAAAG,GAAA,EAACc,KAAAA,UAAG,yEACJ,GAAAjB,EAAAG,GAAA,EAACc,KAAAA,UAAG,sDACJ,GAAAjB,EAAAG,GAAA,EAACc,KAAAA,UAAG,2EACJ,GAAAjB,EAAAG,GAAA,EAACc,KAAAA,UAAG,kDACJ,GAAAjB,EAAAG,GAAA,EAACc,KAAAA,UAAG,mDACJ,GAAAjB,EAAAG,GAAA,EAACc,KAAAA,UAAG,kEACJ,GAAAjB,EAAAG,GAAA,EAACc,KAAAA,UAAG,kDAGN,GAAAjB,EAAAG,GAAA,EAACe,KAAAA,CAAGb,UAAU,4CAAmC,2BACjD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,UAAE,oMAEH,GAAAR,EAAAG,GAAA,EAACe,KAAAA,CAAGb,UAAU,4CAAmC,oBACjD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,UAAE,gMAKP,GAAAR,EAAAC,IAAA,EAACG,MAAAA,CAAIZ,GAAG,kBAAkBa,UAAU,sBAClC,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,yCACb,GAAAL,EAAAG,GAAA,EAACL,EAAAA,CAAaA,CAAAA,CAACO,UAAU,4BACzB,GAAAL,EAAAG,GAAA,EAACM,KAAAA,CAAGJ,UAAU,yCAAgC,uBAEhD,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,oCACb,GAAAL,EAAAG,GAAA,EAACK,IAAAA,UAAE,iFAEH,GAAAR,EAAAC,IAAA,EAACe,KAAAA,CAAGX,UAAU,iDACZ,GAAAL,EAAAG,GAAA,EAACc,KAAAA,UAAG,2DACJ,GAAAjB,EAAAG,GAAA,EAACc,KAAAA,UAAG,kEACJ,GAAAjB,EAAAG,GAAA,EAACc,KAAAA,UAAG,yDACJ,GAAAjB,EAAAG,GAAA,EAACc,KAAAA,UAAG,0DACJ,GAAAjB,EAAAG,GAAA,EAACc,KAAAA,UAAG,0DACJ,GAAAjB,EAAAG,GAAA,EAACc,KAAAA,UAAG,2CACJ,GAAAjB,EAAAG,GAAA,EAACc,KAAAA,UAAG,oDACJ,GAAAjB,EAAAG,GAAA,EAACc,KAAAA,UAAG,mDACJ,GAAAjB,EAAAG,GAAA,EAACc,KAAAA,UAAG,oEACJ,GAAAjB,EAAAG,GAAA,EAACc,KAAAA,UAAG,uEAGN,GAAAjB,EAAAG,GAAA,EAACK,IAAAA,UAAE,qKAKP,GAAAR,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sBACb,GAAAL,EAAAG,GAAA,EAACM,KAAAA,CAAGJ,UAAU,8CAAqC,0BACnD,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,oCACb,GAAAL,EAAAG,GAAA,EAACe,KAAAA,CAAGb,UAAU,4CAAmC,eACjD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,UAAE,qNAEH,GAAAR,EAAAG,GAAA,EAACe,KAAAA,CAAGb,UAAU,4CAAmC,iBACjD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,UAAE,gOAEH,GAAAR,EAAAG,GAAA,EAACe,KAAAA,CAAGb,UAAU,4CAAmC,aACjD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,UAAE,wIAKP,GAAAR,EAAAC,IAAA,EAACG,MAAAA,CAAIZ,GAAG,uBAAuBa,UAAU,sBACvC,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,yCACb,GAAAL,EAAAG,GAAA,EAAClB,EAAKA,CAACoB,UAAU,yBACjB,GAAAL,EAAAG,GAAA,EAACM,KAAAA,CAAGJ,UAAU,yCAAgC,+BAEhD,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,oCACb,GAAAL,EAAAG,GAAA,EAACK,IAAAA,UAAE,sLAEH,GAAAR,EAAAC,IAAA,EAACe,KAAAA,CAAGX,UAAU,iDACZ,GAAAL,EAAAG,GAAA,EAACc,KAAAA,UAAG,qDACJ,GAAAjB,EAAAG,GAAA,EAACc,KAAAA,UAAG,gDACJ,GAAAjB,EAAAG,GAAA,EAACc,KAAAA,UAAG,yDACJ,GAAAjB,EAAAG,GAAA,EAACc,KAAAA,UAAG,sCAGN,GAAAjB,EAAAG,GAAA,EAACK,IAAAA,UAAE,gKAEH,GAAAR,EAAAG,GAAA,EAACe,KAAAA,CAAGb,UAAU,4CAAmC,6BACjD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,UAAE,oNAKP,GAAAR,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sBACb,GAAAL,EAAAG,GAAA,EAACM,KAAAA,CAAGJ,UAAU,8CAAqC,gBACnD,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,oCACb,GAAAL,EAAAG,GAAA,EAACK,IAAAA,UAAE,wHAEH,GAAAR,EAAAG,GAAA,EAACK,IAAAA,UAAE,8IAEH,GAAAR,EAAAG,GAAA,EAACK,IAAAA,UAAE,4KAKP,GAAAR,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sBACb,GAAAL,EAAAG,GAAA,EAACM,KAAAA,CAAGJ,UAAU,8CAAqC,kBACnD,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,oCACb,GAAAL,EAAAG,GAAA,EAACK,IAAAA,UAAE,4KAEH,GAAAR,EAAAG,GAAA,EAACK,IAAAA,UAAE,+LAKP,GAAAR,EAAAC,IAAA,EAACG,MAAAA,CAAIZ,GAAG,UAAUa,UAAU,sBAC1B,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,yCACb,GAAAL,EAAAG,GAAA,EAACJ,EAAAA,CAAIA,CAAAA,CAACM,UAAU,4BAChB,GAAAL,EAAAG,GAAA,EAACM,KAAAA,CAAGJ,UAAU,yCAAgC,2BAEhD,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,oCACb,GAAAL,EAAAG,GAAA,EAACK,IAAAA,UAAE,+EAEH,GAAAR,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sCACb,GAAAL,EAAAC,IAAA,EAACO,IAAAA,WAAE,GAAAR,EAAAG,GAAA,EAACgB,SAAAA,UAAO,WAAe,4BAC1B,GAAAnB,EAAAC,IAAA,EAACO,IAAAA,WAAE,GAAAR,EAAAG,GAAA,EAACgB,SAAAA,UAAO,aAAiB,sBAAmB,GAAAnB,EAAAG,GAAA,EAACiB,KAAAA,CAAAA,GAAK,mBACrC,GAAApB,EAAAG,GAAA,EAACiB,KAAAA,CAAAA,GAAK,0BACC,GAAApB,EAAAG,GAAA,EAACiB,KAAAA,CAAAA,GAAK,sCAW/C,mFCtQeC,EAAA,CACbC,MAAO,6BACPC,MAAO,GACPC,OAAQ,GACRC,QAAS,YACTC,KAAM,OACNC,OAAQ,eACRC,YAAa,EACbC,cAAe,QACfC,eAAgB,OAClB;;;;;GCmBa,IAAAC,EAAc,GACzBC,EACGC,OAAA,CAAQ,qBAAsB,SAC9BC,WAAY,GACZC,IAAK,GAEJjD,EAAmB,CAACkD,EAAkBC,KAC1C,IAAMC,EAAYC,CAAAA,EAAAA,EAAAA,UAAAA,EAChB,CAAAC,EAAiHC,QAAhH,CAAEC,MAAAA,EAAQ,eAAgBC,KAAAA,EAAO,EAAI,CAAAf,YAAAA,EAAc,CAAG,CAAAgB,oBAAAA,CAAA,CAAqBvC,UAAAA,EAAY,GAAIwC,SAAAA,CAAa,IAAAC,EAAA,CAAAN,QACvGO,CAAAA,EAAAA,EAAAA,aAAAA,EACE,MACA,CACEN,IAAAA,EACA,GAAGpB,CAAA,CACHE,MAAOoB,EACPnB,OAAQmB,EACRhB,OAAQe,EACRd,YAAagB,EAAsBI,GAAAA,OAAOpB,GAAoBoB,OAAOL,GAAQf,EAC7EvB,UAAW,CAAC,SAAoB,UAAyBS,MAAA,CAAzBiB,EAAYK,IAAa/B,EAAW,CAAA4C,IAAA,CAAK,KACzE,GAAGH,CAAA,EAEL,IACKT,EAAS3B,GAAA,CAAI,OAAC,CAACwC,EAAKC,EAAW,CAAAX,QAAAO,CAAAA,EAAAA,EAAAA,aAAAA,EAAcG,EAAKC,QACjDC,MAAMC,OAAA,CAAQR,GAAYA,EAAW,CAACA,EAAQ,CACpD,IAMC,OAFPP,EAAUgB,WAAA,CAAc,GAAGxC,MAAA,CAAAsB,GAEpBE,CACT;;;;;GC/CM,IAAAxC,EAAgBZ,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,gBAAiB,CACtD,CACE,OACA,CACEC,EAAG,4EACHC,IAAK,QACP,EACF,CACA,CAAC,OAAQ,CAAED,EAAG,UAAWC,IAAK,UAAU,CACxC,CAAC,OAAQ,CAAED,EAAG,aAAcC,IAAK,UAAU,CAC5C;;;;;GCVK,IAAAO,EAAWT,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,WAAY,CAC5C,CACE,OACA,CAAEC,EAAG,wEAAyEC,IAAK,QAAS,EAC9F,CACA,CAAC,WAAY,CAAEmE,OAAQ,iBAAkBnE,IAAK,UAAU,CACxD,CAAC,OAAQ,CAAEoE,GAAI,KAAMC,GAAI,IAAKC,GAAI,KAAMC,GAAI,KAAMvE,IAAK,UAAU,CACjE,CAAC,OAAQ,CAAEoE,GAAI,KAAMC,GAAI,IAAKC,GAAI,KAAMC,GAAI,KAAMvE,IAAK,UAAU,CACjE,CAAC,OAAQ,CAAEoE,GAAI,KAAMC,GAAI,IAAKC,GAAI,IAAKC,GAAI,IAAKvE,IAAK,UAAU,CAChE;;;;;GCTK,IAAAW,EAAOb,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,OAAQ,CACpC,CAAC,OAAQ,CAAEqC,MAAO,KAAMC,OAAQ,KAAMoC,EAAG,IAAKC,EAAG,IAAKC,GAAI,IAAK1E,IAAK,UAAU,CAC9E,CAAC,OAAQ,CAAED,EAAG,4CAA6CC,IAAK,UAAU,CAC3E;;;;;GCHK,IAAAS,EAASX,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,SAAU,CACxC,CAAC,OAAQ,CAAEC,EAAG,6CAA8CC,IAAK,UAAU,CAC5E;;;;;GCFK,IAAAQ,EAAQV,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,QAAS,CACtC,CAAC,OAAQ,CAAEC,EAAG,4CAA6CC,IAAK,UAAU,CAC1E,CAAC,SAAU,CAAE2E,GAAI,IAAKC,GAAI,IAAKC,EAAG,IAAK7E,IAAK,SAAS,CACrD,CAAC,OAAQ,CAAED,EAAG,6BAA8BC,IAAK,UAAU,CAC3D,CAAC,OAAQ,CAAED,EAAG,4BAA6BC,IAAK,UAAU,CAC3D", "sources": ["webpack://_N_E/?fc82", "webpack://_N_E/../../../src/icons/scale.ts", "webpack://_N_E/./app/terms/page.tsx", "webpack://_N_E/../../src/defaultAttributes.ts", "webpack://_N_E/../../src/createLucideIcon.ts", "webpack://_N_E/../../../src/icons/alert-triangle.ts", "webpack://_N_E/../../../src/icons/file-text.ts", "webpack://_N_E/../../../src/icons/mail.ts", "webpack://_N_E/../../../src/icons/shield.ts", "webpack://_N_E/../../../src/icons/users.ts"], "sourcesContent": ["import(/* webpackMode: \"eager\" */ \"/Volumes/Apps/Websites/streamit-main/app/terms/page.tsx\");\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Scale\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTYgMTYgMy04IDMgOGMtLjg3LjY1LTEuOTIgMS0zIDFzLTIuMTMtLjM1LTMtMVoiIC8+CiAgPHBhdGggZD0ibTIgMTYgMy04IDMgOGMtLjg3LjY1LTEuOTIgMS0zIDFzLTIuMTMtLjM1LTMtMVoiIC8+CiAgPHBhdGggZD0iTTcgMjFoMTAiIC8+CiAgPHBhdGggZD0iTTEyIDN2MTgiIC8+CiAgPHBhdGggZD0iTTMgN2gyYzIgMCA1LTEgNy0yIDIgMSA1IDIgNyAyaDIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/scale\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Scale = createLucideIcon('Scale', [\n  ['path', { d: 'm16 16 3-8 3 8c-.87.65-1.92 1-3 1s-2.13-.35-3-1Z', key: '7g6ntu' }],\n  ['path', { d: 'm2 16 3-8 3 8c-.87.65-1.92 1-3 1s-2.13-.35-3-1Z', key: 'ijws7r' }],\n  ['path', { d: 'M7 21h10', key: '1b0cd5' }],\n  ['path', { d: 'M12 3v18', key: '108xh3' }],\n  ['path', { d: 'M3 7h2c2 0 5-1 7-2 2 1 5 2 7 2h2', key: '3gwbw2' }],\n]);\n\nexport default Scale;\n", "'use client'\n\nimport { FileText, Users, Shield, AlertTriangle, Scale, Mail } from 'lucide-react'\n\n// Force dynamic rendering\nexport const dynamic = 'force-dynamic'\n\nexport default function TermsPage() {\n  const sections = [\n    {\n      id: 'acceptance',\n      title: 'Acceptance of Terms',\n      icon: FileText\n    },\n    {\n      id: 'service-description',\n      title: 'Service Description',\n      icon: Users\n    },\n    {\n      id: 'user-responsibilities',\n      title: 'User Responsibilities',\n      icon: Shield\n    },\n    {\n      id: 'prohibited-uses',\n      title: 'Prohibited Uses',\n      icon: AlertTriangle\n    },\n    {\n      id: 'limitation-liability',\n      title: 'Limitation of Liability',\n      icon: Scale\n    },\n    {\n      id: 'contact',\n      title: 'Contact Information',\n      icon: Mail\n    }\n  ]\n\n  return (\n    <>\n      {/* Animated Background */}\n      <div className=\"animated-bg\"></div>\n\n      <div className=\"min-h-screen pt-20 relative z-10\">\n        {/* Hero Section */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-7xl mx-auto text-center\">\n            <h1 className=\"text-5xl font-bold text-white mb-6 fade-in\">\n              Terms of Service\n            </h1>\n            <p className=\"text-xl text-white/80 max-w-3xl mx-auto leading-relaxed slide-up\">\n              These terms govern your use of StreamIt Pro. Please read them carefully \n              before using our video conferencing service.\n            </p>\n            <p className=\"text-white/60 mt-4\">Last updated: January 1, 2024</p>\n          </div>\n        </section>\n\n        {/* Table of Contents */}\n        <section className=\"py-10 px-4\">\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"glass p-8\">\n              <h2 className=\"text-2xl font-bold text-white mb-6\">Table of Contents</h2>\n              <div className=\"grid md:grid-cols-2 gap-4\">\n                {sections.map((section, index) => (\n                  <a\n                    key={section.id}\n                    href={`#${section.id}`}\n                    className=\"flex items-center gap-3 p-3 hover:bg-white/5 rounded-lg transition-colors\"\n                  >\n                    <section.icon className=\"h-5 w-5 text-purple-400\" />\n                    <span className=\"text-white hover:text-purple-300\">{section.title}</span>\n                  </a>\n                ))}\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* Terms Content */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-4xl mx-auto space-y-12\">\n            \n            {/* Acceptance */}\n            <div id=\"acceptance\" className=\"glass p-8\">\n              <div className=\"flex items-center gap-3 mb-6\">\n                <FileText className=\"h-6 w-6 text-purple-400\" />\n                <h2 className=\"text-2xl font-bold text-white\">Acceptance of Terms</h2>\n              </div>\n              <div className=\"space-y-4 text-white/80\">\n                <p>By accessing or using StreamIt Pro (\"Service\"), you agree to be bound by these Terms of Service (\"Terms\"). If you disagree with any part of these terms, you may not access the Service.</p>\n                \n                <p>These Terms apply to all visitors, users, and others who access or use the Service. By using our Service, you represent that you are at least 18 years old or have reached the age of majority in your jurisdiction.</p>\n                \n                <p>We reserve the right to modify these Terms at any time. We will notify users of any material changes via email or through the Service. Your continued use of the Service after such modifications constitutes acceptance of the updated Terms.</p>\n              </div>\n            </div>\n\n            {/* Service Description */}\n            <div id=\"service-description\" className=\"glass p-8\">\n              <div className=\"flex items-center gap-3 mb-6\">\n                <Users className=\"h-6 w-6 text-blue-400\" />\n                <h2 className=\"text-2xl font-bold text-white\">Service Description</h2>\n              </div>\n              <div className=\"space-y-4 text-white/80\">\n                <p>StreamIt Pro is a video conferencing platform that enables users to conduct virtual meetings, webinars, and collaborative sessions. Our Service includes:</p>\n                \n                <ul className=\"list-disc list-inside space-y-2 ml-4\">\n                  <li>Video and audio conferencing capabilities</li>\n                  <li>Screen sharing and collaboration tools</li>\n                  <li>Chat and messaging features</li>\n                  <li>Meeting recording functionality (where available)</li>\n                  <li>Administrative and security controls</li>\n                </ul>\n                \n                <p>We strive to maintain high availability but do not guarantee uninterrupted access to the Service. We may modify, suspend, or discontinue any part of the Service at any time with reasonable notice.</p>\n                \n                <h3 className=\"text-lg font-semibold text-white\">Account Registration</h3>\n                <p>To use certain features of the Service, you must register for an account. You are responsible for maintaining the confidentiality of your account credentials and for all activities that occur under your account.</p>\n              </div>\n            </div>\n\n            {/* User Responsibilities */}\n            <div id=\"user-responsibilities\" className=\"glass p-8\">\n              <div className=\"flex items-center gap-3 mb-6\">\n                <Shield className=\"h-6 w-6 text-green-400\" />\n                <h2 className=\"text-2xl font-bold text-white\">User Responsibilities</h2>\n              </div>\n              <div className=\"space-y-4 text-white/80\">\n                <p>As a user of StreamIt Pro, you agree to:</p>\n                \n                <ul className=\"list-disc list-inside space-y-2 ml-4\">\n                  <li>Provide accurate and complete information when creating your account</li>\n                  <li>Maintain the security of your account credentials</li>\n                  <li>Use the Service in compliance with all applicable laws and regulations</li>\n                  <li>Respect the privacy and rights of other users</li>\n                  <li>Not share your account with unauthorized users</li>\n                  <li>Notify us immediately of any unauthorized use of your account</li>\n                  <li>Pay all applicable fees in a timely manner</li>\n                </ul>\n                \n                <h3 className=\"text-lg font-semibold text-white\">Content Responsibility</h3>\n                <p>You are solely responsible for any content you share, transmit, or display through the Service. This includes ensuring you have the necessary rights and permissions for any content you share.</p>\n                \n                <h3 className=\"text-lg font-semibold text-white\">Meeting Conduct</h3>\n                <p>You agree to conduct yourself professionally and respectfully in all meetings and interactions through the Service. Harassment, discrimination, or abusive behavior is not tolerated.</p>\n              </div>\n            </div>\n\n            {/* Prohibited Uses */}\n            <div id=\"prohibited-uses\" className=\"glass p-8\">\n              <div className=\"flex items-center gap-3 mb-6\">\n                <AlertTriangle className=\"h-6 w-6 text-orange-400\" />\n                <h2 className=\"text-2xl font-bold text-white\">Prohibited Uses</h2>\n              </div>\n              <div className=\"space-y-4 text-white/80\">\n                <p>You may not use StreamIt Pro for any of the following prohibited activities:</p>\n                \n                <ul className=\"list-disc list-inside space-y-2 ml-4\">\n                  <li>Illegal activities or violation of any applicable laws</li>\n                  <li>Harassment, abuse, or threatening behavior toward other users</li>\n                  <li>Sharing malicious software, viruses, or harmful code</li>\n                  <li>Attempting to gain unauthorized access to our systems</li>\n                  <li>Interfering with or disrupting the Service or servers</li>\n                  <li>Impersonating another person or entity</li>\n                  <li>Sharing copyrighted material without permission</li>\n                  <li>Spamming or sending unsolicited communications</li>\n                  <li>Using the Service for commercial purposes without authorization</li>\n                  <li>Recording meetings without proper consent from all participants</li>\n                </ul>\n                \n                <p>We reserve the right to investigate and take appropriate action against users who violate these prohibitions, including account suspension or termination.</p>\n              </div>\n            </div>\n\n            {/* Intellectual Property */}\n            <div className=\"glass p-8\">\n              <h2 className=\"text-2xl font-bold text-white mb-6\">Intellectual Property</h2>\n              <div className=\"space-y-4 text-white/80\">\n                <h3 className=\"text-lg font-semibold text-white\">Our Rights</h3>\n                <p>The Service and its original content, features, and functionality are owned by StreamIt Pro and are protected by international copyright, trademark, patent, trade secret, and other intellectual property laws.</p>\n                \n                <h3 className=\"text-lg font-semibold text-white\">Your Content</h3>\n                <p>You retain ownership of any content you create or share through the Service. By using the Service, you grant us a limited license to use, store, and transmit your content solely for the purpose of providing the Service.</p>\n                \n                <h3 className=\"text-lg font-semibold text-white\">Feedback</h3>\n                <p>Any feedback, suggestions, or ideas you provide about the Service may be used by us without any obligation to compensate you.</p>\n              </div>\n            </div>\n\n            {/* Limitation of Liability */}\n            <div id=\"limitation-liability\" className=\"glass p-8\">\n              <div className=\"flex items-center gap-3 mb-6\">\n                <Scale className=\"h-6 w-6 text-red-400\" />\n                <h2 className=\"text-2xl font-bold text-white\">Limitation of Liability</h2>\n              </div>\n              <div className=\"space-y-4 text-white/80\">\n                <p>TO THE MAXIMUM EXTENT PERMITTED BY LAW, STREAMIT PRO SHALL NOT BE LIABLE FOR ANY INDIRECT, INCIDENTAL, SPECIAL, CONSEQUENTIAL, OR PUNITIVE DAMAGES, INCLUDING BUT NOT LIMITED TO:</p>\n                \n                <ul className=\"list-disc list-inside space-y-2 ml-4\">\n                  <li>Loss of profits, data, or business opportunities</li>\n                  <li>Service interruptions or technical failures</li>\n                  <li>Unauthorized access to or alteration of your content</li>\n                  <li>Third-party actions or content</li>\n                </ul>\n                \n                <p>Our total liability for any claims arising from or related to the Service shall not exceed the amount you paid us in the twelve months preceding the claim.</p>\n                \n                <h3 className=\"text-lg font-semibold text-white\">Disclaimer of Warranties</h3>\n                <p>The Service is provided \"as is\" and \"as available\" without warranties of any kind, either express or implied. We do not warrant that the Service will be uninterrupted, error-free, or completely secure.</p>\n              </div>\n            </div>\n\n            {/* Termination */}\n            <div className=\"glass p-8\">\n              <h2 className=\"text-2xl font-bold text-white mb-6\">Termination</h2>\n              <div className=\"space-y-4 text-white/80\">\n                <p>You may terminate your account at any time by contacting us or using the account deletion feature in your settings.</p>\n                \n                <p>We may terminate or suspend your account immediately, without prior notice, if you breach these Terms or engage in prohibited activities.</p>\n                \n                <p>Upon termination, your right to use the Service will cease immediately. We may retain certain information as required by law or for legitimate business purposes.</p>\n              </div>\n            </div>\n\n            {/* Governing Law */}\n            <div className=\"glass p-8\">\n              <h2 className=\"text-2xl font-bold text-white mb-6\">Governing Law</h2>\n              <div className=\"space-y-4 text-white/80\">\n                <p>These Terms shall be governed by and construed in accordance with the laws of the State of California, United States, without regard to its conflict of law provisions.</p>\n                \n                <p>Any disputes arising from these Terms or your use of the Service shall be resolved through binding arbitration in accordance with the rules of the American Arbitration Association.</p>\n              </div>\n            </div>\n\n            {/* Contact */}\n            <div id=\"contact\" className=\"glass p-8\">\n              <div className=\"flex items-center gap-3 mb-6\">\n                <Mail className=\"h-6 w-6 text-purple-400\" />\n                <h2 className=\"text-2xl font-bold text-white\">Contact Information</h2>\n              </div>\n              <div className=\"space-y-4 text-white/80\">\n                <p>If you have any questions about these Terms of Service, please contact us:</p>\n                \n                <div className=\"bg-white/5 p-4 rounded-lg\">\n                  <p><strong>Email:</strong> <EMAIL></p>\n                  <p><strong>Address:</strong> StreamIt Pro, Inc.<br />\n                  123 Legal Street<br />\n                  San Francisco, CA 94105<br />\n                  United States</p>\n                </div>\n              </div>\n            </div>\n\n          </div>\n        </section>\n      </div>\n    </>\n  )\n}\n", "export default {\n  xmlns: 'http://www.w3.org/2000/svg',\n  width: 24,\n  height: 24,\n  viewBox: '0 0 24 24',\n  fill: 'none',\n  stroke: 'currentColor',\n  strokeWidth: 2,\n  strokeLinecap: 'round',\n  strokeLinejoin: 'round',\n};\n", "import {\n  forwardRef,\n  createElement,\n  ReactSVG,\n  SVGProps,\n  ForwardRefExoticComponent,\n  RefAttributes,\n} from 'react';\nimport defaultAttributes from './defaultAttributes';\n\nexport type IconNode = [elementName: keyof ReactSVG, attrs: Record<string, string>][];\n\nexport type SVGAttributes = Partial<SVGProps<SVGSVGElement>>;\ntype ComponentAttributes = RefAttributes<SVGSVGElement> & SVGAttributes;\n\nexport interface LucideProps extends ComponentAttributes {\n  size?: string | number;\n  absoluteStrokeWidth?: boolean;\n}\n\nexport type LucideIcon = ForwardRefExoticComponent<LucideProps>;\n/**\n * Converts string to KebabCase\n * Copied from scripts/helper. If anyone knows how to properly import it here\n * then please fix it.\n *\n * @param {string} string\n * @returns {string} A kebabized string\n */\nexport const toKebabCase = (string: string) =>\n  string\n    .replace(/([a-z0-9])([A-Z])/g, '$1-$2')\n    .toLowerCase()\n    .trim();\n\nconst createLucideIcon = (iconName: string, iconNode: IconNode): LucideIcon => {\n  const Component = forwardRef<SVGSVGElement, LucideProps>(\n    ({ color = 'currentColor', size = 24, strokeWidth = 2, absoluteStrokeWidth, className = '', children, ...rest }, ref) =>\n      createElement(\n        'svg',\n        {\n          ref,\n          ...defaultAttributes,\n          width: size,\n          height: size,\n          stroke: color,\n          strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n          className: ['lucide', `lucide-${toKebabCase(iconName)}`, className].join(' '),\n          ...rest,\n        },\n        [\n          ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n          ...(Array.isArray(children) ? children : [children]),\n        ]\n      )\n  );\n\n  Component.displayName = `${iconName}`;\n\n  return Component;\n};\n\nexport default createLucideIcon;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name AlertTriangle\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMjEuNzMgMTgtOC0xNGEyIDIgMCAwIDAtMy40OCAwbC04IDE0QTIgMiAwIDAgMCA0IDIxaDE2YTIgMiAwIDAgMCAxLjczLTNaIiAvPgogIDxwYXRoIGQ9Ik0xMiA5djQiIC8+CiAgPHBhdGggZD0iTTEyIDE3aC4wMSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/alert-triangle\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst AlertTriangle = createLucideIcon('AlertTriangle', [\n  [\n    'path',\n    {\n      d: 'm21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z',\n      key: 'c3ski4',\n    },\n  ],\n  ['path', { d: 'M12 9v4', key: 'juzpu7' }],\n  ['path', { d: 'M12 17h.01', key: 'p32p05' }],\n]);\n\nexport default AlertTriangle;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name FileText\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTQuNSAySDZhMiAyIDAgMCAwLTIgMnYxNmEyIDIgMCAwIDAgMiAyaDEyYTIgMiAwIDAgMCAyLTJWNy41TDE0LjUgMnoiIC8+CiAgPHBvbHlsaW5lIHBvaW50cz0iMTQgMiAxNCA4IDIwIDgiIC8+CiAgPGxpbmUgeDE9IjE2IiB4Mj0iOCIgeTE9IjEzIiB5Mj0iMTMiIC8+CiAgPGxpbmUgeDE9IjE2IiB4Mj0iOCIgeTE9IjE3IiB5Mj0iMTciIC8+CiAgPGxpbmUgeDE9IjEwIiB4Mj0iOCIgeTE9IjkiIHkyPSI5IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/file-text\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FileText = createLucideIcon('FileText', [\n  [\n    'path',\n    { d: 'M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z', key: '1nnpy2' },\n  ],\n  ['polyline', { points: '14 2 14 8 20 8', key: '1ew0cm' }],\n  ['line', { x1: '16', x2: '8', y1: '13', y2: '13', key: '14keom' }],\n  ['line', { x1: '16', x2: '8', y1: '17', y2: '17', key: '17nazh' }],\n  ['line', { x1: '10', x2: '8', y1: '9', y2: '9', key: '1a5vjj' }],\n]);\n\nexport default FileText;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Mail\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMjAiIGhlaWdodD0iMTYiIHg9IjIiIHk9IjQiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Im0yMiA3LTguOTcgNS43YTEuOTQgMS45NCAwIDAgMS0yLjA2IDBMMiA3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/mail\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Mail = createLucideIcon('Mail', [\n  ['rect', { width: '20', height: '16', x: '2', y: '4', rx: '2', key: '18n3k1' }],\n  ['path', { d: 'm22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7', key: '1ocrg3' }],\n]);\n\nexport default Mail;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Shield\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMjJzOC00IDgtMTBWNWwtOC0zLTggM3Y3YzAgNiA4IDEwIDggMTAiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/shield\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Shield = createLucideIcon('Shield', [\n  ['path', { d: 'M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10', key: '1irkt0' }],\n]);\n\nexport default Shield;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Users\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgMjF2LTJhNCA0IDAgMCAwLTQtNEg2YTQgNCAwIDAgMC00IDR2MiIgLz4KICA8Y2lyY2xlIGN4PSI5IiBjeT0iNyIgcj0iNCIgLz4KICA8cGF0aCBkPSJNMjIgMjF2LTJhNCA0IDAgMCAwLTMtMy44NyIgLz4KICA8cGF0aCBkPSJNMTYgMy4xM2E0IDQgMCAwIDEgMCA3Ljc1IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/users\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Users = createLucideIcon('Users', [\n  ['path', { d: 'M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2', key: '1yyitq' }],\n  ['circle', { cx: '9', cy: '7', r: '4', key: 'nufk8' }],\n  ['path', { d: 'M22 21v-2a4 4 0 0 0-3-3.87', key: 'kshegd' }],\n  ['path', { d: 'M16 3.13a4 4 0 0 1 0 7.75', key: '1da9ce' }],\n]);\n\nexport default Users;\n"], "names": ["Promise", "resolve", "then", "__webpack_require__", "bind", "Scale", "createLucideIcon", "d", "key", "dynamic", "TermsPage", "sections", "id", "title", "icon", "FileText", "Users", "Shield", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Mail", "jsx_runtime", "jsxs", "Fragment", "jsx", "div", "className", "section", "h1", "p", "h2", "map", "index", "a", "href", "concat", "span", "ul", "li", "h3", "strong", "br", "defaultAttributes", "xmlns", "width", "height", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "toKebabCase", "string", "replace", "toLowerCase", "trim", "iconName", "iconNode", "Component", "forwardRef", "param", "ref", "color", "size", "absoluteStrokeWidth", "children", "rest", "createElement", "Number", "join", "tag", "attrs", "Array", "isArray", "displayName", "points", "x1", "x2", "y1", "y2", "x", "y", "rx", "cx", "cy", "r"], "sourceRoot": ""}