{"version": 3, "file": "static/chunks/app/careers/page-b82d85dc0291430b.js", "mappings": "oFAAAA,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA;;;;;GCaM,IAAAE,EAAYC,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,YAAa,CAC9C,CAAC,OAAQ,CAAEC,MAAO,KAAMC,OAAQ,KAAMC,EAAG,IAAKC,EAAG,IAAKC,GAAI,IAAKC,GAAI,IAAKC,IAAK,UAAU,CACvF,CAAC,OAAQ,CAAEC,EAAG,6CAA8CD,IAAK,UAAU,CAC5E,0BCXM,IAAME,EAAU,gBAER,SAASC,IA0DtB,IAAMC,EAAW,CACf,CACEC,KAAMC,EAAAA,CAAKA,CACXC,MAAO,oBACPC,YAAa,uEACf,EACA,CACEH,KAAMI,EAAAA,CAAKA,CACXF,MAAO,gBACPC,YAAa,4DACf,EACA,CACEH,KAAMK,EAAAA,CAAGA,CACTH,MAAO,oBACPC,YAAa,2DACf,EACA,CACEH,KAAMM,EAAAA,CAAKA,CACXJ,MAAO,wBACPC,YAAa,6DACf,EACA,CACEH,KAAMO,EAAAA,CAAKA,CACXL,MAAO,eACPC,YAAa,iEACf,EACA,CACEH,KAAMb,EACNe,MAAO,oBACPC,YAAa,oDACf,EACD,CAqBD,MACE,GAAAK,EAAAC,IAAA,EAAAD,EAAAE,QAAA,YAEE,GAAAF,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,gBAEf,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,6CAEb,GAAAL,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,0CACb,GAAAL,EAAAG,GAAA,EAACI,KAAAA,CAAGF,UAAU,sDAA6C,kBAG3D,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,4EAAmE,oKAQpF,GAAAL,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,6BACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,kDACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,kCACb,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,8CAAqC,QACpD,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,iCAAwB,oBAEzC,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,kCACb,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,8CAAqC,OACpD,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,iCAAwB,iBAEzC,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,kCACb,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,8CAAqC,SACpD,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,iCAAwB,oBAEzC,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,kCACb,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,8CAAqC,SACpD,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,iCAAwB,+BAO/C,GAAAL,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,8BACb,GAAAL,EAAAG,GAAA,EAACM,KAAAA,CAAGJ,UAAU,2DAAkD,mBAEhE,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,qBACZK,CA7JX,CACEC,GAAI,EACJjB,MAAO,4BACPkB,WAAY,cACZC,SAAU,6BACVC,KAAM,YACNC,WAAY,WACZpB,YAAa,wFACf,EACA,CACEgB,GAAI,EACJjB,MAAO,kBACPkB,WAAY,UACZC,SAAU,oBACVC,KAAM,YACNC,WAAY,WACZpB,YAAa,6EACf,EACA,CACEgB,GAAI,EACJjB,MAAO,kBACPkB,WAAY,cACZC,SAAU,SACVC,KAAM,YACNC,WAAY,WACZpB,YAAa,uEACf,EACA,CACEgB,GAAI,EACJjB,MAAO,cACPkB,WAAY,SACZC,SAAU,6BACVC,KAAM,YACNC,WAAY,WACZpB,YAAa,sFACf,EACA,CACEgB,GAAI,EACJjB,MAAO,2BACPkB,WAAY,mBACZC,SAAU,eACVC,KAAM,YACNC,WAAY,WACZpB,YAAa,6EACf,EACA,CACEgB,GAAI,EACJjB,MAAO,oBACPkB,WAAY,WACZC,SAAU,SACVC,KAAM,YACNC,WAAY,WACZpB,YAAa,gEACf,EACD,CAuG0BqB,GAAG,CAAC,GACjB,GAAAhB,EAAAG,GAAA,EAACC,MAAAA,CAAsBC,UAAU,oFAC/B,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,+EACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,mBACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,mDACb,GAAAL,EAAAG,GAAA,EAACc,KAAAA,CAAGZ,UAAU,4CAAoCa,EAASxB,KAAK,GAChE,GAAAM,EAAAG,GAAA,EAACgB,OAAAA,CAAKd,UAAU,mEACba,EAASN,UAAU,MAIxB,GAAAZ,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,8BAAsBa,EAASvB,WAAW,GAEvD,GAAAK,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,yCACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,oCACb,GAAAL,EAAAG,GAAA,EAACiB,EAAAA,CAAMA,CAAAA,CAACf,UAAU,0BAClB,GAAAL,EAAAG,GAAA,EAACgB,OAAAA,CAAKd,UAAU,yBAAiBa,EAASL,QAAQ,MAEpD,GAAAb,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,oCACb,GAAAL,EAAAG,GAAA,EAACP,EAAAA,CAAKA,CAAAA,CAACS,UAAU,0BACjB,GAAAL,EAAAG,GAAA,EAACgB,OAAAA,CAAKd,UAAU,yBAAiBa,EAASJ,IAAI,MAEhD,GAAAd,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,oCACb,GAAAL,EAAAG,GAAA,EAACxB,EAASA,CAAC0B,UAAU,0BACrB,GAAAL,EAAAG,GAAA,EAACgB,OAAAA,CAAKd,UAAU,yBAAiBa,EAASH,UAAU,YAK1D,GAAAf,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,4CACb,GAAAL,EAAAG,GAAA,EAACkB,SAAAA,CAAOhB,UAAU,yBAAgB,eAClC,GAAAL,EAAAC,IAAA,EAACoB,SAAAA,CAAOhB,UAAU,gDAAsC,YAEtD,GAAAL,EAAAG,GAAA,EAACmB,EAAAA,CAAUA,CAAAA,CAACjB,UAAU,sBAhCpBa,EAASP,EAAE,UA2C7B,GAAAX,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,8BACb,GAAAL,EAAAG,GAAA,EAACM,KAAAA,CAAGJ,UAAU,2DAAkD,qBAEhE,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,oDACZd,EAASyB,GAAG,CAAC,CAACO,EAASC,IACtB,GAAAxB,EAAAC,IAAA,EAACG,MAAAA,CAAgBC,UAAU,kCACzB,GAAAL,EAAAG,GAAA,EAACoB,EAAQ/B,IAAI,EAACa,UAAU,2CACxB,GAAAL,EAAAG,GAAA,EAACc,KAAAA,CAAGZ,UAAU,iDAAyCkB,EAAQ7B,KAAK,GACpE,GAAAM,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,yBAAiBkB,EAAQ5B,WAAW,KAHzC6B,WAWlB,GAAAxB,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,8BACb,GAAAL,EAAAG,GAAA,EAACM,KAAAA,CAAGJ,UAAU,2DAAkD,eAEhE,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,qCACZoB,CArIX,CACE/B,MAAO,mBACPC,YAAa,wFACf,EACA,CACED,MAAO,eACPC,YAAa,qFACf,EACA,CACED,MAAO,eACPC,YAAa,+EACf,EACA,CACED,MAAO,wBACPC,YAAa,qFACf,EACD,CAqHmBqB,GAAG,CAAC,CAACU,EAAOF,IAClB,GAAAxB,EAAAC,IAAA,EAACG,MAAAA,CAAgBC,UAAU,sBACzB,GAAAL,EAAAG,GAAA,EAACc,KAAAA,CAAGZ,UAAU,iDAAyCqB,EAAMhC,KAAK,GAClE,GAAAM,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,yBAAiBqB,EAAM/B,WAAW,KAFvC6B,WAUlB,GAAAxB,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,8BACb,GAAAL,EAAAG,GAAA,EAACM,KAAAA,CAAGJ,UAAU,2DAAkD,uBAEhE,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sCACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,kCACb,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,2HAAkH,MAGjI,GAAAL,EAAAG,GAAA,EAACc,KAAAA,CAAGZ,UAAU,iDAAwC,gBACtD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,iCAAwB,mEAEvC,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,kCACb,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,2HAAkH,MAGjI,GAAAL,EAAAG,GAAA,EAACc,KAAAA,CAAGZ,UAAU,iDAAwC,iBACtD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,iCAAwB,qDAEvC,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,kCACb,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,2HAAkH,MAGjI,GAAAL,EAAAG,GAAA,EAACc,KAAAA,CAAGZ,UAAU,iDAAwC,eACtD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,iCAAwB,2DAEvC,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,kCACb,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,2HAAkH,MAGjI,GAAAL,EAAAG,GAAA,EAACc,KAAAA,CAAGZ,UAAU,iDAAwC,UACtD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,iCAAwB,qDAO7C,GAAAL,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,yCACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,uBACb,GAAAL,EAAAG,GAAA,EAACM,KAAAA,CAAGJ,UAAU,8CAAqC,8BAGnD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,sCAA6B,4HAG1C,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,2DACb,GAAAL,EAAAG,GAAA,EAACkB,SAAAA,CAAOhB,UAAU,iCAAwB,gBAC1C,GAAAL,EAAAG,GAAA,EAACkB,SAAAA,CAAOhB,UAAU,mCAA0B,oCAQ5D,mFC5SesB,EAAA,CACbC,MAAO,6BACP/C,MAAO,GACPC,OAAQ,GACR+C,QAAS,YACTC,KAAM,OACNC,OAAQ,eACRC,YAAa,EACbC,cAAe,QACfC,eAAgB,OAClB;;;;;GCmBa,IAAAC,EAAc,GACzBC,EACGC,OAAA,CAAQ,qBAAsB,SAC9BC,WAAY,GACZC,IAAK,GAEJ3D,EAAmB,CAAC4D,EAAkBC,KAC1C,IAAMC,EAAYC,CAAAA,EAAAA,EAAAA,UAAAA,EAChB,CAAAC,EAAiHC,QAAhH,CAAEC,MAAAA,EAAQ,eAAgBC,KAAAA,EAAO,EAAI,CAAAf,YAAAA,EAAc,CAAG,CAAAgB,oBAAAA,CAAA,CAAqB3C,UAAAA,EAAY,GAAI4C,SAAAA,CAAa,IAAAC,EAAA,CAAAN,QACvGO,CAAAA,EAAAA,EAAAA,aAAAA,EACE,MACA,CACEN,IAAAA,EACA,GAAGlB,CAAA,CACH9C,MAAOkE,EACPjE,OAAQiE,EACRhB,OAAQe,EACRd,YAAagB,EAAsBI,GAAAA,OAAOpB,GAAoBoB,OAAOL,GAAQf,EAC7E3B,UAAW,CAAC,SAAoB,UAAyBgD,MAAA,CAAzBlB,EAAYK,IAAanC,EAAW,CAAAiD,IAAA,CAAK,KACzE,GAAGJ,CAAA,EAEL,IACKT,EAASzB,GAAA,CAAI,OAAC,CAACuC,EAAKC,EAAW,CAAAZ,QAAAO,CAAAA,EAAAA,EAAAA,aAAAA,EAAcI,EAAKC,QACjDC,MAAMC,OAAA,CAAQT,GAAYA,EAAW,CAACA,EAAQ,CACpD,IAMC,OAFPP,EAAUiB,WAAA,CAAc,GAAGN,MAAA,CAAAb,GAEpBE,CACT;;;;;GC/CM,IAAApB,EAAa1C,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,aAAc,CAChD,CAAC,OAAQ,CAAEQ,EAAG,WAAYD,IAAK,UAAU,CACzC,CAAC,OAAQ,CAAEC,EAAG,gBAAiBD,IAAK,UAAU,CAC/C;;;;;GCHK,IAAAW,EAAQlB,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,QAAS,CACtC,CAAC,SAAU,CAAEgF,GAAI,KAAMC,GAAI,IAAKC,EAAG,IAAK3E,IAAK,UAAU,CACvD,CAAC,OAAQ,CAAEC,EAAG,0CAA2CD,IAAK,UAAU,CACzE;;;;;GCHK,IAAAS,EAAQhB,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,QAAS,CACtC,CAAC,SAAU,CAAEgF,GAAI,KAAMC,GAAI,KAAMC,EAAG,KAAM3E,IAAK,UAAU,CACzD,CAAC,WAAY,CAAE4E,OAAQ,mBAAoB5E,IAAK,UAAU,CAC3D;;;;;GCHK,IAAAM,EAAQb,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,QAAS,CACtC,CACE,OACA,CACEQ,EAAG,2IACHD,IAAK,QACP,EACF,CACD;;;;;GCRK,IAAAiC,EAASxC,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,SAAU,CACxC,CAAC,OAAQ,CAAEQ,EAAG,iDAAkDD,IAAK,UAAU,CAC/E,CAAC,SAAU,CAAEyE,GAAI,KAAMC,GAAI,KAAMC,EAAG,IAAK3E,IAAK,UAAU,CACzD;;;;;GCHK,IAAAY,EAAQnB,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,QAAS,CACtC,CAAC,OAAQ,CAAEQ,EAAG,4CAA6CD,IAAK,UAAU,CAC1E,CAAC,SAAU,CAAEyE,GAAI,IAAKC,GAAI,IAAKC,EAAG,IAAK3E,IAAK,SAAS,CACrD,CAAC,OAAQ,CAAEC,EAAG,6BAA8BD,IAAK,UAAU,CAC3D,CAAC,OAAQ,CAAEC,EAAG,4BAA6BD,IAAK,UAAU,CAC3D;;;;;GCLK,IAAAU,EAAMjB,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,MAAO,CAClC,CAAC,UAAW,CAAEmF,OAAQ,yCAA0C5E,IAAK,UAAU,CAChF", "sources": ["webpack://_N_E/?d482", "webpack://_N_E/../../../src/icons/briefcase.ts", "webpack://_N_E/./app/careers/page.tsx", "webpack://_N_E/../../src/defaultAttributes.ts", "webpack://_N_E/../../src/createLucideIcon.ts", "webpack://_N_E/../../../src/icons/arrow-right.ts", "webpack://_N_E/../../../src/icons/award.ts", "webpack://_N_E/../../../src/icons/clock.ts", "webpack://_N_E/../../../src/icons/heart.ts", "webpack://_N_E/../../../src/icons/map-pin.ts", "webpack://_N_E/../../../src/icons/users.ts", "webpack://_N_E/../../../src/icons/zap.ts"], "sourcesContent": ["import(/* webpackMode: \"eager\" */ \"/Volumes/Apps/Websites/streamit-main/app/careers/page.tsx\");\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Briefcase\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMjAiIGhlaWdodD0iMTQiIHg9IjIiIHk9IjciIHJ4PSIyIiByeT0iMiIgLz4KICA8cGF0aCBkPSJNMTYgMjFWNWEyIDIgMCAwIDAtMi0yaC00YTIgMiAwIDAgMC0yIDJ2MTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/briefcase\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Briefcase = createLucideIcon('Briefcase', [\n  ['rect', { width: '20', height: '14', x: '2', y: '7', rx: '2', ry: '2', key: 'eto64e' }],\n  ['path', { d: 'M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16', key: 'zwj3tp' }],\n]);\n\nexport default Briefcase;\n", "'use client'\n\nimport { Map<PERSON><PERSON>, <PERSON>, <PERSON>, Heart, Zap, Award, ArrowRight, Briefcase } from 'lucide-react'\n\n// Force dynamic rendering\nexport const dynamic = 'force-dynamic'\n\nexport default function CareersPage() {\n  const openPositions = [\n    {\n      id: 1,\n      title: 'Senior Frontend Developer',\n      department: 'Engineering',\n      location: 'San Francisco, CA / Remote',\n      type: 'Full-time',\n      experience: '5+ years',\n      description: 'Join our frontend team to build the next generation of video conferencing experiences.'\n    },\n    {\n      id: 2,\n      title: 'Product Manager',\n      department: 'Product',\n      location: 'San Francisco, CA',\n      type: 'Full-time',\n      experience: '3+ years',\n      description: 'Lead product strategy and roadmap for our core video conferencing platform.'\n    },\n    {\n      id: 3,\n      title: 'DevOps Engineer',\n      department: 'Engineering',\n      location: 'Remote',\n      type: 'Full-time',\n      experience: '4+ years',\n      description: 'Help scale our infrastructure to support millions of users worldwide.'\n    },\n    {\n      id: 4,\n      title: 'UX Designer',\n      department: 'Design',\n      location: 'San Francisco, CA / Remote',\n      type: 'Full-time',\n      experience: '3+ years',\n      description: 'Design intuitive and beautiful user experiences for our video conferencing platform.'\n    },\n    {\n      id: 5,\n      title: 'Customer Success Manager',\n      department: 'Customer Success',\n      location: 'New York, NY',\n      type: 'Full-time',\n      experience: '2+ years',\n      description: 'Help our enterprise customers succeed with StreamIt Pro and drive adoption.'\n    },\n    {\n      id: 6,\n      title: 'Security Engineer',\n      department: 'Security',\n      location: 'Remote',\n      type: 'Full-time',\n      experience: '5+ years',\n      description: 'Ensure the security and privacy of our platform and user data.'\n    }\n  ]\n\n  const benefits = [\n    {\n      icon: Heart,\n      title: 'Health & Wellness',\n      description: 'Comprehensive health insurance, dental, vision, and wellness programs'\n    },\n    {\n      icon: Clock,\n      title: 'Flexible Work',\n      description: 'Remote-first culture with flexible hours and unlimited PTO'\n    },\n    {\n      icon: Zap,\n      title: 'Growth & Learning',\n      description: 'Professional development budget and conference attendance'\n    },\n    {\n      icon: Award,\n      title: 'Equity & Compensation',\n      description: 'Competitive salary, equity package, and performance bonuses'\n    },\n    {\n      icon: Users,\n      title: 'Team Culture',\n      description: 'Collaborative environment with regular team events and offsites'\n    },\n    {\n      icon: Briefcase,\n      title: 'Equipment & Setup',\n      description: 'Top-tier equipment and home office setup allowance'\n    }\n  ]\n\n  const values = [\n    {\n      title: 'Innovation First',\n      description: 'We constantly push the boundaries of what\\'s possible in video conferencing technology.'\n    },\n    {\n      title: 'User-Centric',\n      description: 'Every decision we make is guided by what\\'s best for our users and their experience.'\n    },\n    {\n      title: 'Transparency',\n      description: 'We believe in open communication, honest feedback, and transparent processes.'\n    },\n    {\n      title: 'Diversity & Inclusion',\n      description: 'We celebrate diverse perspectives and create an inclusive environment for everyone.'\n    }\n  ]\n\n  return (\n    <>\n      {/* Animated Background */}\n      <div className=\"animated-bg\"></div>\n\n      <div className=\"min-h-screen pt-20 relative z-10\">\n        {/* Hero Section */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-7xl mx-auto text-center\">\n            <h1 className=\"text-5xl font-bold text-white mb-6 fade-in\">\n              Join Our Team\n            </h1>\n            <p className=\"text-xl text-white/80 max-w-3xl mx-auto leading-relaxed slide-up\">\n              Help us build the future of video conferencing and remote collaboration. \n              Join a team of passionate individuals making communication seamless for millions.\n            </p>\n          </div>\n        </section>\n\n        {/* Company Stats */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-7xl mx-auto\">\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8\">\n              <div className=\"glass p-6 text-center\">\n                <div className=\"text-3xl font-bold text-white mb-2\">50+</div>\n                <div className=\"text-white/60 text-sm\">Team Members</div>\n              </div>\n              <div className=\"glass p-6 text-center\">\n                <div className=\"text-3xl font-bold text-white mb-2\">15</div>\n                <div className=\"text-white/60 text-sm\">Countries</div>\n              </div>\n              <div className=\"glass p-6 text-center\">\n                <div className=\"text-3xl font-bold text-white mb-2\">10M+</div>\n                <div className=\"text-white/60 text-sm\">Users Served</div>\n              </div>\n              <div className=\"glass p-6 text-center\">\n                <div className=\"text-3xl font-bold text-white mb-2\">$50M</div>\n                <div className=\"text-white/60 text-sm\">Series B Funding</div>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* Open Positions */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-7xl mx-auto\">\n            <h2 className=\"text-3xl font-bold text-white mb-12 text-center\">Open Positions</h2>\n            \n            <div className=\"space-y-6\">\n              {openPositions.map((position) => (\n                <div key={position.id} className=\"glass p-8 hover:transform hover:scale-[1.02] transition-all duration-300\">\n                  <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6\">\n                    <div className=\"flex-1\">\n                      <div className=\"flex flex-wrap items-center gap-3 mb-4\">\n                        <h3 className=\"text-xl font-semibold text-white\">{position.title}</h3>\n                        <span className=\"bg-purple-500 text-white px-3 py-1 rounded-full text-sm\">\n                          {position.department}\n                        </span>\n                      </div>\n                      \n                      <p className=\"text-white/70 mb-4\">{position.description}</p>\n                      \n                      <div className=\"flex flex-wrap gap-4 text-sm\">\n                        <div className=\"flex items-center gap-2\">\n                          <MapPin className=\"h-4 w-4 text-white/60\" />\n                          <span className=\"text-white/60\">{position.location}</span>\n                        </div>\n                        <div className=\"flex items-center gap-2\">\n                          <Clock className=\"h-4 w-4 text-white/60\" />\n                          <span className=\"text-white/60\">{position.type}</span>\n                        </div>\n                        <div className=\"flex items-center gap-2\">\n                          <Briefcase className=\"h-4 w-4 text-white/60\" />\n                          <span className=\"text-white/60\">{position.experience}</span>\n                        </div>\n                      </div>\n                    </div>\n                    \n                    <div className=\"flex flex-col sm:flex-row gap-3\">\n                      <button className=\"btn-secondary\">Learn More</button>\n                      <button className=\"btn-primary flex items-center gap-2\">\n                        Apply Now\n                        <ArrowRight className=\"h-4 w-4\" />\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </section>\n\n        {/* Benefits */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-7xl mx-auto\">\n            <h2 className=\"text-3xl font-bold text-white mb-12 text-center\">Why Work With Us</h2>\n            \n            <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n              {benefits.map((benefit, index) => (\n                <div key={index} className=\"glass p-8 text-center\">\n                  <benefit.icon className=\"h-12 w-12 text-purple-400 mx-auto mb-4\" />\n                  <h3 className=\"text-xl font-semibold text-white mb-3\">{benefit.title}</h3>\n                  <p className=\"text-white/70\">{benefit.description}</p>\n                </div>\n              ))}\n            </div>\n          </div>\n        </section>\n\n        {/* Company Values */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-7xl mx-auto\">\n            <h2 className=\"text-3xl font-bold text-white mb-12 text-center\">Our Values</h2>\n            \n            <div className=\"grid md:grid-cols-2 gap-8\">\n              {values.map((value, index) => (\n                <div key={index} className=\"glass p-8\">\n                  <h3 className=\"text-xl font-semibold text-white mb-4\">{value.title}</h3>\n                  <p className=\"text-white/70\">{value.description}</p>\n                </div>\n              ))}\n            </div>\n          </div>\n        </section>\n\n        {/* Application Process */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-7xl mx-auto\">\n            <h2 className=\"text-3xl font-bold text-white mb-12 text-center\">Our Hiring Process</h2>\n            \n            <div className=\"grid md:grid-cols-4 gap-8\">\n              <div className=\"glass p-6 text-center\">\n                <div className=\"w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center text-white font-bold text-lg mx-auto mb-4\">\n                  1\n                </div>\n                <h3 className=\"text-lg font-semibold text-white mb-2\">Application</h3>\n                <p className=\"text-white/70 text-sm\">Submit your application and resume through our careers page</p>\n              </div>\n              <div className=\"glass p-6 text-center\">\n                <div className=\"w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center text-white font-bold text-lg mx-auto mb-4\">\n                  2\n                </div>\n                <h3 className=\"text-lg font-semibold text-white mb-2\">Phone Screen</h3>\n                <p className=\"text-white/70 text-sm\">Initial conversation with our recruiting team</p>\n              </div>\n              <div className=\"glass p-6 text-center\">\n                <div className=\"w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center text-white font-bold text-lg mx-auto mb-4\">\n                  3\n                </div>\n                <h3 className=\"text-lg font-semibold text-white mb-2\">Interviews</h3>\n                <p className=\"text-white/70 text-sm\">Technical and cultural fit interviews with the team</p>\n              </div>\n              <div className=\"glass p-6 text-center\">\n                <div className=\"w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center text-white font-bold text-lg mx-auto mb-4\">\n                  4\n                </div>\n                <h3 className=\"text-lg font-semibold text-white mb-2\">Offer</h3>\n                <p className=\"text-white/70 text-sm\">Reference checks and offer discussion</p>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* CTA Section */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-4xl mx-auto text-center\">\n            <div className=\"glass p-12\">\n              <h2 className=\"text-3xl font-bold text-white mb-4\">\n                Don't See the Right Role?\n              </h2>\n              <p className=\"text-xl text-white/70 mb-8\">\n                We're always looking for talented individuals. Send us your resume and we'll keep you in mind for future opportunities.\n              </p>\n              <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n                <button className=\"btn-primary px-8 py-3\">Send Resume</button>\n                <button className=\"btn-secondary px-8 py-3\">Join Talent Pool</button>\n              </div>\n            </div>\n          </div>\n        </section>\n      </div>\n    </>\n  )\n}\n", "export default {\n  xmlns: 'http://www.w3.org/2000/svg',\n  width: 24,\n  height: 24,\n  viewBox: '0 0 24 24',\n  fill: 'none',\n  stroke: 'currentColor',\n  strokeWidth: 2,\n  strokeLinecap: 'round',\n  strokeLinejoin: 'round',\n};\n", "import {\n  forwardRef,\n  createElement,\n  ReactSVG,\n  SVGProps,\n  ForwardRefExoticComponent,\n  RefAttributes,\n} from 'react';\nimport defaultAttributes from './defaultAttributes';\n\nexport type IconNode = [elementName: keyof ReactSVG, attrs: Record<string, string>][];\n\nexport type SVGAttributes = Partial<SVGProps<SVGSVGElement>>;\ntype ComponentAttributes = RefAttributes<SVGSVGElement> & SVGAttributes;\n\nexport interface LucideProps extends ComponentAttributes {\n  size?: string | number;\n  absoluteStrokeWidth?: boolean;\n}\n\nexport type LucideIcon = ForwardRefExoticComponent<LucideProps>;\n/**\n * Converts string to KebabCase\n * Copied from scripts/helper. If anyone knows how to properly import it here\n * then please fix it.\n *\n * @param {string} string\n * @returns {string} A kebabized string\n */\nexport const toKebabCase = (string: string) =>\n  string\n    .replace(/([a-z0-9])([A-Z])/g, '$1-$2')\n    .toLowerCase()\n    .trim();\n\nconst createLucideIcon = (iconName: string, iconNode: IconNode): LucideIcon => {\n  const Component = forwardRef<SVGSVGElement, LucideProps>(\n    ({ color = 'currentColor', size = 24, strokeWidth = 2, absoluteStrokeWidth, className = '', children, ...rest }, ref) =>\n      createElement(\n        'svg',\n        {\n          ref,\n          ...defaultAttributes,\n          width: size,\n          height: size,\n          stroke: color,\n          strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n          className: ['lucide', `lucide-${toKebabCase(iconName)}`, className].join(' '),\n          ...rest,\n        },\n        [\n          ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n          ...(Array.isArray(children) ? children : [children]),\n        ]\n      )\n  );\n\n  Component.displayName = `${iconName}`;\n\n  return Component;\n};\n\nexport default createLucideIcon;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ArrowRight\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSAxMmgxNCIgLz4KICA8cGF0aCBkPSJtMTIgNSA3IDctNyA3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/arrow-right\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowRight = createLucideIcon('ArrowRight', [\n  ['path', { d: 'M5 12h14', key: '1ays0h' }],\n  ['path', { d: 'm12 5 7 7-7 7', key: 'xquz4c' }],\n]);\n\nexport default ArrowRight;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Award\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjgiIHI9IjYiIC8+CiAgPHBhdGggZD0iTTE1LjQ3NyAxMi44OSAxNyAyMmwtNS0zLTUgMyAxLjUyMy05LjExIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/award\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Award = createLucideIcon('Award', [\n  ['circle', { cx: '12', cy: '8', r: '6', key: '1vp47v' }],\n  ['path', { d: 'M15.477 12.89 17 22l-5-3-5 3 1.523-9.11', key: 'em7aur' }],\n]);\n\nexport default Award;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Clock\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8cG9seWxpbmUgcG9pbnRzPSIxMiA2IDEyIDEyIDE2IDE0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/clock\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Clock = createLucideIcon('Clock', [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['polyline', { points: '12 6 12 12 16 14', key: '68esgv' }],\n]);\n\nexport default Clock;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Heart\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTkgMTRjMS40OS0xLjQ2IDMtMy4yMSAzLTUuNUE1LjUgNS41IDAgMCAwIDE2LjUgM2MtMS43NiAwLTMgLjUtNC41IDItMS41LTEuNS0yLjc0LTItNC41LTJBNS41IDUuNSAwIDAgMCAyIDguNWMwIDIuMyAxLjUgNC4wNSAzIDUuNWw3IDdaIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/heart\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Heart = createLucideIcon('Heart', [\n  [\n    'path',\n    {\n      d: 'M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z',\n      key: 'c3ymky',\n    },\n  ],\n]);\n\nexport default Heart;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name MapPin\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjAgMTBjMCA2LTggMTItOCAxMnMtOC02LTgtMTJhOCA4IDAgMCAxIDE2IDBaIiAvPgogIDxjaXJjbGUgY3g9IjEyIiBjeT0iMTAiIHI9IjMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/map-pin\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MapPin = createLucideIcon('MapPin', [\n  ['path', { d: 'M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z', key: '2oe9fu' }],\n  ['circle', { cx: '12', cy: '10', r: '3', key: 'ilqhr7' }],\n]);\n\nexport default MapPin;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Users\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgMjF2LTJhNCA0IDAgMCAwLTQtNEg2YTQgNCAwIDAgMC00IDR2MiIgLz4KICA8Y2lyY2xlIGN4PSI5IiBjeT0iNyIgcj0iNCIgLz4KICA8cGF0aCBkPSJNMjIgMjF2LTJhNCA0IDAgMCAwLTMtMy44NyIgLz4KICA8cGF0aCBkPSJNMTYgMy4xM2E0IDQgMCAwIDEgMCA3Ljc1IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/users\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Users = createLucideIcon('Users', [\n  ['path', { d: 'M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2', key: '1yyitq' }],\n  ['circle', { cx: '9', cy: '7', r: '4', key: 'nufk8' }],\n  ['path', { d: 'M22 21v-2a4 4 0 0 0-3-3.87', key: 'kshegd' }],\n  ['path', { d: 'M16 3.13a4 4 0 0 1 0 7.75', key: '1da9ce' }],\n]);\n\nexport default Users;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Zap\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWdvbiBwb2ludHM9IjEzIDIgMyAxNCAxMiAxNCAxMSAyMiAyMSAxMCAxMiAxMCAxMyAyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/zap\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Zap = createLucideIcon('Zap', [\n  ['polygon', { points: '13 2 3 14 12 14 11 22 21 10 12 10 13 2', key: '45s27k' }],\n]);\n\nexport default Zap;\n"], "names": ["Promise", "resolve", "then", "__webpack_require__", "bind", "Briefcase", "createLucideIcon", "width", "height", "x", "y", "rx", "ry", "key", "d", "dynamic", "CareersPage", "benefits", "icon", "Heart", "title", "description", "Clock", "Zap", "Award", "Users", "jsx_runtime", "jsxs", "Fragment", "jsx", "div", "className", "section", "h1", "p", "h2", "openPositions", "id", "department", "location", "type", "experience", "map", "h3", "position", "span", "MapPin", "button", "ArrowRight", "benefit", "index", "values", "value", "defaultAttributes", "xmlns", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "toKebabCase", "string", "replace", "toLowerCase", "trim", "iconName", "iconNode", "Component", "forwardRef", "param", "ref", "color", "size", "absoluteStrokeWidth", "children", "rest", "createElement", "Number", "concat", "join", "tag", "attrs", "Array", "isArray", "displayName", "cx", "cy", "r", "points"], "sourceRoot": ""}