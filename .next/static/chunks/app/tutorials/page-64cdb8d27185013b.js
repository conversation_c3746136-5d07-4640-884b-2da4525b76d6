(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[171],{4421:function(e,t,i){Promise.resolve().then(i.bind(i,8538))},8538:function(e,t,i){"use strict";i.r(t),i.d(t,{default:function(){return m}});var s=i(7437),a=i(8954),n=i(5302),r=i(8400),l=i(8094),c=i(933),d=i(2022),o=i(9338),x=i(7592);function m(){let e=[{id:1,title:"Getting Started with StreamIt Pro",description:"Learn the basics of creating your first meeting and inviting participants",duration:"5:30",difficulty:"Beginner",views:"12.5K",rating:4.9,category:"Getting Started",thumbnail:"/api/placeholder/320/180"},{id:2,title:"Advanced Screen Sharing Techniques",description:"Master screen sharing with application-specific sharing and remote control",duration:"8:45",difficulty:"Intermediate",views:"8.2K",rating:4.8,category:"Advanced Features",thumbnail:"/api/placeholder/320/180"},{id:3,title:"Setting Up Perfect Audio and Video",description:"Optimize your audio and video settings for professional-quality calls",duration:"6:15",difficulty:"Beginner",views:"15.3K",rating:4.9,category:"Setup",thumbnail:"/api/placeholder/320/180"},{id:4,title:"Meeting Security Best Practices",description:"Keep your meetings secure with waiting rooms, passwords, and more",duration:"7:20",difficulty:"Intermediate",views:"6.8K",rating:4.7,category:"Security",thumbnail:"/api/placeholder/320/180"},{id:5,title:"Recording and Managing Meeting Content",description:"Learn how to record meetings and organize your recorded content",duration:"9:10",difficulty:"Intermediate",views:"9.1K",rating:4.8,category:"Recording",thumbnail:"/api/placeholder/320/180"},{id:6,title:"Mobile App Complete Guide",description:"Everything you need to know about using StreamIt Pro on mobile devices",duration:"11:30",difficulty:"Beginner",views:"7.4K",rating:4.6,category:"Mobile",thumbnail:"/api/placeholder/320/180"}],t=[{icon:a.Z,title:"Perfect Lighting",tip:"Position yourself facing a window or use a ring light for better video quality"},{icon:n.Z,title:"Dual Monitors",tip:"Use a second monitor to keep notes and materials visible during calls"},{icon:r.Z,title:"Mobile Backup",tip:"Keep the mobile app as a backup option for important meetings"}];return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"animated-bg"}),(0,s.jsxs)("div",{className:"min-h-screen pt-20 relative z-10",children:[(0,s.jsx)("section",{className:"py-20 px-4",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto text-center",children:[(0,s.jsx)("h1",{className:"text-5xl font-bold text-white mb-6 fade-in",children:"Video Tutorials"}),(0,s.jsx)("p",{className:"text-xl text-white/80 max-w-3xl mx-auto leading-relaxed slide-up",children:"Learn how to use StreamIt Pro with our comprehensive video tutorials. From basic setup to advanced features, we've got you covered."})]})}),(0,s.jsx)("section",{className:"py-10 px-4",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto",children:(0,s.jsx)("div",{className:"flex flex-wrap justify-center gap-3 mb-12",children:["All","Getting Started","Advanced Features","Setup","Security","Recording","Mobile"].map((e,t)=>(0,s.jsx)("button",{className:"px-6 py-2 rounded-full text-sm transition-colors ".concat(0===t?"bg-purple-500 text-white":"bg-white/10 text-white/70 hover:bg-white/20"),children:e},t))})})}),(0,s.jsx)("section",{className:"py-10 px-4",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto",children:(0,s.jsx)("div",{className:"glass p-8 md:p-12",children:(0,s.jsxs)("div",{className:"grid lg:grid-cols-2 gap-8 items-center",children:[(0,s.jsx)("div",{className:"relative",children:(0,s.jsxs)("div",{className:"w-full h-64 md:h-80 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-lg flex items-center justify-center relative overflow-hidden",children:[(0,s.jsx)(l.Z,{className:"h-16 w-16 text-white/80 hover:text-white cursor-pointer transition-colors"}),(0,s.jsx)("div",{className:"absolute top-4 left-4 bg-red-500 text-white px-2 py-1 rounded text-sm font-semibold",children:"FEATURED"}),(0,s.jsx)("div",{className:"absolute bottom-4 right-4 bg-black/50 text-white px-2 py-1 rounded text-sm",children:e[0].duration})]})}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[(0,s.jsx)("span",{className:"bg-green-500 text-white px-3 py-1 rounded-full text-sm",children:e[0].difficulty}),(0,s.jsx)("span",{className:"text-white/60 text-sm",children:e[0].category})]}),(0,s.jsx)("h2",{className:"text-3xl font-bold text-white mb-4",children:e[0].title}),(0,s.jsx)("p",{className:"text-white/70 mb-6",children:e[0].description}),(0,s.jsxs)("div",{className:"flex items-center gap-6 mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(c.Z,{className:"h-4 w-4 text-white/60"}),(0,s.jsx)("span",{className:"text-white/60 text-sm",children:e[0].duration})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(d.Z,{className:"h-4 w-4 text-white/60"}),(0,s.jsxs)("span",{className:"text-white/60 text-sm",children:[e[0].views," views"]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(o.Z,{className:"h-4 w-4 text-yellow-400 fill-current"}),(0,s.jsx)("span",{className:"text-white/60 text-sm",children:e[0].rating})]})]}),(0,s.jsxs)("button",{className:"btn-primary flex items-center gap-2",children:[(0,s.jsx)(l.Z,{className:"h-4 w-4"}),"Watch Tutorial"]})]})]})})})}),(0,s.jsx)("section",{className:"py-20 px-4",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-white mb-12 text-center",children:"All Tutorials"}),(0,s.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8",children:e.slice(1).map(e=>(0,s.jsxs)("div",{className:"glass p-6 hover:transform hover:scale-105 transition-all duration-300",children:[(0,s.jsx)("div",{className:"relative mb-4",children:(0,s.jsxs)("div",{className:"w-full h-48 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-lg flex items-center justify-center relative overflow-hidden",children:[(0,s.jsx)(l.Z,{className:"h-12 w-12 text-white/80 hover:text-white cursor-pointer transition-colors"}),(0,s.jsx)("div",{className:"absolute bottom-2 right-2 bg-black/50 text-white px-2 py-1 rounded text-xs",children:e.duration})]})}),(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[(0,s.jsx)("span",{className:"px-2 py-1 rounded-full text-xs ".concat("Beginner"===e.difficulty?"bg-green-500 text-white":"bg-orange-500 text-white"),children:e.difficulty}),(0,s.jsx)("span",{className:"text-purple-400 text-sm",children:e.category})]}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:e.title}),(0,s.jsx)("p",{className:"text-white/70 mb-4 text-sm",children:e.description}),(0,s.jsx)("div",{className:"flex items-center justify-between mb-4",children:(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(d.Z,{className:"h-3 w-3 text-white/60"}),(0,s.jsx)("span",{className:"text-white/60 text-xs",children:e.views})]}),(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(o.Z,{className:"h-3 w-3 text-yellow-400 fill-current"}),(0,s.jsx)("span",{className:"text-white/60 text-xs",children:e.rating})]})]})}),(0,s.jsxs)("button",{className:"btn-secondary w-full flex items-center justify-center gap-2",children:[(0,s.jsx)(l.Z,{className:"h-4 w-4"}),"Watch Now"]})]},e.id))})]})}),(0,s.jsx)("section",{className:"py-20 px-4",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-white mb-12 text-center",children:"Quick Tips"}),(0,s.jsx)("div",{className:"grid md:grid-cols-3 gap-8",children:t.map((e,t)=>(0,s.jsxs)("div",{className:"glass p-6 text-center",children:[(0,s.jsx)(e.icon,{className:"h-12 w-12 text-purple-400 mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-3",children:e.title}),(0,s.jsx)("p",{className:"text-white/70 text-sm",children:e.tip})]},t))})]})}),(0,s.jsx)("section",{className:"py-20 px-4",children:(0,s.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,s.jsxs)("div",{className:"glass p-8",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-white mb-6 text-center",children:"Recommended Learning Path"}),(0,s.jsx)("div",{className:"space-y-4",children:["Getting Started with StreamIt Pro","Setting Up Perfect Audio and Video","Advanced Screen Sharing Techniques","Meeting Security Best Practices","Recording and Managing Meeting Content"].map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center gap-4 p-4 bg-white/5 rounded-lg",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center text-white font-semibold text-sm",children:t+1}),(0,s.jsx)("span",{className:"text-white flex-1",children:e}),(0,s.jsx)(x.Z,{className:"h-4 w-4 text-white/60"})]},t))}),(0,s.jsx)("div",{className:"text-center mt-8",children:(0,s.jsx)("button",{className:"btn-primary",children:"Start Learning Path"})})]})})}),(0,s.jsx)("section",{className:"py-20 px-4",children:(0,s.jsx)("div",{className:"max-w-4xl mx-auto text-center",children:(0,s.jsxs)("div",{className:"glass p-12",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-white mb-4",children:"Ready to Become a StreamIt Pro Expert?"}),(0,s.jsx)("p",{className:"text-xl text-white/70 mb-8",children:"Start with our beginner tutorials and work your way up to advanced features"}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,s.jsx)("button",{className:"btn-primary px-8 py-3",children:"Start Learning"}),(0,s.jsx)("button",{className:"btn-secondary px-8 py-3",children:"Browse All Tutorials"})]})]})})})]})]})}},1066:function(e,t,i){"use strict";i.d(t,{Z:function(){return r}});var s=i(2265),a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),r=(e,t)=>{let i=(0,s.forwardRef)((i,r)=>{let{color:l="currentColor",size:c=24,strokeWidth:d=2,absoluteStrokeWidth:o,className:x="",children:m,...h}=i;return(0,s.createElement)("svg",{ref:r,...a,width:c,height:c,stroke:l,strokeWidth:o?24*Number(d)/Number(c):d,className:["lucide","lucide-".concat(n(e)),x].join(" "),...h},[...t.map(e=>{let[t,i]=e;return(0,s.createElement)(t,i)}),...Array.isArray(m)?m:[m]])});return i.displayName="".concat(e),i}},7592:function(e,t,i){"use strict";i.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,i(1066).Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},933:function(e,t,i){"use strict";i.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,i(1066).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},5302:function(e,t,i){"use strict";i.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,i(1066).Z)("Monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]])},8094:function(e,t,i){"use strict";i.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,i(1066).Z)("Play",[["polygon",{points:"5 3 19 12 5 21 5 3",key:"191637"}]])},8400:function(e,t,i){"use strict";i.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,i(1066).Z)("Smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]])},9338:function(e,t,i){"use strict";i.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,i(1066).Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},2022:function(e,t,i){"use strict";i.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,i(1066).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},8954:function(e,t,i){"use strict";i.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,i(1066).Z)("Video",[["path",{d:"m22 8-6 4 6 4V8Z",key:"50v9me"}],["rect",{width:"14",height:"12",x:"2",y:"6",rx:"2",ry:"2",key:"1rqjg6"}]])}},function(e){e.O(0,[971,23,744],function(){return e(e.s=4421)}),_N_E=e.O()}]);