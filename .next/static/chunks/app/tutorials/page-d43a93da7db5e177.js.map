{"version": 3, "file": "static/chunks/app/tutorials/page-d43a93da7db5e177.js", "mappings": "oFAAAA,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA;;;;;GCaM,IAAAE,EAAOC,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,OAAQ,CACpC,CACE,UACA,CACEC,OACE,iGACFC,IAAK,QACP,EACF,CACD,gBCjBM,IAAMC,EAAU,gBAER,SAASC,IACtB,IAAMC,EAAY,CAChB,CACEC,GAAI,EACJC,MAAO,oCACPC,YAAa,4EACbC,SAAU,OACVC,WAAY,WACZC,MAAO,QACPC,OAAQ,IACRC,SAAU,kBACVC,UAAW,0BACb,EACA,CACER,GAAI,EACJC,MAAO,qCACPC,YAAa,6EACbC,SAAU,OACVC,WAAY,eACZC,MAAO,OACPC,OAAQ,IACRC,SAAU,oBACVC,UAAW,0BACb,EACA,CACER,GAAI,EACJC,MAAO,qCACPC,YAAa,wEACbC,SAAU,OACVC,WAAY,WACZC,MAAO,QACPC,OAAQ,IACRC,SAAU,QACVC,UAAW,0BACb,EACA,CACER,GAAI,EACJC,MAAO,kCACPC,YAAa,oEACbC,SAAU,OACVC,WAAY,eACZC,MAAO,OACPC,OAAQ,IACRC,SAAU,WACVC,UAAW,0BACb,EACA,CACER,GAAI,EACJC,MAAO,yCACPC,YAAa,kEACbC,SAAU,OACVC,WAAY,eACZC,MAAO,OACPC,OAAQ,IACRC,SAAU,YACVC,UAAW,0BACb,EACA,CACER,GAAI,EACJC,MAAO,4BACPC,YAAa,yEACbC,SAAU,QACVC,WAAY,WACZC,MAAO,OACPC,OAAQ,IACRC,SAAU,SACVC,UAAW,0BACb,EACD,CAIKC,EAAY,CAChB,CACEC,KAAMC,EAAAA,CAAKA,CACXV,MAAO,mBACPW,IAAK,gFACP,EACA,CACEF,KAAMG,EAAAA,CAAOA,CACbZ,MAAO,gBACPW,IAAK,uEACP,EACA,CACEF,KAAMI,EAAAA,CAAUA,CAChBb,MAAO,gBACPW,IAAK,+DACP,EACD,CAED,MACE,GAAAG,EAAAC,IAAA,EAAAD,EAAAE,QAAA,YAEE,GAAAF,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,gBAEf,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,6CAEb,GAAAL,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,0CACb,GAAAL,EAAAG,GAAA,EAACI,KAAAA,CAAGF,UAAU,sDAA6C,oBAG3D,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,4EAAmE,6IAQpF,GAAAL,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,6BACb,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,qDACZI,CA3CO,MAAO,kBAAmB,oBAAqB,QAAS,WAAY,YAAa,SAAS,CA2CtFC,GAAG,CAAC,CAAClB,EAAUmB,IACzB,GAAAX,EAAAG,GAAA,EAACS,SAAAA,CAECP,UAAW,oDAIVQ,MAAA,CAHCF,IAAAA,EACI,2BACA,wDAGLnB,GAPImB,UAef,GAAAX,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,6BACb,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,6BACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,mDACb,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,oBACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,yJACb,GAAAL,EAAAG,GAAA,EAACW,EAAAA,CAAIA,CAAAA,CAACT,UAAU,8EAChB,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,+FAAsF,aAGrG,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,sFACZrB,CAAS,CAAC,EAAE,CAACI,QAAQ,QAI5B,GAAAY,EAAAC,IAAA,EAACG,MAAAA,WACC,GAAAJ,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,yCACb,GAAAL,EAAAG,GAAA,EAACY,OAAAA,CAAKV,UAAU,kEACbrB,CAAS,CAAC,EAAE,CAACK,UAAU,GAE1B,GAAAW,EAAAG,GAAA,EAACY,OAAAA,CAAKV,UAAU,iCAAyBrB,CAAS,CAAC,EAAE,CAACQ,QAAQ,MAEhE,GAAAQ,EAAAG,GAAA,EAACa,KAAAA,CAAGX,UAAU,8CAAsCrB,CAAS,CAAC,EAAE,CAACE,KAAK,GACtE,GAAAc,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,8BAAsBrB,CAAS,CAAC,EAAE,CAACG,WAAW,GAC3D,GAAAa,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,yCACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,oCACb,GAAAL,EAAAG,GAAA,EAACc,EAAAA,CAAKA,CAAAA,CAACZ,UAAU,0BACjB,GAAAL,EAAAG,GAAA,EAACY,OAAAA,CAAKV,UAAU,iCAAyBrB,CAAS,CAAC,EAAE,CAACI,QAAQ,MAEhE,GAAAY,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,oCACb,GAAAL,EAAAG,GAAA,EAACe,EAAAA,CAAIA,CAAAA,CAACb,UAAU,0BAChB,GAAAL,EAAAC,IAAA,EAACc,OAAAA,CAAKV,UAAU,kCAAyBrB,CAAS,CAAC,EAAE,CAACM,KAAK,CAAC,eAE9D,GAAAU,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,oCACb,GAAAL,EAAAG,GAAA,EAACzB,EAAIA,CAAC2B,UAAU,yCAChB,GAAAL,EAAAG,GAAA,EAACY,OAAAA,CAAKV,UAAU,iCAAyBrB,CAAS,CAAC,EAAE,CAACO,MAAM,SAGhE,GAAAS,EAAAC,IAAA,EAACW,SAAAA,CAAOP,UAAU,gDAChB,GAAAL,EAAAG,GAAA,EAACW,EAAAA,CAAIA,CAAAA,CAACT,UAAU,YAAY,gCAUxC,GAAAL,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,8BACb,GAAAL,EAAAG,GAAA,EAACa,KAAAA,CAAGX,UAAU,2DAAkD,kBAEhE,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,oDACZrB,EAAUmC,KAAK,CAAC,GAAGT,GAAG,CAAC,GACtB,GAAAV,EAAAC,IAAA,EAACG,MAAAA,CAAsBC,UAAU,kFAC/B,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,yBACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,iJACb,GAAAL,EAAAG,GAAA,EAACW,EAAAA,CAAIA,CAAAA,CAACT,UAAU,8EAChB,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,sFACZe,EAAShC,QAAQ,QAKxB,GAAAY,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,yCACb,GAAAL,EAAAG,GAAA,EAACY,OAAAA,CAAKV,UAAW,kCAIhBQ,MAAA,CAHCO,aAAAA,EAAS/B,UAAU,CACf,0BACA,qCAEH+B,EAAS/B,UAAU,GAEtB,GAAAW,EAAAG,GAAA,EAACY,OAAAA,CAAKV,UAAU,mCAA2Be,EAAS5B,QAAQ,MAG9D,GAAAQ,EAAAG,GAAA,EAACkB,KAAAA,CAAGhB,UAAU,iDAAyCe,EAASlC,KAAK,GACrE,GAAAc,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,sCAA8Be,EAASjC,WAAW,GAE/D,GAAAa,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,kDACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,oCACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,oCACb,GAAAL,EAAAG,GAAA,EAACe,EAAAA,CAAIA,CAAAA,CAACb,UAAU,0BAChB,GAAAL,EAAAG,GAAA,EAACY,OAAAA,CAAKV,UAAU,iCAAyBe,EAAS9B,KAAK,MAEzD,GAAAU,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,oCACb,GAAAL,EAAAG,GAAA,EAACzB,EAAIA,CAAC2B,UAAU,yCAChB,GAAAL,EAAAG,GAAA,EAACY,OAAAA,CAAKV,UAAU,iCAAyBe,EAAS7B,MAAM,WAK9D,GAAAS,EAAAC,IAAA,EAACW,SAAAA,CAAOP,UAAU,wEAChB,GAAAL,EAAAG,GAAA,EAACW,EAAAA,CAAIA,CAAAA,CAACT,UAAU,YAAY,iBAtCtBe,EAASnC,EAAE,UAgD7B,GAAAe,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,8BACb,GAAAL,EAAAG,GAAA,EAACa,KAAAA,CAAGX,UAAU,2DAAkD,eAEhE,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,qCACZX,EAAUgB,GAAG,CAAC,CAACb,EAAKc,IACnB,GAAAX,EAAAC,IAAA,EAACG,MAAAA,CAAgBC,UAAU,kCACzB,GAAAL,EAAAG,GAAA,EAACN,EAAIF,IAAI,EAACU,UAAU,2CACpB,GAAAL,EAAAG,GAAA,EAACkB,KAAAA,CAAGhB,UAAU,iDAAyCR,EAAIX,KAAK,GAChE,GAAAc,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,iCAAyBR,EAAIA,GAAG,KAHrCc,WAWlB,GAAAX,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,6BACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sBACb,GAAAL,EAAAG,GAAA,EAACa,KAAAA,CAAGX,UAAU,0DAAiD,8BAG/D,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,qBACZ,CACC,oCACA,qCACA,qCACA,kCACA,yCACD,CAACK,GAAG,CAAC,CAACY,EAAMX,IACX,GAAAX,EAAAC,IAAA,EAACG,MAAAA,CAAgBC,UAAU,8DACzB,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,gHACZM,EAAQ,IAEX,GAAAX,EAAAG,GAAA,EAACY,OAAAA,CAAKV,UAAU,6BAAqBiB,IACrC,GAAAtB,EAAAG,GAAA,EAACoB,EAAAA,CAAYA,CAAAA,CAAClB,UAAU,4BALhBM,MASd,GAAAX,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,4BACb,GAAAL,EAAAG,GAAA,EAACS,SAAAA,CAAOP,UAAU,uBAAc,iCAOxC,GAAAL,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,yCACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,uBACb,GAAAL,EAAAG,GAAA,EAACa,KAAAA,CAAGX,UAAU,8CAAqC,2CAGnD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,sCAA6B,gFAG1C,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,2DACb,GAAAL,EAAAG,GAAA,EAACS,SAAAA,CAAOP,UAAU,iCAAwB,mBAC1C,GAAAL,EAAAG,GAAA,EAACS,SAAAA,CAAOP,UAAU,mCAA0B,wCAQ5D,mFCnTemB,EAAA,CACbC,MAAO,6BACPC,MAAO,GACPC,OAAQ,GACRC,QAAS,YACTC,KAAM,OACNC,OAAQ,eACRC,YAAa,EACbC,cAAe,QACfC,eAAgB,OAClB;;;;;GCmBa,IAAAC,EAAc,GACzBC,EACGC,OAAA,CAAQ,qBAAsB,SAC9BC,WAAY,GACZC,IAAK,GAEJ3D,EAAmB,CAAC4D,EAAkBC,KAC1C,IAAMC,EAAYC,CAAAA,EAAAA,EAAAA,UAAAA,EAChB,CAAAC,EAAiHC,QAAhH,CAAEC,MAAAA,EAAQ,eAAgBC,KAAAA,EAAO,EAAI,CAAAf,YAAAA,EAAc,CAAG,CAAAgB,oBAAAA,CAAA,CAAqB1C,UAAAA,EAAY,GAAI2C,SAAAA,CAAa,IAAAC,EAAA,CAAAN,QACvGO,CAAAA,EAAAA,EAAAA,aAAAA,EACE,MACA,CACEN,IAAAA,EACA,GAAGpB,CAAA,CACHE,MAAOoB,EACPnB,OAAQmB,EACRhB,OAAQe,EACRd,YAAagB,EAAsBI,GAAAA,OAAOpB,GAAoBoB,OAAOL,GAAQf,EAC7E1B,UAAW,CAAC,SAAoB,UAAyBQ,MAAA,CAAzBqB,EAAYK,IAAalC,EAAW,CAAA+C,IAAA,CAAK,KACzE,GAAGH,CAAA,EAEL,IACKT,EAAS9B,GAAA,CAAI,OAAC,CAAC2C,EAAKC,EAAW,CAAAX,QAAAO,CAAAA,EAAAA,EAAAA,aAAAA,EAAcG,EAAKC,QACjDC,MAAMC,OAAA,CAAQR,GAAYA,EAAW,CAACA,EAAQ,CACpD,IAMC,OAFPP,EAAUgB,WAAA,CAAc,GAAG5C,MAAA,CAAA0B,GAEpBE,CACT;;;;;GC/CM,IAAAlB,EAAe5C,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,eAAgB,CACpD,CAAC,OAAQ,CAAE+E,EAAG,gBAAiB7E,IAAK,UAAU,CAC/C;;;;;GCFK,IAAAoC,EAAQtC,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,QAAS,CACtC,CAAC,SAAU,CAAEgF,GAAI,KAAMC,GAAI,KAAMC,EAAG,KAAMhF,IAAK,UAAU,CACzD,CAAC,WAAY,CAAED,OAAQ,mBAAoBC,IAAK,UAAU,CAC3D;;;;;GCHK,IAAAiB,EAAUnB,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,UAAW,CAC1C,CAAC,OAAQ,CAAE+C,MAAO,KAAMC,OAAQ,KAAMmC,EAAG,IAAKC,EAAG,IAAKC,GAAI,IAAKnF,IAAK,UAAU,CAC9E,CAAC,OAAQ,CAAEoF,GAAI,IAAKC,GAAI,KAAMC,GAAI,KAAMC,GAAI,KAAMvF,IAAK,UAAU,CACjE,CAAC,OAAQ,CAAEoF,GAAI,KAAMC,GAAI,KAAMC,GAAI,KAAMC,GAAI,KAAMvF,IAAK,UAAU,CACnE;;;;;GCJK,IAAAiC,EAAOnC,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,OAAQ,CACpC,CAAC,UAAW,CAAEC,OAAQ,qBAAsBC,IAAK,UAAU,CAC5D;;;;;GCFK,IAAAkB,EAAapB,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,aAAc,CAChD,CAAC,OAAQ,CAAE+C,MAAO,KAAMC,OAAQ,KAAMmC,EAAG,IAAKC,EAAG,IAAKC,GAAI,IAAKK,GAAI,IAAKxF,IAAK,UAAU,CACvF,CAAC,OAAQ,CAAE6E,EAAG,aAAc7E,IAAK,UAAU,CAC5C;;;;;GCHK,IAAAqC,EAAOvC,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,OAAQ,CACpC,CAAC,OAAQ,CAAE+E,EAAG,4CAA6C7E,IAAK,UAAU,CAC1E,CAAC,SAAU,CAAE8E,GAAI,KAAMC,GAAI,IAAKC,EAAG,IAAKhF,IAAK,UAAU,CACxD;;;;;GCHK,IAAAe,EAAQjB,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,QAAS,CACtC,CAAC,OAAQ,CAAE+E,EAAG,mBAAoB7E,IAAK,UAAU,CACjD,CAAC,OAAQ,CAAE6C,MAAO,KAAMC,OAAQ,KAAMmC,EAAG,IAAKC,EAAG,IAAKC,GAAI,IAAKK,GAAI,IAAKxF,IAAK,UAAU,CACxF", "sources": ["webpack://_N_E/?2b05", "webpack://_N_E/../../../src/icons/star.ts", "webpack://_N_E/./app/tutorials/page.tsx", "webpack://_N_E/../../src/defaultAttributes.ts", "webpack://_N_E/../../src/createLucideIcon.ts", "webpack://_N_E/../../../src/icons/chevron-right.ts", "webpack://_N_E/../../../src/icons/clock.ts", "webpack://_N_E/../../../src/icons/monitor.ts", "webpack://_N_E/../../../src/icons/play.ts", "webpack://_N_E/../../../src/icons/smartphone.ts", "webpack://_N_E/../../../src/icons/user.ts", "webpack://_N_E/../../../src/icons/video.ts"], "sourcesContent": ["import(/* webpackMode: \"eager\" */ \"/Volumes/Apps/Websites/streamit-main/app/tutorials/page.tsx\");\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Star\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWdvbiBwb2ludHM9IjEyIDIgMTUuMDkgOC4yNiAyMiA5LjI3IDE3IDE0LjE0IDE4LjE4IDIxLjAyIDEyIDE3Ljc3IDUuODIgMjEuMDIgNyAxNC4xNCAyIDkuMjcgOC45MSA4LjI2IDEyIDIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/star\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Star = createLucideIcon('Star', [\n  [\n    'polygon',\n    {\n      points:\n        '12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2',\n      key: '8f66p6',\n    },\n  ],\n]);\n\nexport default Star;\n", "'use client'\n\nimport { Play, Clock, User, Star, ChevronRight, Video, Monitor, Smartphone } from 'lucide-react'\n\n// Force dynamic rendering\nexport const dynamic = 'force-dynamic'\n\nexport default function TutorialsPage() {\n  const tutorials = [\n    {\n      id: 1,\n      title: 'Getting Started with StreamIt Pro',\n      description: 'Learn the basics of creating your first meeting and inviting participants',\n      duration: '5:30',\n      difficulty: 'Beginner',\n      views: '12.5K',\n      rating: 4.9,\n      category: 'Getting Started',\n      thumbnail: '/api/placeholder/320/180'\n    },\n    {\n      id: 2,\n      title: 'Advanced Screen Sharing Techniques',\n      description: 'Master screen sharing with application-specific sharing and remote control',\n      duration: '8:45',\n      difficulty: 'Intermediate',\n      views: '8.2K',\n      rating: 4.8,\n      category: 'Advanced Features',\n      thumbnail: '/api/placeholder/320/180'\n    },\n    {\n      id: 3,\n      title: 'Setting Up Perfect Audio and Video',\n      description: 'Optimize your audio and video settings for professional-quality calls',\n      duration: '6:15',\n      difficulty: 'Beginner',\n      views: '15.3K',\n      rating: 4.9,\n      category: 'Setup',\n      thumbnail: '/api/placeholder/320/180'\n    },\n    {\n      id: 4,\n      title: 'Meeting Security Best Practices',\n      description: 'Keep your meetings secure with waiting rooms, passwords, and more',\n      duration: '7:20',\n      difficulty: 'Intermediate',\n      views: '6.8K',\n      rating: 4.7,\n      category: 'Security',\n      thumbnail: '/api/placeholder/320/180'\n    },\n    {\n      id: 5,\n      title: 'Recording and Managing Meeting Content',\n      description: 'Learn how to record meetings and organize your recorded content',\n      duration: '9:10',\n      difficulty: 'Intermediate',\n      views: '9.1K',\n      rating: 4.8,\n      category: 'Recording',\n      thumbnail: '/api/placeholder/320/180'\n    },\n    {\n      id: 6,\n      title: 'Mobile App Complete Guide',\n      description: 'Everything you need to know about using StreamIt Pro on mobile devices',\n      duration: '11:30',\n      difficulty: 'Beginner',\n      views: '7.4K',\n      rating: 4.6,\n      category: 'Mobile',\n      thumbnail: '/api/placeholder/320/180'\n    }\n  ]\n\n  const categories = ['All', 'Getting Started', 'Advanced Features', 'Setup', 'Security', 'Recording', 'Mobile']\n\n  const quickTips = [\n    {\n      icon: Video,\n      title: 'Perfect Lighting',\n      tip: 'Position yourself facing a window or use a ring light for better video quality'\n    },\n    {\n      icon: Monitor,\n      title: 'Dual Monitors',\n      tip: 'Use a second monitor to keep notes and materials visible during calls'\n    },\n    {\n      icon: Smartphone,\n      title: 'Mobile Backup',\n      tip: 'Keep the mobile app as a backup option for important meetings'\n    }\n  ]\n\n  return (\n    <>\n      {/* Animated Background */}\n      <div className=\"animated-bg\"></div>\n\n      <div className=\"min-h-screen pt-20 relative z-10\">\n        {/* Hero Section */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-7xl mx-auto text-center\">\n            <h1 className=\"text-5xl font-bold text-white mb-6 fade-in\">\n              Video Tutorials\n            </h1>\n            <p className=\"text-xl text-white/80 max-w-3xl mx-auto leading-relaxed slide-up\">\n              Learn how to use StreamIt Pro with our comprehensive video tutorials. \n              From basic setup to advanced features, we've got you covered.\n            </p>\n          </div>\n        </section>\n\n        {/* Categories */}\n        <section className=\"py-10 px-4\">\n          <div className=\"max-w-7xl mx-auto\">\n            <div className=\"flex flex-wrap justify-center gap-3 mb-12\">\n              {categories.map((category, index) => (\n                <button\n                  key={index}\n                  className={`px-6 py-2 rounded-full text-sm transition-colors ${\n                    index === 0 \n                      ? 'bg-purple-500 text-white' \n                      : 'bg-white/10 text-white/70 hover:bg-white/20'\n                  }`}\n                >\n                  {category}\n                </button>\n              ))}\n            </div>\n          </div>\n        </section>\n\n        {/* Featured Tutorial */}\n        <section className=\"py-10 px-4\">\n          <div className=\"max-w-7xl mx-auto\">\n            <div className=\"glass p-8 md:p-12\">\n              <div className=\"grid lg:grid-cols-2 gap-8 items-center\">\n                <div className=\"relative\">\n                  <div className=\"w-full h-64 md:h-80 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-lg flex items-center justify-center relative overflow-hidden\">\n                    <Play className=\"h-16 w-16 text-white/80 hover:text-white cursor-pointer transition-colors\" />\n                    <div className=\"absolute top-4 left-4 bg-red-500 text-white px-2 py-1 rounded text-sm font-semibold\">\n                      FEATURED\n                    </div>\n                    <div className=\"absolute bottom-4 right-4 bg-black/50 text-white px-2 py-1 rounded text-sm\">\n                      {tutorials[0].duration}\n                    </div>\n                  </div>\n                </div>\n                <div>\n                  <div className=\"flex items-center gap-2 mb-4\">\n                    <span className=\"bg-green-500 text-white px-3 py-1 rounded-full text-sm\">\n                      {tutorials[0].difficulty}\n                    </span>\n                    <span className=\"text-white/60 text-sm\">{tutorials[0].category}</span>\n                  </div>\n                  <h2 className=\"text-3xl font-bold text-white mb-4\">{tutorials[0].title}</h2>\n                  <p className=\"text-white/70 mb-6\">{tutorials[0].description}</p>\n                  <div className=\"flex items-center gap-6 mb-6\">\n                    <div className=\"flex items-center gap-2\">\n                      <Clock className=\"h-4 w-4 text-white/60\" />\n                      <span className=\"text-white/60 text-sm\">{tutorials[0].duration}</span>\n                    </div>\n                    <div className=\"flex items-center gap-2\">\n                      <User className=\"h-4 w-4 text-white/60\" />\n                      <span className=\"text-white/60 text-sm\">{tutorials[0].views} views</span>\n                    </div>\n                    <div className=\"flex items-center gap-2\">\n                      <Star className=\"h-4 w-4 text-yellow-400 fill-current\" />\n                      <span className=\"text-white/60 text-sm\">{tutorials[0].rating}</span>\n                    </div>\n                  </div>\n                  <button className=\"btn-primary flex items-center gap-2\">\n                    <Play className=\"h-4 w-4\" />\n                    Watch Tutorial\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* Tutorials Grid */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-7xl mx-auto\">\n            <h2 className=\"text-3xl font-bold text-white mb-12 text-center\">All Tutorials</h2>\n            \n            <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n              {tutorials.slice(1).map((tutorial) => (\n                <div key={tutorial.id} className=\"glass p-6 hover:transform hover:scale-105 transition-all duration-300\">\n                  <div className=\"relative mb-4\">\n                    <div className=\"w-full h-48 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-lg flex items-center justify-center relative overflow-hidden\">\n                      <Play className=\"h-12 w-12 text-white/80 hover:text-white cursor-pointer transition-colors\" />\n                      <div className=\"absolute bottom-2 right-2 bg-black/50 text-white px-2 py-1 rounded text-xs\">\n                        {tutorial.duration}\n                      </div>\n                    </div>\n                  </div>\n                  \n                  <div className=\"flex items-center gap-2 mb-3\">\n                    <span className={`px-2 py-1 rounded-full text-xs ${\n                      tutorial.difficulty === 'Beginner' \n                        ? 'bg-green-500 text-white' \n                        : 'bg-orange-500 text-white'\n                    }`}>\n                      {tutorial.difficulty}\n                    </span>\n                    <span className=\"text-purple-400 text-sm\">{tutorial.category}</span>\n                  </div>\n                  \n                  <h3 className=\"text-lg font-semibold text-white mb-2\">{tutorial.title}</h3>\n                  <p className=\"text-white/70 mb-4 text-sm\">{tutorial.description}</p>\n                  \n                  <div className=\"flex items-center justify-between mb-4\">\n                    <div className=\"flex items-center gap-4\">\n                      <div className=\"flex items-center gap-1\">\n                        <User className=\"h-3 w-3 text-white/60\" />\n                        <span className=\"text-white/60 text-xs\">{tutorial.views}</span>\n                      </div>\n                      <div className=\"flex items-center gap-1\">\n                        <Star className=\"h-3 w-3 text-yellow-400 fill-current\" />\n                        <span className=\"text-white/60 text-xs\">{tutorial.rating}</span>\n                      </div>\n                    </div>\n                  </div>\n                  \n                  <button className=\"btn-secondary w-full flex items-center justify-center gap-2\">\n                    <Play className=\"h-4 w-4\" />\n                    Watch Now\n                  </button>\n                </div>\n              ))}\n            </div>\n          </div>\n        </section>\n\n        {/* Quick Tips */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-7xl mx-auto\">\n            <h2 className=\"text-3xl font-bold text-white mb-12 text-center\">Quick Tips</h2>\n            \n            <div className=\"grid md:grid-cols-3 gap-8\">\n              {quickTips.map((tip, index) => (\n                <div key={index} className=\"glass p-6 text-center\">\n                  <tip.icon className=\"h-12 w-12 text-purple-400 mx-auto mb-4\" />\n                  <h3 className=\"text-lg font-semibold text-white mb-3\">{tip.title}</h3>\n                  <p className=\"text-white/70 text-sm\">{tip.tip}</p>\n                </div>\n              ))}\n            </div>\n          </div>\n        </section>\n\n        {/* Learning Path */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"glass p-8\">\n              <h2 className=\"text-2xl font-bold text-white mb-6 text-center\">\n                Recommended Learning Path\n              </h2>\n              <div className=\"space-y-4\">\n                {[\n                  'Getting Started with StreamIt Pro',\n                  'Setting Up Perfect Audio and Video',\n                  'Advanced Screen Sharing Techniques',\n                  'Meeting Security Best Practices',\n                  'Recording and Managing Meeting Content'\n                ].map((step, index) => (\n                  <div key={index} className=\"flex items-center gap-4 p-4 bg-white/5 rounded-lg\">\n                    <div className=\"w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center text-white font-semibold text-sm\">\n                      {index + 1}\n                    </div>\n                    <span className=\"text-white flex-1\">{step}</span>\n                    <ChevronRight className=\"h-4 w-4 text-white/60\" />\n                  </div>\n                ))}\n              </div>\n              <div className=\"text-center mt-8\">\n                <button className=\"btn-primary\">Start Learning Path</button>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* CTA Section */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-4xl mx-auto text-center\">\n            <div className=\"glass p-12\">\n              <h2 className=\"text-3xl font-bold text-white mb-4\">\n                Ready to Become a StreamIt Pro Expert?\n              </h2>\n              <p className=\"text-xl text-white/70 mb-8\">\n                Start with our beginner tutorials and work your way up to advanced features\n              </p>\n              <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n                <button className=\"btn-primary px-8 py-3\">Start Learning</button>\n                <button className=\"btn-secondary px-8 py-3\">Browse All Tutorials</button>\n              </div>\n            </div>\n          </div>\n        </section>\n      </div>\n    </>\n  )\n}\n", "export default {\n  xmlns: 'http://www.w3.org/2000/svg',\n  width: 24,\n  height: 24,\n  viewBox: '0 0 24 24',\n  fill: 'none',\n  stroke: 'currentColor',\n  strokeWidth: 2,\n  strokeLinecap: 'round',\n  strokeLinejoin: 'round',\n};\n", "import {\n  forwardRef,\n  createElement,\n  ReactSVG,\n  SVGProps,\n  ForwardRefExoticComponent,\n  RefAttributes,\n} from 'react';\nimport defaultAttributes from './defaultAttributes';\n\nexport type IconNode = [elementName: keyof ReactSVG, attrs: Record<string, string>][];\n\nexport type SVGAttributes = Partial<SVGProps<SVGSVGElement>>;\ntype ComponentAttributes = RefAttributes<SVGSVGElement> & SVGAttributes;\n\nexport interface LucideProps extends ComponentAttributes {\n  size?: string | number;\n  absoluteStrokeWidth?: boolean;\n}\n\nexport type LucideIcon = ForwardRefExoticComponent<LucideProps>;\n/**\n * Converts string to KebabCase\n * Copied from scripts/helper. If anyone knows how to properly import it here\n * then please fix it.\n *\n * @param {string} string\n * @returns {string} A kebabized string\n */\nexport const toKebabCase = (string: string) =>\n  string\n    .replace(/([a-z0-9])([A-Z])/g, '$1-$2')\n    .toLowerCase()\n    .trim();\n\nconst createLucideIcon = (iconName: string, iconNode: IconNode): LucideIcon => {\n  const Component = forwardRef<SVGSVGElement, LucideProps>(\n    ({ color = 'currentColor', size = 24, strokeWidth = 2, absoluteStrokeWidth, className = '', children, ...rest }, ref) =>\n      createElement(\n        'svg',\n        {\n          ref,\n          ...defaultAttributes,\n          width: size,\n          height: size,\n          stroke: color,\n          strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n          className: ['lucide', `lucide-${toKebabCase(iconName)}`, className].join(' '),\n          ...rest,\n        },\n        [\n          ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n          ...(Array.isArray(children) ? children : [children]),\n        ]\n      )\n  );\n\n  Component.displayName = `${iconName}`;\n\n  return Component;\n};\n\nexport default createLucideIcon;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ChevronRight\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtOSAxOCA2LTYtNi02IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/chevron-right\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronRight = createLucideIcon('ChevronRight', [\n  ['path', { d: 'm9 18 6-6-6-6', key: 'mthhwq' }],\n]);\n\nexport default ChevronRight;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Clock\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8cG9seWxpbmUgcG9pbnRzPSIxMiA2IDEyIDEyIDE2IDE0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/clock\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Clock = createLucideIcon('Clock', [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['polyline', { points: '12 6 12 12 16 14', key: '68esgv' }],\n]);\n\nexport default Clock;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Monitor\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMjAiIGhlaWdodD0iMTQiIHg9IjIiIHk9IjMiIHJ4PSIyIiAvPgogIDxsaW5lIHgxPSI4IiB4Mj0iMTYiIHkxPSIyMSIgeTI9IjIxIiAvPgogIDxsaW5lIHgxPSIxMiIgeDI9IjEyIiB5MT0iMTciIHkyPSIyMSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/monitor\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Monitor = createLucideIcon('Monitor', [\n  ['rect', { width: '20', height: '14', x: '2', y: '3', rx: '2', key: '48i651' }],\n  ['line', { x1: '8', x2: '16', y1: '21', y2: '21', key: '1svkeh' }],\n  ['line', { x1: '12', x2: '12', y1: '17', y2: '21', key: 'vw1qmm' }],\n]);\n\nexport default Monitor;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Play\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWdvbiBwb2ludHM9IjUgMyAxOSAxMiA1IDIxIDUgMyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/play\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Play = createLucideIcon('Play', [\n  ['polygon', { points: '5 3 19 12 5 21 5 3', key: '191637' }],\n]);\n\nexport default Play;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Smartphone\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTQiIGhlaWdodD0iMjAiIHg9IjUiIHk9IjIiIHJ4PSIyIiByeT0iMiIgLz4KICA8cGF0aCBkPSJNMTIgMThoLjAxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/smartphone\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Smartphone = createLucideIcon('Smartphone', [\n  ['rect', { width: '14', height: '20', x: '5', y: '2', rx: '2', ry: '2', key: '1yt0o3' }],\n  ['path', { d: 'M12 18h.01', key: 'mhygvu' }],\n]);\n\nexport default Smartphone;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name User\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTkgMjF2LTJhNCA0IDAgMCAwLTQtNEg5YTQgNCAwIDAgMC00IDR2MiIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjciIHI9IjQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/user\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst User = createLucideIcon('User', [\n  ['path', { d: 'M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2', key: '975kel' }],\n  ['circle', { cx: '12', cy: '7', r: '4', key: '17ys0d' }],\n]);\n\nexport default User;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Video\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMjIgOC02IDQgNiA0VjhaIiAvPgogIDxyZWN0IHdpZHRoPSIxNCIgaGVpZ2h0PSIxMiIgeD0iMiIgeT0iNiIgcng9IjIiIHJ5PSIyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/video\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Video = createLucideIcon('Video', [\n  ['path', { d: 'm22 8-6 4 6 4V8Z', key: '50v9me' }],\n  ['rect', { width: '14', height: '12', x: '2', y: '6', rx: '2', ry: '2', key: '1rqjg6' }],\n]);\n\nexport default Video;\n"], "names": ["Promise", "resolve", "then", "__webpack_require__", "bind", "Star", "createLucideIcon", "points", "key", "dynamic", "TutorialsPage", "tutorials", "id", "title", "description", "duration", "difficulty", "views", "rating", "category", "thumbnail", "quickTips", "icon", "Video", "tip", "Monitor", "Smartphone", "jsx_runtime", "jsxs", "Fragment", "jsx", "div", "className", "section", "h1", "p", "categories", "map", "index", "button", "concat", "Play", "span", "h2", "Clock", "User", "slice", "tutorial", "h3", "step", "ChevronRight", "defaultAttributes", "xmlns", "width", "height", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "toKebabCase", "string", "replace", "toLowerCase", "trim", "iconName", "iconNode", "Component", "forwardRef", "param", "ref", "color", "size", "absoluteStrokeWidth", "children", "rest", "createElement", "Number", "join", "tag", "attrs", "Array", "isArray", "displayName", "d", "cx", "cy", "r", "x", "y", "rx", "x1", "x2", "y1", "y2", "ry"], "sourceRoot": ""}