{"version": 3, "file": "static/chunks/app/help-center/page-8838bee3670fb172.js", "mappings": "mFAAAA,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA;;;;;GCaM,IAAAE,EAAOC,CAAAA,EAAAA,EAAAA,CAAAA,EAAiB,OAAQ,CACpC,CAAC,OAAQ,CAAEC,EAAG,iEAAkEC,IAAK,UAAU,CAChG;;;;;GCFK,IAAAC,EAAaH,CAAAA,EAAAA,EAAAA,CAAAA,EAAiB,aAAc,CAChD,CAAC,SAAU,CAAEI,GAAI,KAAMC,GAAI,KAAMC,EAAG,KAAMJ,IAAK,UAAU,CACzD,CAAC,OAAQ,CAAED,EAAG,uCAAwCC,IAAK,UAAU,CACrE,CAAC,OAAQ,CAAED,EAAG,aAAcC,IAAK,UAAU,CAC5C,ECZYK,EAAU,gBAOR,SAASC,IACtB,GAAM,CAACC,EAAaC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IACzC,CAACC,EAASC,EAAW,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,EAAwB,MAEhDG,EAAa,CACjB,CACEC,KAAMC,EAAAA,CAAKA,CACXC,MAAO,kBACPC,YAAa,yCACbC,SAAU,EACZ,EACA,CACEJ,KAAMK,EAAAA,CAAQA,CACdH,MAAO,qBACPC,YAAa,sCACbC,SAAU,CACZ,EACA,CACEJ,KAAMM,EAAAA,CAAKA,CACXJ,MAAO,0BACPC,YAAa,qCACbC,SAAU,EACZ,EACA,CACEJ,KAAMO,EAAAA,CAAMA,CACZL,MAAO,qBACPC,YAAa,4BACbC,SAAU,CACZ,EACA,CACEJ,KAAMhB,EACNkB,MAAO,oBACPC,YAAa,gCACbC,SAAU,EACZ,EACA,CACEJ,KAAMQ,EAAAA,CAAaA,CACnBN,MAAO,kBACPC,YAAa,sBACbC,SAAU,CACZ,EACD,CAwCKK,EAAY,IAChBX,EAAWD,IAAYa,EAAQ,KAAOA,EACxC,EAEA,MACE,GAAAC,EAAAC,IAAA,EAAAD,EAAAE,QAAA,YAEE,GAAAF,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,gBAEf,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,6CAEb,GAAAL,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,0CACb,GAAAL,EAAAG,GAAA,EAACI,KAAAA,CAAGF,UAAU,sDAA6C,gBAG3D,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,iFAAwE,qFAKrF,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,6BACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,qBACb,GAAAL,EAAAG,GAAA,EAACM,EAAAA,CAAMA,CAAAA,CAACJ,UAAU,6EAClB,GAAAL,EAAAG,GAAA,EAACO,QAAAA,CACCC,KAAK,OACLC,YAAY,8BACZC,MAAO9B,EACP+B,SAAU,GAAO9B,EAAe+B,EAAEC,MAAM,CAACH,KAAK,EAC9CR,UAAU,yDAQpB,GAAAL,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,8BACb,GAAAL,EAAAG,GAAA,EAACc,KAAAA,CAAGZ,UAAU,2DAAkD,uBAEhE,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,oDACZjB,EAAW8B,GAAG,CAAC,CAACC,EAAUpB,IACzB,GAAAC,EAAAG,GAAA,EAACC,MAAAA,CAAgBC,UAAU,gGACzB,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,mCACb,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,8HACb,GAAAL,EAAAG,GAAA,EAACgB,EAAS9B,IAAI,EAACgB,UAAU,yBAE3B,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,mBACb,GAAAL,EAAAG,GAAA,EAACiB,KAAAA,CAAGf,UAAU,iDAAyCc,EAAS5B,KAAK,GACrE,GAAAS,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,sCAA8Bc,EAAS3B,WAAW,GAC/D,GAAAQ,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,8CACb,GAAAL,EAAAC,IAAA,EAACoB,OAAAA,CAAKhB,UAAU,kCAAyBc,EAAS1B,QAAQ,CAAC,eAC3D,GAAAO,EAAAG,GAAA,EAACmB,EAAAA,CAAYA,CAAAA,CAACjB,UAAU,oCAVtBN,WAqBlB,GAAAC,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,8BACb,GAAAL,EAAAG,GAAA,EAACc,KAAAA,CAAGZ,UAAU,2DAAkD,qBAEhE,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,qCACZkB,CA1GX,kCACA,kCACA,oCACA,mCACA,8CACA,qCACA,oCACA,6BACD,CAkG4BL,GAAG,CAAC,CAACM,EAASzB,IAC7B,GAAAC,EAAAG,GAAA,EAACC,MAAAA,CAAgBC,UAAU,wEACzB,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,8CACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,oCACb,GAAAL,EAAAG,GAAA,EAACsB,EAAAA,CAAQA,CAAAA,CAACpB,UAAU,4BACpB,GAAAL,EAAAG,GAAA,EAACkB,OAAAA,CAAKhB,UAAU,sBAAcmB,OAEhC,GAAAxB,EAAAG,GAAA,EAACmB,EAAAA,CAAYA,CAAAA,CAACjB,UAAU,8BANlBN,WAelB,GAAAC,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,8BACb,GAAAL,EAAAG,GAAA,EAACc,KAAAA,CAAGZ,UAAU,2DAAkD,+BAIhE,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,qBACZqB,CAtHX,CACEC,SAAU,4BACVC,OAAQ,2LACV,EACA,CACED,SAAU,yBACVC,OAAQ,sLACV,EACA,CACED,SAAU,4CACVC,OAAQ,qIACV,EACA,CACED,SAAU,0BACVC,OAAQ,qJACV,EACA,CACED,SAAU,kCACVC,OAAQ,6IACV,EACA,CACED,SAAU,4BACVC,OAAQ,uJACV,EACD,CA8FiBV,GAAG,CAAC,CAACW,EAAK9B,IACd,GAAAC,EAAAC,IAAA,EAACG,MAAAA,CAAgBC,UAAU,kBACzB,GAAAL,EAAAC,IAAA,EAAC6B,SAAAA,CACCC,QAAS,IAAMjC,EAAUC,GACzBM,UAAU,sGAEV,GAAAL,EAAAG,GAAA,EAACkB,OAAAA,CAAKhB,UAAU,oCAA4BwB,EAAIF,QAAQ,GACvDzC,IAAYa,EACX,GAAAC,EAAAG,GAAA,EAAC6B,EAAAA,CAASA,CAAAA,CAAC3B,UAAU,0BAErB,GAAAL,EAAAG,GAAA,EAAC8B,EAAAA,CAAWA,CAAAA,CAAC5B,UAAU,6BAG1BnB,IAAYa,GACX,GAAAC,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,qBACb,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,yBAAiBwB,EAAID,MAAM,OAdpC7B,WAwBlB,GAAAC,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,8BACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,8BACb,GAAAL,EAAAG,GAAA,EAACc,KAAAA,CAAGZ,UAAU,8CAAqC,qBAGnD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,iCAAwB,+DAKvC,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sCACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,kCACb,GAAAL,EAAAG,GAAA,EAACN,EAAAA,CAAaA,CAAAA,CAACQ,UAAU,2CACzB,GAAAL,EAAAG,GAAA,EAACiB,KAAAA,CAAGf,UAAU,iDAAwC,cACtD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,8BAAqB,2CAGlC,GAAAL,EAAAG,GAAA,EAAC2B,SAAAA,CAAOzB,UAAU,8BAAqB,kBAGzC,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,kCACb,GAAAL,EAAAG,GAAA,EAAC+B,EAAAA,CAAIA,CAAAA,CAAC7B,UAAU,yCAChB,GAAAL,EAAAG,GAAA,EAACiB,KAAAA,CAAGf,UAAU,iDAAwC,kBACtD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,8BAAqB,uDAGlC,GAAAL,EAAAG,GAAA,EAAC2B,SAAAA,CAAOzB,UAAU,gCAAuB,kBAG3C,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,kCACb,GAAAL,EAAAG,GAAA,EAACgC,EAAAA,CAAKA,CAAAA,CAAC9B,UAAU,0CACjB,GAAAL,EAAAG,GAAA,EAACiB,KAAAA,CAAGf,UAAU,iDAAwC,kBACtD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,8BAAqB,qCAGlC,GAAAL,EAAAG,GAAA,EAAC2B,SAAAA,CAAOzB,UAAU,gCAAuB,wBAOjD,GAAAL,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,6BACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sBACb,GAAAL,EAAAG,GAAA,EAACiB,KAAAA,CAAGf,UAAU,0DAAiD,gBAC/D,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sCACb,GAAAL,EAAAC,IAAA,EAACmC,IAAAA,CAAEC,KAAK,IAAIhC,UAAU,sFACpB,GAAAL,EAAAG,GAAA,EAAC1B,EAAUA,CAAC4B,UAAU,4BACtB,GAAAL,EAAAG,GAAA,EAACkB,OAAAA,CAAKhB,UAAU,sBAAa,qBAE/B,GAAAL,EAAAC,IAAA,EAACmC,IAAAA,CAAEC,KAAK,IAAIhC,UAAU,sFACpB,GAAAL,EAAAG,GAAA,EAACsB,EAAAA,CAAQA,CAAAA,CAACpB,UAAU,0BACpB,GAAAL,EAAAG,GAAA,EAACkB,OAAAA,CAAKhB,UAAU,sBAAa,yBAE/B,GAAAL,EAAAC,IAAA,EAACmC,IAAAA,CAAEC,KAAK,IAAIhC,UAAU,sFACpB,GAAAL,EAAAG,GAAA,EAACb,EAAAA,CAAKA,CAAAA,CAACe,UAAU,2BACjB,GAAAL,EAAAG,GAAA,EAACkB,OAAAA,CAAKhB,UAAU,sBAAa,uBAE/B,GAAAL,EAAAC,IAAA,EAACmC,IAAAA,CAAEC,KAAK,IAAIhC,UAAU,sFACpB,GAAAL,EAAAG,GAAA,EAACN,EAAAA,CAAaA,CAAAA,CAACQ,UAAU,4BACzB,GAAAL,EAAAG,GAAA,EAACkB,OAAAA,CAAKhB,UAAU,sBAAa,sCAS/C,mFCzReiC,EAAA,CACbC,MAAO,6BACPC,MAAO,GACPC,OAAQ,GACRC,QAAS,YACTC,KAAM,OACNC,OAAQ,eACRC,YAAa,EACbC,cAAe,QACfC,eAAgB,OAClB;;;;;GCmBa,IAAAC,EAAc,GACzBC,EACGC,OAAA,CAAQ,qBAAsB,SAC9BC,WAAY,GACZC,IAAK,GAEJ9E,EAAmB,CAAC+E,EAAkBC,KAC1C,IAAMC,EAAYC,CAAAA,EAAAA,EAAAA,UAAAA,EAChB,CAAAC,EAAiHC,QAAhH,CAAEC,MAAAA,EAAQ,eAAgBC,KAAAA,EAAO,EAAI,CAAAf,YAAAA,EAAc,CAAG,CAAAgB,oBAAAA,CAAA,CAAqBxD,UAAAA,EAAY,GAAIyD,SAAAA,CAAa,IAAAC,EAAA,CAAAN,QACvGO,CAAAA,EAAAA,EAAAA,aAAAA,EACE,MACA,CACEN,IAAAA,EACA,GAAGpB,CAAA,CACHE,MAAOoB,EACPnB,OAAQmB,EACRhB,OAAQe,EACRd,YAAagB,EAAsBI,GAAAA,OAAOpB,GAAoBoB,OAAOL,GAAQf,EAC7ExC,UAAW,CAAC,SAAoB,UAAyB6D,MAAA,CAAzBlB,EAAYK,IAAahD,EAAW,CAAA8D,IAAA,CAAK,KACzE,GAAGJ,CAAA,EAEL,IACKT,EAASpC,GAAA,CAAI,OAAC,CAACkD,EAAKC,EAAW,CAAAZ,QAAAO,CAAAA,EAAAA,EAAAA,aAAAA,EAAcI,EAAKC,QACjDC,MAAMC,OAAA,CAAQT,GAAYA,EAAW,CAACA,EAAQ,CACpD,IAMC,OAFPP,EAAUiB,WAAA,CAAc,GAAGN,MAAA,CAAAb,GAEpBE,CACT;;;;;GC/CM,IAAAtB,EAAc3D,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,cAAe,CAClD,CAAC,OAAQ,CAAEC,EAAG,eAAgBC,IAAK,UAAU,CAC9C;;;;;GCFK,IAAA8C,EAAehD,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,eAAgB,CACpD,CAAC,OAAQ,CAAEC,EAAG,gBAAiBC,IAAK,UAAU,CAC/C;;;;;GCFD,IAAMwD,EAAY1D,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,YAAa,CAAC,CAAC,OAAQ,CAAEC,EAAG,iBAAkBC,IAAK,QAAS,EAAE,CAAC;;;;;GCA5F,IAAAiD,EAAWnD,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,WAAY,CAC5C,CACE,OACA,CAAEC,EAAG,wEAAyEC,IAAK,QAAS,EAC9F,CACA,CAAC,WAAY,CAAEiG,OAAQ,iBAAkBjG,IAAK,UAAU,CACxD,CAAC,OAAQ,CAAEkG,GAAI,KAAMC,GAAI,IAAKC,GAAI,KAAMC,GAAI,KAAMrG,IAAK,UAAU,CACjE,CAAC,OAAQ,CAAEkG,GAAI,KAAMC,GAAI,IAAKC,GAAI,KAAMC,GAAI,KAAMrG,IAAK,UAAU,CACjE,CAAC,OAAQ,CAAEkG,GAAI,KAAMC,GAAI,IAAKC,GAAI,IAAKC,GAAI,IAAKrG,IAAK,UAAU,CAChE;;;;;GCTK,IAAA0D,EAAO5D,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,OAAQ,CACpC,CAAC,OAAQ,CAAEkE,MAAO,KAAMC,OAAQ,KAAMqC,EAAG,IAAKC,EAAG,IAAKC,GAAI,IAAKxG,IAAK,UAAU,CAC9E,CAAC,OAAQ,CAAED,EAAG,4CAA6CC,IAAK,UAAU,CAC3E;;;;;GCHK,IAAAqB,EAAgBvB,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,gBAAiB,CACtD,CAAC,OAAQ,CAAEC,EAAG,uCAAwCC,IAAK,UAAU,CACtE;;;;;GCFK,IAAA2D,EAAQ7D,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,QAAS,CACtC,CACE,OACA,CACEC,EAAG,gSACHC,IAAK,QACP,EACF,CACD;;;;;GCRK,IAAAiC,EAASnC,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,SAAU,CACxC,CAAC,SAAU,CAAEI,GAAI,KAAMC,GAAI,KAAMC,EAAG,IAAKJ,IAAK,UAAU,CACxD,CAAC,OAAQ,CAAED,EAAG,iBAAkBC,IAAK,UAAU,CAChD;;;;;GCHK,IAAAkB,EAAWpB,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,WAAY,CAC5C,CACE,OACA,CACEC,EAAG,wjBACHC,IAAK,QACP,EACF,CACA,CAAC,SAAU,CAAEE,GAAI,KAAMC,GAAI,KAAMC,EAAG,IAAKJ,IAAK,UAAU,CACzD;;;;;GCTK,IAAAoB,EAAStB,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,SAAU,CACxC,CAAC,OAAQ,CAAEC,EAAG,6CAA8CC,IAAK,UAAU,CAC5E;;;;;GCFK,IAAAmB,EAAQrB,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,QAAS,CACtC,CAAC,OAAQ,CAAEC,EAAG,4CAA6CC,IAAK,UAAU,CAC1E,CAAC,SAAU,CAAEE,GAAI,IAAKC,GAAI,IAAKC,EAAG,IAAKJ,IAAK,SAAS,CACrD,CAAC,OAAQ,CAAED,EAAG,6BAA8BC,IAAK,UAAU,CAC3D,CAAC,OAAQ,CAAED,EAAG,4BAA6BC,IAAK,UAAU,CAC3D;;;;;GCLK,IAAAc,EAAQhB,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,QAAS,CACtC,CAAC,OAAQ,CAAEC,EAAG,mBAAoBC,IAAK,UAAU,CACjD,CAAC,OAAQ,CAAEgE,MAAO,KAAMC,OAAQ,KAAMqC,EAAG,IAAKC,EAAG,IAAKC,GAAI,IAAKC,GAAI,IAAKzG,IAAK,UAAU,CACxF", "sources": ["webpack://_N_E/?80e8", "webpack://_N_E/../../../src/icons/book.ts", "webpack://_N_E/../../../src/icons/help-circle.ts", "webpack://_N_E/./app/help-center/page.tsx", "webpack://_N_E/../../src/defaultAttributes.ts", "webpack://_N_E/../../src/createLucideIcon.ts", "webpack://_N_E/../../../src/icons/chevron-down.ts", "webpack://_N_E/../../../src/icons/chevron-right.ts", "webpack://_N_E/../../../src/icons/chevron-up.ts", "webpack://_N_E/../../../src/icons/file-text.ts", "webpack://_N_E/../../../src/icons/mail.ts", "webpack://_N_E/../../../src/icons/message-circle.ts", "webpack://_N_E/../../../src/icons/phone.ts", "webpack://_N_E/../../../src/icons/search.ts", "webpack://_N_E/../../../src/icons/settings.ts", "webpack://_N_E/../../../src/icons/shield.ts", "webpack://_N_E/../../../src/icons/users.ts", "webpack://_N_E/../../../src/icons/video.ts"], "sourcesContent": ["import(/* webpackMode: \"eager\" */ \"/Volumes/Apps/Websites/streamit-main/app/help-center/page.tsx\");\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Book\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNCAxOS41di0xNUEyLjUgMi41IDAgMCAxIDYuNSAySDIwdjIwSDYuNWEyLjUgMi41IDAgMCAxIDAtNUgyMCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/book\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Book = createLucideIcon('Book', [\n  ['path', { d: 'M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20', key: 't4utmx' }],\n]);\n\nexport default Book;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name HelpCircle\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8cGF0aCBkPSJNOS4wOSA5YTMgMyAwIDAgMSA1LjgzIDFjMCAyLTMgMy0zIDMiIC8+CiAgPHBhdGggZD0iTTEyIDE3aC4wMSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/help-circle\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst HelpCircle = createLucideIcon('HelpCircle', [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['path', { d: 'M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3', key: '1u773s' }],\n  ['path', { d: 'M12 17h.01', key: 'p32p05' }],\n]);\n\nexport default HelpCircle;\n", "'use client'\n\nimport { useState } from 'react'\n\n// Force dynamic rendering\nexport const dynamic = 'force-dynamic'\nimport { \n  Search, Book, Video, Settings, Shield, Users, \n  MessageCircle, Phone, Mail, ChevronRight, \n  ChevronDown, ChevronUp, HelpCircle, FileText\n} from 'lucide-react'\n\nexport default function HelpCenterPage() {\n  const [searchQuery, setSearchQuery] = useState('')\n  const [openFaq, setOpenFaq] = useState<number | null>(null)\n\n  const categories = [\n    {\n      icon: Video,\n      title: 'Getting Started',\n      description: 'Learn the basics of using StreamIt Pro',\n      articles: 12\n    },\n    {\n      icon: Settings,\n      title: 'Account & Settings',\n      description: 'Manage your account and preferences',\n      articles: 8\n    },\n    {\n      icon: Users,\n      title: 'Meetings & Participants',\n      description: 'Host and join meetings effectively',\n      articles: 15\n    },\n    {\n      icon: Shield,\n      title: 'Security & Privacy',\n      description: 'Keep your meetings secure',\n      articles: 6\n    },\n    {\n      icon: Book,\n      title: 'Advanced Features',\n      description: 'Make the most of StreamIt Pro',\n      articles: 10\n    },\n    {\n      icon: MessageCircle,\n      title: 'Troubleshooting',\n      description: 'Solve common issues',\n      articles: 9\n    }\n  ]\n\n  const popularArticles = [\n    'How to start your first meeting',\n    'Setting up your audio and video',\n    'Inviting participants to meetings',\n    'Using screen sharing effectively',\n    'Recording meetings and accessing recordings',\n    'Managing meeting security settings',\n    'Troubleshooting connection issues',\n    'Using chat during meetings'\n  ]\n\n  const faqs = [\n    {\n      question: 'How do I start a meeting?',\n      answer: 'To start a meeting, click the \"Start Meeting\" button on the homepage, enter your name, and you\\'ll be taken to your meeting room. You can then invite others by sharing the meeting link.'\n    },\n    {\n      question: 'Can I record meetings?',\n      answer: 'Yes, meeting recording is available on Pro and Enterprise plans. Click the record button during your meeting to start recording. Recordings are automatically saved to your account.'\n    },\n    {\n      question: 'How many participants can join a meeting?',\n      answer: 'The number of participants depends on your plan: Basic (5 participants), Pro (25 participants), and Enterprise (100+ participants).'\n    },\n    {\n      question: 'Is StreamIt Pro secure?',\n      answer: 'Yes, StreamIt Pro uses end-to-end encryption, is SOC 2 certified, and complies with GDPR and other security standards to keep your meetings secure.'\n    },\n    {\n      question: 'Do I need to download software?',\n      answer: 'No, StreamIt Pro works directly in your web browser. No downloads or installations required. We also offer mobile apps for iOS and Android.'\n    },\n    {\n      question: 'How do I share my screen?',\n      answer: 'During a meeting, click the screen share button in the toolbar. You can choose to share your entire screen, a specific application, or a browser tab.'\n    }\n  ]\n\n  const toggleFaq = (index: number) => {\n    setOpenFaq(openFaq === index ? null : index)\n  }\n\n  return (\n    <>\n      {/* Animated Background */}\n      <div className=\"animated-bg\"></div>\n\n      <div className=\"min-h-screen pt-20 relative z-10\">\n        {/* Hero Section */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-7xl mx-auto text-center\">\n            <h1 className=\"text-5xl font-bold text-white mb-6 fade-in\">\n              Help Center\n            </h1>\n            <p className=\"text-xl text-white/80 max-w-3xl mx-auto leading-relaxed slide-up mb-8\">\n              Find answers to your questions and learn how to get the most out of StreamIt Pro\n            </p>\n            \n            {/* Search Bar */}\n            <div className=\"max-w-2xl mx-auto\">\n              <div className=\"relative\">\n                <Search className=\"absolute left-4 top-1/2 transform -translate-y-1/2 h-6 w-6 text-white/60\" />\n                <input\n                  type=\"text\"\n                  placeholder=\"Search for help articles...\"\n                  value={searchQuery}\n                  onChange={(e) => setSearchQuery(e.target.value)}\n                  className=\"glass-input w-full pl-12 pr-4 py-4 text-lg\"\n                />\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* Categories */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-7xl mx-auto\">\n            <h2 className=\"text-3xl font-bold text-white mb-12 text-center\">Browse by Category</h2>\n            \n            <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-6\">\n              {categories.map((category, index) => (\n                <div key={index} className=\"glass p-6 hover:transform hover:scale-105 transition-all duration-300 cursor-pointer\">\n                  <div className=\"flex items-start gap-4\">\n                    <div className=\"w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center flex-shrink-0\">\n                      <category.icon className=\"h-6 w-6 text-white\" />\n                    </div>\n                    <div className=\"flex-1\">\n                      <h3 className=\"text-xl font-semibold text-white mb-2\">{category.title}</h3>\n                      <p className=\"text-white/70 text-sm mb-3\">{category.description}</p>\n                      <div className=\"flex items-center justify-between\">\n                        <span className=\"text-white/60 text-sm\">{category.articles} articles</span>\n                        <ChevronRight className=\"h-4 w-4 text-white/60\" />\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </section>\n\n        {/* Popular Articles */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-7xl mx-auto\">\n            <h2 className=\"text-3xl font-bold text-white mb-12 text-center\">Popular Articles</h2>\n            \n            <div className=\"grid md:grid-cols-2 gap-4\">\n              {popularArticles.map((article, index) => (\n                <div key={index} className=\"glass p-4 hover:bg-white/10 transition-colors cursor-pointer\">\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center gap-3\">\n                      <FileText className=\"h-5 w-5 text-purple-400\" />\n                      <span className=\"text-white\">{article}</span>\n                    </div>\n                    <ChevronRight className=\"h-4 w-4 text-white/60\" />\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </section>\n\n        {/* FAQ Section */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-4xl mx-auto\">\n            <h2 className=\"text-3xl font-bold text-white mb-12 text-center\">\n              Frequently Asked Questions\n            </h2>\n            \n            <div className=\"space-y-4\">\n              {faqs.map((faq, index) => (\n                <div key={index} className=\"glass\">\n                  <button\n                    onClick={() => toggleFaq(index)}\n                    className=\"w-full p-6 text-left flex items-center justify-between hover:bg-white/5 transition-colors\"\n                  >\n                    <span className=\"text-white font-semibold\">{faq.question}</span>\n                    {openFaq === index ? (\n                      <ChevronUp className=\"h-5 w-5 text-white/60\" />\n                    ) : (\n                      <ChevronDown className=\"h-5 w-5 text-white/60\" />\n                    )}\n                  </button>\n                  {openFaq === index && (\n                    <div className=\"px-6 pb-6\">\n                      <p className=\"text-white/70\">{faq.answer}</p>\n                    </div>\n                  )}\n                </div>\n              ))}\n            </div>\n          </div>\n        </section>\n\n        {/* Contact Support */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-7xl mx-auto\">\n            <div className=\"text-center mb-12\">\n              <h2 className=\"text-3xl font-bold text-white mb-4\">\n                Still Need Help?\n              </h2>\n              <p className=\"text-xl text-white/70\">\n                Our support team is here to help you with any questions\n              </p>\n            </div>\n\n            <div className=\"grid md:grid-cols-3 gap-8\">\n              <div className=\"glass p-8 text-center\">\n                <MessageCircle className=\"h-12 w-12 text-purple-400 mx-auto mb-4\" />\n                <h3 className=\"text-xl font-semibold text-white mb-3\">Live Chat</h3>\n                <p className=\"text-white/70 mb-4\">\n                  Get instant help from our support team\n                </p>\n                <button className=\"btn-primary w-full\">Start Chat</button>\n              </div>\n\n              <div className=\"glass p-8 text-center\">\n                <Mail className=\"h-12 w-12 text-blue-400 mx-auto mb-4\" />\n                <h3 className=\"text-xl font-semibold text-white mb-3\">Email Support</h3>\n                <p className=\"text-white/70 mb-4\">\n                  Send us an email and we'll respond within 24 hours\n                </p>\n                <button className=\"btn-secondary w-full\">Send Email</button>\n              </div>\n\n              <div className=\"glass p-8 text-center\">\n                <Phone className=\"h-12 w-12 text-green-400 mx-auto mb-4\" />\n                <h3 className=\"text-xl font-semibold text-white mb-3\">Phone Support</h3>\n                <p className=\"text-white/70 mb-4\">\n                  Call us for immediate assistance\n                </p>\n                <button className=\"btn-secondary w-full\">Call Now</button>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* Quick Links */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"glass p-8\">\n              <h3 className=\"text-2xl font-bold text-white mb-6 text-center\">Quick Links</h3>\n              <div className=\"grid md:grid-cols-2 gap-4\">\n                <a href=\"#\" className=\"flex items-center gap-3 p-3 hover:bg-white/5 rounded-lg transition-colors\">\n                  <HelpCircle className=\"h-5 w-5 text-purple-400\" />\n                  <span className=\"text-white\">System Status</span>\n                </a>\n                <a href=\"#\" className=\"flex items-center gap-3 p-3 hover:bg-white/5 rounded-lg transition-colors\">\n                  <FileText className=\"h-5 w-5 text-blue-400\" />\n                  <span className=\"text-white\">API Documentation</span>\n                </a>\n                <a href=\"#\" className=\"flex items-center gap-3 p-3 hover:bg-white/5 rounded-lg transition-colors\">\n                  <Video className=\"h-5 w-5 text-green-400\" />\n                  <span className=\"text-white\">Video Tutorials</span>\n                </a>\n                <a href=\"#\" className=\"flex items-center gap-3 p-3 hover:bg-white/5 rounded-lg transition-colors\">\n                  <MessageCircle className=\"h-5 w-5 text-orange-400\" />\n                  <span className=\"text-white\">Community Forum</span>\n                </a>\n              </div>\n            </div>\n          </div>\n        </section>\n      </div>\n    </>\n  )\n}\n", "export default {\n  xmlns: 'http://www.w3.org/2000/svg',\n  width: 24,\n  height: 24,\n  viewBox: '0 0 24 24',\n  fill: 'none',\n  stroke: 'currentColor',\n  strokeWidth: 2,\n  strokeLinecap: 'round',\n  strokeLinejoin: 'round',\n};\n", "import {\n  forwardRef,\n  createElement,\n  ReactSVG,\n  SVGProps,\n  ForwardRefExoticComponent,\n  RefAttributes,\n} from 'react';\nimport defaultAttributes from './defaultAttributes';\n\nexport type IconNode = [elementName: keyof ReactSVG, attrs: Record<string, string>][];\n\nexport type SVGAttributes = Partial<SVGProps<SVGSVGElement>>;\ntype ComponentAttributes = RefAttributes<SVGSVGElement> & SVGAttributes;\n\nexport interface LucideProps extends ComponentAttributes {\n  size?: string | number;\n  absoluteStrokeWidth?: boolean;\n}\n\nexport type LucideIcon = ForwardRefExoticComponent<LucideProps>;\n/**\n * Converts string to KebabCase\n * Copied from scripts/helper. If anyone knows how to properly import it here\n * then please fix it.\n *\n * @param {string} string\n * @returns {string} A kebabized string\n */\nexport const toKebabCase = (string: string) =>\n  string\n    .replace(/([a-z0-9])([A-Z])/g, '$1-$2')\n    .toLowerCase()\n    .trim();\n\nconst createLucideIcon = (iconName: string, iconNode: IconNode): LucideIcon => {\n  const Component = forwardRef<SVGSVGElement, LucideProps>(\n    ({ color = 'currentColor', size = 24, strokeWidth = 2, absoluteStrokeWidth, className = '', children, ...rest }, ref) =>\n      createElement(\n        'svg',\n        {\n          ref,\n          ...defaultAttributes,\n          width: size,\n          height: size,\n          stroke: color,\n          strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n          className: ['lucide', `lucide-${toKebabCase(iconName)}`, className].join(' '),\n          ...rest,\n        },\n        [\n          ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n          ...(Array.isArray(children) ? children : [children]),\n        ]\n      )\n  );\n\n  Component.displayName = `${iconName}`;\n\n  return Component;\n};\n\nexport default createLucideIcon;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ChevronDown\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtNiA5IDYgNiA2LTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/chevron-down\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronDown = createLucideIcon('ChevronDown', [\n  ['path', { d: 'm6 9 6 6 6-6', key: 'qrunsl' }],\n]);\n\nexport default ChevronDown;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ChevronRight\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtOSAxOCA2LTYtNi02IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/chevron-right\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronRight = createLucideIcon('ChevronRight', [\n  ['path', { d: 'm9 18 6-6-6-6', key: 'mthhwq' }],\n]);\n\nexport default ChevronRight;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ChevronUp\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTggMTUtNi02LTYgNiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/chevron-up\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronUp = createLucideIcon('ChevronUp', [['path', { d: 'm18 15-6-6-6 6', key: '153udz' }]]);\n\nexport default ChevronUp;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name FileText\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTQuNSAySDZhMiAyIDAgMCAwLTIgMnYxNmEyIDIgMCAwIDAgMiAyaDEyYTIgMiAwIDAgMCAyLTJWNy41TDE0LjUgMnoiIC8+CiAgPHBvbHlsaW5lIHBvaW50cz0iMTQgMiAxNCA4IDIwIDgiIC8+CiAgPGxpbmUgeDE9IjE2IiB4Mj0iOCIgeTE9IjEzIiB5Mj0iMTMiIC8+CiAgPGxpbmUgeDE9IjE2IiB4Mj0iOCIgeTE9IjE3IiB5Mj0iMTciIC8+CiAgPGxpbmUgeDE9IjEwIiB4Mj0iOCIgeTE9IjkiIHkyPSI5IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/file-text\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FileText = createLucideIcon('FileText', [\n  [\n    'path',\n    { d: 'M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z', key: '1nnpy2' },\n  ],\n  ['polyline', { points: '14 2 14 8 20 8', key: '1ew0cm' }],\n  ['line', { x1: '16', x2: '8', y1: '13', y2: '13', key: '14keom' }],\n  ['line', { x1: '16', x2: '8', y1: '17', y2: '17', key: '17nazh' }],\n  ['line', { x1: '10', x2: '8', y1: '9', y2: '9', key: '1a5vjj' }],\n]);\n\nexport default FileText;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Mail\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMjAiIGhlaWdodD0iMTYiIHg9IjIiIHk9IjQiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Im0yMiA3LTguOTcgNS43YTEuOTQgMS45NCAwIDAgMS0yLjA2IDBMMiA3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/mail\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Mail = createLucideIcon('Mail', [\n  ['rect', { width: '20', height: '16', x: '2', y: '4', rx: '2', key: '18n3k1' }],\n  ['path', { d: 'm22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7', key: '1ocrg3' }],\n]);\n\nexport default Mail;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name MessageCircle\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMyAyMSAxLjktNS43YTguNSA4LjUgMCAxIDEgMy44IDMuOHoiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/message-circle\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MessageCircle = createLucideIcon('MessageCircle', [\n  ['path', { d: 'm3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z', key: 'v2veuj' }],\n]);\n\nexport default MessageCircle;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Phone\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjIgMTYuOTJ2M2EyIDIgMCAwIDEtMi4xOCAyIDE5Ljc5IDE5Ljc5IDAgMCAxLTguNjMtMy4wNyAxOS41IDE5LjUgMCAwIDEtNi02IDE5Ljc5IDE5Ljc5IDAgMCAxLTMuMDctOC42N0EyIDIgMCAwIDEgNC4xMSAyaDNhMiAyIDAgMCAxIDIgMS43MiAxMi44NCAxMi44NCAwIDAgMCAuNyAyLjgxIDIgMiAwIDAgMS0uNDUgMi4xMUw4LjA5IDkuOTFhMTYgMTYgMCAwIDAgNiA2bDEuMjctMS4yN2EyIDIgMCAwIDEgMi4xMS0uNDUgMTIuODQgMTIuODQgMCAwIDAgMi44MS43QTIgMiAwIDAgMSAyMiAxNi45MnoiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/phone\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Phone = createLucideIcon('Phone', [\n  [\n    'path',\n    {\n      d: 'M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z',\n      key: 'foiqr5',\n    },\n  ],\n]);\n\nexport default Phone;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Search\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMSIgY3k9IjExIiByPSI4IiAvPgogIDxwYXRoIGQ9Im0yMSAyMS00LjMtNC4zIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/search\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Search = createLucideIcon('Search', [\n  ['circle', { cx: '11', cy: '11', r: '8', key: '4ej97u' }],\n  ['path', { d: 'm21 21-4.3-4.3', key: '1qie3q' }],\n]);\n\nexport default Search;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Settings\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIuMjIgMmgtLjQ0YTIgMiAwIDAgMC0yIDJ2LjE4YTIgMiAwIDAgMS0xIDEuNzNsLS40My4yNWEyIDIgMCAwIDEtMiAwbC0uMTUtLjA4YTIgMiAwIDAgMC0yLjczLjczbC0uMjIuMzhhMiAyIDAgMCAwIC43MyAyLjczbC4xNS4xYTIgMiAwIDAgMSAxIDEuNzJ2LjUxYTIgMiAwIDAgMS0xIDEuNzRsLS4xNS4wOWEyIDIgMCAwIDAtLjczIDIuNzNsLjIyLjM4YTIgMiAwIDAgMCAyLjczLjczbC4xNS0uMDhhMiAyIDAgMCAxIDIgMGwuNDMuMjVhMiAyIDAgMCAxIDEgMS43M1YyMGEyIDIgMCAwIDAgMiAyaC40NGEyIDIgMCAwIDAgMi0ydi0uMThhMiAyIDAgMCAxIDEtMS43M2wuNDMtLjI1YTIgMiAwIDAgMSAyIDBsLjE1LjA4YTIgMiAwIDAgMCAyLjczLS43M2wuMjItLjM5YTIgMiAwIDAgMC0uNzMtMi43M2wtLjE1LS4wOGEyIDIgMCAwIDEtMS0xLjc0di0uNWEyIDIgMCAwIDEgMS0xLjc0bC4xNS0uMDlhMiAyIDAgMCAwIC43My0yLjczbC0uMjItLjM4YTIgMiAwIDAgMC0yLjczLS43M2wtLjE1LjA4YTIgMiAwIDAgMS0yIDBsLS40My0uMjVhMiAyIDAgMCAxLTEtMS43M1Y0YTIgMiAwIDAgMC0yLTJ6IiAvPgogIDxjaXJjbGUgY3g9IjEyIiBjeT0iMTIiIHI9IjMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/settings\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Settings = createLucideIcon('Settings', [\n  [\n    'path',\n    {\n      d: 'M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z',\n      key: '1qme2f',\n    },\n  ],\n  ['circle', { cx: '12', cy: '12', r: '3', key: '1v7zrd' }],\n]);\n\nexport default Settings;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Shield\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMjJzOC00IDgtMTBWNWwtOC0zLTggM3Y3YzAgNiA4IDEwIDggMTAiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/shield\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Shield = createLucideIcon('Shield', [\n  ['path', { d: 'M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10', key: '1irkt0' }],\n]);\n\nexport default Shield;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Users\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgMjF2LTJhNCA0IDAgMCAwLTQtNEg2YTQgNCAwIDAgMC00IDR2MiIgLz4KICA8Y2lyY2xlIGN4PSI5IiBjeT0iNyIgcj0iNCIgLz4KICA8cGF0aCBkPSJNMjIgMjF2LTJhNCA0IDAgMCAwLTMtMy44NyIgLz4KICA8cGF0aCBkPSJNMTYgMy4xM2E0IDQgMCAwIDEgMCA3Ljc1IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/users\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Users = createLucideIcon('Users', [\n  ['path', { d: 'M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2', key: '1yyitq' }],\n  ['circle', { cx: '9', cy: '7', r: '4', key: 'nufk8' }],\n  ['path', { d: 'M22 21v-2a4 4 0 0 0-3-3.87', key: 'kshegd' }],\n  ['path', { d: 'M16 3.13a4 4 0 0 1 0 7.75', key: '1da9ce' }],\n]);\n\nexport default Users;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Video\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMjIgOC02IDQgNiA0VjhaIiAvPgogIDxyZWN0IHdpZHRoPSIxNCIgaGVpZ2h0PSIxMiIgeD0iMiIgeT0iNiIgcng9IjIiIHJ5PSIyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/video\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Video = createLucideIcon('Video', [\n  ['path', { d: 'm22 8-6 4 6 4V8Z', key: '50v9me' }],\n  ['rect', { width: '14', height: '12', x: '2', y: '6', rx: '2', ry: '2', key: '1rqjg6' }],\n]);\n\nexport default Video;\n"], "names": ["Promise", "resolve", "then", "__webpack_require__", "bind", "Book", "createLucideIcon", "d", "key", "HelpCircle", "cx", "cy", "r", "dynamic", "HelpCenterPage", "searchQuery", "setSearch<PERSON>uery", "useState", "openFaq", "setOpenFaq", "categories", "icon", "Video", "title", "description", "articles", "Settings", "Users", "Shield", "MessageCircle", "toggleFaq", "index", "jsx_runtime", "jsxs", "Fragment", "jsx", "div", "className", "section", "h1", "p", "Search", "input", "type", "placeholder", "value", "onChange", "e", "target", "h2", "map", "category", "h3", "span", "ChevronRight", "popularArticles", "article", "FileText", "faqs", "question", "answer", "faq", "button", "onClick", "ChevronUp", "ChevronDown", "Mail", "Phone", "a", "href", "defaultAttributes", "xmlns", "width", "height", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "toKebabCase", "string", "replace", "toLowerCase", "trim", "iconName", "iconNode", "Component", "forwardRef", "param", "ref", "color", "size", "absoluteStrokeWidth", "children", "rest", "createElement", "Number", "concat", "join", "tag", "attrs", "Array", "isArray", "displayName", "points", "x1", "x2", "y1", "y2", "x", "y", "rx", "ry"], "sourceRoot": ""}