{"version": 3, "file": "static/chunks/app/not-found-d0b44e3d7d53b0ec.js", "mappings": "oFAAAA,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA;;;;;GCaM,IAAAE,EAAOC,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,OAAQ,CACpC,CAAC,OAAQ,CAAEC,EAAG,iDAAkDC,IAAK,UAAU,CAC/E,CAAC,WAAY,CAAEC,OAAQ,wBAAyBD,IAAK,UAAU,CAChE,gBCXc,SAASE,IACtB,MACE,GAAAC,EAAAC,GAAA,EAACC,MAAAA,CAAIC,UAAU,2HACb,GAAAH,EAAAI,IAAA,EAACF,MAAAA,CAAIC,UAAU,wBACb,GAAAH,EAAAI,IAAA,EAACF,MAAAA,CAAIC,UAAU,iBACb,GAAAH,EAAAC,GAAA,EAACI,KAAAA,CAAGF,UAAU,iDAAwC,QACtD,GAAAH,EAAAC,GAAA,EAACK,KAAAA,CAAGH,UAAU,8CAAqC,mBACnD,GAAAH,EAAAC,GAAA,EAACM,IAAAA,CAAEJ,UAAU,uDAA8C,oEAK7D,GAAAH,EAAAI,IAAA,EAACF,MAAAA,CAAIC,UAAU,2DACb,GAAAH,EAAAI,IAAA,EAACI,EAAAA,OAAIA,CAAAA,CACHC,KAAK,IACLN,UAAU,yHAEV,GAAAH,EAAAC,GAAA,EAACP,EAAIA,CAACS,UAAU,iBAAiB,aAInC,GAAAH,EAAAI,IAAA,EAACM,SAAAA,CACCC,QAAS,IAAMC,OAAOC,OAAO,CAACC,IAAI,GAClCX,UAAU,qHAEV,GAAAH,EAAAC,GAAA,EAACc,EAAAA,CAASA,CAAAA,CAACZ,UAAU,iBAAiB,oBAOlD;;;;;GCxBM,IAAAY,EAAYpB,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,YAAa,CAC9C,CAAC,OAAQ,CAAEC,EAAG,iBAAkBC,IAAK,UAAU,CAC/C,CAAC,OAAQ,CAAED,EAAG,WAAYC,IAAK,UAAU,CAC1C", "sources": ["webpack://_N_E/?c7df", "webpack://_N_E/../../../src/icons/home.ts", "webpack://_N_E/./app/not-found.tsx", "webpack://_N_E/../../../src/icons/arrow-left.ts"], "sourcesContent": ["import(/* webpackMode: \"eager\" */ \"/Volumes/Apps/Websites/streamit-main/app/not-found.tsx\");\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Home\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMyA5IDktNyA5IDd2MTFhMiAyIDAgMCAxLTIgMkg1YTIgMiAwIDAgMS0yLTJ6IiAvPgogIDxwb2x5bGluZSBwb2ludHM9IjkgMjIgOSAxMiAxNSAxMiAxNSAyMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/home\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Home = createLucideIcon('Home', [\n  ['path', { d: 'm3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z', key: 'y5dka4' }],\n  ['polyline', { points: '9 22 9 12 15 12 15 22', key: 'e2us08' }],\n]);\n\nexport default Home;\n", "'use client'\n\nimport Link from 'next/link'\nimport { Home, ArrowLeft } from 'lucide-react'\n\nexport default function NotFound() {\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center px-4\">\n      <div className=\"text-center\">\n        <div className=\"mb-8\">\n          <h1 className=\"text-9xl font-bold text-white/20 mb-4\">404</h1>\n          <h2 className=\"text-3xl font-bold text-white mb-4\">Page Not Found</h2>\n          <p className=\"text-white/80 text-lg mb-8 max-w-md mx-auto\">\n            The page you're looking for doesn't exist or has been moved.\n          </p>\n        </div>\n        \n        <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n          <Link\n            href=\"/\"\n            className=\"inline-flex items-center px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors\"\n          >\n            <Home className=\"w-5 h-5 mr-2\" />\n            Go Home\n          </Link>\n          \n          <button\n            onClick={() => window.history.back()}\n            className=\"inline-flex items-center px-6 py-3 bg-white/10 hover:bg-white/20 text-white rounded-lg transition-colors\"\n          >\n            <ArrowLeft className=\"w-5 h-5 mr-2\" />\n            Go Back\n          </button>\n        </div>\n      </div>\n    </div>\n  )\n}\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ArrowLeft\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTIgMTktNy03IDctNyIgLz4KICA8cGF0aCBkPSJNMTkgMTJINSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/arrow-left\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowLeft = createLucideIcon('ArrowLeft', [\n  ['path', { d: 'm12 19-7-7 7-7', key: '1l729n' }],\n  ['path', { d: 'M19 12H5', key: 'x3x0zl' }],\n]);\n\nexport default ArrowLeft;\n"], "names": ["Promise", "resolve", "then", "__webpack_require__", "bind", "Home", "createLucideIcon", "d", "key", "points", "NotFound", "jsx_runtime", "jsx", "div", "className", "jsxs", "h1", "h2", "p", "Link", "href", "button", "onClick", "window", "history", "back", "ArrowLeft"], "sourceRoot": ""}