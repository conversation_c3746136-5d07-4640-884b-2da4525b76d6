(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[185],{7429:function(e,t,r){Promise.resolve().then(r.t.bind(r,7960,23)),Promise.resolve().then(r.bind(r,8349)),Promise.resolve().then(r.bind(r,2611)),Promise.resolve().then(r.bind(r,842)),Promise.resolve().then(r.t.bind(r,4742,23)),Promise.resolve().then(r.bind(r,5119))},8349:function(e,t,r){"use strict";r.d(t,{default:function(){return c}});var s=r(7437),i=r(7648),a=r(9374),n=r(2351),o=r(5135),l=r(598);function c(){return(0,s.jsx)("footer",{className:"bg-black/50 backdrop-blur-md border-t border-white/10 mt-20",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center mb-4",children:[(0,s.jsx)("div",{className:"w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mr-3",children:(0,s.jsx)(a.Z,{className:"h-5 w-5 text-white"})}),(0,s.jsx)("span",{className:"text-xl font-bold text-white",children:"StreamIt Pro"})]}),(0,s.jsx)("p",{className:"text-white/60 text-sm mt-2",children:"Professional video conferencing solution for teams of all sizes."}),(0,s.jsxs)("div",{className:"flex space-x-4 mt-4",children:[(0,s.jsx)("a",{href:"#",className:"text-white/60 hover:text-white transition-colors",children:(0,s.jsx)(n.Z,{className:"h-5 w-5"})}),(0,s.jsx)("a",{href:"#",className:"text-white/60 hover:text-white transition-colors",children:(0,s.jsx)(o.Z,{className:"h-5 w-5"})}),(0,s.jsx)("a",{href:"#",className:"text-white/60 hover:text-white transition-colors",children:(0,s.jsx)(l.Z,{className:"h-5 w-5"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-white font-semibold mb-4",children:"Product"}),(0,s.jsxs)("ul",{className:"space-y-2",children:[(0,s.jsx)("li",{children:(0,s.jsx)(i.default,{href:"/features",className:"text-white/60 hover:text-white transition-colors text-sm",children:"Features"})}),(0,s.jsx)("li",{children:(0,s.jsx)(i.default,{href:"/pricing",className:"text-white/60 hover:text-white transition-colors text-sm",children:"Pricing"})}),(0,s.jsx)("li",{children:(0,s.jsx)(i.default,{href:"/enterprise",className:"text-white/60 hover:text-white transition-colors text-sm",children:"Enterprise"})}),(0,s.jsx)("li",{children:(0,s.jsx)(i.default,{href:"/security",className:"text-white/60 hover:text-white transition-colors text-sm",children:"Security"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-white font-semibold mb-4",children:"Resources"}),(0,s.jsxs)("ul",{className:"space-y-2",children:[(0,s.jsx)("li",{children:(0,s.jsx)(i.default,{href:"/blog",className:"text-white/60 hover:text-white transition-colors text-sm",children:"Blog"})}),(0,s.jsx)("li",{children:(0,s.jsx)(i.default,{href:"/help-center",className:"text-white/60 hover:text-white transition-colors text-sm",children:"Help Center"})}),(0,s.jsx)("li",{children:(0,s.jsx)(i.default,{href:"/tutorials",className:"text-white/60 hover:text-white transition-colors text-sm",children:"Tutorials"})}),(0,s.jsx)("li",{children:(0,s.jsx)(i.default,{href:"/webinars",className:"text-white/60 hover:text-white transition-colors text-sm",children:"Webinars"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-white font-semibold mb-4",children:"Company"}),(0,s.jsxs)("ul",{className:"space-y-2",children:[(0,s.jsx)("li",{children:(0,s.jsx)(i.default,{href:"/about",className:"text-white/60 hover:text-white transition-colors text-sm",children:"About Us"})}),(0,s.jsx)("li",{children:(0,s.jsx)(i.default,{href:"/careers",className:"text-white/60 hover:text-white transition-colors text-sm",children:"Careers"})}),(0,s.jsx)("li",{children:(0,s.jsx)(i.default,{href:"/contact",className:"text-white/60 hover:text-white transition-colors text-sm",children:"Contact Us"})}),(0,s.jsx)("li",{children:(0,s.jsx)(i.default,{href:"/privacy",className:"text-white/60 hover:text-white transition-colors text-sm",children:"Privacy Policy"})})]})]})]}),(0,s.jsxs)("div",{className:"border-t border-white/10 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center",children:[(0,s.jsxs)("p",{className:"text-white/50 text-sm",children:["\xa9 ",new Date().getFullYear()," StreamIt Pro. All rights reserved."]}),(0,s.jsxs)("div",{className:"flex space-x-6 mt-4 md:mt-0",children:[(0,s.jsx)(i.default,{href:"/terms",className:"text-white/60 hover:text-white transition-colors text-sm",children:"Terms of Service"}),(0,s.jsx)(i.default,{href:"/privacy",className:"text-white/60 hover:text-white transition-colors text-sm",children:"Privacy Policy"}),(0,s.jsx)(i.default,{href:"/cookies",className:"text-white/60 hover:text-white transition-colors text-sm",children:"Cookies"})]})]})]})})}},2611:function(e,t,r){"use strict";r.d(t,{default:function(){return n}});var s=r(7437),i=r(7648),a=r(9374);function n(){return(0,s.jsx)("nav",{className:"fixed top-0 left-0 right-0 z-50 bg-black/50 backdrop-blur-md border-b border-white/10",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex justify-between h-16",children:[(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsxs)(i.default,{href:"/",className:"flex items-center",children:[(0,s.jsx)("div",{className:"w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mr-3",children:(0,s.jsx)(a.Z,{className:"h-5 w-5 text-white"})}),(0,s.jsx)("span",{className:"text-xl font-bold text-white",children:"StreamIt Pro"})]})}),(0,s.jsxs)("div",{className:"hidden md:flex items-center space-x-8",children:[(0,s.jsx)("a",{href:"#features",className:"text-white/80 hover:text-white transition-colors cursor-pointer",onClick:e=>{var t;e.preventDefault(),null===(t=document.getElementById("features"))||void 0===t||t.scrollIntoView({behavior:"smooth"})},children:"Features"}),(0,s.jsx)("a",{href:"#pricing",className:"text-white/80 hover:text-white transition-colors cursor-pointer",onClick:e=>{var t;e.preventDefault(),null===(t=document.getElementById("pricing"))||void 0===t||t.scrollIntoView({behavior:"smooth"})},children:"Pricing"}),(0,s.jsx)(i.default,{href:"/about",className:"text-white/80 hover:text-white transition-colors",children:"About"}),(0,s.jsx)(i.default,{href:"/contact",className:"text-white/80 hover:text-white transition-colors",children:"Contact"})]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(i.default,{href:"/login",className:"px-4 py-2 rounded-md text-sm font-medium text-white hover:bg-white/10 transition-colors",children:"Log in"}),(0,s.jsx)(i.default,{href:"/signup",className:"ml-4 px-4 py-2 rounded-md text-sm font-medium text-white bg-gradient-to-r from-purple-600 to-pink-600 hover:opacity-90 transition-opacity",children:"Sign up free"})]})]})})})}},842:function(e,t,r){"use strict";r.d(t,{Toaster:function(){return M}});var s=r(7437),i=r(2265),a=r(4080),n=r(535),o=r(2489),l=r(1994),c=r(3335);function d(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,c.m6)((0,l.W)(t))}let u=a.zt,h=i.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,s.jsx)(a.l_,{ref:t,className:d("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",r),...i})});h.displayName=a.l_.displayName;let m=(0,n.j)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),x=i.forwardRef((e,t)=>{let{className:r,variant:i,...n}=e;return(0,s.jsx)(a.fC,{ref:t,className:d(m({variant:i}),r),...n})});x.displayName=a.fC.displayName,i.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,s.jsx)(a.aU,{ref:t,className:d("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",r),...i})}).displayName=a.aU.displayName;let p=i.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,s.jsx)(a.x8,{ref:t,className:d("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",r),"toast-close":"",...i,children:(0,s.jsx)(o.Z,{className:"h-4 w-4"})})});p.displayName=a.x8.displayName;let f=i.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,s.jsx)(a.Dx,{ref:t,className:d("text-sm font-semibold",r),...i})});f.displayName=a.Dx.displayName;let v=i.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,s.jsx)(a.dk,{ref:t,className:d("text-sm opacity-90",r),...i})});v.displayName=a.dk.displayName;let g=0,w=new Map,j=e=>{if(w.has(e))return;let t=setTimeout(()=>{w.delete(e),y({type:"REMOVE_TOAST",toastId:e})},1e6);w.set(e,t)},b=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?j(r):e.toasts.forEach(e=>{j(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},N=[],S={toasts:[]};function y(e){S=b(S,e),N.forEach(e=>{e(S)})}function C(e){let{...t}=e,r=(g=(g+1)%Number.MAX_SAFE_INTEGER).toString(),s=()=>y({type:"DISMISS_TOAST",toastId:r});return y({type:"ADD_TOAST",toast:{...t,id:r,open:!0,onOpenChange:e=>{e||s()}}}),{id:r,dismiss:s,update:e=>y({type:"UPDATE_TOAST",toast:{...e,id:r}})}}function M(){let{toasts:e}=function(){let[e,t]=i.useState(S);return i.useEffect(()=>(N.push(t),()=>{let e=N.indexOf(t);e>-1&&N.splice(e,1)}),[e]),{...e,toast:C,dismiss:e=>y({type:"DISMISS_TOAST",toastId:e})}}();return(0,s.jsxs)(u,{children:[e.map(function(e){let{id:t,title:r,description:i,action:a,...n}=e;return(0,s.jsxs)(x,{...n,children:[(0,s.jsxs)("div",{className:"grid gap-1",children:[r&&(0,s.jsx)(f,{children:r}),i&&(0,s.jsx)(v,{children:i})]}),a,(0,s.jsx)(p,{})]},t)}),(0,s.jsx)(h,{})]})}},2097:function(e,t,r){"use strict";r.d(t,{g:function(){return n}});var s=r(9625),i=r(6885);let a={roomId:null,isConnected:!1,roomLocked:!1,currentUser:null,participants:new Map,localStream:null,isAudioMuted:!1,isVideoMuted:!1,isScreenSharing:!1,messages:[],unreadCount:0,messageHistory:new Map,isChatOpen:!1,isSettingsOpen:!1,securitySettings:{encryptionEnabled:!0,antiSpamEnabled:!0,maxMessagesPerMinute:10,allowScreenShare:!0,allowFileSharing:!0,requireApprovalToJoin:!1},adminControls:{canMuteAll:!0,canMuteParticipant:!0,canRemoveParticipant:!0,canControlCamera:!0,canManageRoles:!0},blockedUsers:new Set,spamDetection:new Map},n=(0,s.Ue)()((0,i.mW)((e,t)=>({...a,setRoomId:t=>e({roomId:t}),setConnected:t=>e({isConnected:t}),setCurrentUser:t=>e({currentUser:t}),addParticipant:t=>e(e=>{let r=new Map(e.participants);return r.set(t.id,t),{participants:r}}),removeParticipant:t=>e(e=>{let r=new Map(e.participants);return r.delete(t),{participants:r}}),updateParticipant:(t,r)=>e(e=>{let s=new Map(e.participants),i=s.get(t);return i&&s.set(t,{...i,...r}),{participants:s}}),setLocalStream:t=>e({localStream:t}),toggleAudio:()=>e(e=>{let t=!e.isAudioMuted;return e.localStream&&e.localStream.getAudioTracks().forEach(e=>{e.enabled=!t}),{isAudioMuted:t}}),toggleVideo:()=>e(e=>{let t=!e.isVideoMuted;return e.localStream&&e.localStream.getVideoTracks().forEach(e=>{e.enabled=!t}),{isVideoMuted:t}}),toggleScreenShare:()=>e(e=>({isScreenSharing:!e.isScreenSharing})),addMessage:t=>e(e=>{if(e.securitySettings.antiSpamEnabled){let r=Date.now(),s=e.spamDetection.get(t.userId)||{count:0,lastReset:r};r-s.lastReset>6e4&&(s.count=0,s.lastReset=r),s.count++,e.spamDetection.set(t.userId,s),s.count>e.securitySettings.maxMessagesPerMinute&&(t.isSpam=!0)}return{messages:[...e.messages,t],unreadCount:e.isChatOpen?e.unreadCount:e.unreadCount+1}}),clearUnreadCount:()=>e({unreadCount:0}),toggleChat:()=>e(e=>({isChatOpen:!e.isChatOpen,unreadCount:e.isChatOpen?e.unreadCount:0})),toggleSettings:()=>e(e=>({isSettingsOpen:!e.isSettingsOpen})),muteParticipant:t=>e(e=>{var r,s;let i=e.participants.get(t);if(i&&((null===(r=e.currentUser)||void 0===r?void 0:r.role)==="host"||(null===(s=e.currentUser)||void 0===s?void 0:s.role)==="co-host")){let r=new Map(e.participants);return r.set(t,{...i,isAudioMuted:!0}),{participants:r}}return e}),muteAllParticipants:()=>e(e=>{var t,r;if((null===(t=e.currentUser)||void 0===t?void 0:t.role)==="host"||(null===(r=e.currentUser)||void 0===r?void 0:r.role)==="co-host"){let t=new Map;return e.participants.forEach((e,r)=>{t.set(r,{...e,isAudioMuted:!0})}),{participants:t}}return e}),removeParticipantAsAdmin:t=>e(e=>{var r,s;if((null===(r=e.currentUser)||void 0===r?void 0:r.role)==="host"||(null===(s=e.currentUser)||void 0===s?void 0:s.role)==="co-host"){let r=new Map(e.participants);return r.delete(t),{participants:r}}return e}),promoteToCoHost:t=>e(e=>{var r;let s=e.participants.get(t);if(s&&(null===(r=e.currentUser)||void 0===r?void 0:r.role)==="host"){let r=new Map(e.participants);return r.set(t,{...s,role:"co-host"}),{participants:r}}return e}),demoteFromCoHost:t=>e(e=>{var r;let s=e.participants.get(t);if(s&&(null===(r=e.currentUser)||void 0===r?void 0:r.role)==="host"){let r=new Map(e.participants);return r.set(t,{...s,role:"participant"}),{participants:r}}return e}),toggleRoomLock:()=>e(e=>{var t;return(null===(t=e.currentUser)||void 0===t?void 0:t.role)==="host"?{roomLocked:!e.roomLocked}:e}),blockUser:t=>e(e=>{var r,s;if((null===(r=e.currentUser)||void 0===r?void 0:r.role)==="host"||(null===(s=e.currentUser)||void 0===s?void 0:s.role)==="co-host"){let r=new Set(e.blockedUsers);return r.add(t),{blockedUsers:r}}return e}),unblockUser:t=>e(e=>{var r,s;if((null===(r=e.currentUser)||void 0===r?void 0:r.role)==="host"||(null===(s=e.currentUser)||void 0===s?void 0:s.role)==="co-host"){let r=new Set(e.blockedUsers);return r.delete(t),{blockedUsers:r}}return e}),updateSecuritySettings:t=>e(e=>{var r;return(null===(r=e.currentUser)||void 0===r?void 0:r.role)==="host"?{securitySettings:{...e.securitySettings,...t}}:e}),detectSpam:e=>{let r=t();if(!r.securitySettings.antiSpamEnabled)return!1;let s=r.spamDetection.get(e);return!(!s||Date.now()-s.lastReset>6e4)&&s.count>r.securitySettings.maxMessagesPerMinute},reset:()=>e(a)}),{name:"video-call-store"}))},5119:function(e,t,r){"use strict";r.d(t,{StoreProvider:function(){return l},c:function(){return c}});var s=r(7437),i=r(2265),a=r(9625),n=r(2097);let o=(0,i.createContext)(null);function l(e){let{children:t}=e,r=(0,i.useRef)();return r.current||(r.current=n.g),(0,s.jsx)(o.Provider,{value:r.current,children:t})}function c(e,t){let r=(0,i.useContext)(o);if(!r)throw Error("useVideoCallStoreContext must be used within a StoreProvider");return(0,a.oR)(r,e,t)}},7960:function(){}},function(e){e.O(0,[540,451,74,896,971,117,744],function(){return e(e.s=7429)}),_N_E=e.O()}]);