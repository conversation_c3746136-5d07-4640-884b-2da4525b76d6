(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[470],{3276:function(e,t,n){Promise.resolve().then(n.bind(n,6314))},6314:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return i}});var r=n(7437);function i(e){let{error:t,reset:n}=e;return(0,r.jsx)("html",{children:(0,r.jsx)("body",{children:(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-red-900 via-purple-900 to-indigo-900 flex items-center justify-center px-4",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-9xl font-bold text-white/20 mb-4",children:"500"}),(0,r.jsx)("h2",{className:"text-3xl font-bold text-white mb-4",children:"Something went wrong!"}),(0,r.jsx)("p",{className:"text-white/80 text-lg mb-8 max-w-md mx-auto",children:"An unexpected error occurred. Please try again."})]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,r.jsx)("button",{onClick:()=>n(),className:"inline-flex items-center px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors",children:"Try again"}),(0,r.jsx)("a",{href:"/",className:"inline-flex items-center px-6 py-3 bg-white/10 hover:bg-white/20 text-white rounded-lg transition-colors",children:"Go Home"})]})]})})})})}}},function(e){e.O(0,[971,23,744],function(){return e(e.s=3276)}),_N_E=e.O()}]);
//# sourceMappingURL=global-error-c11f75afe3374c0e.js.map