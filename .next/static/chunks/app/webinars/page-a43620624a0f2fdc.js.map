{"version": 3, "file": "static/chunks/app/webinars/page-a43620624a0f2fdc.js", "mappings": "oFAAAA,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA;;;;;GCaM,IAAAE,EAAOC,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,OAAQ,CACpC,CAAC,OAAQ,CAAEC,EAAG,4CAA6CC,IAAK,UAAU,CAC1E,CAAC,OAAQ,CAAED,EAAG,iCAAkCC,IAAK,UAAU,CAChE,0BCXM,IAAMC,EAAU,gBAER,SAASC,IAsEtB,MACE,GAAAC,EAAAC,IAAA,EAAAD,EAAAE,QAAA,YAEE,GAAAF,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,gBAEf,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,6CAEb,GAAAL,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,0CACb,GAAAL,EAAAG,GAAA,EAACI,KAAAA,CAAGF,UAAU,sDAA6C,0BAG3D,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,4EAAmE,iIAQpF,GAAAL,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,8BACb,GAAAL,EAAAG,GAAA,EAACM,KAAAA,CAAGJ,UAAU,2DAAkD,sBAEhE,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,qBACZK,CA7FX,CACEC,GAAI,EACJC,MAAO,6CACPC,YAAa,sHACbC,KAAM,aACNC,KAAM,cACNC,SAAU,aACVC,UAAW,+BACXC,UAAW,IACXC,MAAO,0BACT,EACA,CACER,GAAI,EACJC,MAAO,oCACPC,YAAa,8GACbC,KAAM,aACNC,KAAM,eACNC,SAAU,aACVC,UAAW,0BACXC,UAAW,IACXC,MAAO,0BACT,EACA,CACER,GAAI,EACJC,MAAO,iDACPC,YAAa,qFACbC,KAAM,aACNC,KAAM,cACNC,SAAU,aACVC,UAAW,mCACXC,UAAW,IACXC,MAAO,0BACT,EACD,CA4D6BC,GAAG,CAAC,CAACC,EAASC,IAC9B,GAAAtB,EAAAC,IAAA,EAACG,MAAAA,CAAqBC,UAAW,aAA6DkB,MAAA,CAAhDD,IAAAA,EAAc,6BAA+B,cACxFA,IAAAA,GACC,GAAAtB,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,mGAA0F,iBAI3G,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,mDACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,0BACb,GAAAL,EAAAG,GAAA,EAACqB,KAAAA,CAAGnB,UAAU,8CAAsCgB,EAAQT,KAAK,GACjE,GAAAZ,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,8BAAsBgB,EAAQR,WAAW,GAEtD,GAAAb,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,2CACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,oCACb,GAAAL,EAAAG,GAAA,EAACsB,EAAAA,CAAQA,CAAAA,CAACpB,UAAU,4BACpB,GAAAL,EAAAG,GAAA,EAACuB,OAAAA,CAAKrB,UAAU,sBAAcgB,EAAQP,IAAI,MAE5C,GAAAd,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,oCACb,GAAAL,EAAAG,GAAA,EAACwB,EAAAA,CAAKA,CAAAA,CAACtB,UAAU,0BACjB,GAAAL,EAAAG,GAAA,EAACuB,OAAAA,CAAKrB,UAAU,sBAAcgB,EAAQN,IAAI,MAE5C,GAAAf,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,oCACb,GAAAL,EAAAG,GAAA,EAACyB,EAAAA,CAAKA,CAAAA,CAACvB,UAAU,2BACjB,GAAAL,EAAAC,IAAA,EAACyB,OAAAA,CAAKrB,UAAU,uBAAcgB,EAAQH,SAAS,CAAC,oBAElD,GAAAlB,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,oCACb,GAAAL,EAAAG,GAAA,EAACwB,EAAAA,CAAKA,CAAAA,CAACtB,UAAU,4BACjB,GAAAL,EAAAG,GAAA,EAACuB,OAAAA,CAAKrB,UAAU,sBAAcgB,EAAQL,QAAQ,SAIlD,GAAAhB,EAAAC,IAAA,EAACO,IAAAA,CAAEH,UAAU,+BAAqB,gBAAcgB,EAAQJ,SAAS,IAEjE,GAAAjB,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,4CACb,GAAAL,EAAAC,IAAA,EAAC4B,SAAAA,CAAOxB,UAAU,gDAChB,GAAAL,EAAAG,GAAA,EAACsB,EAAAA,CAAQA,CAAAA,CAACpB,UAAU,YAAY,kBAGlC,GAAAL,EAAAC,IAAA,EAAC4B,SAAAA,CAAOxB,UAAU,kDAChB,GAAAL,EAAAG,GAAA,EAACT,EAAIA,CAACW,UAAU,YAAY,wBAMlC,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,qCACb,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,uHACb,GAAAL,EAAAG,GAAA,EAACuB,OAAAA,CAAKrB,UAAU,yBAAgB,6BA9C9BgB,EAAQV,EAAE,UAyD5B,GAAAX,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,8BACb,GAAAL,EAAAG,GAAA,EAACM,KAAAA,CAAGJ,UAAU,2DAAkD,uBAEhE,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,oDACZyB,CAxHX,CACEnB,GAAI,EACJC,MAAO,2CACPC,YAAa,mFACbC,KAAM,aACNE,SAAU,aACVC,UAAW,6BACXc,MAAO,KACPZ,MAAO,0BACT,EACA,CACER,GAAI,EACJC,MAAO,kCACPC,YAAa,mEACbC,KAAM,aACNE,SAAU,aACVC,UAAW,6BACXc,MAAO,IACPZ,MAAO,0BACT,EACA,CACER,GAAI,EACJC,MAAO,oCACPC,YAAa,2EACbC,KAAM,aACNE,SAAU,aACVC,UAAW,yBACXc,MAAO,KACPZ,MAAO,0BACT,EACD,CA0FyBC,GAAG,CAAC,GAChB,GAAApB,EAAAC,IAAA,EAACG,MAAAA,CAAqBC,UAAU,kFAC9B,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,yBACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,iJACb,GAAAL,EAAAG,GAAA,EAAC6B,EAAAA,CAAIA,CAAAA,CAAC3B,UAAU,8EAChB,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,+FAAsF,aAGrG,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,sFACZgB,EAAQL,QAAQ,QAKvB,GAAAhB,EAAAG,GAAA,EAACqB,KAAAA,CAAGnB,UAAU,iDAAyCgB,EAAQT,KAAK,GACpE,GAAAZ,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,sCAA8BgB,EAAQR,WAAW,GAE9D,GAAAb,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,2BACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,oCACb,GAAAL,EAAAG,GAAA,EAACsB,EAAAA,CAAQA,CAAAA,CAACpB,UAAU,0BACpB,GAAAL,EAAAG,GAAA,EAACuB,OAAAA,CAAKrB,UAAU,iCAAyBgB,EAAQP,IAAI,MAEvD,GAAAd,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,oCACb,GAAAL,EAAAG,GAAA,EAACyB,EAAAA,CAAKA,CAAAA,CAACvB,UAAU,0BACjB,GAAAL,EAAAC,IAAA,EAACyB,OAAAA,CAAKrB,UAAU,kCAAyBgB,EAAQU,KAAK,CAAC,kBAI3D,GAAA/B,EAAAC,IAAA,EAACO,IAAAA,CAAEH,UAAU,uCAA6B,MAAIgB,EAAQJ,SAAS,IAE/D,GAAAjB,EAAAC,IAAA,EAAC4B,SAAAA,CAAOxB,UAAU,wEAChB,GAAAL,EAAAG,GAAA,EAAC6B,EAAAA,CAAIA,CAAAA,CAAC3B,UAAU,YAAY,iBA9BtBgB,EAAQV,EAAE,UAwC5B,GAAAX,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,6BACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,mCACb,GAAAL,EAAAG,GAAA,EAACM,KAAAA,CAAGJ,UAAU,8CAAqC,yBAGnD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,sCAA6B,4EAG1C,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,6DACb,GAAAL,EAAAG,GAAA,EAAC8B,QAAAA,CACCC,KAAK,QACLC,YAAY,mBACZ9B,UAAU,uBAEZ,GAAAL,EAAAC,IAAA,EAAC4B,SAAAA,CAAOxB,UAAU,0DAChB,GAAAL,EAAAG,GAAA,EAACT,EAAIA,CAACW,UAAU,YAAY,kBAIhC,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,sCAA6B,qEAQhD,GAAAL,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,8BACb,GAAAL,EAAAG,GAAA,EAACM,KAAAA,CAAGJ,UAAU,2DAAkD,6BAEhE,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,qDACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,kCACb,GAAAL,EAAAG,GAAA,EAACyB,EAAAA,CAAKA,CAAAA,CAACvB,UAAU,2CACjB,GAAAL,EAAAG,GAAA,EAACqB,KAAAA,CAAGnB,UAAU,iDAAwC,oBACtD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,iCAAwB,iEAEvC,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,kCACb,GAAAL,EAAAG,GAAA,EAAC6B,EAAAA,CAAIA,CAAAA,CAAC3B,UAAU,yCAChB,GAAAL,EAAAG,GAAA,EAACqB,KAAAA,CAAGnB,UAAU,iDAAwC,yBACtD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,iCAAwB,2DAEvC,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,kCACb,GAAAL,EAAAG,GAAA,EAACsB,EAAAA,CAAQA,CAAAA,CAACpB,UAAU,0CACpB,GAAAL,EAAAG,GAAA,EAACqB,KAAAA,CAAGnB,UAAU,iDAAwC,qBACtD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,iCAAwB,yDAEvC,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,kCACb,GAAAL,EAAAG,GAAA,EAACiC,EAAAA,CAAUA,CAAAA,CAAC/B,UAAU,2CACtB,GAAAL,EAAAG,GAAA,EAACqB,KAAAA,CAAGnB,UAAU,iDAAwC,oBACtD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,iCAAwB,uEAQrD,mFCzQegC,EAAA,CACbC,MAAO,6BACPC,MAAO,GACPC,OAAQ,GACRC,QAAS,YACTC,KAAM,OACNC,OAAQ,eACRC,YAAa,EACbC,cAAe,QACfC,eAAgB,OAClB;;;;;GCmBa,IAAAC,EAAc,GACzBC,EACGC,OAAA,CAAQ,qBAAsB,SAC9BC,WAAY,GACZC,IAAK,GAEJxD,EAAmB,CAACyD,EAAkBC,KAC1C,IAAMC,EAAYC,CAAAA,EAAAA,EAAAA,UAAAA,EAChB,CAAAC,EAAiHC,QAAhH,CAAEC,MAAAA,EAAQ,eAAgBC,KAAAA,EAAO,EAAI,CAAAf,YAAAA,EAAc,CAAG,CAAAgB,oBAAAA,CAAA,CAAqBvD,UAAAA,EAAY,GAAIwD,SAAAA,CAAa,IAAAC,EAAA,CAAAN,QACvGO,CAAAA,EAAAA,EAAAA,aAAAA,EACE,MACA,CACEN,IAAAA,EACA,GAAGpB,CAAA,CACHE,MAAOoB,EACPnB,OAAQmB,EACRhB,OAAQe,EACRd,YAAagB,EAAsBI,GAAAA,OAAOpB,GAAoBoB,OAAOL,GAAQf,EAC7EvC,UAAW,CAAC,SAAoB,UAAyBkB,MAAA,CAAzBwB,EAAYK,IAAa/C,EAAW,CAAA4D,IAAA,CAAK,KACzE,GAAGH,CAAA,EAEL,IACKT,EAASjC,GAAA,CAAI,OAAC,CAAC8C,EAAKC,EAAW,CAAAX,QAAAO,CAAAA,EAAAA,EAAAA,aAAAA,EAAcG,EAAKC,QACjDC,MAAMC,OAAA,CAAQR,GAAYA,EAAW,CAACA,EAAQ,CACpD,IAMC,OAFPP,EAAUgB,WAAA,CAAc,GAAG/C,MAAA,CAAA6B,GAEpBE,CACT;;;;;GC/CM,IAAAlB,EAAazC,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,aAAc,CAChD,CAAC,OAAQ,CAAEC,EAAG,WAAYC,IAAK,UAAU,CACzC,CAAC,OAAQ,CAAED,EAAG,gBAAiBC,IAAK,UAAU,CAC/C;;;;;GCHK,IAAA4B,EAAW9B,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,WAAY,CAC5C,CAAC,OAAQ,CAAE4C,MAAO,KAAMC,OAAQ,KAAM+B,EAAG,IAAKC,EAAG,IAAKC,GAAI,IAAKC,GAAI,IAAK7E,IAAK,UAAU,CACvF,CAAC,OAAQ,CAAE8E,GAAI,KAAMC,GAAI,KAAMC,GAAI,IAAKC,GAAI,IAAKjF,IAAK,UAAU,CAChE,CAAC,OAAQ,CAAE8E,GAAI,IAAKC,GAAI,IAAKC,GAAI,IAAKC,GAAI,IAAKjF,IAAK,UAAU,CAC9D,CAAC,OAAQ,CAAE8E,GAAI,IAAKC,GAAI,KAAMC,GAAI,KAAMC,GAAI,KAAMjF,IAAK,UAAU,CAClE;;;;;GCLK,IAAA8B,EAAQhC,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,QAAS,CACtC,CAAC,SAAU,CAAEoF,GAAI,KAAMC,GAAI,KAAMC,EAAG,KAAMpF,IAAK,UAAU,CACzD,CAAC,WAAY,CAAEqF,OAAQ,mBAAoBrF,IAAK,UAAU,CAC3D;;;;;GCHK,IAAAmC,EAAOrC,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,OAAQ,CACpC,CAAC,UAAW,CAAEuF,OAAQ,qBAAsBrF,IAAK,UAAU,CAC5D;;;;;GCFK,IAAA+B,EAAQjC,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,QAAS,CACtC,CAAC,OAAQ,CAAEC,EAAG,4CAA6CC,IAAK,UAAU,CAC1E,CAAC,SAAU,CAAEkF,GAAI,IAAKC,GAAI,IAAKC,EAAG,IAAKpF,IAAK,SAAS,CACrD,CAAC,OAAQ,CAAED,EAAG,6BAA8BC,IAAK,UAAU,CAC3D,CAAC,OAAQ,CAAED,EAAG,4BAA6BC,IAAK,UAAU,CAC3D", "sources": ["webpack://_N_E/?3d9f", "webpack://_N_E/../../../src/icons/bell.ts", "webpack://_N_E/./app/webinars/page.tsx", "webpack://_N_E/../../src/defaultAttributes.ts", "webpack://_N_E/../../src/createLucideIcon.ts", "webpack://_N_E/../../../src/icons/arrow-right.ts", "webpack://_N_E/../../../src/icons/calendar.ts", "webpack://_N_E/../../../src/icons/clock.ts", "webpack://_N_E/../../../src/icons/play.ts", "webpack://_N_E/../../../src/icons/users.ts"], "sourcesContent": ["import(/* webpackMode: \"eager\" */ \"/Volumes/Apps/Websites/streamit-main/app/webinars/page.tsx\");\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Bell\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNiA4YTYgNiAwIDAgMSAxMiAwYzAgNyAzIDkgMyA5SDNzMy0yIDMtOSIgLz4KICA8cGF0aCBkPSJNMTAuMyAyMWExLjk0IDEuOTQgMCAwIDAgMy40IDAiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/bell\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Bell = createLucideIcon('Bell', [\n  ['path', { d: 'M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9', key: '1qo2s2' }],\n  ['path', { d: 'M10.3 21a1.94 1.94 0 0 0 3.4 0', key: 'qgo35s' }],\n]);\n\nexport default Bell;\n", "'use client'\n\nimport { Calendar, Clock, Users, Play, ArrowR<PERSON>, Bell } from 'lucide-react'\n\n// Force dynamic rendering\nexport const dynamic = 'force-dynamic'\n\nexport default function WebinarsPage() {\n  const upcomingWebinars = [\n    {\n      id: 1,\n      title: 'The Future of Remote Work: Trends for 2024',\n      description: 'Join industry experts as they discuss the latest trends shaping remote work and how video conferencing is evolving.',\n      date: '2024-02-15',\n      time: '2:00 PM PST',\n      duration: '60 minutes',\n      presenter: '<PERSON>, VP of Product',\n      attendees: 245,\n      image: '/api/placeholder/400/250'\n    },\n    {\n      id: 2,\n      title: 'Mastering Virtual Team Management',\n      description: 'Learn proven strategies for managing distributed teams and maintaining productivity in remote environments.',\n      date: '2024-02-22',\n      time: '11:00 AM PST',\n      duration: '45 minutes',\n      presenter: '<PERSON>, Team Lead',\n      attendees: 189,\n      image: '/api/placeholder/400/250'\n    },\n    {\n      id: 3,\n      title: 'Security Best Practices for Video Conferencing',\n      description: 'Discover how to keep your video meetings secure and protect sensitive information.',\n      date: '2024-03-01',\n      time: '1:00 PM PST',\n      duration: '50 minutes',\n      presenter: '<PERSON>, Security Expert',\n      attendees: 156,\n      image: '/api/placeholder/400/250'\n    }\n  ]\n\n  const pastWebinars = [\n    {\n      id: 4,\n      title: 'StreamIt Pro Advanced Features Deep Dive',\n      description: 'Explore advanced features like breakout rooms, polling, and custom integrations.',\n      date: '2024-01-25',\n      duration: '55 minutes',\n      presenter: 'David Kim, Product Manager',\n      views: 1250,\n      image: '/api/placeholder/400/250'\n    },\n    {\n      id: 5,\n      title: 'Building Better Meeting Culture',\n      description: 'Tips for creating more engaging and productive virtual meetings.',\n      date: '2024-01-18',\n      duration: '40 minutes',\n      presenter: 'Lisa Thompson, HR Director',\n      views: 980,\n      image: '/api/placeholder/400/250'\n    },\n    {\n      id: 6,\n      title: 'Optimizing Your Home Office Setup',\n      description: 'Create the perfect home office environment for professional video calls.',\n      date: '2024-01-11',\n      duration: '35 minutes',\n      presenter: 'Alex Park, UX Designer',\n      views: 1450,\n      image: '/api/placeholder/400/250'\n    }\n  ]\n\n  return (\n    <>\n      {/* Animated Background */}\n      <div className=\"animated-bg\"></div>\n\n      <div className=\"min-h-screen pt-20 relative z-10\">\n        {/* Hero Section */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-7xl mx-auto text-center\">\n            <h1 className=\"text-5xl font-bold text-white mb-6 fade-in\">\n              StreamIt Pro Webinars\n            </h1>\n            <p className=\"text-xl text-white/80 max-w-3xl mx-auto leading-relaxed slide-up\">\n              Join our expert-led webinars to learn best practices, discover new features, \n              and connect with other StreamIt Pro users.\n            </p>\n          </div>\n        </section>\n\n        {/* Upcoming Webinars */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-7xl mx-auto\">\n            <h2 className=\"text-3xl font-bold text-white mb-12 text-center\">Upcoming Webinars</h2>\n            \n            <div className=\"space-y-8\">\n              {upcomingWebinars.map((webinar, index) => (\n                <div key={webinar.id} className={`glass p-8 ${index === 0 ? 'border-2 border-purple-500' : ''}`}>\n                  {index === 0 && (\n                    <div className=\"bg-purple-500 text-white px-4 py-2 rounded-full text-sm font-semibold inline-block mb-4\">\n                      Next Webinar\n                    </div>\n                  )}\n                  <div className=\"grid lg:grid-cols-3 gap-8 items-center\">\n                    <div className=\"lg:col-span-2\">\n                      <h3 className=\"text-2xl font-bold text-white mb-4\">{webinar.title}</h3>\n                      <p className=\"text-white/70 mb-6\">{webinar.description}</p>\n                      \n                      <div className=\"grid md:grid-cols-2 gap-4 mb-6\">\n                        <div className=\"flex items-center gap-3\">\n                          <Calendar className=\"h-5 w-5 text-purple-400\" />\n                          <span className=\"text-white\">{webinar.date}</span>\n                        </div>\n                        <div className=\"flex items-center gap-3\">\n                          <Clock className=\"h-5 w-5 text-blue-400\" />\n                          <span className=\"text-white\">{webinar.time}</span>\n                        </div>\n                        <div className=\"flex items-center gap-3\">\n                          <Users className=\"h-5 w-5 text-green-400\" />\n                          <span className=\"text-white\">{webinar.attendees} registered</span>\n                        </div>\n                        <div className=\"flex items-center gap-3\">\n                          <Clock className=\"h-5 w-5 text-orange-400\" />\n                          <span className=\"text-white\">{webinar.duration}</span>\n                        </div>\n                      </div>\n                      \n                      <p className=\"text-white/60 mb-6\">Presented by {webinar.presenter}</p>\n                      \n                      <div className=\"flex flex-col sm:flex-row gap-4\">\n                        <button className=\"btn-primary flex items-center gap-2\">\n                          <Calendar className=\"h-4 w-4\" />\n                          Register Now\n                        </button>\n                        <button className=\"btn-secondary flex items-center gap-2\">\n                          <Bell className=\"h-4 w-4\" />\n                          Set Reminder\n                        </button>\n                      </div>\n                    </div>\n                    \n                    <div className=\"glass-dark p-4 rounded-lg\">\n                      <div className=\"w-full h-48 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-lg flex items-center justify-center\">\n                        <span className=\"text-white/60\">Webinar Preview</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </section>\n\n        {/* Past Webinars */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-7xl mx-auto\">\n            <h2 className=\"text-3xl font-bold text-white mb-12 text-center\">On-Demand Webinars</h2>\n            \n            <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n              {pastWebinars.map((webinar) => (\n                <div key={webinar.id} className=\"glass p-6 hover:transform hover:scale-105 transition-all duration-300\">\n                  <div className=\"relative mb-4\">\n                    <div className=\"w-full h-48 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-lg flex items-center justify-center relative overflow-hidden\">\n                      <Play className=\"h-12 w-12 text-white/80 hover:text-white cursor-pointer transition-colors\" />\n                      <div className=\"absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-xs font-semibold\">\n                        RECORDED\n                      </div>\n                      <div className=\"absolute bottom-2 right-2 bg-black/50 text-white px-2 py-1 rounded text-xs\">\n                        {webinar.duration}\n                      </div>\n                    </div>\n                  </div>\n                  \n                  <h3 className=\"text-lg font-semibold text-white mb-3\">{webinar.title}</h3>\n                  <p className=\"text-white/70 mb-4 text-sm\">{webinar.description}</p>\n                  \n                  <div className=\"space-y-2 mb-4\">\n                    <div className=\"flex items-center gap-2\">\n                      <Calendar className=\"h-4 w-4 text-white/60\" />\n                      <span className=\"text-white/60 text-sm\">{webinar.date}</span>\n                    </div>\n                    <div className=\"flex items-center gap-2\">\n                      <Users className=\"h-4 w-4 text-white/60\" />\n                      <span className=\"text-white/60 text-sm\">{webinar.views} views</span>\n                    </div>\n                  </div>\n                  \n                  <p className=\"text-white/60 text-sm mb-4\">By {webinar.presenter}</p>\n                  \n                  <button className=\"btn-secondary w-full flex items-center justify-center gap-2\">\n                    <Play className=\"h-4 w-4\" />\n                    Watch Now\n                  </button>\n                </div>\n              ))}\n            </div>\n          </div>\n        </section>\n\n        {/* Newsletter Signup */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"glass p-12 text-center\">\n              <h2 className=\"text-3xl font-bold text-white mb-4\">\n                Never Miss a Webinar\n              </h2>\n              <p className=\"text-xl text-white/70 mb-8\">\n                Subscribe to get notified about upcoming webinars and exclusive content\n              </p>\n              <div className=\"flex flex-col sm:flex-row gap-4 max-w-md mx-auto\">\n                <input\n                  type=\"email\"\n                  placeholder=\"Enter your email\"\n                  className=\"glass-input flex-1\"\n                />\n                <button className=\"btn-primary px-6 py-3 flex items-center gap-2\">\n                  <Bell className=\"h-4 w-4\" />\n                  Subscribe\n                </button>\n              </div>\n              <p className=\"text-white/60 text-sm mt-4\">\n                Get weekly updates and early access to webinar recordings\n              </p>\n            </div>\n          </div>\n        </section>\n\n        {/* Webinar Benefits */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-7xl mx-auto\">\n            <h2 className=\"text-3xl font-bold text-white mb-12 text-center\">Why Attend Our Webinars?</h2>\n            \n            <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-6\">\n              <div className=\"glass p-6 text-center\">\n                <Users className=\"h-12 w-12 text-purple-400 mx-auto mb-4\" />\n                <h3 className=\"text-lg font-semibold text-white mb-2\">Expert Insights</h3>\n                <p className=\"text-white/70 text-sm\">Learn from industry experts and StreamIt Pro team members</p>\n              </div>\n              <div className=\"glass p-6 text-center\">\n                <Play className=\"h-12 w-12 text-blue-400 mx-auto mb-4\" />\n                <h3 className=\"text-lg font-semibold text-white mb-2\">Interactive Sessions</h3>\n                <p className=\"text-white/70 text-sm\">Participate in Q&A sessions and live demonstrations</p>\n              </div>\n              <div className=\"glass p-6 text-center\">\n                <Calendar className=\"h-12 w-12 text-green-400 mx-auto mb-4\" />\n                <h3 className=\"text-lg font-semibold text-white mb-2\">Regular Schedule</h3>\n                <p className=\"text-white/70 text-sm\">New webinars every week covering different topics</p>\n              </div>\n              <div className=\"glass p-6 text-center\">\n                <ArrowRight className=\"h-12 w-12 text-orange-400 mx-auto mb-4\" />\n                <h3 className=\"text-lg font-semibold text-white mb-2\">Actionable Tips</h3>\n                <p className=\"text-white/70 text-sm\">Get practical advice you can implement immediately</p>\n              </div>\n            </div>\n          </div>\n        </section>\n      </div>\n    </>\n  )\n}\n", "export default {\n  xmlns: 'http://www.w3.org/2000/svg',\n  width: 24,\n  height: 24,\n  viewBox: '0 0 24 24',\n  fill: 'none',\n  stroke: 'currentColor',\n  strokeWidth: 2,\n  strokeLinecap: 'round',\n  strokeLinejoin: 'round',\n};\n", "import {\n  forwardRef,\n  createElement,\n  ReactSVG,\n  SVGProps,\n  ForwardRefExoticComponent,\n  RefAttributes,\n} from 'react';\nimport defaultAttributes from './defaultAttributes';\n\nexport type IconNode = [elementName: keyof ReactSVG, attrs: Record<string, string>][];\n\nexport type SVGAttributes = Partial<SVGProps<SVGSVGElement>>;\ntype ComponentAttributes = RefAttributes<SVGSVGElement> & SVGAttributes;\n\nexport interface LucideProps extends ComponentAttributes {\n  size?: string | number;\n  absoluteStrokeWidth?: boolean;\n}\n\nexport type LucideIcon = ForwardRefExoticComponent<LucideProps>;\n/**\n * Converts string to KebabCase\n * Copied from scripts/helper. If anyone knows how to properly import it here\n * then please fix it.\n *\n * @param {string} string\n * @returns {string} A kebabized string\n */\nexport const toKebabCase = (string: string) =>\n  string\n    .replace(/([a-z0-9])([A-Z])/g, '$1-$2')\n    .toLowerCase()\n    .trim();\n\nconst createLucideIcon = (iconName: string, iconNode: IconNode): LucideIcon => {\n  const Component = forwardRef<SVGSVGElement, LucideProps>(\n    ({ color = 'currentColor', size = 24, strokeWidth = 2, absoluteStrokeWidth, className = '', children, ...rest }, ref) =>\n      createElement(\n        'svg',\n        {\n          ref,\n          ...defaultAttributes,\n          width: size,\n          height: size,\n          stroke: color,\n          strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n          className: ['lucide', `lucide-${toKebabCase(iconName)}`, className].join(' '),\n          ...rest,\n        },\n        [\n          ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n          ...(Array.isArray(children) ? children : [children]),\n        ]\n      )\n  );\n\n  Component.displayName = `${iconName}`;\n\n  return Component;\n};\n\nexport default createLucideIcon;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ArrowRight\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSAxMmgxNCIgLz4KICA8cGF0aCBkPSJtMTIgNSA3IDctNyA3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/arrow-right\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowRight = createLucideIcon('ArrowRight', [\n  ['path', { d: 'M5 12h14', key: '1ays0h' }],\n  ['path', { d: 'm12 5 7 7-7 7', key: 'xquz4c' }],\n]);\n\nexport default ArrowRight;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Calendar\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHg9IjMiIHk9IjQiIHJ4PSIyIiByeT0iMiIgLz4KICA8bGluZSB4MT0iMTYiIHgyPSIxNiIgeTE9IjIiIHkyPSI2IiAvPgogIDxsaW5lIHgxPSI4IiB4Mj0iOCIgeTE9IjIiIHkyPSI2IiAvPgogIDxsaW5lIHgxPSIzIiB4Mj0iMjEiIHkxPSIxMCIgeTI9IjEwIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/calendar\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Calendar = createLucideIcon('Calendar', [\n  ['rect', { width: '18', height: '18', x: '3', y: '4', rx: '2', ry: '2', key: 'eu3xkr' }],\n  ['line', { x1: '16', x2: '16', y1: '2', y2: '6', key: 'm3sa8f' }],\n  ['line', { x1: '8', x2: '8', y1: '2', y2: '6', key: '18kwsl' }],\n  ['line', { x1: '3', x2: '21', y1: '10', y2: '10', key: 'xt86sb' }],\n]);\n\nexport default Calendar;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Clock\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8cG9seWxpbmUgcG9pbnRzPSIxMiA2IDEyIDEyIDE2IDE0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/clock\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Clock = createLucideIcon('Clock', [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['polyline', { points: '12 6 12 12 16 14', key: '68esgv' }],\n]);\n\nexport default Clock;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Play\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWdvbiBwb2ludHM9IjUgMyAxOSAxMiA1IDIxIDUgMyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/play\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Play = createLucideIcon('Play', [\n  ['polygon', { points: '5 3 19 12 5 21 5 3', key: '191637' }],\n]);\n\nexport default Play;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Users\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgMjF2LTJhNCA0IDAgMCAwLTQtNEg2YTQgNCAwIDAgMC00IDR2MiIgLz4KICA8Y2lyY2xlIGN4PSI5IiBjeT0iNyIgcj0iNCIgLz4KICA8cGF0aCBkPSJNMjIgMjF2LTJhNCA0IDAgMCAwLTMtMy44NyIgLz4KICA8cGF0aCBkPSJNMTYgMy4xM2E0IDQgMCAwIDEgMCA3Ljc1IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/users\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Users = createLucideIcon('Users', [\n  ['path', { d: 'M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2', key: '1yyitq' }],\n  ['circle', { cx: '9', cy: '7', r: '4', key: 'nufk8' }],\n  ['path', { d: 'M22 21v-2a4 4 0 0 0-3-3.87', key: 'kshegd' }],\n  ['path', { d: 'M16 3.13a4 4 0 0 1 0 7.75', key: '1da9ce' }],\n]);\n\nexport default Users;\n"], "names": ["Promise", "resolve", "then", "__webpack_require__", "bind", "Bell", "createLucideIcon", "d", "key", "dynamic", "WebinarsPage", "jsx_runtime", "jsxs", "Fragment", "jsx", "div", "className", "section", "h1", "p", "h2", "upcomingWebinars", "id", "title", "description", "date", "time", "duration", "presenter", "attendees", "image", "map", "webinar", "index", "concat", "h3", "Calendar", "span", "Clock", "Users", "button", "pastWebinars", "views", "Play", "input", "type", "placeholder", "ArrowRight", "defaultAttributes", "xmlns", "width", "height", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "toKebabCase", "string", "replace", "toLowerCase", "trim", "iconName", "iconNode", "Component", "forwardRef", "param", "ref", "color", "size", "absoluteStrokeWidth", "children", "rest", "createElement", "Number", "join", "tag", "attrs", "Array", "isArray", "displayName", "x", "y", "rx", "ry", "x1", "x2", "y1", "y2", "cx", "cy", "r", "points"], "sourceRoot": ""}