{"version": 3, "file": "static/chunks/app/page-cf6fd70f7371443a.js", "mappings": "oFAAAA,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA;;;;;GCaM,IAAAE,EAAWC,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,WAAY,CAC5C,CAAC,OAAQ,CAAEC,EAAG,4CAA6CC,IAAK,UAAU,CAC1E,CAAC,SAAU,CAAEC,GAAI,IAAKC,GAAI,IAAKC,EAAG,IAAKH,IAAK,SAAS,CACrD,CAAC,OAAQ,CAAEI,GAAI,KAAMC,GAAI,KAAMC,GAAI,IAAKC,GAAI,KAAMP,IAAK,UAAU,CACjE,CAAC,OAAQ,CAAEI,GAAI,KAAMC,GAAI,KAAMC,GAAI,KAAMC,GAAI,KAAMP,IAAK,UAAU,CACnE,mCCbM,IAAMQ,EAAU,gBAIR,SAASC,IACtB,GAAM,CAACC,EAAeC,EAAiB,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IAC7C,CAACC,EAAeC,EAAiB,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IAC7C,CAACG,EAAUC,EAAY,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IACnC,CAACK,EAAQC,EAAU,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IAC/B,CAACO,EAAYC,EAAc,CAAGR,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IACvCS,EAASC,CAAAA,EAAAA,EAAAA,SAAAA,IAYTC,EAAe,KACnB,GAAI,CAACR,EAASS,IAAI,GAAI,CACpBC,MAAM,0BACN,MACF,CAEA,IAAMC,EAAYC,KAAKC,MAAM,GAAGC,QAAQ,CAAC,IAAIC,SAAS,CAAC,EAAG,IAC1DC,aAAaC,OAAO,CAAC,WAAYjB,EAASS,IAAI,IAC9Cb,EAAiB,IACjBU,EAAOY,IAAI,CAAC,SAAmBC,MAAA,CAAVR,GACvB,EAEMS,EAAW,KACf,GAAI,CAAClB,EAAOO,IAAI,IAAM,CAACT,EAASS,IAAI,GAAI,CACtCC,MAAM,2CACN,MACF,CAEAM,aAAaC,OAAO,CAAC,WAAYjB,EAASS,IAAI,IAC9CV,EAAiB,IACjBO,EAAOY,IAAI,CAAC,SAAuBC,MAAA,CAAdjB,EAAOO,IAAI,IAClC,EAEMY,EAAc,KAClBzB,EAAiB,IACjBG,EAAiB,IACjBE,EAAY,IACZE,EAAU,GACZ,EAEA,MACE,GAAAmB,EAAAC,IAAA,EAAAD,EAAAE,QAAA,YAEE,GAAAF,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,gBAEf,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,qFAEb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sCACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,kDACb,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,+HACb,GAAAL,EAAAG,GAAA,EAACG,EAAAA,CAAKA,CAAAA,CAACD,UAAU,yBAEnB,GAAAL,EAAAG,GAAA,EAACI,KAAAA,CAAGF,UAAU,yCAAgC,oBAIhD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,mEAA0D,yIAOzE,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,2DACb,GAAAL,EAAAC,IAAA,EAACQ,SAAAA,CACCC,QAjEiB,KACzBpC,EAAiB,IACjBS,EAAc,GAChB,EA+DUsB,UAAU,+FAEV,GAAAL,EAAAG,GAAA,EAACQ,EAAAA,CAAIA,CAAAA,CAACN,UAAU,YAAY,mBAI9B,GAAAL,EAAAC,IAAA,EAACQ,SAAAA,CACCC,QApEgB,KACxBjC,EAAiB,IACjBM,EAAc,GAChB,EAkEUsB,UAAU,iGAEV,GAAAL,EAAAG,GAAA,EAAC3C,EAAQA,CAAC6C,UAAU,YAAY,qBAMpC,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,iEACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,kCACb,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,2HACb,GAAAL,EAAAG,GAAA,EAACS,EAAAA,CAAKA,CAAAA,CAACP,UAAU,yBAEnB,GAAAL,EAAAG,GAAA,EAACU,KAAAA,CAAGR,UAAU,iDAAwC,sBACtD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,yBAAgB,yGAK/B,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,kCACb,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,+HACb,GAAAL,EAAAG,GAAA,EAACW,EAAAA,CAAMA,CAAAA,CAACT,UAAU,yBAEpB,GAAAL,EAAAG,GAAA,EAACU,KAAAA,CAAGR,UAAU,iDAAwC,qBACtD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,yBAAgB,qGAK/B,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,kCACb,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,+HACb,GAAAL,EAAAG,GAAA,EAACY,EAAAA,CAAGA,CAAAA,CAACV,UAAU,yBAEjB,GAAAL,EAAAG,GAAA,EAACU,KAAAA,CAAGR,UAAU,iDAAwC,mBACtD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,yBAAgB,yGAOjC,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,2CACb,GAAAL,EAAAG,GAAA,EAACK,IAAAA,UAAE,0EAKNnC,GACC,GAAA2B,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,gGACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sCACb,GAAAL,EAAAG,GAAA,EAACa,KAAAA,CAAGX,UAAU,0DACXvB,EAAa,qBAAuB,iBAEvC,GAAAkB,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sBACb,GAAAL,EAAAG,GAAA,EAACc,QAAAA,CACCC,KAAK,OACLC,YAAY,kBACZC,MAAO1C,EACP2C,SAAU,GAAO1C,EAAY2C,EAAEC,MAAM,CAACH,KAAK,EAC3Cf,UAAU,qBACVmB,UAAS,GACTC,UAAW,GAAOH,UAAAA,EAAE3D,GAAG,EAAgBuB,MAEzC,GAAAc,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,uBACb,GAAAL,EAAAG,GAAA,EAACM,SAAAA,CACCC,QAASX,EACTM,UAAU,gCACX,WAGD,GAAAL,EAAAG,GAAA,EAACM,SAAAA,CACCC,QAASxB,EACTmB,UAAU,8BACX,6BAUV7B,GACC,GAAAwB,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,gGACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sCACb,GAAAL,EAAAG,GAAA,EAACa,KAAAA,CAAGX,UAAU,0DAAiD,iBAG/D,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sBACb,GAAAL,EAAAG,GAAA,EAACc,QAAAA,CACCC,KAAK,OACLC,YAAY,kBACZC,MAAO1C,EACP2C,SAAU,GAAO1C,EAAY2C,EAAEC,MAAM,CAACH,KAAK,EAC3Cf,UAAU,qBACVmB,UAAS,KAEX,GAAAxB,EAAAG,GAAA,EAACc,QAAAA,CACCC,KAAK,OACLC,YAAY,mBACZC,MAAOxC,EACPyC,SAAU,GAAOxC,EAAUyC,EAAEC,MAAM,CAACH,KAAK,EACzCf,UAAU,qBACVoB,UAAW,GAAOH,UAAAA,EAAE3D,GAAG,EAAgBmC,MAEzC,GAAAE,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,uBACb,GAAAL,EAAAG,GAAA,EAACM,SAAAA,CACCC,QAASX,EACTM,UAAU,gCACX,WAGD,GAAAL,EAAAG,GAAA,EAACM,SAAAA,CACCC,QAASZ,EACTO,UAAU,8BACX,8BAUjB,mFCzNeqB,EAAA,CACbC,MAAO,6BACPC,MAAO,GACPC,OAAQ,GACRC,QAAS,YACTC,KAAM,OACNC,OAAQ,eACRC,YAAa,EACbC,cAAe,QACfC,eAAgB,OAClB;;;;;GCmBa,IAAAC,EAAc,GACzBC,EACGC,OAAA,CAAQ,qBAAsB,SAC9BC,WAAY,GACZpD,IAAK,GAEJ1B,EAAmB,CAAC+E,EAAkBC,KAC1C,IAAMC,EAAYC,CAAAA,EAAAA,EAAAA,UAAAA,EAChB,CAAAC,EAAiHC,QAAhH,CAAEC,MAAAA,EAAQ,eAAgBC,KAAAA,EAAO,EAAI,CAAAd,YAAAA,EAAc,CAAG,CAAAe,oBAAAA,CAAA,CAAqB3C,UAAAA,EAAY,GAAI4C,SAAAA,CAAa,IAAAC,EAAA,CAAAN,QACvGO,CAAAA,EAAAA,EAAAA,aAAAA,EACE,MACA,CACEN,IAAAA,EACA,GAAGnB,CAAA,CACHE,MAAOmB,EACPlB,OAAQkB,EACRf,OAAQc,EACRb,YAAae,EAAsBI,GAAAA,OAAOnB,GAAoBmB,OAAOL,GAAQd,EAC7E5B,UAAW,CAAC,SAAoB,UAAyBR,MAAA,CAAzBuC,EAAYI,IAAanC,EAAW,CAAAgD,IAAA,CAAK,KACzE,GAAGH,CAAA,EAEL,IACKT,EAASa,GAAA,CAAI,OAAC,CAACC,EAAKC,EAAW,CAAAZ,QAAAO,CAAAA,EAAAA,EAAAA,aAAAA,EAAcI,EAAKC,QACjDC,MAAMC,OAAA,CAAQT,GAAYA,EAAW,CAACA,EAAQ,CACpD,IAMC,OAFPP,EAAUiB,WAAA,CAAc,GAAG9D,MAAA,CAAA2C,GAEpBE,CACT;;;;;GC/CM,IAAA/B,EAAOlD,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,OAAQ,CACpC,CAAC,UAAW,CAAEmG,OAAQ,qBAAsBjG,IAAK,UAAU,CAC5D;;;;;GCFK,IAAAmD,EAASrD,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,SAAU,CACxC,CAAC,OAAQ,CAAEC,EAAG,6CAA8CC,IAAK,UAAU,CAC5E;;;;;GCFK,IAAAiD,EAAQnD,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,QAAS,CACtC,CAAC,OAAQ,CAAEC,EAAG,4CAA6CC,IAAK,UAAU,CAC1E,CAAC,SAAU,CAAEC,GAAI,IAAKC,GAAI,IAAKC,EAAG,IAAKH,IAAK,SAAS,CACrD,CAAC,OAAQ,CAAED,EAAG,6BAA8BC,IAAK,UAAU,CAC3D,CAAC,OAAQ,CAAED,EAAG,4BAA6BC,IAAK,UAAU,CAC3D;;;;;GCLK,IAAA2C,EAAQ7C,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,QAAS,CACtC,CAAC,OAAQ,CAAEC,EAAG,mBAAoBC,IAAK,UAAU,CACjD,CAAC,OAAQ,CAAEiE,MAAO,KAAMC,OAAQ,KAAMgC,EAAG,IAAKC,EAAG,IAAKC,GAAI,IAAKC,GAAI,IAAKrG,IAAK,UAAU,CACxF;;;;;GCHK,IAAAoD,EAAMtD,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,MAAO,CAClC,CAAC,UAAW,CAAEmG,OAAQ,yCAA0CjG,IAAK,UAAU,CAChF", "sources": ["webpack://_N_E/", "webpack://_N_E/../../../src/icons/user-plus.ts", "webpack://_N_E/./app/page.tsx", "webpack://_N_E/../../src/defaultAttributes.ts", "webpack://_N_E/../../src/createLucideIcon.ts", "webpack://_N_E/../../../src/icons/play.ts", "webpack://_N_E/../../../src/icons/shield.ts", "webpack://_N_E/../../../src/icons/users.ts", "webpack://_N_E/../../../src/icons/video.ts", "webpack://_N_E/../../../src/icons/zap.ts", "webpack://_N_E/./node_modules/next/dist/api/navigation.js"], "sourcesContent": ["import(/* webpackMode: \"eager\" */ \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\");\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name UserPlus\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgMjF2LTJhNCA0IDAgMCAwLTQtNEg2YTQgNCAwIDAgMC00IDR2MiIgLz4KICA8Y2lyY2xlIGN4PSI5IiBjeT0iNyIgcj0iNCIgLz4KICA8bGluZSB4MT0iMTkiIHgyPSIxOSIgeTE9IjgiIHkyPSIxNCIgLz4KICA8bGluZSB4MT0iMjIiIHgyPSIxNiIgeTE9IjExIiB5Mj0iMTEiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/user-plus\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst UserPlus = createLucideIcon('UserPlus', [\n  ['path', { d: 'M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2', key: '1yyitq' }],\n  ['circle', { cx: '9', cy: '7', r: '4', key: 'nufk8' }],\n  ['line', { x1: '19', x2: '19', y1: '8', y2: '14', key: '1bvyxn' }],\n  ['line', { x1: '22', x2: '16', y1: '11', y2: '11', key: '1shjgl' }],\n]);\n\nexport default UserPlus;\n", "'use client'\n\nimport { useState } from 'react'\n\n// Force dynamic rendering\nexport const dynamic = 'force-dynamic'\nimport { useRouter } from 'next/navigation'\nimport { Video, Users, Shield, Zap, Play, UserPlus } from 'lucide-react'\n\nexport default function HomePage() {\n  const [showNameModal, setShowNameModal] = useState(false)\n  const [showJoinModal, setShowJoinModal] = useState(false)\n  const [userName, setUserName] = useState('')\n  const [roomId, setRoomId] = useState('')\n  const [isStarting, setIsStarting] = useState(false)\n  const router = useRouter()\n\n  const handleStartMeeting = () => {\n    setShowNameModal(true)\n    setIsStarting(true)\n  }\n\n  const handleJoinMeeting = () => {\n    setShowJoinModal(true)\n    setIsStarting(false)\n  }\n\n  const startMeeting = () => {\n    if (!userName.trim()) {\n      alert('Please enter your name')\n      return\n    }\n\n    const newRoomId = Math.random().toString(36).substring(2, 15)\n    localStorage.setItem('userName', userName.trim())\n    setShowNameModal(false)\n    router.push(`/room/${newRoomId}`)\n  }\n\n  const joinRoom = () => {\n    if (!roomId.trim() || !userName.trim()) {\n      alert('Please enter both room ID and your name')\n      return\n    }\n\n    localStorage.setItem('userName', userName.trim())\n    setShowJoinModal(false)\n    router.push(`/room/${roomId.trim()}`)\n  }\n\n  const closeModals = () => {\n    setShowNameModal(false)\n    setShowJoinModal(false)\n    setUserName('')\n    setRoomId('')\n  }\n\n  return (\n    <>\n      {/* Animated Background */}\n      <div className=\"animated-bg\"></div>\n\n      <div className=\"min-h-screen flex flex-col items-center justify-center p-4 relative z-10\">\n        {/* Header */}\n        <div className=\"text-center mb-12 fade-in\">\n          <div className=\"flex items-center justify-center mb-6\">\n            <div className=\"w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mr-4 shadow-lg\">\n              <Video className=\"h-8 w-8 text-white\" />\n            </div>\n            <h1 className=\"text-5xl font-bold text-white\">\n              StreamIt Pro\n            </h1>\n          </div>\n          <p className=\"text-xl text-white/80 max-w-2xl mx-auto leading-relaxed\">\n            Professional video conferencing platform with crystal-clear HD video,\n            advanced audio processing, and seamless collaboration tools\n          </p>\n        </div>\n\n        {/* Main Action Buttons */}\n        <div className=\"flex flex-col sm:flex-row gap-6 mb-16 slide-up\">\n          <button\n            onClick={handleStartMeeting}\n            className=\"btn-primary flex items-center gap-3 text-lg px-8 py-4 min-w-[200px] justify-center\"\n          >\n            <Play className=\"h-6 w-6\" />\n            Start Meeting\n          </button>\n\n          <button\n            onClick={handleJoinMeeting}\n            className=\"btn-secondary flex items-center gap-3 text-lg px-8 py-4 min-w-[200px] justify-center\"\n          >\n            <UserPlus className=\"h-6 w-6\" />\n            Join Meeting\n          </button>\n        </div>\n\n        {/* Features Grid */}\n        <div className=\"grid md:grid-cols-3 gap-8 max-w-4xl w-full bounce-in\">\n          <div className=\"glass text-center p-8\">\n            <div className=\"w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center mx-auto mb-4\">\n              <Users className=\"h-8 w-8 text-white\" />\n            </div>\n            <h3 className=\"text-xl font-semibold text-white mb-3\">Multi-Participant</h3>\n            <p className=\"text-white/70\">\n              Connect with multiple participants in crystal-clear HD video calls with advanced audio processing\n            </p>\n          </div>\n\n          <div className=\"glass text-center p-8\">\n            <div className=\"w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4\">\n              <Shield className=\"h-8 w-8 text-white\" />\n            </div>\n            <h3 className=\"text-xl font-semibold text-white mb-3\">Secure & Private</h3>\n            <p className=\"text-white/70\">\n              End-to-end encrypted communications with WebRTC technology ensuring your privacy and security\n            </p>\n          </div>\n\n          <div className=\"glass text-center p-8\">\n            <div className=\"w-16 h-16 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4\">\n              <Zap className=\"h-8 w-8 text-white\" />\n            </div>\n            <h3 className=\"text-xl font-semibold text-white mb-3\">Lightning Fast</h3>\n            <p className=\"text-white/70\">\n              Optimized for performance with minimal latency, adaptive quality, and seamless user experience\n            </p>\n          </div>\n        </div>\n\n        {/* Footer */}\n        <div className=\"text-center mt-16 text-white/60\">\n          <p>Built with Next.js, TypeScript, WebRTC & Modern Web Technologies</p>\n        </div>\n      </div>\n\n      {/* Name Input Modal */}\n      {showNameModal && (\n        <div className=\"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50\">\n          <div className=\"glass p-8 max-w-md w-full\">\n            <h2 className=\"text-2xl font-bold text-white mb-6 text-center\">\n              {isStarting ? 'Start Your Meeting' : 'Join Meeting'}\n            </h2>\n            <div className=\"space-y-4\">\n              <input\n                type=\"text\"\n                placeholder=\"Enter your name\"\n                value={userName}\n                onChange={(e) => setUserName(e.target.value)}\n                className=\"glass-input w-full\"\n                autoFocus\n                onKeyDown={(e) => e.key === 'Enter' && startMeeting()}\n              />\n              <div className=\"flex gap-3\">\n                <button\n                  onClick={closeModals}\n                  className=\"btn-secondary flex-1\"\n                >\n                  Cancel\n                </button>\n                <button\n                  onClick={startMeeting}\n                  className=\"btn-primary flex-1\"\n                >\n                  Start Meeting\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Join Meeting Modal */}\n      {showJoinModal && (\n        <div className=\"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50\">\n          <div className=\"glass p-8 max-w-md w-full\">\n            <h2 className=\"text-2xl font-bold text-white mb-6 text-center\">\n              Join Meeting\n            </h2>\n            <div className=\"space-y-4\">\n              <input\n                type=\"text\"\n                placeholder=\"Enter your name\"\n                value={userName}\n                onChange={(e) => setUserName(e.target.value)}\n                className=\"glass-input w-full\"\n                autoFocus\n              />\n              <input\n                type=\"text\"\n                placeholder=\"Enter meeting ID\"\n                value={roomId}\n                onChange={(e) => setRoomId(e.target.value)}\n                className=\"glass-input w-full\"\n                onKeyDown={(e) => e.key === 'Enter' && joinRoom()}\n              />\n              <div className=\"flex gap-3\">\n                <button\n                  onClick={closeModals}\n                  className=\"btn-secondary flex-1\"\n                >\n                  Cancel\n                </button>\n                <button\n                  onClick={joinRoom}\n                  className=\"btn-primary flex-1\"\n                >\n                  Join Meeting\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </>\n  )\n}\n", "export default {\n  xmlns: 'http://www.w3.org/2000/svg',\n  width: 24,\n  height: 24,\n  viewBox: '0 0 24 24',\n  fill: 'none',\n  stroke: 'currentColor',\n  strokeWidth: 2,\n  strokeLinecap: 'round',\n  strokeLinejoin: 'round',\n};\n", "import {\n  forwardRef,\n  createElement,\n  ReactSVG,\n  SVGProps,\n  ForwardRefExoticComponent,\n  RefAttributes,\n} from 'react';\nimport defaultAttributes from './defaultAttributes';\n\nexport type IconNode = [elementName: keyof ReactSVG, attrs: Record<string, string>][];\n\nexport type SVGAttributes = Partial<SVGProps<SVGSVGElement>>;\ntype ComponentAttributes = RefAttributes<SVGSVGElement> & SVGAttributes;\n\nexport interface LucideProps extends ComponentAttributes {\n  size?: string | number;\n  absoluteStrokeWidth?: boolean;\n}\n\nexport type LucideIcon = ForwardRefExoticComponent<LucideProps>;\n/**\n * Converts string to KebabCase\n * Copied from scripts/helper. If anyone knows how to properly import it here\n * then please fix it.\n *\n * @param {string} string\n * @returns {string} A kebabized string\n */\nexport const toKebabCase = (string: string) =>\n  string\n    .replace(/([a-z0-9])([A-Z])/g, '$1-$2')\n    .toLowerCase()\n    .trim();\n\nconst createLucideIcon = (iconName: string, iconNode: IconNode): LucideIcon => {\n  const Component = forwardRef<SVGSVGElement, LucideProps>(\n    ({ color = 'currentColor', size = 24, strokeWidth = 2, absoluteStrokeWidth, className = '', children, ...rest }, ref) =>\n      createElement(\n        'svg',\n        {\n          ref,\n          ...defaultAttributes,\n          width: size,\n          height: size,\n          stroke: color,\n          strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n          className: ['lucide', `lucide-${toKebabCase(iconName)}`, className].join(' '),\n          ...rest,\n        },\n        [\n          ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n          ...(Array.isArray(children) ? children : [children]),\n        ]\n      )\n  );\n\n  Component.displayName = `${iconName}`;\n\n  return Component;\n};\n\nexport default createLucideIcon;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Play\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWdvbiBwb2ludHM9IjUgMyAxOSAxMiA1IDIxIDUgMyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/play\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Play = createLucideIcon('Play', [\n  ['polygon', { points: '5 3 19 12 5 21 5 3', key: '191637' }],\n]);\n\nexport default Play;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Shield\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMjJzOC00IDgtMTBWNWwtOC0zLTggM3Y3YzAgNiA4IDEwIDggMTAiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/shield\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Shield = createLucideIcon('Shield', [\n  ['path', { d: 'M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10', key: '1irkt0' }],\n]);\n\nexport default Shield;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Users\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgMjF2LTJhNCA0IDAgMCAwLTQtNEg2YTQgNCAwIDAgMC00IDR2MiIgLz4KICA8Y2lyY2xlIGN4PSI5IiBjeT0iNyIgcj0iNCIgLz4KICA8cGF0aCBkPSJNMjIgMjF2LTJhNCA0IDAgMCAwLTMtMy44NyIgLz4KICA8cGF0aCBkPSJNMTYgMy4xM2E0IDQgMCAwIDEgMCA3Ljc1IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/users\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Users = createLucideIcon('Users', [\n  ['path', { d: 'M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2', key: '1yyitq' }],\n  ['circle', { cx: '9', cy: '7', r: '4', key: 'nufk8' }],\n  ['path', { d: 'M22 21v-2a4 4 0 0 0-3-3.87', key: 'kshegd' }],\n  ['path', { d: 'M16 3.13a4 4 0 0 1 0 7.75', key: '1da9ce' }],\n]);\n\nexport default Users;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Video\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMjIgOC02IDQgNiA0VjhaIiAvPgogIDxyZWN0IHdpZHRoPSIxNCIgaGVpZ2h0PSIxMiIgeD0iMiIgeT0iNiIgcng9IjIiIHJ5PSIyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/video\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Video = createLucideIcon('Video', [\n  ['path', { d: 'm22 8-6 4 6 4V8Z', key: '50v9me' }],\n  ['rect', { width: '14', height: '12', x: '2', y: '6', rx: '2', ry: '2', key: '1rqjg6' }],\n]);\n\nexport default Video;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Zap\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWdvbiBwb2ludHM9IjEzIDIgMyAxNCAxMiAxNCAxMSAyMiAyMSAxMCAxMiAxMCAxMyAyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/zap\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Zap = createLucideIcon('Zap', [\n  ['polygon', { points: '13 2 3 14 12 14 11 22 21 10 12 10 13 2', key: '45s27k' }],\n]);\n\nexport default Zap;\n", "export * from \"../client/components/navigation\";\n\n//# sourceMappingURL=navigation.js.map"], "names": ["Promise", "resolve", "then", "__webpack_require__", "bind", "UserPlus", "createLucideIcon", "d", "key", "cx", "cy", "r", "x1", "x2", "y1", "y2", "dynamic", "HomePage", "showNameModal", "setShowNameModal", "useState", "showJoinModal", "setShowJoinModal", "userName", "setUserName", "roomId", "setRoomId", "isStarting", "setIsStarting", "router", "useRouter", "startMeeting", "trim", "alert", "newRoomId", "Math", "random", "toString", "substring", "localStorage", "setItem", "push", "concat", "joinRoom", "closeModals", "jsx_runtime", "jsxs", "Fragment", "jsx", "div", "className", "Video", "h1", "p", "button", "onClick", "Play", "Users", "h3", "Shield", "Zap", "h2", "input", "type", "placeholder", "value", "onChange", "e", "target", "autoFocus", "onKeyDown", "defaultAttributes", "xmlns", "width", "height", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "toKebabCase", "string", "replace", "toLowerCase", "iconName", "iconNode", "Component", "forwardRef", "param", "ref", "color", "size", "absoluteStrokeWidth", "children", "rest", "createElement", "Number", "join", "map", "tag", "attrs", "Array", "isArray", "displayName", "points", "x", "y", "rx", "ry"], "sourceRoot": ""}