{"version": 3, "file": "static/chunks/app/blog/page-ae1a4082400c1f17.js", "mappings": "oFAAAA,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA;;;;;GCaM,IAAAE,EAAMC,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,MAAO,CAClC,CACE,OACA,CACEC,EAAG,oFACHC,IAAK,QACP,EACF,CACA,CAAC,OAAQ,CAAED,EAAG,WAAYC,IAAK,UAAU,CAC1C,gBCjBM,IAAMC,EAAU,gBAGR,SAASC,IACtB,IAAMC,EAAY,CAChB,CACEC,GAAI,EACJC,MAAO,gEACPC,QAAS,wIACTC,OAAQ,gBACRC,KAAM,aACNC,SAAU,oBACVC,SAAU,aACVC,MAAO,0BACT,EACA,CACEP,GAAI,EACJC,MAAO,+CACPC,QAAS,iIACTC,OAAQ,eACRC,KAAM,aACNC,SAAU,WACVC,SAAU,aACVC,MAAO,0BACT,EACA,CACEP,GAAI,EACJC,MAAO,mDACPC,QAAS,iIACTC,OAAQ,kBACRC,KAAM,aACNC,SAAU,gBACVC,SAAU,aACVC,MAAO,0BACT,EACA,CACEP,GAAI,EACJC,MAAO,0DACPC,QAAS,gIACTC,OAAQ,YACRC,KAAM,aACNC,SAAU,kBACVC,SAAU,aACVC,MAAO,0BACT,EACA,CACEP,GAAI,EACJC,MAAO,+CACPC,QAAS,6HACTC,OAAQ,gBACRC,KAAM,aACNC,SAAU,kBACVC,SAAU,aACVC,MAAO,0BACT,EACA,CACEP,GAAI,EACJC,MAAO,8CACPC,QAAS,sHACTC,OAAQ,YACRC,KAAM,aACNC,SAAU,kBACVC,SAAU,aACVC,MAAO,0BACT,EACD,CAID,MACE,GAAAC,EAAAC,IAAA,EAAAD,EAAAE,QAAA,YAEE,GAAAF,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,gBAEf,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,6CAEb,GAAAL,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,0CACb,GAAAL,EAAAG,GAAA,EAACI,KAAAA,CAAGF,UAAU,sDAA6C,sBAG3D,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,4EAAmE,8HAQpF,GAAAL,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,6BACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,yEAEb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,qCACb,GAAAL,EAAAG,GAAA,EAACM,EAAAA,CAAMA,CAAAA,CAACJ,UAAU,6EAClB,GAAAL,EAAAG,GAAA,EAACO,QAAAA,CACCC,KAAK,OACLC,YAAY,qBACZP,UAAU,gCAKd,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,gCACZQ,CArCK,MAAO,oBAAqB,WAAY,gBAAiB,kBAAmB,kBAAkB,CAqCxFC,GAAG,CAAC,CAACjB,EAAUkB,IACzB,GAAAf,EAAAG,GAAA,EAACa,SAAAA,CAECX,UAAW,oDAIVY,MAAA,CAHCF,IAAAA,EACI,2BACA,wDAGLlB,GAPIkB,aAgBjB,GAAAf,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,6BACb,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,6BACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,mDACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,WACC,GAAAJ,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,yCACb,GAAAL,EAAAG,GAAA,EAACe,OAAAA,CAAKb,UAAU,mEAA0D,aAC1E,GAAAL,EAAAG,GAAA,EAACe,OAAAA,CAAKb,UAAU,iCAAyBd,CAAS,CAAC,EAAE,CAACM,QAAQ,MAEhE,GAAAG,EAAAG,GAAA,EAACgB,KAAAA,CAAGd,UAAU,8CAAsCd,CAAS,CAAC,EAAE,CAACE,KAAK,GACtE,GAAAO,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,8BAAsBd,CAAS,CAAC,EAAE,CAACG,OAAO,GACvD,GAAAM,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,yCACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,oCACb,GAAAL,EAAAG,GAAA,EAACiB,EAAAA,CAAIA,CAAAA,CAACf,UAAU,0BAChB,GAAAL,EAAAG,GAAA,EAACe,OAAAA,CAAKb,UAAU,iCAAyBd,CAAS,CAAC,EAAE,CAACI,MAAM,MAE9D,GAAAK,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,oCACb,GAAAL,EAAAG,GAAA,EAACkB,EAAAA,CAAQA,CAAAA,CAAChB,UAAU,0BACpB,GAAAL,EAAAG,GAAA,EAACe,OAAAA,CAAKb,UAAU,iCAAyBd,CAAS,CAAC,EAAE,CAACK,IAAI,MAE5D,GAAAI,EAAAG,GAAA,EAACe,OAAAA,CAAKb,UAAU,iCAAyBd,CAAS,CAAC,EAAE,CAACO,QAAQ,MAEhE,GAAAE,EAAAC,IAAA,EAACqB,EAAAA,OAAIA,CAAAA,CAACC,KAAM,SAAyBN,MAAA,CAAhB1B,CAAS,CAAC,EAAE,CAACC,EAAE,EAAIa,UAAU,uDAA6C,eAE7F,GAAAL,EAAAG,GAAA,EAACqB,EAAAA,CAAUA,CAAAA,CAACnB,UAAU,kBAG1B,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,qCACb,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,uHACb,GAAAL,EAAAG,GAAA,EAACe,OAAAA,CAAKb,UAAU,yBAAgB,wCAS5C,GAAAL,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,8BACb,GAAAL,EAAAG,GAAA,EAACgB,KAAAA,CAAGd,UAAU,2DAAkD,oBAEhE,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,oDACZd,EAAUkC,KAAK,CAAC,GAAGX,GAAG,CAAC,GACtB,GAAAd,EAAAC,IAAA,EAACyB,UAAAA,CAAsBrB,UAAU,kFAC/B,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,4HACb,GAAAL,EAAAG,GAAA,EAACe,OAAAA,CAAKb,UAAU,yBAAgB,oBAGlC,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,yCACb,GAAAL,EAAAG,GAAA,EAAClB,EAAGA,CAACoB,UAAU,4BACf,GAAAL,EAAAG,GAAA,EAACe,OAAAA,CAAKb,UAAU,mCAA2BsB,EAAK9B,QAAQ,MAG1D,GAAAG,EAAAG,GAAA,EAACyB,KAAAA,CAAGvB,UAAU,8DAAsDsB,EAAKlC,KAAK,GAC9E,GAAAO,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,mDAA2CsB,EAAKjC,OAAO,GAEpE,GAAAM,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,mDACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,oCACb,GAAAL,EAAAG,GAAA,EAACiB,EAAAA,CAAIA,CAAAA,CAACf,UAAU,0BAChB,GAAAL,EAAAG,GAAA,EAACe,OAAAA,CAAKb,UAAU,iCAAyBsB,EAAKhC,MAAM,MAEtD,GAAAK,EAAAG,GAAA,EAACe,OAAAA,CAAKb,UAAU,iCAAyBsB,EAAK7B,QAAQ,MAGxD,GAAAE,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,8CACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,oCACb,GAAAL,EAAAG,GAAA,EAACkB,EAAAA,CAAQA,CAAAA,CAAChB,UAAU,0BACpB,GAAAL,EAAAG,GAAA,EAACe,OAAAA,CAAKb,UAAU,iCAAyBsB,EAAK/B,IAAI,MAEpD,GAAAI,EAAAC,IAAA,EAACqB,EAAAA,OAAIA,CAAAA,CACHC,KAAM,SAAiBN,MAAA,CAARU,EAAKnC,EAAE,EACtBa,UAAU,8FACX,YAEC,GAAAL,EAAAG,GAAA,EAACqB,EAAAA,CAAUA,CAAAA,CAACnB,UAAU,oBA/BdsB,EAAKnC,EAAE,UAyC7B,GAAAQ,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,6BACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,mCACb,GAAAL,EAAAG,GAAA,EAACgB,KAAAA,CAAGd,UAAU,8CAAqC,iBAGnD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,sCAA6B,+EAG1C,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,6DACb,GAAAL,EAAAG,GAAA,EAACO,QAAAA,CACCC,KAAK,QACLC,YAAY,mBACZP,UAAU,uBAEZ,GAAAL,EAAAG,GAAA,EAACa,SAAAA,CAAOX,UAAU,iCAAwB,iBAE5C,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,sCAA6B,iDASxD;;;;;GCjOM,IAAAmB,EAAatC,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,aAAc,CAChD,CAAC,OAAQ,CAAEC,EAAG,WAAYC,IAAK,UAAU,CACzC,CAAC,OAAQ,CAAED,EAAG,gBAAiBC,IAAK,UAAU,CAC/C;;;;;GCHK,IAAAiC,EAAWnC,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,WAAY,CAC5C,CAAC,OAAQ,CAAE2C,MAAO,KAAMC,OAAQ,KAAMC,EAAG,IAAKC,EAAG,IAAKC,GAAI,IAAKC,GAAI,IAAK9C,IAAK,UAAU,CACvF,CAAC,OAAQ,CAAE+C,GAAI,KAAMC,GAAI,KAAMC,GAAI,IAAKC,GAAI,IAAKlD,IAAK,UAAU,CAChE,CAAC,OAAQ,CAAE+C,GAAI,IAAKC,GAAI,IAAKC,GAAI,IAAKC,GAAI,IAAKlD,IAAK,UAAU,CAC9D,CAAC,OAAQ,CAAE+C,GAAI,IAAKC,GAAI,KAAMC,GAAI,KAAMC,GAAI,KAAMlD,IAAK,UAAU,CAClE;;;;;GCLK,IAAAqB,EAASvB,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,SAAU,CACxC,CAAC,SAAU,CAAEqD,GAAI,KAAMC,GAAI,KAAMC,EAAG,IAAKrD,IAAK,UAAU,CACxD,CAAC,OAAQ,CAAED,EAAG,iBAAkBC,IAAK,UAAU,CAChD;;;;;GCHK,IAAAgC,EAAOlC,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,OAAQ,CACpC,CAAC,OAAQ,CAAEC,EAAG,4CAA6CC,IAAK,UAAU,CAC1E,CAAC,SAAU,CAAEmD,GAAI,KAAMC,GAAI,IAAKC,EAAG,IAAKrD,IAAK,UAAU,CACxD", "sources": ["webpack://_N_E/?4a4d", "webpack://_N_E/../../../src/icons/tag.ts", "webpack://_N_E/./app/blog/page.tsx", "webpack://_N_E/../../../src/icons/arrow-right.ts", "webpack://_N_E/../../../src/icons/calendar.ts", "webpack://_N_E/../../../src/icons/search.ts", "webpack://_N_E/../../../src/icons/user.ts"], "sourcesContent": ["import(/* webpackMode: \"eager\" */ \"/Volumes/Apps/Websites/streamit-main/app/blog/page.tsx\");\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Tag\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMkgydjEwbDkuMjkgOS4yOWMuOTQuOTQgMi40OC45NCAzLjQyIDBsNi41OC02LjU4Yy45NC0uOTQuOTQtMi40OCAwLTMuNDJMMTIgMloiIC8+CiAgPHBhdGggZD0iTTcgN2guMDEiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/tag\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Tag = createLucideIcon('Tag', [\n  [\n    'path',\n    {\n      d: 'M12 2H2v10l9.29 9.29c.94.94 2.48.94 3.42 0l6.58-6.58c.94-.94.94-2.48 0-3.42L12 2Z',\n      key: '14b2ls',\n    },\n  ],\n  ['path', { d: 'M7 7h.01', key: '7u93v4' }],\n]);\n\nexport default Tag;\n", "'use client'\n\nimport { Calendar, User, ArrowRight, Tag, Search } from 'lucide-react'\n\n// Force dynamic rendering\nexport const dynamic = 'force-dynamic'\nimport Link from 'next/link'\n\nexport default function BlogPage() {\n  const blogPosts = [\n    {\n      id: 1,\n      title: 'The Future of Remote Work: Video Conferencing Trends for 2024',\n      excerpt: 'Explore the latest trends shaping the future of remote work and how video conferencing technology is evolving to meet new challenges.',\n      author: '<PERSON>',\n      date: '2024-01-15',\n      category: 'Industry Insights',\n      readTime: '5 min read',\n      image: '/api/placeholder/400/250'\n    },\n    {\n      id: 2,\n      title: 'Best Practices for Secure Video Conferencing',\n      excerpt: 'Learn essential security practices to protect your video conferences and ensure your communications remain private and secure.',\n      author: '<PERSON>',\n      date: '2024-01-10',\n      category: 'Security',\n      readTime: '7 min read',\n      image: '/api/placeholder/400/250'\n    },\n    {\n      id: 3,\n      title: 'How to Optimize Your Home Office for Video Calls',\n      excerpt: 'Tips and tricks for creating the perfect home office setup that will make you look and sound professional in every video call.',\n      author: '<PERSON>',\n      date: '2024-01-05',\n      category: 'Tips & Tricks',\n      readTime: '4 min read',\n      image: '/api/placeholder/400/250'\n    },\n    {\n      id: 4,\n      title: 'StreamIt Pro vs Competitors: A Comprehensive Comparison',\n      excerpt: 'An in-depth comparison of StreamIt Pro with other leading video conferencing platforms to help you make an informed decision.',\n      author: 'David Kim',\n      date: '2023-12-28',\n      category: 'Product Updates',\n      readTime: '8 min read',\n      image: '/api/placeholder/400/250'\n    },\n    {\n      id: 5,\n      title: 'Building Better Team Culture in Remote Teams',\n      excerpt: 'Strategies for maintaining strong team culture and collaboration when your team is distributed across different locations.',\n      author: 'Lisa Thompson',\n      date: '2023-12-20',\n      category: 'Team Management',\n      readTime: '6 min read',\n      image: '/api/placeholder/400/250'\n    },\n    {\n      id: 6,\n      title: 'New Features: AI-Powered Noise Cancellation',\n      excerpt: 'Discover our latest AI-powered noise cancellation feature that eliminates background noise for crystal-clear audio.',\n      author: 'Alex Park',\n      date: '2023-12-15',\n      category: 'Product Updates',\n      readTime: '3 min read',\n      image: '/api/placeholder/400/250'\n    }\n  ]\n\n  const categories = ['All', 'Industry Insights', 'Security', 'Tips & Tricks', 'Product Updates', 'Team Management']\n\n  return (\n    <>\n      {/* Animated Background */}\n      <div className=\"animated-bg\"></div>\n\n      <div className=\"min-h-screen pt-20 relative z-10\">\n        {/* Hero Section */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-7xl mx-auto text-center\">\n            <h1 className=\"text-5xl font-bold text-white mb-6 fade-in\">\n              StreamIt Pro Blog\n            </h1>\n            <p className=\"text-xl text-white/80 max-w-3xl mx-auto leading-relaxed slide-up\">\n              Stay updated with the latest insights, tips, and news about video conferencing, \n              remote work, and team collaboration.\n            </p>\n          </div>\n        </section>\n\n        {/* Search and Filter */}\n        <section className=\"py-10 px-4\">\n          <div className=\"max-w-7xl mx-auto\">\n            <div className=\"flex flex-col md:flex-row gap-6 items-center justify-between\">\n              {/* Search */}\n              <div className=\"relative flex-1 max-w-md\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-white/60\" />\n                <input\n                  type=\"text\"\n                  placeholder=\"Search articles...\"\n                  className=\"glass-input w-full pl-10\"\n                />\n              </div>\n\n              {/* Categories */}\n              <div className=\"flex flex-wrap gap-2\">\n                {categories.map((category, index) => (\n                  <button\n                    key={index}\n                    className={`px-4 py-2 rounded-full text-sm transition-colors ${\n                      index === 0 \n                        ? 'bg-purple-500 text-white' \n                        : 'bg-white/10 text-white/70 hover:bg-white/20'\n                    }`}\n                  >\n                    {category}\n                  </button>\n                ))}\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* Featured Post */}\n        <section className=\"py-10 px-4\">\n          <div className=\"max-w-7xl mx-auto\">\n            <div className=\"glass p-8 md:p-12\">\n              <div className=\"grid lg:grid-cols-2 gap-8 items-center\">\n                <div>\n                  <div className=\"flex items-center gap-4 mb-4\">\n                    <span className=\"bg-purple-500 text-white px-3 py-1 rounded-full text-sm\">Featured</span>\n                    <span className=\"text-white/60 text-sm\">{blogPosts[0].category}</span>\n                  </div>\n                  <h2 className=\"text-3xl font-bold text-white mb-4\">{blogPosts[0].title}</h2>\n                  <p className=\"text-white/70 mb-6\">{blogPosts[0].excerpt}</p>\n                  <div className=\"flex items-center gap-6 mb-6\">\n                    <div className=\"flex items-center gap-2\">\n                      <User className=\"h-4 w-4 text-white/60\" />\n                      <span className=\"text-white/60 text-sm\">{blogPosts[0].author}</span>\n                    </div>\n                    <div className=\"flex items-center gap-2\">\n                      <Calendar className=\"h-4 w-4 text-white/60\" />\n                      <span className=\"text-white/60 text-sm\">{blogPosts[0].date}</span>\n                    </div>\n                    <span className=\"text-white/60 text-sm\">{blogPosts[0].readTime}</span>\n                  </div>\n                  <Link href={`/blog/${blogPosts[0].id}`} className=\"btn-primary inline-flex items-center gap-2\">\n                    Read Article\n                    <ArrowRight className=\"h-4 w-4\" />\n                  </Link>\n                </div>\n                <div className=\"glass-dark p-4 rounded-lg\">\n                  <div className=\"w-full h-64 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-lg flex items-center justify-center\">\n                    <span className=\"text-white/60\">Featured Article Image</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* Blog Posts Grid */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-7xl mx-auto\">\n            <h2 className=\"text-3xl font-bold text-white mb-12 text-center\">Latest Articles</h2>\n            \n            <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n              {blogPosts.slice(1).map((post) => (\n                <article key={post.id} className=\"glass p-6 hover:transform hover:scale-105 transition-all duration-300\">\n                  <div className=\"w-full h-48 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-lg mb-6 flex items-center justify-center\">\n                    <span className=\"text-white/60\">Article Image</span>\n                  </div>\n                  \n                  <div className=\"flex items-center gap-2 mb-3\">\n                    <Tag className=\"h-4 w-4 text-purple-400\" />\n                    <span className=\"text-purple-400 text-sm\">{post.category}</span>\n                  </div>\n                  \n                  <h3 className=\"text-xl font-semibold text-white mb-3 line-clamp-2\">{post.title}</h3>\n                  <p className=\"text-white/70 mb-4 text-sm line-clamp-3\">{post.excerpt}</p>\n                  \n                  <div className=\"flex items-center justify-between mb-4\">\n                    <div className=\"flex items-center gap-2\">\n                      <User className=\"h-4 w-4 text-white/60\" />\n                      <span className=\"text-white/60 text-sm\">{post.author}</span>\n                    </div>\n                    <span className=\"text-white/60 text-sm\">{post.readTime}</span>\n                  </div>\n                  \n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center gap-2\">\n                      <Calendar className=\"h-4 w-4 text-white/60\" />\n                      <span className=\"text-white/60 text-sm\">{post.date}</span>\n                    </div>\n                    <Link \n                      href={`/blog/${post.id}`} \n                      className=\"text-purple-400 hover:text-purple-300 text-sm font-medium flex items-center gap-1\"\n                    >\n                      Read More\n                      <ArrowRight className=\"h-3 w-3\" />\n                    </Link>\n                  </div>\n                </article>\n              ))}\n            </div>\n          </div>\n        </section>\n\n        {/* Newsletter Signup */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"glass p-12 text-center\">\n              <h2 className=\"text-3xl font-bold text-white mb-4\">\n                Stay Updated\n              </h2>\n              <p className=\"text-xl text-white/70 mb-8\">\n                Subscribe to our newsletter and never miss the latest insights and updates\n              </p>\n              <div className=\"flex flex-col sm:flex-row gap-4 max-w-md mx-auto\">\n                <input\n                  type=\"email\"\n                  placeholder=\"Enter your email\"\n                  className=\"glass-input flex-1\"\n                />\n                <button className=\"btn-primary px-6 py-3\">Subscribe</button>\n              </div>\n              <p className=\"text-white/60 text-sm mt-4\">\n                No spam, unsubscribe at any time\n              </p>\n            </div>\n          </div>\n        </section>\n      </div>\n    </>\n  )\n}\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ArrowRight\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSAxMmgxNCIgLz4KICA8cGF0aCBkPSJtMTIgNSA3IDctNyA3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/arrow-right\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowRight = createLucideIcon('ArrowRight', [\n  ['path', { d: 'M5 12h14', key: '1ays0h' }],\n  ['path', { d: 'm12 5 7 7-7 7', key: 'xquz4c' }],\n]);\n\nexport default ArrowRight;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Calendar\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHg9IjMiIHk9IjQiIHJ4PSIyIiByeT0iMiIgLz4KICA8bGluZSB4MT0iMTYiIHgyPSIxNiIgeTE9IjIiIHkyPSI2IiAvPgogIDxsaW5lIHgxPSI4IiB4Mj0iOCIgeTE9IjIiIHkyPSI2IiAvPgogIDxsaW5lIHgxPSIzIiB4Mj0iMjEiIHkxPSIxMCIgeTI9IjEwIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/calendar\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Calendar = createLucideIcon('Calendar', [\n  ['rect', { width: '18', height: '18', x: '3', y: '4', rx: '2', ry: '2', key: 'eu3xkr' }],\n  ['line', { x1: '16', x2: '16', y1: '2', y2: '6', key: 'm3sa8f' }],\n  ['line', { x1: '8', x2: '8', y1: '2', y2: '6', key: '18kwsl' }],\n  ['line', { x1: '3', x2: '21', y1: '10', y2: '10', key: 'xt86sb' }],\n]);\n\nexport default Calendar;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Search\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMSIgY3k9IjExIiByPSI4IiAvPgogIDxwYXRoIGQ9Im0yMSAyMS00LjMtNC4zIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/search\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Search = createLucideIcon('Search', [\n  ['circle', { cx: '11', cy: '11', r: '8', key: '4ej97u' }],\n  ['path', { d: 'm21 21-4.3-4.3', key: '1qie3q' }],\n]);\n\nexport default Search;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name User\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTkgMjF2LTJhNCA0IDAgMCAwLTQtNEg5YTQgNCAwIDAgMC00IDR2MiIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjciIHI9IjQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/user\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst User = createLucideIcon('User', [\n  ['path', { d: 'M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2', key: '975kel' }],\n  ['circle', { cx: '12', cy: '7', r: '4', key: '17ys0d' }],\n]);\n\nexport default User;\n"], "names": ["Promise", "resolve", "then", "__webpack_require__", "bind", "Tag", "createLucideIcon", "d", "key", "dynamic", "BlogPage", "blogPosts", "id", "title", "excerpt", "author", "date", "category", "readTime", "image", "jsx_runtime", "jsxs", "Fragment", "jsx", "div", "className", "section", "h1", "p", "Search", "input", "type", "placeholder", "categories", "map", "index", "button", "concat", "span", "h2", "User", "Calendar", "Link", "href", "ArrowRight", "slice", "article", "post", "h3", "width", "height", "x", "y", "rx", "ry", "x1", "x2", "y1", "y2", "cx", "cy", "r"], "sourceRoot": ""}