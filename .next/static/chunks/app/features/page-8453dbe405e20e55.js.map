{"version": 3, "file": "static/chunks/app/features/page-8453dbe405e20e55.js", "mappings": "oFAAAA,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA;;;;;GCaM,IAAAE,EAASC,CAAAA,EAAAA,EAAAA,CAAAA,EAAiB,SAAU,CACxC,CAAC,SAAU,CAAEC,GAAI,KAAMC,GAAI,IAAKC,EAAG,IAAKC,IAAK,UAAU,CACvD,CAAC,SAAU,CAAEH,GAAI,IAAKC,GAAI,KAAMC,EAAG,IAAKC,IAAK,UAAU,CACvD,CAAC,SAAU,CAAEH,GAAI,KAAMC,GAAI,KAAMC,EAAG,IAAKC,IAAK,UAAU,CACxD,CAAC,OAAQ,CAAEC,GAAI,OAAQC,GAAI,QAASC,GAAI,QAASC,GAAI,QAASJ,IAAK,UAAU,CAC7E,CAAC,OAAQ,CAAEC,GAAI,QAASC,GAAI,OAAQC,GAAI,OAAQC,GAAI,QAASJ,IAAK,UAAU,CAC7E;;;;;GCNK,IAAAK,EAAST,CAAAA,EAAAA,EAAAA,CAAAA,EAAiB,SAAU,CACxC,CACE,OACA,CACEU,EAAG,+GACHN,IAAK,QACP,EACF,CACD,8CCbc,SAASO,IACtB,IAAMC,EAAW,CACf,CACEC,KAAMC,EAAAA,CAAKA,CACXC,MAAO,yBACPC,YAAa,gGACbC,QAAS,CAAC,sBAAuB,6BAA8B,wBAAyB,sBAAsB,EAEhH,CACEJ,KAAMK,EAAAA,CAAKA,CACXH,MAAO,0BACPC,YAAa,qFACbC,QAAS,CAAC,yBAA0B,2BAA4B,yBAA0B,iBAAiB,EAE7G,CACEJ,KAAMM,EAAAA,CAAMA,CACZJ,MAAO,sBACPC,YAAa,gFACbC,QAAS,CAAC,wBAAyB,kBAAmB,iBAAkB,uBAAuB,EAEjG,CACEJ,KAAMO,EAAAA,CAAGA,CACTL,MAAO,wBACPC,YAAa,8EACbC,QAAS,CAAC,oBAAqB,qBAAsB,iBAAkB,mBAAmB,EAE5F,CACEJ,KAAMQ,EAAAA,CAAaA,CACnBN,MAAO,gBACPC,YAAa,4EACbC,QAAS,CAAC,sBAAuB,eAAgB,kBAAmB,eAAe,EAErF,CACEJ,KAAMd,EACNgB,MAAO,iBACPC,YAAa,8EACbC,QAAS,CAAC,sBAAuB,sBAAuB,sBAAuB,iBAAiB,EAElG,CACEJ,KAAMS,EAAAA,CAAIA,CACVP,MAAO,oBACPC,YAAa,wEACbC,QAAS,CAAC,eAAgB,qBAAsB,gBAAiB,eAAe,EAElF,CACEJ,KAAMU,EAAAA,CAAKA,CACXR,MAAO,uBACPC,YAAa,8EACbC,QAAS,CAAC,iBAAkB,oBAAqB,0BAA2B,cAAc,EAE7F,CAEKO,EAAY,CAChB,CAAEX,KAAMY,EAAAA,CAAOA,CAAEC,KAAM,UAAWV,YAAa,uBAAwB,EACvE,CAAEH,KAAMc,EAAAA,CAAUA,CAAED,KAAM,SAAUV,YAAa,sBAAuB,EACxE,CAAEH,KAAMJ,EAAQiB,KAAM,cAAeV,YAAa,+BAAgC,EACnF,CAED,MACE,GAAAY,EAAAC,IAAA,EAAAD,EAAAE,QAAA,YAEE,GAAAF,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,gBAEf,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,6CAEb,GAAAL,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,0CACb,GAAAL,EAAAG,GAAA,EAACI,KAAAA,CAAGF,UAAU,sDAA6C,uCAG3D,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,4EAAmE,8IAQpF,GAAAL,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,6BACb,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,oDACZrB,EAASyB,GAAG,CAAC,CAACC,EAASC,IACtB,GAAAX,EAAAC,IAAA,EAACG,MAAAA,CAAgBC,UAAU,kFACzB,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,6HACb,GAAAL,EAAAG,GAAA,EAACO,EAAQzB,IAAI,EAACoB,UAAU,yBAE1B,GAAAL,EAAAG,GAAA,EAACS,KAAAA,CAAGP,UAAU,6DAAqDK,EAAQvB,KAAK,GAChF,GAAAa,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,0CAAkCK,EAAQtB,WAAW,GAClE,GAAAY,EAAAG,GAAA,EAACU,KAAAA,CAAGR,UAAU,qBACXK,EAAQrB,OAAO,CAACoB,GAAG,CAAC,CAACK,EAAQC,IAC5B,GAAAf,EAAAC,IAAA,EAACe,KAAAA,CAAqBX,UAAU,0DAC9B,GAAAL,EAAAG,GAAA,EAACc,EAAAA,CAAKA,CAAAA,CAACZ,UAAU,yCACjB,GAAAL,EAAAG,GAAA,EAACe,OAAAA,UAAMJ,MAFAC,QARLJ,UAqBlB,GAAAX,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,8BACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,8BACb,GAAAL,EAAAG,GAAA,EAACgB,KAAAA,CAAGd,UAAU,8CAAqC,4BAGnD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,mDAA0C,kEAKzD,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,qCACZT,EAAUa,GAAG,CAAC,CAACW,EAAUT,IACxB,GAAAX,EAAAC,IAAA,EAACG,MAAAA,CAAgBC,UAAU,kCACzB,GAAAL,EAAAG,GAAA,EAACiB,EAASnC,IAAI,EAACoB,UAAU,2CACzB,GAAAL,EAAAG,GAAA,EAACS,KAAAA,CAAGP,UAAU,iDAAyCe,EAAStB,IAAI,GACpE,GAAAE,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,yBAAiBe,EAAShC,WAAW,KAH1CuB,WAWlB,GAAAX,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,8BACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,8BACb,GAAAL,EAAAG,GAAA,EAACgB,KAAAA,CAAGd,UAAU,8CAAqC,6BAGnD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,iCAAwB,gEAKvC,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sCACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sBACb,GAAAL,EAAAG,GAAA,EAACS,KAAAA,CAAGP,UAAU,8CAAqC,kBACnD,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sBACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,iCACb,GAAAL,EAAAG,GAAA,EAACe,OAAAA,CAAKb,UAAU,yBAAgB,qBAChC,GAAAL,EAAAG,GAAA,EAACe,OAAAA,CAAKb,UAAU,sBAAa,4BAE/B,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,iCACb,GAAAL,EAAAG,GAAA,EAACe,OAAAA,CAAKb,UAAU,yBAAgB,eAChC,GAAAL,EAAAG,GAAA,EAACe,OAAAA,CAAKb,UAAU,sBAAa,cAE/B,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,iCACb,GAAAL,EAAAG,GAAA,EAACe,OAAAA,CAAKb,UAAU,yBAAgB,kBAChC,GAAAL,EAAAG,GAAA,EAACe,OAAAA,CAAKb,UAAU,sBAAa,4BAE/B,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,iCACb,GAAAL,EAAAG,GAAA,EAACe,OAAAA,CAAKb,UAAU,yBAAgB,uBAChC,GAAAL,EAAAG,GAAA,EAACe,OAAAA,CAAKb,UAAU,sBAAa,kBAE/B,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,iCACb,GAAAL,EAAAG,GAAA,EAACe,OAAAA,CAAKb,UAAU,yBAAgB,sBAChC,GAAAL,EAAAG,GAAA,EAACe,OAAAA,CAAKb,UAAU,sBAAa,0BAKnC,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sBACb,GAAAL,EAAAG,GAAA,EAACS,KAAAA,CAAGP,UAAU,8CAAqC,0BACnD,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sBACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,iCACb,GAAAL,EAAAG,GAAA,EAACe,OAAAA,CAAKb,UAAU,yBAAgB,sBAChC,GAAAL,EAAAG,GAAA,EAACe,OAAAA,CAAKb,UAAU,sBAAa,gBAE/B,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,iCACb,GAAAL,EAAAG,GAAA,EAACe,OAAAA,CAAKb,UAAU,yBAAgB,0BAChC,GAAAL,EAAAG,GAAA,EAACe,OAAAA,CAAKb,UAAU,sBAAa,gBAE/B,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,iCACb,GAAAL,EAAAG,GAAA,EAACe,OAAAA,CAAKb,UAAU,yBAAgB,YAChC,GAAAL,EAAAG,GAAA,EAACe,OAAAA,CAAKb,UAAU,sBAAa,iBAE/B,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,iCACb,GAAAL,EAAAG,GAAA,EAACe,OAAAA,CAAKb,UAAU,yBAAgB,aAChC,GAAAL,EAAAG,GAAA,EAACe,OAAAA,CAAKb,UAAU,sBAAa,cAE/B,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,iCACb,GAAAL,EAAAG,GAAA,EAACe,OAAAA,CAAKb,UAAU,yBAAgB,eAChC,GAAAL,EAAAG,GAAA,EAACe,OAAAA,CAAKb,UAAU,sBAAa,6BASzC,GAAAL,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,8BACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,8BACb,GAAAL,EAAAG,GAAA,EAACgB,KAAAA,CAAGd,UAAU,8CAAqC,8BAGnD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,iCAAwB,6DAKvC,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,qDACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,kCACb,GAAAL,EAAAG,GAAA,EAACkB,EAAAA,CAAIA,CAAAA,CAAChB,UAAU,0CAChB,GAAAL,EAAAG,GAAA,EAACmB,KAAAA,CAAGjB,UAAU,yCAAgC,0BAC9C,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,iCAAwB,qDAEvC,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,kCACb,GAAAL,EAAAG,GAAA,EAACZ,EAAAA,CAAMA,CAAAA,CAACc,UAAU,yCAClB,GAAAL,EAAAG,GAAA,EAACmB,KAAAA,CAAGjB,UAAU,yCAAgC,oBAC9C,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,iCAAwB,+CAEvC,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,kCACb,GAAAL,EAAAG,GAAA,EAACoB,EAAAA,CAAKA,CAAAA,CAAClB,UAAU,2CACjB,GAAAL,EAAAG,GAAA,EAACmB,KAAAA,CAAGjB,UAAU,yCAAgC,mBAC9C,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,iCAAwB,wDAEvC,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,kCACb,GAAAL,EAAAG,GAAA,EAACqB,EAAAA,CAAQA,CAAAA,CAACnB,UAAU,2CACpB,GAAAL,EAAAG,GAAA,EAACmB,KAAAA,CAAGjB,UAAU,yCAAgC,mBAC9C,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,iCAAwB,kEAO7C,GAAAL,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,yCACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,uBACb,GAAAL,EAAAG,GAAA,EAACgB,KAAAA,CAAGd,UAAU,8CAAqC,wCAGnD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,sCAA6B,sEAG1C,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,2DACb,GAAAL,EAAAG,GAAA,EAACsB,SAAAA,CAAOpB,UAAU,iCAAwB,qBAC1C,GAAAL,EAAAG,GAAA,EAACsB,SAAAA,CAAOpB,UAAU,mCAA0B,iCAQ5D,mFCnQeqB,EAAA,CACbC,MAAO,6BACPC,MAAO,GACPC,OAAQ,GACRC,QAAS,YACTC,KAAM,OACNC,OAAQ,eACRC,YAAa,EACbC,cAAe,QACfC,eAAgB,OAClB;;;;;GCmBa,IAAAC,EAAc,GACzBC,EACGC,OAAA,CAAQ,qBAAsB,SAC9BC,WAAY,GACZC,IAAK,GAEJpE,EAAmB,CAACqE,EAAkBC,KAC1C,IAAMC,EAAYC,CAAAA,EAAAA,EAAAA,UAAAA,EAChB,CAAAC,EAAiHC,QAAhH,CAAEC,MAAAA,EAAQ,eAAgBC,KAAAA,EAAO,EAAI,CAAAf,YAAAA,EAAc,CAAG,CAAAgB,oBAAAA,CAAA,CAAqB5C,UAAAA,EAAY,GAAI6C,SAAAA,CAAa,IAAAC,EAAA,CAAAN,QACvGO,CAAAA,EAAAA,EAAAA,aAAAA,EACE,MACA,CACEN,IAAAA,EACA,GAAGpB,CAAA,CACHE,MAAOoB,EACPnB,OAAQmB,EACRhB,OAAQe,EACRd,YAAagB,EAAsBI,GAAAA,OAAOpB,GAAoBoB,OAAOL,GAAQf,EAC7E5B,UAAW,CAAC,SAAoB,UAAyBiD,MAAA,CAAzBlB,EAAYK,IAAapC,EAAW,CAAAkD,IAAA,CAAK,KACzE,GAAGJ,CAAA,EAEL,IACKT,EAASjC,GAAA,CAAI,OAAC,CAAC+C,EAAKC,EAAW,CAAAZ,QAAAO,CAAAA,EAAAA,EAAAA,aAAAA,EAAcI,EAAKC,QACjDC,MAAMC,OAAA,CAAQT,GAAYA,EAAW,CAACA,EAAQ,CACpD,IAMC,OAFPP,EAAUiB,WAAA,CAAc,GAAGN,MAAA,CAAAb,GAEpBE,CACT;;;;;GC/CM,IAAApB,EAAQnD,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,QAAS,CACtC,CAAC,SAAU,CAAEC,GAAI,KAAMC,GAAI,IAAKC,EAAG,IAAKC,IAAK,UAAU,CACvD,CAAC,OAAQ,CAAEM,EAAG,0CAA2CN,IAAK,UAAU,CACzE;;;;;GCHD,IAAMyC,EAAQ7C,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,QAAS,CAAC,CAAC,OAAQ,CAAEU,EAAG,kBAAmBN,IAAK,QAAS,EAAE,CAAC;;;;;GCArF,IAAAmB,EAAQvB,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,QAAS,CACtC,CAAC,SAAU,CAAEC,GAAI,KAAMC,GAAI,KAAMC,EAAG,KAAMC,IAAK,UAAU,CACzD,CAAC,OAAQ,CAAEM,EAAG,kDAAmDN,IAAK,UAAU,CAChF,CAAC,OAAQ,CAAEM,EAAG,WAAYN,IAAK,UAAU,CAC1C;;;;;GCJK,IAAA6C,EAAOjD,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,OAAQ,CACpC,CAAC,OAAQ,CAAEwD,MAAO,KAAMC,OAAQ,KAAMgC,EAAG,IAAKC,EAAG,KAAMC,GAAI,IAAKC,GAAI,IAAKxF,IAAK,UAAU,CACxF,CAAC,OAAQ,CAAEM,EAAG,2BAA4BN,IAAK,UAAU,CAC1D;;;;;GCHK,IAAAiB,EAAgBrB,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,gBAAiB,CACtD,CAAC,OAAQ,CAAEU,EAAG,uCAAwCN,IAAK,UAAU,CACtE;;;;;GCFK,IAAAqB,EAAUzB,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,UAAW,CAC1C,CAAC,OAAQ,CAAEwD,MAAO,KAAMC,OAAQ,KAAMgC,EAAG,IAAKC,EAAG,IAAKC,GAAI,IAAKvF,IAAK,UAAU,CAC9E,CAAC,OAAQ,CAAEC,GAAI,IAAKC,GAAI,KAAMC,GAAI,KAAMC,GAAI,KAAMJ,IAAK,UAAU,CACjE,CAAC,OAAQ,CAAEC,GAAI,KAAMC,GAAI,KAAMC,GAAI,KAAMC,GAAI,KAAMJ,IAAK,UAAU,CACnE;;;;;GCJK,IAAAkB,EAAOtB,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,OAAQ,CACpC,CAAC,UAAW,CAAE6F,OAAQ,qBAAsBzF,IAAK,UAAU,CAC5D;;;;;GCFK,IAAAgD,EAAWpD,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,WAAY,CAC5C,CACE,OACA,CACEU,EAAG,wjBACHN,IAAK,QACP,EACF,CACA,CAAC,SAAU,CAAEH,GAAI,KAAMC,GAAI,KAAMC,EAAG,IAAKC,IAAK,UAAU,CACzD;;;;;GCTK,IAAAe,EAASnB,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,SAAU,CACxC,CAAC,OAAQ,CAAEU,EAAG,6CAA8CN,IAAK,UAAU,CAC5E;;;;;GCFK,IAAAuB,EAAa3B,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,aAAc,CAChD,CAAC,OAAQ,CAAEwD,MAAO,KAAMC,OAAQ,KAAMgC,EAAG,IAAKC,EAAG,IAAKC,GAAI,IAAKC,GAAI,IAAKxF,IAAK,UAAU,CACvF,CAAC,OAAQ,CAAEM,EAAG,aAAcN,IAAK,UAAU,CAC5C;;;;;GCHK,IAAAc,EAAQlB,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,QAAS,CACtC,CAAC,OAAQ,CAAEU,EAAG,4CAA6CN,IAAK,UAAU,CAC1E,CAAC,SAAU,CAAEH,GAAI,IAAKC,GAAI,IAAKC,EAAG,IAAKC,IAAK,SAAS,CACrD,CAAC,OAAQ,CAAEM,EAAG,6BAA8BN,IAAK,UAAU,CAC3D,CAAC,OAAQ,CAAEM,EAAG,4BAA6BN,IAAK,UAAU,CAC3D;;;;;GCLK,IAAAU,EAAQd,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,QAAS,CACtC,CAAC,OAAQ,CAAEU,EAAG,mBAAoBN,IAAK,UAAU,CACjD,CAAC,OAAQ,CAAEoD,MAAO,KAAMC,OAAQ,KAAMgC,EAAG,IAAKC,EAAG,IAAKC,GAAI,IAAKC,GAAI,IAAKxF,IAAK,UAAU,CACxF;;;;;GCHK,IAAAgB,EAAMpB,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,MAAO,CAClC,CAAC,UAAW,CAAE6F,OAAQ,yCAA0CzF,IAAK,UAAU,CAChF", "sources": ["webpack://_N_E/?cf3c", "webpack://_N_E/../../../src/icons/share-2.ts", "webpack://_N_E/../../../src/icons/laptop.ts", "webpack://_N_E/./app/features/page.tsx", "webpack://_N_E/../../src/defaultAttributes.ts", "webpack://_N_E/../../src/createLucideIcon.ts", "webpack://_N_E/../../../src/icons/award.ts", "webpack://_N_E/../../../src/icons/check.ts", "webpack://_N_E/../../../src/icons/globe.ts", "webpack://_N_E/../../../src/icons/lock.ts", "webpack://_N_E/../../../src/icons/message-circle.ts", "webpack://_N_E/../../../src/icons/monitor.ts", "webpack://_N_E/../../../src/icons/play.ts", "webpack://_N_E/../../../src/icons/settings.ts", "webpack://_N_E/../../../src/icons/shield.ts", "webpack://_N_E/../../../src/icons/smartphone.ts", "webpack://_N_E/../../../src/icons/users.ts", "webpack://_N_E/../../../src/icons/video.ts", "webpack://_N_E/../../../src/icons/zap.ts"], "sourcesContent": ["import(/* webpackMode: \"eager\" */ \"/Volumes/Apps/Websites/streamit-main/app/features/page.tsx\");\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Share2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxOCIgY3k9IjUiIHI9IjMiIC8+CiAgPGNpcmNsZSBjeD0iNiIgY3k9IjEyIiByPSIzIiAvPgogIDxjaXJjbGUgY3g9IjE4IiBjeT0iMTkiIHI9IjMiIC8+CiAgPGxpbmUgeDE9IjguNTkiIHgyPSIxNS40MiIgeTE9IjEzLjUxIiB5Mj0iMTcuNDkiIC8+CiAgPGxpbmUgeDE9IjE1LjQxIiB4Mj0iOC41OSIgeTE9IjYuNTEiIHkyPSIxMC40OSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/share-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Share2 = createLucideIcon('Share2', [\n  ['circle', { cx: '18', cy: '5', r: '3', key: 'gq8acd' }],\n  ['circle', { cx: '6', cy: '12', r: '3', key: 'w7nqdw' }],\n  ['circle', { cx: '18', cy: '19', r: '3', key: '1xt0gg' }],\n  ['line', { x1: '8.59', x2: '15.42', y1: '13.51', y2: '17.49', key: '47mynk' }],\n  ['line', { x1: '15.41', x2: '8.59', y1: '6.51', y2: '10.49', key: '1n3mei' }],\n]);\n\nexport default Share2;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Laptop\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjAgMTZWN2EyIDIgMCAwIDAtMi0ySDZhMiAyIDAgMCAwLTIgMnY5bTE2IDBING0xNiAwIDEuMjggMi41NWExIDEgMCAwIDEtLjkgMS40NUgzLjYyYTEgMSAwIDAgMS0uOS0xLjQ1TDQgMTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/laptop\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Laptop = createLucideIcon('Laptop', [\n  [\n    'path',\n    {\n      d: 'M20 16V7a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v9m16 0H4m16 0 1.28 2.55a1 1 0 0 1-.9 1.45H3.62a1 1 0 0 1-.9-1.45L4 16',\n      key: 'tarvll',\n    },\n  ],\n]);\n\nexport default Laptop;\n", "'use client'\n\nimport {\n  Video, Users, Shield, Zap, Monitor, Smartphone, Laptop,\n  Globe, Clock, Headphones, MessageCircle, Share2,\n  Play, Settings, Lock, Award, TrendingUp, Check\n} from 'lucide-react'\n\nexport default function FeaturesPage() {\n  const features = [\n    {\n      icon: Video,\n      title: 'Crystal Clear HD Video',\n      description: 'Experience stunning 4K video quality with adaptive streaming that adjusts to your connection.',\n      details: ['4K video resolution', 'Adaptive bitrate streaming', 'Low-light enhancement', 'Virtual backgrounds']\n    },\n    {\n      icon: Users,\n      title: 'Multi-Participant Calls',\n      description: 'Connect with up to 100 participants in a single meeting with seamless performance.',\n      details: ['Up to 100 participants', 'Gallery and speaker view', 'Participant management', 'Breakout rooms']\n    },\n    {\n      icon: Shield,\n      title: 'Enterprise Security',\n      description: 'Bank-level security with end-to-end encryption and compliance certifications.',\n      details: ['End-to-end encryption', 'SOC 2 certified', 'GDPR compliant', 'Single sign-on (SSO)']\n    },\n    {\n      icon: Zap,\n      title: 'Lightning Performance',\n      description: 'Optimized for speed with global CDN and edge computing for minimal latency.',\n      details: ['Sub-100ms latency', 'Global CDN network', 'Edge computing', '99.9% uptime SLA']\n    },\n    {\n      icon: MessageCircle,\n      title: 'Advanced Chat',\n      description: 'Rich messaging with file sharing, reactions, and persistent chat history.',\n      details: ['Real-time messaging', 'File sharing', 'Emoji reactions', 'Chat history']\n    },\n    {\n      icon: Share2,\n      title: 'Screen Sharing',\n      description: 'Share your entire screen, specific applications, or browser tabs with ease.',\n      details: ['Full screen sharing', 'Application sharing', 'Browser tab sharing', 'Remote control']\n    },\n    {\n      icon: Play,\n      title: 'Meeting Recording',\n      description: 'Record meetings in HD with automatic transcription and cloud storage.',\n      details: ['HD recording', 'Auto transcription', 'Cloud storage', 'Easy sharing']\n    },\n    {\n      icon: Globe,\n      title: 'Global Accessibility',\n      description: 'Available worldwide with multi-language support and accessibility features.',\n      details: ['150+ countries', 'Multi-language UI', 'Accessibility compliant', 'Mobile apps']\n    }\n  ]\n\n  const platforms = [\n    { icon: Monitor, name: 'Desktop', description: 'Windows, macOS, Linux' },\n    { icon: Smartphone, name: 'Mobile', description: 'iOS and Android apps' },\n    { icon: Laptop, name: 'Web Browser', description: 'Chrome, Firefox, Safari, Edge' }\n  ]\n\n  return (\n    <>\n      {/* Animated Background */}\n      <div className=\"animated-bg\"></div>\n\n      <div className=\"min-h-screen pt-20 relative z-10\">\n        {/* Hero Section */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-7xl mx-auto text-center\">\n            <h1 className=\"text-5xl font-bold text-white mb-6 fade-in\">\n              Powerful Features for Modern Teams\n            </h1>\n            <p className=\"text-xl text-white/80 max-w-3xl mx-auto leading-relaxed slide-up\">\n              Discover all the tools and capabilities that make StreamIt Pro the ultimate \n              video conferencing solution for businesses of all sizes.\n            </p>\n          </div>\n        </section>\n\n        {/* Main Features Grid */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-7xl mx-auto\">\n            <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n              {features.map((feature, index) => (\n                <div key={index} className=\"glass p-8 hover:transform hover:scale-105 transition-all duration-300\">\n                  <div className=\"w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-6\">\n                    <feature.icon className=\"h-8 w-8 text-white\" />\n                  </div>\n                  <h3 className=\"text-xl font-semibold text-white mb-4 text-center\">{feature.title}</h3>\n                  <p className=\"text-white/70 mb-6 text-center\">{feature.description}</p>\n                  <ul className=\"space-y-2\">\n                    {feature.details.map((detail, detailIndex) => (\n                      <li key={detailIndex} className=\"flex items-center gap-2 text-white/60 text-sm\">\n                        <Check className=\"h-4 w-4 text-green-400 flex-shrink-0\" />\n                        <span>{detail}</span>\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n              ))}\n            </div>\n          </div>\n        </section>\n\n        {/* Platform Support */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-7xl mx-auto\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-4xl font-bold text-white mb-4\">\n                Works Everywhere You Do\n              </h2>\n              <p className=\"text-xl text-white/70 max-w-3xl mx-auto\">\n                Access StreamIt Pro from any device, anywhere in the world\n              </p>\n            </div>\n\n            <div className=\"grid md:grid-cols-3 gap-8\">\n              {platforms.map((platform, index) => (\n                <div key={index} className=\"glass p-8 text-center\">\n                  <platform.icon className=\"h-16 w-16 text-purple-400 mx-auto mb-4\" />\n                  <h3 className=\"text-xl font-semibold text-white mb-2\">{platform.name}</h3>\n                  <p className=\"text-white/70\">{platform.description}</p>\n                </div>\n              ))}\n            </div>\n          </div>\n        </section>\n\n        {/* Technical Specifications */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-7xl mx-auto\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-4xl font-bold text-white mb-4\">\n                Technical Specifications\n              </h2>\n              <p className=\"text-xl text-white/70\">\n                Built on cutting-edge technology for maximum performance\n              </p>\n            </div>\n\n            <div className=\"grid md:grid-cols-2 gap-8\">\n              <div className=\"glass p-8\">\n                <h3 className=\"text-2xl font-bold text-white mb-6\">Video & Audio</h3>\n                <div className=\"space-y-4\">\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-white/70\">Video Resolution</span>\n                    <span className=\"text-white\">Up to 4K (3840x2160)</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-white/70\">Frame Rate</span>\n                    <span className=\"text-white\">30 FPS</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-white/70\">Audio Quality</span>\n                    <span className=\"text-white\">48kHz, 16-bit stereo</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-white/70\">Noise Cancellation</span>\n                    <span className=\"text-white\">AI-powered</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-white/70\">Echo Cancellation</span>\n                    <span className=\"text-white\">Advanced AEC</span>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"glass p-8\">\n                <h3 className=\"text-2xl font-bold text-white mb-6\">Network & Performance</h3>\n                <div className=\"space-y-4\">\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-white/70\">Minimum Bandwidth</span>\n                    <span className=\"text-white\">150 kbps</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-white/70\">Recommended Bandwidth</span>\n                    <span className=\"text-white\">1.5 Mbps</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-white/70\">Latency</span>\n                    <span className=\"text-white\">Sub-100ms</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-white/70\">Protocol</span>\n                    <span className=\"text-white\">WebRTC</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-white/70\">Encryption</span>\n                    <span className=\"text-white\">AES-256</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* Security Features */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-7xl mx-auto\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-4xl font-bold text-white mb-4\">\n                Enterprise-Grade Security\n              </h2>\n              <p className=\"text-xl text-white/70\">\n                Your privacy and data security are our top priorities\n              </p>\n            </div>\n\n            <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-6\">\n              <div className=\"glass p-6 text-center\">\n                <Lock className=\"h-12 w-12 text-green-400 mx-auto mb-4\" />\n                <h4 className=\"text-white font-semibold mb-2\">End-to-End Encryption</h4>\n                <p className=\"text-white/60 text-sm\">All communications are encrypted with AES-256</p>\n              </div>\n              <div className=\"glass p-6 text-center\">\n                <Shield className=\"h-12 w-12 text-blue-400 mx-auto mb-4\" />\n                <h4 className=\"text-white font-semibold mb-2\">SOC 2 Certified</h4>\n                <p className=\"text-white/60 text-sm\">Independently audited security controls</p>\n              </div>\n              <div className=\"glass p-6 text-center\">\n                <Award className=\"h-12 w-12 text-purple-400 mx-auto mb-4\" />\n                <h4 className=\"text-white font-semibold mb-2\">GDPR Compliant</h4>\n                <p className=\"text-white/60 text-sm\">Full compliance with data protection regulations</p>\n              </div>\n              <div className=\"glass p-6 text-center\">\n                <Settings className=\"h-12 w-12 text-orange-400 mx-auto mb-4\" />\n                <h4 className=\"text-white font-semibold mb-2\">Admin Controls</h4>\n                <p className=\"text-white/60 text-sm\">Comprehensive administrative and security controls</p>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* CTA Section */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-4xl mx-auto text-center\">\n            <div className=\"glass p-12\">\n              <h2 className=\"text-3xl font-bold text-white mb-4\">\n                Ready to Experience These Features?\n              </h2>\n              <p className=\"text-xl text-white/70 mb-8\">\n                Start your free trial today and see why teams choose StreamIt Pro\n              </p>\n              <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n                <button className=\"btn-primary px-8 py-3\">Start Free Trial</button>\n                <button className=\"btn-secondary px-8 py-3\">Schedule Demo</button>\n              </div>\n            </div>\n          </div>\n        </section>\n      </div>\n    </>\n  )\n}\n", "export default {\n  xmlns: 'http://www.w3.org/2000/svg',\n  width: 24,\n  height: 24,\n  viewBox: '0 0 24 24',\n  fill: 'none',\n  stroke: 'currentColor',\n  strokeWidth: 2,\n  strokeLinecap: 'round',\n  strokeLinejoin: 'round',\n};\n", "import {\n  forwardRef,\n  createElement,\n  ReactSVG,\n  SVGProps,\n  ForwardRefExoticComponent,\n  RefAttributes,\n} from 'react';\nimport defaultAttributes from './defaultAttributes';\n\nexport type IconNode = [elementName: keyof ReactSVG, attrs: Record<string, string>][];\n\nexport type SVGAttributes = Partial<SVGProps<SVGSVGElement>>;\ntype ComponentAttributes = RefAttributes<SVGSVGElement> & SVGAttributes;\n\nexport interface LucideProps extends ComponentAttributes {\n  size?: string | number;\n  absoluteStrokeWidth?: boolean;\n}\n\nexport type LucideIcon = ForwardRefExoticComponent<LucideProps>;\n/**\n * Converts string to KebabCase\n * Copied from scripts/helper. If anyone knows how to properly import it here\n * then please fix it.\n *\n * @param {string} string\n * @returns {string} A kebabized string\n */\nexport const toKebabCase = (string: string) =>\n  string\n    .replace(/([a-z0-9])([A-Z])/g, '$1-$2')\n    .toLowerCase()\n    .trim();\n\nconst createLucideIcon = (iconName: string, iconNode: IconNode): LucideIcon => {\n  const Component = forwardRef<SVGSVGElement, LucideProps>(\n    ({ color = 'currentColor', size = 24, strokeWidth = 2, absoluteStrokeWidth, className = '', children, ...rest }, ref) =>\n      createElement(\n        'svg',\n        {\n          ref,\n          ...defaultAttributes,\n          width: size,\n          height: size,\n          stroke: color,\n          strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n          className: ['lucide', `lucide-${toKebabCase(iconName)}`, className].join(' '),\n          ...rest,\n        },\n        [\n          ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n          ...(Array.isArray(children) ? children : [children]),\n        ]\n      )\n  );\n\n  Component.displayName = `${iconName}`;\n\n  return Component;\n};\n\nexport default createLucideIcon;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Award\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjgiIHI9IjYiIC8+CiAgPHBhdGggZD0iTTE1LjQ3NyAxMi44OSAxNyAyMmwtNS0zLTUgMyAxLjUyMy05LjExIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/award\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Award = createLucideIcon('Award', [\n  ['circle', { cx: '12', cy: '8', r: '6', key: '1vp47v' }],\n  ['path', { d: 'M15.477 12.89 17 22l-5-3-5 3 1.523-9.11', key: 'em7aur' }],\n]);\n\nexport default Award;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Check\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjAgNiA5IDE3bC01LTUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/check\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Check = createLucideIcon('Check', [['path', { d: 'M20 6 9 17l-5-5', key: '1gmf2c' }]]);\n\nexport default Check;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Globe\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8cGF0aCBkPSJNMTIgMmExNC41IDE0LjUgMCAwIDAgMCAyMCAxNC41IDE0LjUgMCAwIDAgMC0yMCIgLz4KICA8cGF0aCBkPSJNMiAxMmgyMCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/globe\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Globe = createLucideIcon('Globe', [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['path', { d: 'M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20', key: '13o1zl' }],\n  ['path', { d: 'M2 12h20', key: '9i4pu4' }],\n]);\n\nexport default Globe;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Lock\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTEiIHg9IjMiIHk9IjExIiByeD0iMiIgcnk9IjIiIC8+CiAgPHBhdGggZD0iTTcgMTFWN2E1IDUgMCAwIDEgMTAgMHY0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/lock\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Lock = createLucideIcon('Lock', [\n  ['rect', { width: '18', height: '11', x: '3', y: '11', rx: '2', ry: '2', key: '1w4ew1' }],\n  ['path', { d: 'M7 11V7a5 5 0 0 1 10 0v4', key: 'fwvmzm' }],\n]);\n\nexport default Lock;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name MessageCircle\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMyAyMSAxLjktNS43YTguNSA4LjUgMCAxIDEgMy44IDMuOHoiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/message-circle\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MessageCircle = createLucideIcon('MessageCircle', [\n  ['path', { d: 'm3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z', key: 'v2veuj' }],\n]);\n\nexport default MessageCircle;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Monitor\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMjAiIGhlaWdodD0iMTQiIHg9IjIiIHk9IjMiIHJ4PSIyIiAvPgogIDxsaW5lIHgxPSI4IiB4Mj0iMTYiIHkxPSIyMSIgeTI9IjIxIiAvPgogIDxsaW5lIHgxPSIxMiIgeDI9IjEyIiB5MT0iMTciIHkyPSIyMSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/monitor\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Monitor = createLucideIcon('Monitor', [\n  ['rect', { width: '20', height: '14', x: '2', y: '3', rx: '2', key: '48i651' }],\n  ['line', { x1: '8', x2: '16', y1: '21', y2: '21', key: '1svkeh' }],\n  ['line', { x1: '12', x2: '12', y1: '17', y2: '21', key: 'vw1qmm' }],\n]);\n\nexport default Monitor;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Play\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWdvbiBwb2ludHM9IjUgMyAxOSAxMiA1IDIxIDUgMyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/play\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Play = createLucideIcon('Play', [\n  ['polygon', { points: '5 3 19 12 5 21 5 3', key: '191637' }],\n]);\n\nexport default Play;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Settings\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIuMjIgMmgtLjQ0YTIgMiAwIDAgMC0yIDJ2LjE4YTIgMiAwIDAgMS0xIDEuNzNsLS40My4yNWEyIDIgMCAwIDEtMiAwbC0uMTUtLjA4YTIgMiAwIDAgMC0yLjczLjczbC0uMjIuMzhhMiAyIDAgMCAwIC43MyAyLjczbC4xNS4xYTIgMiAwIDAgMSAxIDEuNzJ2LjUxYTIgMiAwIDAgMS0xIDEuNzRsLS4xNS4wOWEyIDIgMCAwIDAtLjczIDIuNzNsLjIyLjM4YTIgMiAwIDAgMCAyLjczLjczbC4xNS0uMDhhMiAyIDAgMCAxIDIgMGwuNDMuMjVhMiAyIDAgMCAxIDEgMS43M1YyMGEyIDIgMCAwIDAgMiAyaC40NGEyIDIgMCAwIDAgMi0ydi0uMThhMiAyIDAgMCAxIDEtMS43M2wuNDMtLjI1YTIgMiAwIDAgMSAyIDBsLjE1LjA4YTIgMiAwIDAgMCAyLjczLS43M2wuMjItLjM5YTIgMiAwIDAgMC0uNzMtMi43M2wtLjE1LS4wOGEyIDIgMCAwIDEtMS0xLjc0di0uNWEyIDIgMCAwIDEgMS0xLjc0bC4xNS0uMDlhMiAyIDAgMCAwIC43My0yLjczbC0uMjItLjM4YTIgMiAwIDAgMC0yLjczLS43M2wtLjE1LjA4YTIgMiAwIDAgMS0yIDBsLS40My0uMjVhMiAyIDAgMCAxLTEtMS43M1Y0YTIgMiAwIDAgMC0yLTJ6IiAvPgogIDxjaXJjbGUgY3g9IjEyIiBjeT0iMTIiIHI9IjMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/settings\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Settings = createLucideIcon('Settings', [\n  [\n    'path',\n    {\n      d: 'M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z',\n      key: '1qme2f',\n    },\n  ],\n  ['circle', { cx: '12', cy: '12', r: '3', key: '1v7zrd' }],\n]);\n\nexport default Settings;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Shield\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMjJzOC00IDgtMTBWNWwtOC0zLTggM3Y3YzAgNiA4IDEwIDggMTAiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/shield\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Shield = createLucideIcon('Shield', [\n  ['path', { d: 'M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10', key: '1irkt0' }],\n]);\n\nexport default Shield;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Smartphone\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTQiIGhlaWdodD0iMjAiIHg9IjUiIHk9IjIiIHJ4PSIyIiByeT0iMiIgLz4KICA8cGF0aCBkPSJNMTIgMThoLjAxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/smartphone\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Smartphone = createLucideIcon('Smartphone', [\n  ['rect', { width: '14', height: '20', x: '5', y: '2', rx: '2', ry: '2', key: '1yt0o3' }],\n  ['path', { d: 'M12 18h.01', key: 'mhygvu' }],\n]);\n\nexport default Smartphone;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Users\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgMjF2LTJhNCA0IDAgMCAwLTQtNEg2YTQgNCAwIDAgMC00IDR2MiIgLz4KICA8Y2lyY2xlIGN4PSI5IiBjeT0iNyIgcj0iNCIgLz4KICA8cGF0aCBkPSJNMjIgMjF2LTJhNCA0IDAgMCAwLTMtMy44NyIgLz4KICA8cGF0aCBkPSJNMTYgMy4xM2E0IDQgMCAwIDEgMCA3Ljc1IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/users\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Users = createLucideIcon('Users', [\n  ['path', { d: 'M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2', key: '1yyitq' }],\n  ['circle', { cx: '9', cy: '7', r: '4', key: 'nufk8' }],\n  ['path', { d: 'M22 21v-2a4 4 0 0 0-3-3.87', key: 'kshegd' }],\n  ['path', { d: 'M16 3.13a4 4 0 0 1 0 7.75', key: '1da9ce' }],\n]);\n\nexport default Users;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Video\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMjIgOC02IDQgNiA0VjhaIiAvPgogIDxyZWN0IHdpZHRoPSIxNCIgaGVpZ2h0PSIxMiIgeD0iMiIgeT0iNiIgcng9IjIiIHJ5PSIyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/video\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Video = createLucideIcon('Video', [\n  ['path', { d: 'm22 8-6 4 6 4V8Z', key: '50v9me' }],\n  ['rect', { width: '14', height: '12', x: '2', y: '6', rx: '2', ry: '2', key: '1rqjg6' }],\n]);\n\nexport default Video;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Zap\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWdvbiBwb2ludHM9IjEzIDIgMyAxNCAxMiAxNCAxMSAyMiAyMSAxMCAxMiAxMCAxMyAyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/zap\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Zap = createLucideIcon('Zap', [\n  ['polygon', { points: '13 2 3 14 12 14 11 22 21 10 12 10 13 2', key: '45s27k' }],\n]);\n\nexport default Zap;\n"], "names": ["Promise", "resolve", "then", "__webpack_require__", "bind", "Share2", "createLucideIcon", "cx", "cy", "r", "key", "x1", "x2", "y1", "y2", "Laptop", "d", "FeaturesPage", "features", "icon", "Video", "title", "description", "details", "Users", "Shield", "Zap", "MessageCircle", "Play", "Globe", "platforms", "Monitor", "name", "Smartphone", "jsx_runtime", "jsxs", "Fragment", "jsx", "div", "className", "section", "h1", "p", "map", "feature", "index", "h3", "ul", "detail", "detailIndex", "li", "Check", "span", "h2", "platform", "Lock", "h4", "Award", "Settings", "button", "defaultAttributes", "xmlns", "width", "height", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "toKebabCase", "string", "replace", "toLowerCase", "trim", "iconName", "iconNode", "Component", "forwardRef", "param", "ref", "color", "size", "absoluteStrokeWidth", "children", "rest", "createElement", "Number", "concat", "join", "tag", "attrs", "Array", "isArray", "displayName", "x", "y", "rx", "ry", "points"], "sourceRoot": ""}