{"version": 3, "file": "static/chunks/app/cookies/page-0db92a7191d2076a.js", "mappings": "oFAAAA,QAAAC,OAAA,GAAAC,IAAA,CAAAC,EAAAC,IAAA,CAAAD,EAAA;;;;;GCaM,IAAAE,EAASC,CAAAA,EAAAA,EAAAA,CAAAA,EAAiB,SAAU,CACxC,CAAC,OAAQ,CAAEC,EAAG,sDAAuDC,IAAK,UAAU,CACpF,CAAC,OAAQ,CAAED,EAAG,eAAgBC,IAAK,UAAU,CAC7C,CAAC,OAAQ,CAAED,EAAG,eAAgBC,IAAK,UAAU,CAC7C,CAAC,OAAQ,CAAED,EAAG,aAAcC,IAAK,UAAU,CAC3C,CAAC,OAAQ,CAAED,EAAG,aAAcC,IAAK,UAAU,CAC3C,CAAC,OAAQ,CAAED,EAAG,YAAaC,IAAK,UAAU,CAC3C;;;;;GCPK,IAAAC,EAAcH,CAAAA,EAAAA,EAAAA,CAAAA,EAAiB,cAAe,CAClD,CAAC,OAAQ,CAAEI,MAAO,KAAMC,OAAQ,KAAMC,EAAG,IAAKC,EAAG,IAAKC,GAAI,IAAKC,GAAI,IAAKP,IAAK,UAAU,CACvF,CAAC,SAAU,CAAEQ,GAAI,KAAMC,GAAI,KAAMC,EAAG,IAAKV,IAAK,UAAU,CACzD,ECHKW,EAAab,CAAAA,EAAAA,EAAAA,CAAAA,EAAiB,aAAc,CAChD,CAAC,OAAQ,CAAEI,MAAO,KAAMC,OAAQ,KAAMC,EAAG,IAAKC,EAAG,IAAKC,GAAI,IAAKC,GAAI,IAAKP,IAAK,UAAU,CACvF,CAAC,SAAU,CAAEQ,GAAI,IAAKC,GAAI,KAAMC,EAAG,IAAKV,IAAK,UAAU,CACxD,mCCXc,SAASY,IACtB,GAAM,CAACC,EAAgBC,EAAkB,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,CACnDC,UAAW,GACXC,UAAW,GACXC,UAAW,GACXC,YAAa,EACf,GAEMC,EAAe,IACN,cAATC,GACJP,EAAkBQ,GAAS,EACzB,GAAGA,CAAI,CACP,CAACD,EAAK,CAAE,CAACC,CAAI,CAACD,EAAK,CACrB,EACF,EAEME,EAAc,CAClB,CACEC,GAAI,YACJC,MAAO,oBACPC,YAAa,kFACbC,SAAU,CAAC,iBAAkB,WAAY,iBAAiB,CAC1DC,QAASf,EAAeG,SAAS,CACjCa,SAAU,EACZ,EACA,CACEL,GAAI,YACJC,MAAO,oBACPC,YAAa,2EACbC,SAAU,CAAC,mBAAoB,mBAAoB,yBAAyB,CAC5EC,QAASf,EAAeI,SAAS,CACjCY,SAAU,EACZ,EACA,CACEL,GAAI,YACJC,MAAO,oBACPC,YAAa,8FACbC,SAAU,CAAC,eAAgB,sBAAuB,2BAA2B,CAC7EC,QAASf,EAAeK,SAAS,CACjCW,SAAU,EACZ,EACA,CACEL,GAAI,cACJC,MAAO,qBACPC,YAAa,4FACbC,SAAU,CAAC,oBAAqB,oBAAqB,uBAAuB,CAC5EC,QAASf,EAAeM,WAAW,CACnCU,SAAU,EACZ,EACD,CAED,MACE,GAAAC,EAAAC,IAAA,EAAAD,EAAAE,QAAA,YAEE,GAAAF,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,gBAEf,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,6CAEb,GAAAL,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,0CACb,GAAAL,EAAAG,GAAA,EAACI,KAAAA,CAAGF,UAAU,sDAA6C,kBAG3D,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,4EAAmE,4HAIhF,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,8BAAqB,uCAKtC,GAAAL,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,6BACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sBACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,yCACb,GAAAL,EAAAG,GAAA,EAACpC,EAAMA,CAACsC,UAAU,4BAClB,GAAAL,EAAAG,GAAA,EAACM,KAAAA,CAAGJ,UAAU,yCAAgC,yBAEhD,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,oCACb,GAAAL,EAAAG,GAAA,EAACK,IAAAA,UAAE,+QAKH,GAAAR,EAAAG,GAAA,EAACK,IAAAA,UAAE,yMAUX,GAAAR,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,6BACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sBACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,yCACb,GAAAL,EAAAG,GAAA,EAACO,EAAAA,CAAQA,CAAAA,CAACL,UAAU,0BACpB,GAAAL,EAAAG,GAAA,EAACM,KAAAA,CAAGJ,UAAU,yCAAgC,uBAEhD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,8BAAqB,2JAKlC,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,qBACZZ,EAAYkB,GAAG,CAAC,GACf,GAAAX,EAAAC,IAAA,EAACG,MAAAA,CAAoBC,UAAU,sCAC7B,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,mDACb,GAAAL,EAAAG,GAAA,EAACS,KAAAA,CAAGP,UAAU,4CAAoCQ,EAAOlB,KAAK,GAC9D,GAAAK,EAAAC,IAAA,EAACa,SAAAA,CACCC,QAAS,IAAMzB,EAAauB,EAAOnB,EAAE,EACrCsB,SAAUH,EAAOd,QAAQ,CACzBM,UAAW,2BAEVY,MAAA,CADCJ,EAAOd,QAAQ,CAAG,gCAAkC,4BAGrDc,EAAOf,OAAO,CACb,GAAAE,EAAAG,GAAA,EAAChC,EAAWA,CAACkC,UAAU,2BAEvB,GAAAL,EAAAG,GAAA,EAACtB,EAAUA,CAACwB,UAAU,0BAExB,GAAAL,EAAAG,GAAA,EAACe,OAAAA,CAAKb,UAAU,iCACbQ,EAAOf,OAAO,CAAG,UAAY,mBAKpC,GAAAE,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,8BAAsBQ,EAAOjB,WAAW,GAEpDiB,EAAOd,QAAQ,EACd,GAAAC,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,4EACb,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,mCAA0B,8FAM3C,GAAAL,EAAAC,IAAA,EAACG,MAAAA,WACC,GAAAJ,EAAAG,GAAA,EAACgB,KAAAA,CAAGd,UAAU,uCAA8B,cAC5C,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,gCACZQ,EAAOhB,QAAQ,CAACc,GAAG,CAAC,CAACS,EAASC,IAC7B,GAAArB,EAAAG,GAAA,EAACe,OAAAA,CAECb,UAAU,oEAETe,GAHIC,WApCLR,EAAOnB,EAAE,KAgDvB,GAAAM,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,iDACb,GAAAL,EAAAG,GAAA,EAACW,SAAAA,CAAOT,UAAU,uBAAc,qBAChC,GAAAL,EAAAG,GAAA,EAACW,SAAAA,CAAOT,UAAU,yBAAgB,eAClC,GAAAL,EAAAG,GAAA,EAACW,SAAAA,CAAOT,UAAU,yBAAgB,4CAO1C,GAAAL,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,6BACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sBACb,GAAAL,EAAAG,GAAA,EAACM,KAAAA,CAAGJ,UAAU,8CAAqC,4BAEnD,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sBACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,WACC,GAAAJ,EAAAG,GAAA,EAACS,KAAAA,CAAGP,UAAU,iDAAwC,wBACtD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,yBAAgB,kKAM/B,GAAAL,EAAAC,IAAA,EAACG,MAAAA,WACC,GAAAJ,EAAAG,GAAA,EAACS,KAAAA,CAAGP,UAAU,iDAAwC,wBACtD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,yBAAgB,0MAO/B,GAAAL,EAAAC,IAAA,EAACG,MAAAA,WACC,GAAAJ,EAAAG,GAAA,EAACS,KAAAA,CAAGP,UAAU,iDAAwC,oBACtD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,yBAAgB,sJAM/B,GAAAL,EAAAC,IAAA,EAACG,MAAAA,WACC,GAAAJ,EAAAG,GAAA,EAACS,KAAAA,CAAGP,UAAU,iDAAwC,uBACtD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,yBAAgB,+JAWvC,GAAAL,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,6BACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sBACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,yCACb,GAAAL,EAAAG,GAAA,EAACmB,EAAAA,CAAGA,CAAAA,CAACjB,UAAU,2BACf,GAAAL,EAAAG,GAAA,EAACM,KAAAA,CAAGJ,UAAU,yCAAgC,wBAGhD,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,oCACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,WACC,GAAAJ,EAAAG,GAAA,EAACS,KAAAA,CAAGP,UAAU,iDAAwC,qBACtD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,UAAE,qLAML,GAAAR,EAAAC,IAAA,EAACG,MAAAA,WACC,GAAAJ,EAAAG,GAAA,EAACS,KAAAA,CAAGP,UAAU,iDAAwC,kCACtD,GAAAL,EAAAC,IAAA,EAACsB,KAAAA,CAAGlB,UAAU,iDACZ,GAAAL,EAAAC,IAAA,EAACuB,KAAAA,WAAG,GAAAxB,EAAAG,GAAA,EAACsB,SAAAA,UAAO,YAAgB,oEAC5B,GAAAzB,EAAAC,IAAA,EAACuB,KAAAA,WAAG,GAAAxB,EAAAG,GAAA,EAACsB,SAAAA,UAAO,aAAiB,4DAC7B,GAAAzB,EAAAC,IAAA,EAACuB,KAAAA,WAAG,GAAAxB,EAAAG,GAAA,EAACsB,SAAAA,UAAO,YAAgB,kDAC5B,GAAAzB,EAAAC,IAAA,EAACuB,KAAAA,WAAG,GAAAxB,EAAAG,GAAA,EAACsB,SAAAA,UAAO,UAAc,4EAI9B,GAAAzB,EAAAC,IAAA,EAACG,MAAAA,WACC,GAAAJ,EAAAG,GAAA,EAACS,KAAAA,CAAGP,UAAU,iDAAwC,mBACtD,GAAAL,EAAAG,GAAA,EAACK,IAAAA,UAAE,qJAML,GAAAR,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,uEACb,GAAAL,EAAAC,IAAA,EAACO,IAAAA,CAAEH,UAAU,oCACX,GAAAL,EAAAG,GAAA,EAACsB,SAAAA,UAAO,UAAc,4KAUlC,GAAAzB,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,6BACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sBACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,yCACb,GAAAL,EAAAG,GAAA,EAACuB,EAAAA,CAAMA,CAAAA,CAACrB,UAAU,4BAClB,GAAAL,EAAAG,GAAA,EAACM,KAAAA,CAAGJ,UAAU,yCAAgC,4BAGhD,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,oCACb,GAAAL,EAAAG,GAAA,EAACK,IAAAA,UAAE,oEAEH,GAAAR,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sCACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sCACb,GAAAL,EAAAG,GAAA,EAACgB,KAAAA,CAAGd,UAAU,yCAAgC,qBAC9C,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,mBAAU,kEAEzB,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sCACb,GAAAL,EAAAG,GAAA,EAACgB,KAAAA,CAAGd,UAAU,yCAAgC,aAC9C,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,mBAAU,qDAEzB,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sCACb,GAAAL,EAAAG,GAAA,EAACgB,KAAAA,CAAGd,UAAU,yCAAgC,WAC9C,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,mBAAU,oDAEzB,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sCACb,GAAAL,EAAAG,GAAA,EAACgB,KAAAA,CAAGd,UAAU,yCAAgC,WAC9C,GAAAL,EAAAG,GAAA,EAACK,IAAAA,CAAEH,UAAU,mBAAU,4DAI3B,GAAAL,EAAAG,GAAA,EAACK,IAAAA,UAAE,wKAUX,GAAAR,EAAAG,GAAA,EAACG,UAAAA,CAAQD,UAAU,sBACjB,GAAAL,EAAAG,GAAA,EAACC,MAAAA,CAAIC,UAAU,6BACb,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sBACb,GAAAL,EAAAG,GAAA,EAACM,KAAAA,CAAGJ,UAAU,8CAAqC,6BACnD,GAAAL,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,oCACb,GAAAL,EAAAG,GAAA,EAACK,IAAAA,UAAE,iGAKH,GAAAR,EAAAC,IAAA,EAACG,MAAAA,CAAIC,UAAU,sCACb,GAAAL,EAAAC,IAAA,EAACO,IAAAA,WAAE,GAAAR,EAAAG,GAAA,EAACsB,SAAAA,UAAO,WAAe,8BAC1B,GAAAzB,EAAAC,IAAA,EAACO,IAAAA,WAAE,GAAAR,EAAAG,GAAA,EAACsB,SAAAA,UAAO,aAAiB,+BAG9B,GAAAzB,EAAAG,GAAA,EAACK,IAAAA,UAAE,6HAWnB,mFCzUemB,EAAA,CACbC,MAAO,6BACPxD,MAAO,GACPC,OAAQ,GACRwD,QAAS,YACTC,KAAM,OACNC,OAAQ,eACRC,YAAa,EACbC,cAAe,QACfC,eAAgB,OAClB;;;;;GCmBa,IAAAC,EAAc,GACzBC,EACGC,OAAA,CAAQ,qBAAsB,SAC9BC,WAAY,GACZC,IAAK,GAEJvE,EAAmB,CAACwE,EAAkBC,KAC1C,IAAMC,EAAYC,CAAAA,EAAAA,EAAAA,UAAAA,EAChB,CAAAC,EAAiHC,QAAhH,CAAEC,MAAAA,EAAQ,eAAgBC,KAAAA,EAAO,EAAI,CAAAf,YAAAA,EAAc,CAAG,CAAAgB,oBAAAA,CAAA,CAAqB3C,UAAAA,EAAY,GAAI4C,SAAAA,CAAa,IAAAC,EAAA,CAAAN,QACvGO,CAAAA,EAAAA,EAAAA,aAAAA,EACE,MACA,CACEN,IAAAA,EACA,GAAGlB,CAAA,CACHvD,MAAO2E,EACP1E,OAAQ0E,EACRhB,OAAQe,EACRd,YAAagB,EAAsBI,GAAAA,OAAOpB,GAAoBoB,OAAOL,GAAQf,EAC7E3B,UAAW,CAAC,SAAoB,UAAyBY,MAAA,CAAzBkB,EAAYK,IAAanC,EAAW,CAAAgD,IAAA,CAAK,KACzE,GAAGH,CAAA,EAEL,IACKT,EAAS9B,GAAA,CAAI,OAAC,CAAC2C,EAAKC,EAAW,CAAAX,QAAAO,CAAAA,EAAAA,EAAAA,aAAAA,EAAcG,EAAKC,QACjDC,MAAMC,OAAA,CAAQR,GAAYA,EAAW,CAACA,EAAQ,CACpD,IAMC,OAFPP,EAAUgB,WAAA,CAAc,GAAGzC,MAAA,CAAAuB,GAEpBE,CACT;;;;;GC/CM,IAAApB,EAAMtD,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,MAAO,CAClC,CAAC,OAAQ,CAAEC,EAAG,+CAAgDC,IAAK,UAAU,CAC7E,CAAC,SAAU,CAAEQ,GAAI,KAAMC,GAAI,KAAMC,EAAG,IAAKV,IAAK,UAAU,CACzD;;;;;GCHK,IAAAwC,EAAW1C,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,WAAY,CAC5C,CACE,OACA,CACEC,EAAG,wjBACHC,IAAK,QACP,EACF,CACA,CAAC,SAAU,CAAEQ,GAAI,KAAMC,GAAI,KAAMC,EAAG,IAAKV,IAAK,UAAU,CACzD;;;;;GCTK,IAAAwD,EAAS1D,CAAAA,EAAAA,QAAAA,CAAAA,EAAiB,SAAU,CACxC,CAAC,OAAQ,CAAEC,EAAG,6CAA8CC,IAAK,UAAU,CAC5E", "sources": ["webpack://_N_E/?378a", "webpack://_N_E/../../../src/icons/cookie.ts", "webpack://_N_E/../../../src/icons/toggle-right.ts", "webpack://_N_E/../../../src/icons/toggle-left.ts", "webpack://_N_E/./app/cookies/page.tsx", "webpack://_N_E/../../src/defaultAttributes.ts", "webpack://_N_E/../../src/createLucideIcon.ts", "webpack://_N_E/../../../src/icons/eye.ts", "webpack://_N_E/../../../src/icons/settings.ts", "webpack://_N_E/../../../src/icons/shield.ts"], "sourcesContent": ["import(/* webpackMode: \"eager\" */ \"/Volumes/Apps/Websites/streamit-main/app/cookies/page.tsx\");\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name <PERSON>ie\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMmExMCAxMCAwIDEgMCAxMCAxMCA0IDQgMCAwIDEtNS01IDQgNCAwIDAgMS01LTUiIC8+CiAgPHBhdGggZD0iTTguNSA4LjV2LjAxIiAvPgogIDxwYXRoIGQ9Ik0xNiAxNS41di4wMSIgLz4KICA8cGF0aCBkPSJNMTIgMTJ2LjAxIiAvPgogIDxwYXRoIGQ9Ik0xMSAxN3YuMDEiIC8+CiAgPHBhdGggZD0iTTcgMTR2LjAxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/cookie\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Cookie = createLucideIcon('Cookie', [\n  ['path', { d: 'M12 2a10 10 0 1 0 10 10 4 4 0 0 1-5-5 4 4 0 0 1-5-5', key: 'laymnq' }],\n  ['path', { d: 'M8.5 8.5v.01', key: 'ue8clq' }],\n  ['path', { d: 'M16 15.5v.01', key: '14dtrp' }],\n  ['path', { d: 'M12 12v.01', key: 'u5ubse' }],\n  ['path', { d: 'M11 17v.01', key: '1hyl5a' }],\n  ['path', { d: 'M7 14v.01', key: 'uct60s' }],\n]);\n\nexport default Cookie;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ToggleRight\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMjAiIGhlaWdodD0iMTIiIHg9IjIiIHk9IjYiIHJ4PSI2IiByeT0iNiIgLz4KICA8Y2lyY2xlIGN4PSIxNiIgY3k9IjEyIiByPSIyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/toggle-right\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ToggleRight = createLucideIcon('ToggleRight', [\n  ['rect', { width: '20', height: '12', x: '2', y: '6', rx: '6', ry: '6', key: 'f2vt7d' }],\n  ['circle', { cx: '16', cy: '12', r: '2', key: '4ma0v8' }],\n]);\n\nexport default ToggleRight;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ToggleLeft\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMjAiIGhlaWdodD0iMTIiIHg9IjIiIHk9IjYiIHJ4PSI2IiByeT0iNiIgLz4KICA8Y2lyY2xlIGN4PSI4IiBjeT0iMTIiIHI9IjIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/toggle-left\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ToggleLeft = createLucideIcon('ToggleLeft', [\n  ['rect', { width: '20', height: '12', x: '2', y: '6', rx: '6', ry: '6', key: 'f2vt7d' }],\n  ['circle', { cx: '8', cy: '12', r: '2', key: '1nvbw3' }],\n]);\n\nexport default ToggleLeft;\n", "'use client'\n\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Eye, Shield, ToggleLeft, ToggleRight } from 'lucide-react'\nimport { useState } from 'react'\n\nexport default function CookiesPage() {\n  const [cookieSettings, setCookieSettings] = useState({\n    essential: true,\n    analytics: true,\n    marketing: false,\n    preferences: true\n  })\n\n  const toggleCookie = (type: keyof typeof cookieSettings) => {\n    if (type === 'essential') return // Essential cookies cannot be disabled\n    setCookieSettings(prev => ({\n      ...prev,\n      [type]: !prev[type]\n    }))\n  }\n\n  const cookieTypes = [\n    {\n      id: 'essential',\n      title: 'Essential Cookies',\n      description: 'These cookies are necessary for the website to function and cannot be disabled.',\n      examples: ['Authentication', 'Security', 'Load balancing'],\n      enabled: cookieSettings.essential,\n      required: true\n    },\n    {\n      id: 'analytics',\n      title: 'Analytics Cookies',\n      description: 'These cookies help us understand how visitors interact with our website.',\n      examples: ['Google Analytics', 'Usage statistics', 'Performance monitoring'],\n      enabled: cookieSettings.analytics,\n      required: false\n    },\n    {\n      id: 'marketing',\n      title: 'Marketing Cookies',\n      description: 'These cookies are used to deliver relevant advertisements and track campaign effectiveness.',\n      examples: ['Ad targeting', 'Conversion tracking', 'Social media integration'],\n      enabled: cookieSettings.marketing,\n      required: false\n    },\n    {\n      id: 'preferences',\n      title: 'Preference Cookies',\n      description: 'These cookies remember your choices and preferences to provide a personalized experience.',\n      examples: ['Language settings', 'Theme preferences', 'Layout customization'],\n      enabled: cookieSettings.preferences,\n      required: false\n    }\n  ]\n\n  return (\n    <>\n      {/* Animated Background */}\n      <div className=\"animated-bg\"></div>\n\n      <div className=\"min-h-screen pt-20 relative z-10\">\n        {/* Hero Section */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-7xl mx-auto text-center\">\n            <h1 className=\"text-5xl font-bold text-white mb-6 fade-in\">\n              Cookie Policy\n            </h1>\n            <p className=\"text-xl text-white/80 max-w-3xl mx-auto leading-relaxed slide-up\">\n              Learn about how StreamIt Pro uses cookies and similar technologies \n              to improve your experience and provide our services.\n            </p>\n            <p className=\"text-white/60 mt-4\">Last updated: January 1, 2024</p>\n          </div>\n        </section>\n\n        {/* What are Cookies */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"glass p-8\">\n              <div className=\"flex items-center gap-3 mb-6\">\n                <Cookie className=\"h-6 w-6 text-purple-400\" />\n                <h2 className=\"text-2xl font-bold text-white\">What are Cookies?</h2>\n              </div>\n              <div className=\"space-y-4 text-white/80\">\n                <p>\n                  Cookies are small text files that are stored on your device when you visit a website. \n                  They help websites remember information about your visit, such as your preferred language \n                  and other settings, which can make your next visit easier and the site more useful to you.\n                </p>\n                <p>\n                  StreamIt Pro uses cookies and similar technologies to provide, protect, and improve our services. \n                  This policy explains what cookies we use, why we use them, and how you can control them.\n                </p>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* Cookie Settings */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"glass p-8\">\n              <div className=\"flex items-center gap-3 mb-6\">\n                <Settings className=\"h-6 w-6 text-blue-400\" />\n                <h2 className=\"text-2xl font-bold text-white\">Cookie Settings</h2>\n              </div>\n              <p className=\"text-white/80 mb-8\">\n                You can control which cookies we use by adjusting the settings below. \n                Note that disabling certain cookies may affect the functionality of our website.\n              </p>\n              \n              <div className=\"space-y-6\">\n                {cookieTypes.map((cookie) => (\n                  <div key={cookie.id} className=\"bg-white/5 p-6 rounded-lg\">\n                    <div className=\"flex items-center justify-between mb-4\">\n                      <h3 className=\"text-lg font-semibold text-white\">{cookie.title}</h3>\n                      <button\n                        onClick={() => toggleCookie(cookie.id as keyof typeof cookieSettings)}\n                        disabled={cookie.required}\n                        className={`flex items-center gap-2 ${\n                          cookie.required ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'\n                        }`}\n                      >\n                        {cookie.enabled ? (\n                          <ToggleRight className=\"h-6 w-6 text-green-400\" />\n                        ) : (\n                          <ToggleLeft className=\"h-6 w-6 text-white/40\" />\n                        )}\n                        <span className=\"text-white/60 text-sm\">\n                          {cookie.enabled ? 'Enabled' : 'Disabled'}\n                        </span>\n                      </button>\n                    </div>\n                    \n                    <p className=\"text-white/70 mb-4\">{cookie.description}</p>\n                    \n                    {cookie.required && (\n                      <div className=\"bg-orange-500/20 border border-orange-500/30 p-3 rounded-lg mb-4\">\n                        <p className=\"text-orange-200 text-sm\">\n                          These cookies are required for the website to function properly and cannot be disabled.\n                        </p>\n                      </div>\n                    )}\n                    \n                    <div>\n                      <h4 className=\"text-white font-medium mb-2\">Examples:</h4>\n                      <div className=\"flex flex-wrap gap-2\">\n                        {cookie.examples.map((example, index) => (\n                          <span\n                            key={index}\n                            className=\"bg-white/10 text-white/80 px-3 py-1 rounded-full text-sm\"\n                          >\n                            {example}\n                          </span>\n                        ))}\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n              \n              <div className=\"mt-8 flex flex-col sm:flex-row gap-4\">\n                <button className=\"btn-primary\">Save Preferences</button>\n                <button className=\"btn-secondary\">Accept All</button>\n                <button className=\"btn-secondary\">Reject All (except essential)</button>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* Types of Cookies */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"glass p-8\">\n              <h2 className=\"text-2xl font-bold text-white mb-6\">Types of Cookies We Use</h2>\n              \n              <div className=\"space-y-6\">\n                <div>\n                  <h3 className=\"text-lg font-semibold text-white mb-3\">First-Party Cookies</h3>\n                  <p className=\"text-white/80\">\n                    These are cookies set directly by StreamIt Pro. We use them to provide core functionality, \n                    remember your preferences, and analyze how you use our service.\n                  </p>\n                </div>\n                \n                <div>\n                  <h3 className=\"text-lg font-semibold text-white mb-3\">Third-Party Cookies</h3>\n                  <p className=\"text-white/80\">\n                    These are cookies set by external services we use, such as analytics providers or \n                    customer support tools. We carefully select our third-party partners and ensure \n                    they meet our privacy standards.\n                  </p>\n                </div>\n                \n                <div>\n                  <h3 className=\"text-lg font-semibold text-white mb-3\">Session Cookies</h3>\n                  <p className=\"text-white/80\">\n                    These cookies are temporary and are deleted when you close your browser. \n                    They help us maintain your session and provide a seamless experience.\n                  </p>\n                </div>\n                \n                <div>\n                  <h3 className=\"text-lg font-semibold text-white mb-3\">Persistent Cookies</h3>\n                  <p className=\"text-white/80\">\n                    These cookies remain on your device for a set period or until you delete them. \n                    They help us remember your preferences across multiple visits.\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* Managing Cookies */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"glass p-8\">\n              <div className=\"flex items-center gap-3 mb-6\">\n                <Eye className=\"h-6 w-6 text-green-400\" />\n                <h2 className=\"text-2xl font-bold text-white\">Managing Cookies</h2>\n              </div>\n              \n              <div className=\"space-y-6 text-white/80\">\n                <div>\n                  <h3 className=\"text-lg font-semibold text-white mb-3\">Browser Settings</h3>\n                  <p>\n                    Most web browsers allow you to control cookies through their settings. \n                    You can usually find these options in the \"Privacy\" or \"Security\" section of your browser's settings.\n                  </p>\n                </div>\n                \n                <div>\n                  <h3 className=\"text-lg font-semibold text-white mb-3\">Browser-Specific Instructions</h3>\n                  <ul className=\"list-disc list-inside space-y-2 ml-4\">\n                    <li><strong>Chrome:</strong> Settings → Privacy and security → Cookies and other site data</li>\n                    <li><strong>Firefox:</strong> Settings → Privacy & Security → Cookies and Site Data</li>\n                    <li><strong>Safari:</strong> Preferences → Privacy → Manage Website Data</li>\n                    <li><strong>Edge:</strong> Settings → Cookies and site permissions → Cookies and site data</li>\n                  </ul>\n                </div>\n                \n                <div>\n                  <h3 className=\"text-lg font-semibold text-white mb-3\">Mobile Devices</h3>\n                  <p>\n                    On mobile devices, you can manage cookies through your browser app's settings. \n                    The exact steps vary depending on your device and browser app.\n                  </p>\n                </div>\n                \n                <div className=\"bg-yellow-500/20 border border-yellow-500/30 p-4 rounded-lg\">\n                  <p className=\"text-yellow-200 text-sm\">\n                    <strong>Note:</strong> Disabling cookies may affect the functionality of StreamIt Pro \n                    and other websites you visit. Some features may not work properly without cookies enabled.\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* Third-Party Services */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"glass p-8\">\n              <div className=\"flex items-center gap-3 mb-6\">\n                <Shield className=\"h-6 w-6 text-orange-400\" />\n                <h2 className=\"text-2xl font-bold text-white\">Third-Party Services</h2>\n              </div>\n              \n              <div className=\"space-y-4 text-white/80\">\n                <p>We use the following third-party services that may set cookies:</p>\n                \n                <div className=\"grid md:grid-cols-2 gap-4\">\n                  <div className=\"bg-white/5 p-4 rounded-lg\">\n                    <h4 className=\"text-white font-semibold mb-2\">Google Analytics</h4>\n                    <p className=\"text-sm\">Helps us understand website usage and improve our service.</p>\n                  </div>\n                  <div className=\"bg-white/5 p-4 rounded-lg\">\n                    <h4 className=\"text-white font-semibold mb-2\">Intercom</h4>\n                    <p className=\"text-sm\">Provides customer support chat functionality.</p>\n                  </div>\n                  <div className=\"bg-white/5 p-4 rounded-lg\">\n                    <h4 className=\"text-white font-semibold mb-2\">Stripe</h4>\n                    <p className=\"text-sm\">Processes payments securely for our service.</p>\n                  </div>\n                  <div className=\"bg-white/5 p-4 rounded-lg\">\n                    <h4 className=\"text-white font-semibold mb-2\">Hotjar</h4>\n                    <p className=\"text-sm\">Helps us understand user behavior and improve UX.</p>\n                  </div>\n                </div>\n                \n                <p>\n                  Each of these services has their own privacy policy and cookie policy. \n                  We encourage you to review their policies to understand how they handle your data.\n                </p>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* Contact */}\n        <section className=\"py-20 px-4\">\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"glass p-8\">\n              <h2 className=\"text-2xl font-bold text-white mb-6\">Questions About Cookies?</h2>\n              <div className=\"space-y-4 text-white/80\">\n                <p>\n                  If you have any questions about our use of cookies or this Cookie Policy, \n                  please contact us:\n                </p>\n                \n                <div className=\"bg-white/5 p-4 rounded-lg\">\n                  <p><strong>Email:</strong> <EMAIL></p>\n                  <p><strong>Subject:</strong> Cookie Policy Inquiry</p>\n                </div>\n                \n                <p>\n                  We will respond to your inquiry within 30 days and help you understand \n                  or modify your cookie preferences.\n                </p>\n              </div>\n            </div>\n          </div>\n        </section>\n      </div>\n    </>\n  )\n}\n", "export default {\n  xmlns: 'http://www.w3.org/2000/svg',\n  width: 24,\n  height: 24,\n  viewBox: '0 0 24 24',\n  fill: 'none',\n  stroke: 'currentColor',\n  strokeWidth: 2,\n  strokeLinecap: 'round',\n  strokeLinejoin: 'round',\n};\n", "import {\n  forwardRef,\n  createElement,\n  ReactSVG,\n  SVGProps,\n  ForwardRefExoticComponent,\n  RefAttributes,\n} from 'react';\nimport defaultAttributes from './defaultAttributes';\n\nexport type IconNode = [elementName: keyof ReactSVG, attrs: Record<string, string>][];\n\nexport type SVGAttributes = Partial<SVGProps<SVGSVGElement>>;\ntype ComponentAttributes = RefAttributes<SVGSVGElement> & SVGAttributes;\n\nexport interface LucideProps extends ComponentAttributes {\n  size?: string | number;\n  absoluteStrokeWidth?: boolean;\n}\n\nexport type LucideIcon = ForwardRefExoticComponent<LucideProps>;\n/**\n * Converts string to KebabCase\n * Copied from scripts/helper. If anyone knows how to properly import it here\n * then please fix it.\n *\n * @param {string} string\n * @returns {string} A kebabized string\n */\nexport const toKebabCase = (string: string) =>\n  string\n    .replace(/([a-z0-9])([A-Z])/g, '$1-$2')\n    .toLowerCase()\n    .trim();\n\nconst createLucideIcon = (iconName: string, iconNode: IconNode): LucideIcon => {\n  const Component = forwardRef<SVGSVGElement, LucideProps>(\n    ({ color = 'currentColor', size = 24, strokeWidth = 2, absoluteStrokeWidth, className = '', children, ...rest }, ref) =>\n      createElement(\n        'svg',\n        {\n          ref,\n          ...defaultAttributes,\n          width: size,\n          height: size,\n          stroke: color,\n          strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n          className: ['lucide', `lucide-${toKebabCase(iconName)}`, className].join(' '),\n          ...rest,\n        },\n        [\n          ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n          ...(Array.isArray(children) ? children : [children]),\n        ]\n      )\n  );\n\n  Component.displayName = `${iconName}`;\n\n  return Component;\n};\n\nexport default createLucideIcon;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Eye\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMiAxMnMzLTcgMTAtNyAxMCA3IDEwIDctMyA3LTEwIDctMTAtNy0xMC03WiIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIzIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/eye\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Eye = createLucideIcon('Eye', [\n  ['path', { d: 'M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z', key: 'rwhkz3' }],\n  ['circle', { cx: '12', cy: '12', r: '3', key: '1v7zrd' }],\n]);\n\nexport default Eye;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Settings\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIuMjIgMmgtLjQ0YTIgMiAwIDAgMC0yIDJ2LjE4YTIgMiAwIDAgMS0xIDEuNzNsLS40My4yNWEyIDIgMCAwIDEtMiAwbC0uMTUtLjA4YTIgMiAwIDAgMC0yLjczLjczbC0uMjIuMzhhMiAyIDAgMCAwIC43MyAyLjczbC4xNS4xYTIgMiAwIDAgMSAxIDEuNzJ2LjUxYTIgMiAwIDAgMS0xIDEuNzRsLS4xNS4wOWEyIDIgMCAwIDAtLjczIDIuNzNsLjIyLjM4YTIgMiAwIDAgMCAyLjczLjczbC4xNS0uMDhhMiAyIDAgMCAxIDIgMGwuNDMuMjVhMiAyIDAgMCAxIDEgMS43M1YyMGEyIDIgMCAwIDAgMiAyaC40NGEyIDIgMCAwIDAgMi0ydi0uMThhMiAyIDAgMCAxIDEtMS43M2wuNDMtLjI1YTIgMiAwIDAgMSAyIDBsLjE1LjA4YTIgMiAwIDAgMCAyLjczLS43M2wuMjItLjM5YTIgMiAwIDAgMC0uNzMtMi43M2wtLjE1LS4wOGEyIDIgMCAwIDEtMS0xLjc0di0uNWEyIDIgMCAwIDEgMS0xLjc0bC4xNS0uMDlhMiAyIDAgMCAwIC43My0yLjczbC0uMjItLjM4YTIgMiAwIDAgMC0yLjczLS43M2wtLjE1LjA4YTIgMiAwIDAgMS0yIDBsLS40My0uMjVhMiAyIDAgMCAxLTEtMS43M1Y0YTIgMiAwIDAgMC0yLTJ6IiAvPgogIDxjaXJjbGUgY3g9IjEyIiBjeT0iMTIiIHI9IjMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/settings\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Settings = createLucideIcon('Settings', [\n  [\n    'path',\n    {\n      d: 'M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z',\n      key: '1qme2f',\n    },\n  ],\n  ['circle', { cx: '12', cy: '12', r: '3', key: '1v7zrd' }],\n]);\n\nexport default Settings;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Shield\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMjJzOC00IDgtMTBWNWwtOC0zLTggM3Y3YzAgNiA4IDEwIDggMTAiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/shield\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Shield = createLucideIcon('Shield', [\n  ['path', { d: 'M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10', key: '1irkt0' }],\n]);\n\nexport default Shield;\n"], "names": ["Promise", "resolve", "then", "__webpack_require__", "bind", "<PERSON><PERSON>", "createLucideIcon", "d", "key", "ToggleRight", "width", "height", "x", "y", "rx", "ry", "cx", "cy", "r", "ToggleLeft", "CookiesPage", "cookieSettings", "setCookieSettings", "useState", "essential", "analytics", "marketing", "preferences", "to<PERSON><PERSON><PERSON><PERSON>", "type", "prev", "cookieTypes", "id", "title", "description", "examples", "enabled", "required", "jsx_runtime", "jsxs", "Fragment", "jsx", "div", "className", "section", "h1", "p", "h2", "Settings", "map", "h3", "cookie", "button", "onClick", "disabled", "concat", "span", "h4", "example", "index", "Eye", "ul", "li", "strong", "Shield", "defaultAttributes", "xmlns", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "toKebabCase", "string", "replace", "toLowerCase", "trim", "iconName", "iconNode", "Component", "forwardRef", "param", "ref", "color", "size", "absoluteStrokeWidth", "children", "rest", "createElement", "Number", "join", "tag", "attrs", "Array", "isArray", "displayName"], "sourceRoot": ""}