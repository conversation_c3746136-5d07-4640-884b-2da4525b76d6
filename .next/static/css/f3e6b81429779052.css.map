{"version": 3, "sources": ["webpack://_N_E/f3e6b81429779052.css", "webpack://_N_E/node_modules/next/font/google/%3Cinput%20css%20umpwAm%3E", "webpack://_N_E/<no source>"], "names": [], "mappings": "AAAA,mGAAmG,CACnG,iBACE,uBAAwB,CACxB,uBAAwB,CACxB,kBAAmB,CACnB,kBAAmB,CACnB,aAAc,CACd,aAAc,CACd,aAAc,CACd,cAAe,CACf,cAAe,CACf,YAAa,CACb,YAAa,CACb,iBAAkB,CAClB,qCAAsC,CACtC,6BAA8B,CAC9B,4BAA6B,CAC7B,2BAA4B,CAC5B,cAAe,CACf,mBAAoB,CACpB,qBAAsB,CACtB,sBAAuB,CACvB,uBAAwB,CACxB,iBAAkB,CAClB,0BAA2B,CAC3B,2BAA4B,CAC5B,mCAAsC,CACtC,iCAAkC,CAClC,0BAA2B,CAC3B,qBAAsB,CACtB,6BAA8B,CAC9B,WAAY,CACZ,iBAAkB,CAClB,eAAgB,CAChB,gBAAiB,CACjB,iBAAkB,CAClB,aAAc,CACd,eAAgB,CAChB,YAAa,CACb,kBAAmB,CACnB,oBAAqB,CACrB,0BAA2B,CAC3B,wBAAyB,CACzB,yBAA0B,CAC1B,0BAA2B,CAC3B,sBAAuB,CACvB,uBAAwB,CACxB,wBAAyB,CACzB,qBAAsB,CACtB,mBAAoB,CACpB,qBAAsB,CACtB,oBAAqB,CACrB,oBACF,CACA,WACE,uBAAwB,CACxB,uBAAwB,CACxB,kBAAmB,CACnB,kBAAmB,CACnB,aAAc,CACd,aAAc,CACd,aAAc,CACd,cAAe,CACf,cAAe,CACf,YAAa,CACb,YAAa,CACb,iBAAkB,CAClB,qCAAsC,CACtC,6BAA8B,CAC9B,4BAA6B,CAC7B,2BAA4B,CAC5B,cAAe,CACf,mBAAoB,CACpB,qBAAsB,CACtB,sBAAuB,CACvB,uBAAwB,CACxB,iBAAkB,CAClB,0BAA2B,CAC3B,2BAA4B,CAC5B,mCAAsC,CACtC,iCAAkC,CAClC,0BAA2B,CAC3B,qBAAsB,CACtB,6BAA8B,CAC9B,WAAY,CACZ,iBAAkB,CAClB,eAAgB,CAChB,gBAAiB,CACjB,iBAAkB,CAClB,aAAc,CACd,eAAgB,CAChB,YAAa,CACb,kBAAmB,CACnB,oBAAqB,CACrB,0BAA2B,CAC3B,wBAAyB,CACzB,yBAA0B,CAC1B,0BAA2B,CAC3B,sBAAuB,CACvB,uBAAwB,CACxB,wBAAyB,CACzB,qBAAsB,CACtB,mBAAoB,CACpB,qBAAsB,CACtB,oBAAqB,CACrB,oBACF;AACA;;CAEC,CAKD,iBAGE,qBAAsB,CAGtB,sBACF,CACA,eAEE,eACF,CAUA,WAEE,eAAgB,CAChB,6BAA8B,CAC9B,eAAgB,CAChB,aAAc,CACX,UAAW,CACd,gHAA+H,CAC/H,4BAA6B,CAC7B,8BAA+B,CAC/B,uCACF,CAKA,KACE,QAAS,CACT,mBACF,CAMA,GACE,QAAS,CACT,aAAc,CACd,oBACF,CAIA,oBACE,wCAAyC,CACjC,gCACV,CAIA,kBAME,iBAAkB,CAClB,mBACF,CAIA,EACE,aAAc,CACd,uBACF,CAIA,SAEE,kBACF,CAOA,kBAIE,mGAA+G,CAC/G,4BAA6B,CAC7B,8BAA+B,CAC/B,aACF,CAIA,MACE,aACF,CAIA,QAEE,aAAc,CACd,aAAc,CACd,iBAAkB,CAClB,uBACF,CACA,IACE,aACF,CACA,IACE,SACF,CAMA,MACE,aAAc,CACd,oBAAqB,CACrB,wBACF,CAMA,sCAKE,mBAAoB,CACpB,6BAA8B,CAC9B,+BAAgC,CAChC,cAAe,CACf,mBAAoB,CACpB,mBAAoB,CACpB,sBAAuB,CACvB,aAAc,CACd,QAAS,CACT,SACF,CAIA,cAEE,mBACF,CAKA,uFAIE,yBAA0B,CAC1B,4BAA6B,CAC7B,qBACF,CAIA,gBACE,YACF,CAIA,iBACE,eACF,CAIA,SACE,uBACF,CAIA,wDAEE,WACF,CAKA,cACE,4BAA6B,CAC7B,mBACF,CAIA,4BACE,uBACF,CAKA,6BACE,yBAA0B,CAC1B,YACF,CAIA,QACE,iBACF,CAIA,mDAaE,QACF,CACA,SACE,QAEF,CACA,gBAFE,SAIF,CACA,WAGE,eAAgB,CAChB,QAAS,CACT,SACF,CAIA,OACE,SACF,CAIA,SACE,eACF,CAKA,mDACE,SAAU,CACV,aACF,CACA,yCAEE,SAAU,CACV,aACF,CAIA,qBAEE,cACF,CAIA,UACE,cACF,CAMA,+CAQE,aAAc,CACd,qBACF,CAIA,UAEE,cAAe,CACf,WACF,CAEA,2CACE,YACF,CACA,qBACE,mBACF,CACA,SACE,kBACF,CACA,OACE,cACF,CACA,UACE,iBACF,CACA,UACE,iBACF,CACA,SACE,OACF,CACA,UACE,aACF,CACA,QACE,WACF,CACA,QACE,WACF,CACA,UACE,YACF,CACA,WACE,WACF,CACA,UACE,WACF,CACA,aACE,WACF,CACA,QACE,MACF,CACA,WACE,QACF,CACA,QACE,UACF,CACA,QACE,WACF,CACA,QACE,SACF,CACA,SACE,OACF,CACA,SACE,WACF,CACA,SACE,YACF,CACA,SACE,UACF,CACA,OACE,KACF,CACA,OACE,UACF,CACA,UACE,OACF,CACA,OACE,SACF,CACA,QACE,QACF,CACA,OACE,QACF,CACA,UACE,QACF,CACA,MACE,UACF,CACA,MACE,UACF,CACA,WACE,WACF,CACA,SACE,gBAAiB,CACjB,iBACF,CACA,MACE,iBAAkB,CAClB,oBACF,CACA,MACE,oBACF,CACA,OACE,kBACF,CACA,OACE,kBACF,CACA,MACE,mBACF,CACA,MACE,oBACF,CACA,MACE,kBACF,CACA,MACE,oBACF,CACA,MACE,kBACF,CACA,MACE,iBACF,CACA,MACE,gBACF,CACA,MACE,mBACF,CACA,MACE,iBACF,CACA,SACE,kBACF,CACA,MACE,iBACF,CACA,OACE,eACF,CACA,OACE,eACF,CACA,MACE,gBACF,CACA,OACE,eACF,CACA,MACE,eACF,CACA,MACE,eACF,CACA,cAIE,oBACF,CACA,4BALE,eAAgB,CAChB,mBAAoB,CACpB,2BAQF,CALA,cAIE,oBACF,CACA,OACE,aACF,CACA,cACE,oBACF,CACA,MACE,YACF,CACA,aACE,mBACF,CACA,OACE,aACF,CACA,MACE,YACF,CACA,QACE,YACF,CACA,QACE,UACF,CACA,MACE,aACF,CACA,MACE,cACF,CACA,MACE,WACF,CACA,MACE,WACF,CACA,KACE,YACF,CACA,MACE,WACF,CACA,MACE,WACF,CACA,KACE,aACF,CACA,KACE,WACF,CACA,MACE,YACF,CACA,KACE,cACF,CACA,KACE,aACF,CACA,MACE,YACF,CACA,KACE,cACF,CACA,KACE,WACF,CACA,KACE,cACF,CACA,aACE,YACF,CACA,QACE,WACF,CACA,UACE,gBACF,CACA,gBACE,eACF,CACA,cACE,gBACF,CACA,cACE,gBACF,CACA,QACE,aACF,CACA,QACE,gBACF,CACA,MACE,YACF,CACA,MACE,UACF,CACA,MACE,YACF,CACA,MACE,UACF,CACA,KACE,WACF,CACA,QACE,gBACF,CACA,MACE,UACF,CACA,KACE,YACF,CACA,KACE,UACF,CACA,KACE,aACF,CACA,KACE,YACF,CACA,MACE,WACF,CACA,KACE,UACF,CACA,QACE,UACF,CACA,SACE,WACF,CACA,UACE,cACF,CACA,UACE,eACF,CACA,iBACE,eACF,CACA,WACE,eACF,CACA,WACE,eACF,CACA,WACE,eACF,CACA,WACE,eACF,CACA,gBACE,aACF,CACA,UACE,eACF,CACA,UACE,eACF,CACA,QACE,WACF,CAIA,yBACE,aACF,CACA,mBACE,qBAEF,CACA,sCAFE,6LAKF,CAHA,mBACE,qBAEF,CACA,eACE,wBAEF,CACA,8BAFE,6LAKF,CAHA,eACE,wBAEF,CACA,WACE,6LACF,CACA,gBACE,GACE,uBACF,CACF,CACA,cACE,iCACF,CACA,oBACE,kBACF,CACA,gBACE,cACF,CACA,aACE,WACF,CACA,aACE,0BACF,CACA,WACE,oBACF,CACA,aACE,6CACF,CACA,aACE,6CACF,CACA,aACE,6CACF,CACA,aACE,0CACF,CACA,aACE,0CACF,CACA,aACE,0CACF,CACA,UACE,qBACF,CACA,kBACE,6BACF,CACA,WACE,cACF,CACA,oBACE,kBACF,CACA,aACE,sBACF,CACA,WACE,oBACF,CACA,cACE,kBACF,CACA,aACE,wBACF,CACA,gBACE,sBACF,CACA,iBACE,6BACF,CACA,OACE,UACF,CACA,QACE,QACF,CACA,OACE,SACF,CACA,OACE,UACF,CACA,OACE,QACF,CACA,OACE,UACF,CACA,OACE,QACF,CACA,4CACE,sBAAuB,CACvB,sDAAwD,CACxD,+DACF,CACA,yCACE,sBAAuB,CACvB,qDAAuD,CACvD,8DACF,CACA,yCACE,sBAAuB,CACvB,oDAAsD,CACtD,6DACF,CACA,yCACE,sBAAuB,CACvB,qDAAuD,CACvD,8DACF,CACA,yCACE,sBAAuB,CACvB,mDAAoD,CACpD,4DACF,CACA,yCACE,sBAAuB,CACvB,qDAAsD,CACtD,8DACF,CACA,yCACE,sBAAuB,CACvB,mDAAoD,CACpD,4DACF,CACA,4CACE,sBAAuB,CACvB,8DAAgE,CAChE,uDACF,CACA,0CACE,sBAAuB,CACvB,2DAA4D,CAC5D,oDACF,CACA,yCACE,sBAAuB,CACvB,4DAA8D,CAC9D,qDACF,CACA,yCACE,sBAAuB,CACvB,6DAA+D,CAC/D,sDACF,CACA,yCACE,sBAAuB,CACvB,2DAA4D,CAC5D,oDACF,CACA,yCACE,sBAAuB,CACvB,6DAA8D,CAC9D,sDACF,CACA,yCACE,sBAAuB,CACvB,2DAA4D,CAC5D,oDACF,CACA,iBACE,eACF,CACA,iBACE,eACF,CACA,iBACE,eACF,CACA,mBACE,iBACF,CACA,UACE,eAAgB,CAChB,sBAEF,CACA,6BAFE,kBAIF,CACA,SACE,oBACF,CACA,cACE,oBACF,CACA,YACE,2BACF,CACA,YACE,uCACF,CACA,gBACE,wBAA2B,CAC3B,2BACF,CACA,gBACE,yBAA4B,CAC5B,4BACF,CACA,YACE,gCACF,CACA,QACE,gBACF,CACA,UACE,gBACF,CACA,UACE,gBACF,CACA,UACE,uBACF,CACA,UACE,qBACF,CACA,UACE,sBACF,CACA,UACE,oBACF,CACA,oBACE,oCACF,CACA,cACE,8BACF,CACA,uBACE,gCACF,CACA,mBACE,qBAAsB,CACtB,uDACF,CACA,oBACE,+BACF,CACA,kBACE,+BACF,CACA,kBACE,+BACF,CACA,kBACE,+BACF,CACA,iBACE,gCACF,CACA,uBACE,+BACF,CACA,gBACE,qBAAsB,CACtB,4DACF,CACA,eACE,uCACF,CACA,cACE,+BACF,CACA,cACE,+BACF,CACA,iBACE,oCACF,CACA,SACE,iCACF,CACA,gBACE,wCACF,CACA,aACE,iBAAkB,CAClB,qDACF,CACA,cACE,iBAAkB,CAClB,uDACF,CACA,cACE,iBAAkB,CAClB,sDACF,CACA,kBACE,mCACF,CACA,eACE,iBAAkB,CAClB,uDACF,CACA,mBACE,oCACF,CACA,YACE,oCACF,CACA,eACE,iBAAkB,CAClB,uDACF,CACA,mBACE,oCACF,CACA,mBACE,oCACF,CACA,YACE,iBAAkB,CAClB,sDACF,CACA,gBACE,mCACF,CACA,gBACE,mCACF,CACA,YACE,iBAAkB,CAClB,sDACF,CACA,cACE,sCACF,CACA,gBACE,4BACF,CACA,UACE,iBAAkB,CAClB,wDACF,CACA,cACE,mCACF,CACA,cACE,mCACF,CACA,aACE,oCACF,CACA,eACE,iBAAkB,CAClB,uDACF,CACA,mBACE,mCACF,CACA,mBACE,0EACF,CACA,kBACE,mEACF,CACA,eACE,2DAA4D,CAC5D,kEAAoE,CACpE,iEACF,CACA,eACE,2DAA4D,CAC5D,gEAAkE,CAClE,iEACF,CACA,eACE,2DAA4D,CAC5D,gEAAkE,CAClE,iEACF,CACA,gBACE,2DAA4D,CAC5D,iEAAmE,CACnE,iEACF,CACA,iBACE,2DAA4D,CAC5D,kEAAoE,CACpE,iEACF,CACA,qBACE,uEAA0E,CAC1E,kEAAoE,CACpE,iEACF,CACA,iBACE,2DAA4D,CAC5D,kEAAoE,CACpE,iEACF,CACA,iBACE,2DAA4D,CAC5D,iEAAmE,CACnE,iEACF,CACA,aACE,uDACF,CACA,gBACE,uDACF,CACA,aACE,uDACF,CACA,aACE,uDACF,CACA,eACE,uDACF,CACA,aACE,uDACF,CACA,iBACE,mEACF,CACA,aACE,uDACF,CACA,eACE,uDACF,CACA,cACE,iBACF,CACA,cACE,mBAAoB,CACjB,gBACL,CACA,KACE,cACF,CACA,MACE,YACF,CACA,KACE,aACF,CACA,KACE,cACF,CACA,KACE,YACF,CACA,KACE,cACF,CACA,KACE,YACF,CACA,MACE,mBAAqB,CACrB,oBACF,CACA,MACE,kBAAoB,CACpB,mBACF,CACA,MACE,mBAAqB,CACrB,oBACF,CACA,MACE,iBAAkB,CAClB,kBACF,CACA,MACE,mBAAoB,CACpB,oBACF,CACA,MACE,iBAAkB,CAClB,kBACF,CACA,MACE,kBAAoB,CACpB,qBACF,CACA,OACE,kBAAmB,CACnB,qBACF,CACA,OACE,gBAAiB,CACjB,mBACF,CACA,MACE,iBAAmB,CACnB,oBACF,CACA,OACE,gBAAiB,CACjB,mBACF,CACA,MACE,kBAAoB,CACpB,qBACF,CACA,MACE,gBAAiB,CACjB,mBACF,CACA,MACE,gBAAiB,CACjB,mBACF,CACA,OACE,mBACF,CACA,MACE,qBACF,CACA,OACE,mBACF,CACA,OACE,iBACF,CACA,OACE,oBACF,CACA,MACE,kBACF,CACA,MACE,kBACF,CACA,MACE,aACF,CACA,OACE,gBACF,CACA,MACE,gBACF,CACA,WACE,eACF,CACA,aACE,iBACF,CACA,WACE,mGACF,CACA,UACE,gBAAiB,CACjB,gBACF,CACA,UACE,kBAAmB,CACnB,mBACF,CACA,UACE,iBAAkB,CAClB,kBACF,CACA,UACE,cAAe,CACf,aACF,CACA,SACE,kBAAmB,CACnB,mBACF,CACA,SACE,iBAAmB,CACnB,mBACF,CACA,SACE,iBAAkB,CAClB,mBACF,CACA,SACE,gBAAkB,CAClB,gBACF,CACA,WACE,eACF,CACA,aACE,eACF,CACA,eACE,eACF,CACA,cACE,aACF,CACA,iBACE,iBACF,CACA,gBACE,sBACF,CACA,YACE,mBAAoB,CACpB,yCACF,CACA,eACE,mBAAoB,CACpB,8CACF,CACA,sBACE,iCACF,CACA,6BACE,wCACF,CACA,iBACE,4BACF,CACA,qBACE,+BACF,CACA,gBACE,mBAAoB,CACpB,8CACF,CACA,uBACE,kCACF,CACA,iBACE,mBAAoB,CACpB,+CACF,CACA,iBACE,mBAAoB,CACpB,8CACF,CACA,cACE,yBACF,CACA,yBACE,oCACF,CACA,iBACE,mBAAoB,CACpB,+CACF,CACA,iBACE,mBAAoB,CACpB,+CACF,CACA,iBACE,mBAAoB,CACpB,8CACF,CACA,cACE,mBAAoB,CACpB,+CACF,CACA,2BACE,sCACF,CACA,YACE,mBAAoB,CACpB,+CACF,CACA,gBACE,wBACF,CACA,gBACE,wBACF,CACA,gBACE,wBACF,CACA,gBACE,wBACF,CACA,gBACE,wBACF,CACA,iBACE,mBAAoB,CACpB,+CACF,CACA,iBACE,mBAAoB,CACpB,8CACF,CACA,oBACE,yBACF,CACA,WACE,SACF,CACA,YACE,UACF,CACA,YACE,UACF,CACA,YACE,UACF,CACA,WACE,yEAA+E,CAC/E,iGAEF,CACA,sBAFE,kGAMF,CAJA,WACE,uCAA0C,CAC1C,sDAEF,CACA,SACE,mBACF,CACA,MACE,0GAA2G,CAC3G,wGAAyG,CACzG,wFACF,CACA,wBACE,6CACF,CACA,MACE,mBAEF,CACA,cAFE,gLAIF,CACA,kBACE,6BAGF,CACA,oCAHE,8QAA+Q,CAC/Q,sQAMF,CAJA,kBACE,4BAGF,CACA,gBACE,uBAAwB,CACxB,kDAAwD,CACxD,wBACF,CACA,mBACE,yFAA+F,CAC/F,kDAAwD,CACxD,wBACF,CACA,oBACE,2BAA4B,CAC5B,kDAAwD,CACxD,wBACF,CACA,sBACE,6BAA8B,CAC9B,kDAAwD,CACxD,wBACF,CACA,cACE,uBACF,CACA,iBACE,GACE,iCAAmC,CACnC,sMACF,CACF,CACA,gBACE,GACE,gCAAkC,CAClC,gMACF,CACF,CACA,SACE,oBACF,CACA,cACE,sBACF,CAGA,EACE,QAAS,CACT,SAAU,CACV,qBACF,CAEA,KACE,4BAAgC,CAChC,kDAA6D,CAC7D,gBAAiB,CACjB,iBACF,CAGA,aACE,cAAe,CAKf,kDAA6D,CAC7D,UACF,CAEA,iCARE,KAAM,CACN,MAAO,CACP,UAAW,CACX,WAiBF,CAZA,oBACE,UAAW,CACX,iBAAkB,CAKlB,wOAGkF,CAClF,wCACF,CAEA,iBACE,MAAW,oCAAyC,CACpD,IAAM,wCAA2C,CACjD,IAAM,yCAA4C,CACpD,CAGA,OACE,6BAAoC,CAGpC,mCAA0C,CAC1C,kBAAmB,CACnB,oCACF,CAEA,mBAPE,kCAAmC,CAC3B,0BAaV,CAPA,YACE,yBAA8B,CAG9B,mCAA0C,CAC1C,kBAAmB,CACnB,oCACF,CAEA,cACE,8BAAqC,CACrC,kCAAmC,CAC3B,0BAA2B,CACnC,mCAA0C,CAC1C,kBAAmB,CACnB,uBAAyB,CACzB,oCACF,CAEA,oBACE,8BAAqC,CACrC,0BAA2B,CAC3B,qCACF,CAEA,qBACE,uBACF,CAGA,aACE,kDAA6D,CAC7D,UAAY,CACZ,WAAY,CACZ,iBAAkB,CAClB,kBAAmB,CACnB,eAAgB,CAChB,uBAAyB,CACzB,yCACF,CAEA,mBACE,0BAA2B,CAC3B,yCAA8C,CAC9C,kDACF,CAEA,eACE,6BAAoC,CACpC,UAAY,CACZ,mCAA0C,CAC1C,iBAAkB,CAClB,kBAAmB,CACnB,eAAgB,CAChB,uBAAyB,CACzB,kCAAmC,CAC3B,0BACV,CAEA,qBACE,6BAAoC,CACpC,0BACF,CAGA,aACE,6BAAoC,CACpC,kCAAmC,CAC3B,0BAA2B,CACnC,mCAA0C,CAC1C,kBAAmB,CACnB,iBAAkB,CAClB,UAAY,CACZ,cAAe,CACf,uBACF,CAEA,+BACE,wBACF,CAEA,0BACE,wBACF,CAEA,mBACE,YAAa,CAEb,uCAA4C,CAC5C,8BACF,CAGA,iBACE,yBAA8B,CAC9B,kCAAmC,CAC3B,0BAA2B,CACnC,mCAA0C,CAC1C,kBAIF,CAEA,YACE,kBAAmB,CACnB,kBAAmB,CACnB,eAAgB,CAChB,iBAAkB,CAClB,iBACF,CAEA,kBACE,UAAW,CACX,WAAY,CACZ,mBAAoB,CACjB,gBACL,CAEA,gBACE,yBAA8B,CAC9B,kCAAmC,CAC3B,0BAA2B,CACnC,kBAAmB,CACnB,YAAa,CAEb,sBAAuB,CACvB,OAAQ,CACR,aAAc,CACd,sBAAuB,CACvB,iBACF,CAEA,aACE,UAAW,CACX,WAAY,CACZ,iBAAkB,CAClB,WAAY,CACZ,YAAa,CACb,kBAAmB,CACnB,sBAAuB,CACvB,uBAAyB,CACzB,cAAe,CACf,iBACF,CAEA,oBACE,kBAAmB,CACnB,UAAY,CACZ,wCACF,CAEA,sBACE,6BAAoC,CACpC,UAAY,CACZ,kCAAmC,CAC3B,0BAA2B,CACnC,mCACF,CAEA,mBACE,qBAAsB,CACtB,oCACF,CAEA,oBACE,oBACF,CAGA,gBACE,yBAA8B,CAC9B,kCAAmC,CAC3B,0BAA2B,CACnC,kBAAmB,CACnB,mCACF,CAEA,cACE,6BAAoC,CACpC,kCAAmC,CAC3B,0BAA2B,CACnC,kBAAmB,CACnB,YAAa,CACb,YAAa,CACb,mCACF,CAGA,kBACE,GAAO,SAAU,CAAE,0BAA6B,CAChD,GAAK,SAAU,CAAE,uBAA0B,CAC7C,CAEA,mBACE,GAAO,SAAU,CAAE,0BAA6B,CAChD,GAAK,SAAU,CAAE,uBAA0B,CAC7C,CAEA,oBACE,GAAK,SAAU,CAAE,mBAAuB,CACxC,IAAM,SAAU,CAAE,qBAAwB,CAC1C,IAAM,mBAAuB,CAC7B,GAAO,SAAU,CAAE,kBAAqB,CAC1C,CAEA,SAAW,6BAAiC,CAC5C,UAAY,8BAAkC,CAC9C,WAAa,+BAAmC,CAGhD,yBACE,OACE,WAAY,CACZ,kBACF,CAEA,cACE,iBAAkB,CAClB,cACF,CAEA,aACE,UAAW,CACX,WACF,CACF,CAGA,YACE,YAAa,CACb,QAAS,CACT,YACF,CAEA,cACE,yBACF,CAEA,cACE,6BACF,CAOA,4BAJE,6BAA8B,CAC9B,0BAMF,CAEA,sEACE,mCAAqC,CACrC,kBACF,CAEA,iBACE,iBAAkB,CAClB,iBAAoB,CACpB,eAAgB,CAChB,2BAA4B,CAC5B,iBAAkB,CAClB,qDACF,CAEA,uBACE,WAAY,CACZ,UAAW,CACX,mBAAoB,CACjB,gBACL,CAEA,gBACE,iBAAkB,CAClB,WAAY,CACZ,QAAS,CACT,YAAa,CACb,qBAAsB,CACtB,6LAA+L,CAC/L,SACF,CAEA,gBACE,oBAAqB,CACrB,cAAgB,CAChB,yFAA+F,CAC/F,kDAAwD,CACxD,uBAA0B,CAC1B,sBACF,CAEA,uBACE,iBAAkB,CAClB,sDACF,CAEA,6BACE,iBAAkB,CAClB,sDACF,CAEA,yBACE,iBAAkB,CAClB,qDACF,CAEA,+BACE,iBAAkB,CAClB,qDACF,CAGA,KACE,sBACF,CAGA,QACE,sBACF,CAGA,yBACE,OACE,UAAW,CACX,cAAe,CACf,kBACF,CAEA,4BACE,iBAAkB,CAClB,cACF,CAEA,GACE,0BACF,CAEA,GACE,wBACF,CAEA,SACE,4BACF,CACF,CAGA,SACE,UAAY,CACZ,mBACF,CAGA,aACE,0BAA2B,CAC3B,uBAAyB,CACzB,sCACF,CAGA,eACE,kDAA6D,CAC7D,4BAA6B,CAC7B,mCAAoC,CACpC,oBACF,CAGA,aACE,2BAA4B,CAC5B,gBAAiB,CACjB,+BAAoC,CACpC,mCAAwC,CAIxC,mBAAuB,CACvB,mBAAoB,CACpB,+CACF,CACA,+BACE,wBACF,CACA,0BACE,wBACF,CACA,mBACE,wBAAyB,CACzB,6BAA8B,CAC9B,kBAAmB,CACnB,0GAA2G,CAC3G,wGAAyG,CACzG,wFAA4F,CAC5F,mBAAoB,CACpB,wDACF,CACA,aACE,4BAA6B,CAC7B,8QAA+Q,CAC/Q,sQAAuQ,CACvQ,uBAAwB,CACxB,kDAAwD,CACxD,uBAA0B,CAC1B,sBACF,CAEA,mBACE,gCAAmC,CACnC,oCACF,CAGA,YACE,2BAA4B,CAC5B,gBAAiB,CACjB,+BAAoC,CACpC,+BAAkC,CAClC,6BAA8B,CAC9B,8QAA+Q,CAC/Q,sQACF,CACA,sCACE,cACF,CACA,4CACE,4BACF,CACA,qCACE,iBAAmB,CACnB,mBACF,CACA,yCACE,eACF,CACA,sDACE,kCACF,CACA,iDACE,kCACF,CACA,wBACE,iBAAkB,CAClB,iBAEF,CACA,sDAFE,6LAMF,CAJA,8BACE,iBAAkB,CAClB,iBAEF,CACA,wBACE,6LACF,CACA,+BACE,+BACF,CACA,wBACE,mCACF,CACA,iCACE,2CACF,CACA,0BACE,iBAAkB,CAClB,wDACF,CACA,0BACE,iBAAkB,CAClB,wDACF,CACA,6BACE,uCACF,CACA,yBACE,iBAAkB,CAClB,sDACF,CACA,yBACE,iBAAkB,CAClB,sDACF,CACA,2BACE,sCACF,CACA,+BACE,yCACF,CACA,2BACE,mCACF,CACA,2BACE,mCACF,CACA,0BACE,oCACF,CACA,8BACE,2DAA4D,CAC5D,kEAAoE,CACpE,iEACF,CACA,0BACE,uDACF,CACA,qCACE,mCACF,CACA,8BACE,4BACF,CACA,8BACE,mBAAoB,CACpB,+CACF,CACA,2BACE,mBAAoB,CACpB,+CACF,CACA,yBACE,mBAAoB,CACpB,+CACF,CACA,wBACE,8BACF,CACA,yBACE,UACF,CACA,0BACE,SACF,CACA,2BACE,6BAA8B,CAC9B,kBACF,CACA,qBACE,0GAA2G,CAC3G,wGAAyG,CACzG,wFACF,CACA,8BACE,mBAAoB,CACpB,wDACF,CACA,wBACE,gCACF,CACA,4BACE,0BACF,CACA,2CACE,6BAA8B,CAC9B,kBACF,CACA,qCACE,0GAA2G,CAC3G,wGAAyG,CACzG,wFACF,CACA,wCACE,gCACF,CACA,4CACE,0BACF,CACA,wCACE,mBACF,CACA,uCACE,kBACF,CACA,+BACE,UACF,CACA,uCACE,SACF,CACA,8DACE,iCACF,CACA,0DACE,mBAAoB,CACpB,+CACF,CACA,iFACE,uCACF,CACA,yEACE,wCACF,CACA,sFACE,wCACF,CACA,sEACE,mBAAoB,CACpB,+CACF,CACA,2EACE,uCACF,CACA,uEACE,mBAAoB,CACpB,yDACF,CACA,8EACE,8BACF,CACA,0DACE,oBAEF,CACA,iJAFE,6LAKF,CAHA,uFACE,+CAEF,CACA,0FACE,gDAAiD,CACjD,6LACF,CACA,wDACE,wBACF,CACA,mDACE,oBAAqB,CACrB,uBAAyB,CACzB,0BAA2B,CAC3B,wBAAyB,CACzB,yBAA0B,CAC1B,8BAA+B,CAC/B,8BACF,CAUA,0GARE,mBAAoB,CACpB,uBAAyB,CACzB,yBAA0B,CAC1B,uBAAwB,CACxB,wBAAyB,CACzB,6BAA8B,CAC9B,6BAUF,CACA,wDACE,qBACF,CACA,oEACE,0BACF,CACA,+DACE,4BACF,CACA,yBACE,cACE,QACF,CACA,aACE,OACF,CACA,cACE,QACF,CACA,cACE,kBACF,CACA,cACE,qBACF,CACA,UACE,mBAAoB,CACpB,oBACF,CACA,sEACE,2BACF,CACF,CACA,yBACE,UACE,YACF,CACA,UACE,YACF,CACA,UACE,YACF,CACA,qBACE,eACF,CACA,iBACE,6CACF,CACA,iBACE,6CACF,CACA,iBACE,6CACF,CACA,cACE,kBACF,CACA,UACE,YACF,CACF,CACA,0BACE,gBACE,yBACF,CACA,iBACE,6CACF,CACA,iBACE,6CACF,CACA,iBACE,6CACF,CACA,cACE,kBACF,CACA,kBACE,kBACF,CACA,qBACE,6BACF,CACA,UACE,iBAAkB,CAClB,kBACF,CACF,CCn1EA,WACE,0BAAoB,CACpB,iBAAkB,CAClB,mBAAoB,CACpB,iBAAkB,CAClB,qEAAsE,CACtE,gFACF,CAEA,WACE,0BAAoB,CACpB,iBAAkB,CAClB,mBAAoB,CACpB,iBAAkB,CAClB,qEAAsE,CACtE,+DACF,CAEA,WACE,0BAAoB,CACpB,iBAAkB,CAClB,mBAAoB,CACpB,iBAAkB,CAClB,qEAAsE,CACtE,oBACF,CAEA,WACE,0BAAoB,CACpB,iBAAkB,CAClB,mBAAoB,CACpB,iBAAkB,CAClB,qEAAsE,CACtE,gFACF,CAEA,WACE,0BAAoB,CACpB,iBAAkB,CAClB,mBAAoB,CACpB,iBAAkB,CAClB,qEAAsE,CACtE,0JACF,CAEA,WACE,0BAAoB,CACpB,iBAAkB,CAClB,mBAAoB,CACpB,iBAAkB,CAClB,qEAAsE,CACtE,gMACF,CAEA,WACE,0BAAoB,CACpB,iBAAkB,CAClB,mBAAoB,CACpB,iBAAkB,CAClB,uEAAwE,CACxE,iKACF,CC9DA,WAAA,mCAAA,CAAA,kBAAA,CAAA,sBAAA,CAAA,uBAAA,CAAA,uBAAA,CAAA,mBFq5EA,CEr5EA,oBAAA,kDAAA,CAAA,iBFs5EA", "file": "static/css/f3e6b81429779052.css", "sourcesContent": ["@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');\n*, ::before, ::after{\n  --tw-border-spacing-x: 0;\n  --tw-border-spacing-y: 0;\n  --tw-translate-x: 0;\n  --tw-translate-y: 0;\n  --tw-rotate: 0;\n  --tw-skew-x: 0;\n  --tw-skew-y: 0;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  --tw-pan-x:  ;\n  --tw-pan-y:  ;\n  --tw-pinch-zoom:  ;\n  --tw-scroll-snap-strictness: proximity;\n  --tw-gradient-from-position:  ;\n  --tw-gradient-via-position:  ;\n  --tw-gradient-to-position:  ;\n  --tw-ordinal:  ;\n  --tw-slashed-zero:  ;\n  --tw-numeric-figure:  ;\n  --tw-numeric-spacing:  ;\n  --tw-numeric-fraction:  ;\n  --tw-ring-inset:  ;\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: rgb(59 130 246 / 0.5);\n  --tw-ring-offset-shadow: 0 0 #0000;\n  --tw-ring-shadow: 0 0 #0000;\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  --tw-blur:  ;\n  --tw-brightness:  ;\n  --tw-contrast:  ;\n  --tw-grayscale:  ;\n  --tw-hue-rotate:  ;\n  --tw-invert:  ;\n  --tw-saturate:  ;\n  --tw-sepia:  ;\n  --tw-drop-shadow:  ;\n  --tw-backdrop-blur:  ;\n  --tw-backdrop-brightness:  ;\n  --tw-backdrop-contrast:  ;\n  --tw-backdrop-grayscale:  ;\n  --tw-backdrop-hue-rotate:  ;\n  --tw-backdrop-invert:  ;\n  --tw-backdrop-opacity:  ;\n  --tw-backdrop-saturate:  ;\n  --tw-backdrop-sepia:  ;\n  --tw-contain-size:  ;\n  --tw-contain-layout:  ;\n  --tw-contain-paint:  ;\n  --tw-contain-style:  ;\n}\n::backdrop{\n  --tw-border-spacing-x: 0;\n  --tw-border-spacing-y: 0;\n  --tw-translate-x: 0;\n  --tw-translate-y: 0;\n  --tw-rotate: 0;\n  --tw-skew-x: 0;\n  --tw-skew-y: 0;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  --tw-pan-x:  ;\n  --tw-pan-y:  ;\n  --tw-pinch-zoom:  ;\n  --tw-scroll-snap-strictness: proximity;\n  --tw-gradient-from-position:  ;\n  --tw-gradient-via-position:  ;\n  --tw-gradient-to-position:  ;\n  --tw-ordinal:  ;\n  --tw-slashed-zero:  ;\n  --tw-numeric-figure:  ;\n  --tw-numeric-spacing:  ;\n  --tw-numeric-fraction:  ;\n  --tw-ring-inset:  ;\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: rgb(59 130 246 / 0.5);\n  --tw-ring-offset-shadow: 0 0 #0000;\n  --tw-ring-shadow: 0 0 #0000;\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  --tw-blur:  ;\n  --tw-brightness:  ;\n  --tw-contrast:  ;\n  --tw-grayscale:  ;\n  --tw-hue-rotate:  ;\n  --tw-invert:  ;\n  --tw-saturate:  ;\n  --tw-sepia:  ;\n  --tw-drop-shadow:  ;\n  --tw-backdrop-blur:  ;\n  --tw-backdrop-brightness:  ;\n  --tw-backdrop-contrast:  ;\n  --tw-backdrop-grayscale:  ;\n  --tw-backdrop-hue-rotate:  ;\n  --tw-backdrop-invert:  ;\n  --tw-backdrop-opacity:  ;\n  --tw-backdrop-saturate:  ;\n  --tw-backdrop-sepia:  ;\n  --tw-contain-size:  ;\n  --tw-contain-layout:  ;\n  --tw-contain-paint:  ;\n  --tw-contain-style:  ;\n}\n/*\n! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com\n*/\n/*\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\n*/\n*,\n::before,\n::after {\n  box-sizing: border-box; /* 1 */\n  border-width: 0; /* 2 */\n  border-style: solid; /* 2 */\n  border-color: #e5e7eb; /* 2 */\n}\n::before,\n::after {\n  --tw-content: '';\n}\n/*\n1. Use a consistent sensible line-height in all browsers.\n2. Prevent adjustments of font size after orientation changes in iOS.\n3. Use a more readable tab size.\n4. Use the user's configured `sans` font-family by default.\n5. Use the user's configured `sans` font-feature-settings by default.\n6. Use the user's configured `sans` font-variation-settings by default.\n7. Disable tap highlights on iOS\n*/\nhtml,\n:host {\n  line-height: 1.5; /* 1 */\n  -webkit-text-size-adjust: 100%; /* 2 */\n  -moz-tab-size: 4; /* 3 */\n  -o-tab-size: 4;\n     tab-size: 4; /* 3 */\n  font-family: ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\"; /* 4 */\n  font-feature-settings: normal; /* 5 */\n  font-variation-settings: normal; /* 6 */\n  -webkit-tap-highlight-color: transparent; /* 7 */\n}\n/*\n1. Remove the margin in all browsers.\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\n*/\nbody {\n  margin: 0; /* 1 */\n  line-height: inherit; /* 2 */\n}\n/*\n1. Add the correct height in Firefox.\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\n3. Ensure horizontal rules are visible by default.\n*/\nhr {\n  height: 0; /* 1 */\n  color: inherit; /* 2 */\n  border-top-width: 1px; /* 3 */\n}\n/*\nAdd the correct text decoration in Chrome, Edge, and Safari.\n*/\nabbr:where([title]) {\n  -webkit-text-decoration: underline dotted;\n          text-decoration: underline dotted;\n}\n/*\nRemove the default font size and weight for headings.\n*/\nh1,\nh2,\nh3,\nh4,\nh5,\nh6 {\n  font-size: inherit;\n  font-weight: inherit;\n}\n/*\nReset links to optimize for opt-in styling instead of opt-out.\n*/\na {\n  color: inherit;\n  text-decoration: inherit;\n}\n/*\nAdd the correct font weight in Edge and Safari.\n*/\nb,\nstrong {\n  font-weight: bolder;\n}\n/*\n1. Use the user's configured `mono` font-family by default.\n2. Use the user's configured `mono` font-feature-settings by default.\n3. Use the user's configured `mono` font-variation-settings by default.\n4. Correct the odd `em` font sizing in all browsers.\n*/\ncode,\nkbd,\nsamp,\npre {\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace; /* 1 */\n  font-feature-settings: normal; /* 2 */\n  font-variation-settings: normal; /* 3 */\n  font-size: 1em; /* 4 */\n}\n/*\nAdd the correct font size in all browsers.\n*/\nsmall {\n  font-size: 80%;\n}\n/*\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\n*/\nsub,\nsup {\n  font-size: 75%;\n  line-height: 0;\n  position: relative;\n  vertical-align: baseline;\n}\nsub {\n  bottom: -0.25em;\n}\nsup {\n  top: -0.5em;\n}\n/*\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\n3. Remove gaps between table borders by default.\n*/\ntable {\n  text-indent: 0; /* 1 */\n  border-color: inherit; /* 2 */\n  border-collapse: collapse; /* 3 */\n}\n/*\n1. Change the font styles in all browsers.\n2. Remove the margin in Firefox and Safari.\n3. Remove default padding in all browsers.\n*/\nbutton,\ninput,\noptgroup,\nselect,\ntextarea {\n  font-family: inherit; /* 1 */\n  font-feature-settings: inherit; /* 1 */\n  font-variation-settings: inherit; /* 1 */\n  font-size: 100%; /* 1 */\n  font-weight: inherit; /* 1 */\n  line-height: inherit; /* 1 */\n  letter-spacing: inherit; /* 1 */\n  color: inherit; /* 1 */\n  margin: 0; /* 2 */\n  padding: 0; /* 3 */\n}\n/*\nRemove the inheritance of text transform in Edge and Firefox.\n*/\nbutton,\nselect {\n  text-transform: none;\n}\n/*\n1. Correct the inability to style clickable types in iOS and Safari.\n2. Remove default button styles.\n*/\nbutton,\ninput:where([type='button']),\ninput:where([type='reset']),\ninput:where([type='submit']) {\n  -webkit-appearance: button; /* 1 */\n  background-color: transparent; /* 2 */\n  background-image: none; /* 2 */\n}\n/*\nUse the modern Firefox focus style for all focusable elements.\n*/\n:-moz-focusring {\n  outline: auto;\n}\n/*\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\n*/\n:-moz-ui-invalid {\n  box-shadow: none;\n}\n/*\nAdd the correct vertical alignment in Chrome and Firefox.\n*/\nprogress {\n  vertical-align: baseline;\n}\n/*\nCorrect the cursor style of increment and decrement buttons in Safari.\n*/\n::-webkit-inner-spin-button,\n::-webkit-outer-spin-button {\n  height: auto;\n}\n/*\n1. Correct the odd appearance in Chrome and Safari.\n2. Correct the outline style in Safari.\n*/\n[type='search'] {\n  -webkit-appearance: textfield; /* 1 */\n  outline-offset: -2px; /* 2 */\n}\n/*\nRemove the inner padding in Chrome and Safari on macOS.\n*/\n::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n/*\n1. Correct the inability to style clickable types in iOS and Safari.\n2. Change font properties to `inherit` in Safari.\n*/\n::-webkit-file-upload-button {\n  -webkit-appearance: button; /* 1 */\n  font: inherit; /* 2 */\n}\n/*\nAdd the correct display in Chrome and Safari.\n*/\nsummary {\n  display: list-item;\n}\n/*\nRemoves the default spacing and border for appropriate elements.\n*/\nblockquote,\ndl,\ndd,\nh1,\nh2,\nh3,\nh4,\nh5,\nh6,\nhr,\nfigure,\np,\npre {\n  margin: 0;\n}\nfieldset {\n  margin: 0;\n  padding: 0;\n}\nlegend {\n  padding: 0;\n}\nol,\nul,\nmenu {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n}\n/*\nReset default styling for dialogs.\n*/\ndialog {\n  padding: 0;\n}\n/*\nPrevent resizing textareas horizontally by default.\n*/\ntextarea {\n  resize: vertical;\n}\n/*\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\n2. Set the default placeholder color to the user's configured gray 400 color.\n*/\ninput::-moz-placeholder, textarea::-moz-placeholder {\n  opacity: 1; /* 1 */\n  color: #9ca3af; /* 2 */\n}\ninput::placeholder,\ntextarea::placeholder {\n  opacity: 1; /* 1 */\n  color: #9ca3af; /* 2 */\n}\n/*\nSet the default cursor for buttons.\n*/\nbutton,\n[role=\"button\"] {\n  cursor: pointer;\n}\n/*\nMake sure disabled buttons don't get the pointer cursor.\n*/\n:disabled {\n  cursor: default;\n}\n/*\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\n   This can trigger a poorly considered lint error in some tools but is included by design.\n*/\nimg,\nsvg,\nvideo,\ncanvas,\naudio,\niframe,\nembed,\nobject {\n  display: block; /* 1 */\n  vertical-align: middle; /* 2 */\n}\n/*\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\n*/\nimg,\nvideo {\n  max-width: 100%;\n  height: auto;\n}\n/* Make elements with the HTML hidden attribute stay hidden by default */\n[hidden]:where(:not([hidden=\"until-found\"])) {\n  display: none;\n}\n.pointer-events-auto{\n  pointer-events: auto;\n}\n.visible{\n  visibility: visible;\n}\n.fixed{\n  position: fixed;\n}\n.absolute{\n  position: absolute;\n}\n.relative{\n  position: relative;\n}\n.inset-0{\n  inset: 0px;\n}\n.-right-1{\n  right: -0.25rem;\n}\n.-top-1{\n  top: -0.25rem;\n}\n.-top-3{\n  top: -0.75rem;\n}\n.bottom-2{\n  bottom: 0.5rem;\n}\n.bottom-20{\n  bottom: 5rem;\n}\n.bottom-4{\n  bottom: 1rem;\n}\n.bottom-full{\n  bottom: 100%;\n}\n.left-0{\n  left: 0px;\n}\n.left-1\\/2{\n  left: 50%;\n}\n.left-2{\n  left: 0.5rem;\n}\n.left-3{\n  left: 0.75rem;\n}\n.left-4{\n  left: 1rem;\n}\n.right-0{\n  right: 0px;\n}\n.right-2{\n  right: 0.5rem;\n}\n.right-3{\n  right: 0.75rem;\n}\n.right-4{\n  right: 1rem;\n}\n.top-0{\n  top: 0px;\n}\n.top-1{\n  top: 0.25rem;\n}\n.top-1\\/2{\n  top: 50%;\n}\n.top-2{\n  top: 0.5rem;\n}\n.top-20{\n  top: 5rem;\n}\n.top-4{\n  top: 1rem;\n}\n.top-full{\n  top: 100%;\n}\n.z-10{\n  z-index: 10;\n}\n.z-50{\n  z-index: 50;\n}\n.z-\\[100\\]{\n  z-index: 100;\n}\n.mx-auto{\n  margin-left: auto;\n  margin-right: auto;\n}\n.my-6{\n  margin-top: 1.5rem;\n  margin-bottom: 1.5rem;\n}\n.mb-1{\n  margin-bottom: 0.25rem;\n}\n.mb-12{\n  margin-bottom: 3rem;\n}\n.mb-16{\n  margin-bottom: 4rem;\n}\n.mb-2{\n  margin-bottom: 0.5rem;\n}\n.mb-3{\n  margin-bottom: 0.75rem;\n}\n.mb-4{\n  margin-bottom: 1rem;\n}\n.mb-6{\n  margin-bottom: 1.5rem;\n}\n.mb-8{\n  margin-bottom: 2rem;\n}\n.ml-2{\n  margin-left: 0.5rem;\n}\n.ml-4{\n  margin-left: 1rem;\n}\n.mr-3{\n  margin-right: 0.75rem;\n}\n.mr-4{\n  margin-right: 1rem;\n}\n.mt-0\\.5{\n  margin-top: 0.125rem;\n}\n.mt-1{\n  margin-top: 0.25rem;\n}\n.mt-12{\n  margin-top: 3rem;\n}\n.mt-16{\n  margin-top: 4rem;\n}\n.mt-2{\n  margin-top: 0.5rem;\n}\n.mt-20{\n  margin-top: 5rem;\n}\n.mt-4{\n  margin-top: 1rem;\n}\n.mt-8{\n  margin-top: 2rem;\n}\n.line-clamp-2{\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 2;\n}\n.line-clamp-3{\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 3;\n}\n.block{\n  display: block;\n}\n.inline-block{\n  display: inline-block;\n}\n.flex{\n  display: flex;\n}\n.inline-flex{\n  display: inline-flex;\n}\n.table{\n  display: table;\n}\n.grid{\n  display: grid;\n}\n.hidden{\n  display: none;\n}\n.h-1\\/2{\n  height: 50%;\n}\n.h-10{\n  height: 2.5rem;\n}\n.h-11{\n  height: 2.75rem;\n}\n.h-12{\n  height: 3rem;\n}\n.h-16{\n  height: 4rem;\n}\n.h-2{\n  height: 0.5rem;\n}\n.h-20{\n  height: 5rem;\n}\n.h-24{\n  height: 6rem;\n}\n.h-3{\n  height: 0.75rem;\n}\n.h-4{\n  height: 1rem;\n}\n.h-48{\n  height: 12rem;\n}\n.h-5{\n  height: 1.25rem;\n}\n.h-6{\n  height: 1.5rem;\n}\n.h-64{\n  height: 16rem;\n}\n.h-7{\n  height: 1.75rem;\n}\n.h-8{\n  height: 2rem;\n}\n.h-9{\n  height: 2.25rem;\n}\n.h-\\[600px\\]{\n  height: 600px;\n}\n.h-full{\n  height: 100%;\n}\n.max-h-64{\n  max-height: 16rem;\n}\n.max-h-\\[90vh\\]{\n  max-height: 90vh;\n}\n.max-h-screen{\n  max-height: 100vh;\n}\n.min-h-screen{\n  min-height: 100vh;\n}\n.w-0\\.5{\n  width: 0.125rem;\n}\n.w-1\\/3{\n  width: 33.333333%;\n}\n.w-10{\n  width: 2.5rem;\n}\n.w-12{\n  width: 3rem;\n}\n.w-14{\n  width: 3.5rem;\n}\n.w-16{\n  width: 4rem;\n}\n.w-2{\n  width: 0.5rem;\n}\n.w-2\\/3{\n  width: 66.666667%;\n}\n.w-24{\n  width: 6rem;\n}\n.w-3{\n  width: 0.75rem;\n}\n.w-4{\n  width: 1rem;\n}\n.w-5{\n  width: 1.25rem;\n}\n.w-6{\n  width: 1.5rem;\n}\n.w-64{\n  width: 16rem;\n}\n.w-8{\n  width: 2rem;\n}\n.w-full{\n  width: 100%;\n}\n.min-w-0{\n  min-width: 0px;\n}\n.min-w-32{\n  min-width: 8rem;\n}\n.min-w-48{\n  min-width: 12rem;\n}\n.min-w-\\[200px\\]{\n  min-width: 200px;\n}\n.max-w-2xl{\n  max-width: 42rem;\n}\n.max-w-3xl{\n  max-width: 48rem;\n}\n.max-w-4xl{\n  max-width: 56rem;\n}\n.max-w-7xl{\n  max-width: 80rem;\n}\n.max-w-\\[90\\%\\]{\n  max-width: 90%;\n}\n.max-w-md{\n  max-width: 28rem;\n}\n.max-w-sm{\n  max-width: 24rem;\n}\n.flex-1{\n  flex: 1 1 0%;\n}\n.flex-shrink-0{\n  flex-shrink: 0;\n}\n.shrink-0{\n  flex-shrink: 0;\n}\n.-translate-x-1\\/2{\n  --tw-translate-x: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.-translate-y-1\\/2{\n  --tw-translate-y: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.translate-x-1{\n  --tw-translate-x: 0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.translate-x-7{\n  --tw-translate-x: 1.75rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.transform{\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n@keyframes spin{\n  to{\n    transform: rotate(360deg);\n  }\n}\n.animate-spin{\n  animation: spin 1s linear infinite;\n}\n.cursor-not-allowed{\n  cursor: not-allowed;\n}\n.cursor-pointer{\n  cursor: pointer;\n}\n.resize-none{\n  resize: none;\n}\n.list-inside{\n  list-style-position: inside;\n}\n.list-disc{\n  list-style-type: disc;\n}\n.grid-cols-1{\n  grid-template-columns: repeat(1, minmax(0, 1fr));\n}\n.grid-cols-2{\n  grid-template-columns: repeat(2, minmax(0, 1fr));\n}\n.grid-cols-3{\n  grid-template-columns: repeat(3, minmax(0, 1fr));\n}\n.grid-rows-1{\n  grid-template-rows: repeat(1, minmax(0, 1fr));\n}\n.grid-rows-2{\n  grid-template-rows: repeat(2, minmax(0, 1fr));\n}\n.grid-rows-3{\n  grid-template-rows: repeat(3, minmax(0, 1fr));\n}\n.flex-col{\n  flex-direction: column;\n}\n.flex-col-reverse{\n  flex-direction: column-reverse;\n}\n.flex-wrap{\n  flex-wrap: wrap;\n}\n.place-items-center{\n  place-items: center;\n}\n.items-start{\n  align-items: flex-start;\n}\n.items-end{\n  align-items: flex-end;\n}\n.items-center{\n  align-items: center;\n}\n.justify-end{\n  justify-content: flex-end;\n}\n.justify-center{\n  justify-content: center;\n}\n.justify-between{\n  justify-content: space-between;\n}\n.gap-1{\n  gap: 0.25rem;\n}\n.gap-12{\n  gap: 3rem;\n}\n.gap-2{\n  gap: 0.5rem;\n}\n.gap-3{\n  gap: 0.75rem;\n}\n.gap-4{\n  gap: 1rem;\n}\n.gap-6{\n  gap: 1.5rem;\n}\n.gap-8{\n  gap: 2rem;\n}\n.space-x-0\\.5 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.125rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.125rem * calc(1 - var(--tw-space-x-reverse)));\n}\n.space-x-1 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.25rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));\n}\n.space-x-2 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\n}\n.space-x-3 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.75rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));\n}\n.space-x-4 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-x-reverse: 0;\n  margin-right: calc(1rem * var(--tw-space-x-reverse));\n  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\n}\n.space-x-6 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-x-reverse: 0;\n  margin-right: calc(1.5rem * var(--tw-space-x-reverse));\n  margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));\n}\n.space-x-8 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-x-reverse: 0;\n  margin-right: calc(2rem * var(--tw-space-x-reverse));\n  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));\n}\n.space-y-1\\.5 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));\n}\n.space-y-12 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-y-reverse: 0;\n  margin-top: calc(3rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(3rem * var(--tw-space-y-reverse));\n}\n.space-y-2 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));\n}\n.space-y-3 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));\n}\n.space-y-4 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-y-reverse: 0;\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\n}\n.space-y-6 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-y-reverse: 0;\n  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));\n}\n.space-y-8 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-y-reverse: 0;\n  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(2rem * var(--tw-space-y-reverse));\n}\n.overflow-hidden{\n  overflow: hidden;\n}\n.overflow-x-auto{\n  overflow-x: auto;\n}\n.overflow-y-auto{\n  overflow-y: auto;\n}\n.overflow-x-hidden{\n  overflow-x: hidden;\n}\n.truncate{\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n.whitespace-nowrap{\n  white-space: nowrap;\n}\n.rounded{\n  border-radius: 0.25rem;\n}\n.rounded-full{\n  border-radius: 9999px;\n}\n.rounded-lg{\n  border-radius: var(--radius);\n}\n.rounded-md{\n  border-radius: calc(var(--radius) - 2px);\n}\n.rounded-l-none{\n  border-top-left-radius: 0px;\n  border-bottom-left-radius: 0px;\n}\n.rounded-r-none{\n  border-top-right-radius: 0px;\n  border-bottom-right-radius: 0px;\n}\n.rounded-bl{\n  border-bottom-left-radius: 0.25rem;\n}\n.border{\n  border-width: 1px;\n}\n.border-2{\n  border-width: 2px;\n}\n.border-4{\n  border-width: 4px;\n}\n.border-b{\n  border-bottom-width: 1px;\n}\n.border-l{\n  border-left-width: 1px;\n}\n.border-r{\n  border-right-width: 1px;\n}\n.border-t{\n  border-top-width: 1px;\n}\n.border-destructive{\n  border-color: hsl(var(--destructive));\n}\n.border-input{\n  border-color: hsl(var(--input));\n}\n.border-orange-500\\/30{\n  border-color: rgb(249 115 22 / 0.3);\n}\n.border-purple-500{\n  --tw-border-opacity: 1;\n  border-color: rgb(168 85 247 / var(--tw-border-opacity, 1));\n}\n.border-red-500\\/50{\n  border-color: rgb(239 68 68 / 0.5);\n}\n.border-white\\/10{\n  border-color: rgb(255 255 255 / 0.1);\n}\n.border-white\\/20{\n  border-color: rgb(255 255 255 / 0.2);\n}\n.border-white\\/30{\n  border-color: rgb(255 255 255 / 0.3);\n}\n.border-white\\/5{\n  border-color: rgb(255 255 255 / 0.05);\n}\n.border-yellow-500\\/30{\n  border-color: rgb(234 179 8 / 0.3);\n}\n.border-t-white{\n  --tw-border-opacity: 1;\n  border-top-color: rgb(255 255 255 / var(--tw-border-opacity, 1));\n}\n.bg-background{\n  background-color: hsl(var(--background));\n}\n.bg-black\\/50{\n  background-color: rgb(0 0 0 / 0.5);\n}\n.bg-black\\/70{\n  background-color: rgb(0 0 0 / 0.7);\n}\n.bg-blue-500\\/80{\n  background-color: rgb(59 130 246 / 0.8);\n}\n.bg-card{\n  background-color: hsl(var(--card));\n}\n.bg-destructive{\n  background-color: hsl(var(--destructive));\n}\n.bg-gray-600{\n  --tw-bg-opacity: 1;\n  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));\n}\n.bg-green-400{\n  --tw-bg-opacity: 1;\n  background-color: rgb(74 222 128 / var(--tw-bg-opacity, 1));\n}\n.bg-green-500{\n  --tw-bg-opacity: 1;\n  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));\n}\n.bg-green-500\\/80{\n  background-color: rgb(34 197 94 / 0.8);\n}\n.bg-orange-500{\n  --tw-bg-opacity: 1;\n  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));\n}\n.bg-orange-500\\/20{\n  background-color: rgb(249 115 22 / 0.2);\n}\n.bg-primary{\n  background-color: hsl(var(--primary));\n}\n.bg-purple-500{\n  --tw-bg-opacity: 1;\n  background-color: rgb(168 85 247 / var(--tw-bg-opacity, 1));\n}\n.bg-purple-500\\/20{\n  background-color: rgb(168 85 247 / 0.2);\n}\n.bg-purple-500\\/30{\n  background-color: rgb(168 85 247 / 0.3);\n}\n.bg-red-500{\n  --tw-bg-opacity: 1;\n  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));\n}\n.bg-red-500\\/20{\n  background-color: rgb(239 68 68 / 0.2);\n}\n.bg-red-500\\/80{\n  background-color: rgb(239 68 68 / 0.8);\n}\n.bg-red-600{\n  --tw-bg-opacity: 1;\n  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));\n}\n.bg-secondary{\n  background-color: hsl(var(--secondary));\n}\n.bg-transparent{\n  background-color: transparent;\n}\n.bg-white{\n  --tw-bg-opacity: 1;\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\n}\n.bg-white\\/10{\n  background-color: rgb(255 255 255 / 0.1);\n}\n.bg-white\\/20{\n  background-color: rgb(255 255 255 / 0.2);\n}\n.bg-white\\/5{\n  background-color: rgb(255 255 255 / 0.05);\n}\n.bg-yellow-400{\n  --tw-bg-opacity: 1;\n  background-color: rgb(250 204 21 / var(--tw-bg-opacity, 1));\n}\n.bg-yellow-500\\/20{\n  background-color: rgb(234 179 8 / 0.2);\n}\n.bg-gradient-to-br{\n  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));\n}\n.bg-gradient-to-r{\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\n}\n.from-blue-500{\n  --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-gray-600{\n  --tw-gradient-from: #4b5563 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(75 85 99 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-gray-800{\n  --tw-gradient-from: #1f2937 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(31 41 55 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-green-500{\n  --tw-gradient-from: #22c55e var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(34 197 94 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-purple-500{\n  --tw-gradient-from: #a855f7 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-purple-500\\/20{\n  --tw-gradient-from: rgb(168 85 247 / 0.2) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-purple-600{\n  --tw-gradient-from: #9333ea var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(147 51 234 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-yellow-500{\n  --tw-gradient-from: #eab308 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(234 179 8 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.to-cyan-500{\n  --tw-gradient-to: #06b6d4 var(--tw-gradient-to-position);\n}\n.to-emerald-500{\n  --tw-gradient-to: #10b981 var(--tw-gradient-to-position);\n}\n.to-gray-800{\n  --tw-gradient-to: #1f2937 var(--tw-gradient-to-position);\n}\n.to-gray-900{\n  --tw-gradient-to: #111827 var(--tw-gradient-to-position);\n}\n.to-orange-500{\n  --tw-gradient-to: #f97316 var(--tw-gradient-to-position);\n}\n.to-pink-500{\n  --tw-gradient-to: #ec4899 var(--tw-gradient-to-position);\n}\n.to-pink-500\\/20{\n  --tw-gradient-to: rgb(236 72 153 / 0.2) var(--tw-gradient-to-position);\n}\n.to-pink-600{\n  --tw-gradient-to: #db2777 var(--tw-gradient-to-position);\n}\n.to-purple-500{\n  --tw-gradient-to: #a855f7 var(--tw-gradient-to-position);\n}\n.fill-current{\n  fill: currentColor;\n}\n.object-cover{\n  -o-object-fit: cover;\n     object-fit: cover;\n}\n.p-1{\n  padding: 0.25rem;\n}\n.p-12{\n  padding: 3rem;\n}\n.p-2{\n  padding: 0.5rem;\n}\n.p-3{\n  padding: 0.75rem;\n}\n.p-4{\n  padding: 1rem;\n}\n.p-6{\n  padding: 1.5rem;\n}\n.p-8{\n  padding: 2rem;\n}\n.px-1{\n  padding-left: 0.25rem;\n  padding-right: 0.25rem;\n}\n.px-2{\n  padding-left: 0.5rem;\n  padding-right: 0.5rem;\n}\n.px-3{\n  padding-left: 0.75rem;\n  padding-right: 0.75rem;\n}\n.px-4{\n  padding-left: 1rem;\n  padding-right: 1rem;\n}\n.px-6{\n  padding-left: 1.5rem;\n  padding-right: 1.5rem;\n}\n.px-8{\n  padding-left: 2rem;\n  padding-right: 2rem;\n}\n.py-1{\n  padding-top: 0.25rem;\n  padding-bottom: 0.25rem;\n}\n.py-10{\n  padding-top: 2.5rem;\n  padding-bottom: 2.5rem;\n}\n.py-12{\n  padding-top: 3rem;\n  padding-bottom: 3rem;\n}\n.py-2{\n  padding-top: 0.5rem;\n  padding-bottom: 0.5rem;\n}\n.py-20{\n  padding-top: 5rem;\n  padding-bottom: 5rem;\n}\n.py-3{\n  padding-top: 0.75rem;\n  padding-bottom: 0.75rem;\n}\n.py-4{\n  padding-top: 1rem;\n  padding-bottom: 1rem;\n}\n.py-8{\n  padding-top: 2rem;\n  padding-bottom: 2rem;\n}\n.pb-20{\n  padding-bottom: 5rem;\n}\n.pb-6{\n  padding-bottom: 1.5rem;\n}\n.pl-10{\n  padding-left: 2.5rem;\n}\n.pl-12{\n  padding-left: 3rem;\n}\n.pr-10{\n  padding-right: 2.5rem;\n}\n.pr-4{\n  padding-right: 1rem;\n}\n.pr-8{\n  padding-right: 2rem;\n}\n.pt-0{\n  padding-top: 0px;\n}\n.pt-20{\n  padding-top: 5rem;\n}\n.pt-8{\n  padding-top: 2rem;\n}\n.text-left{\n  text-align: left;\n}\n.text-center{\n  text-align: center;\n}\n.font-mono{\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace;\n}\n.text-2xl{\n  font-size: 1.5rem;\n  line-height: 2rem;\n}\n.text-3xl{\n  font-size: 1.875rem;\n  line-height: 2.25rem;\n}\n.text-4xl{\n  font-size: 2.25rem;\n  line-height: 2.5rem;\n}\n.text-5xl{\n  font-size: 3rem;\n  line-height: 1;\n}\n.text-lg{\n  font-size: 1.125rem;\n  line-height: 1.75rem;\n}\n.text-sm{\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n}\n.text-xl{\n  font-size: 1.25rem;\n  line-height: 1.75rem;\n}\n.text-xs{\n  font-size: 0.75rem;\n  line-height: 1rem;\n}\n.font-bold{\n  font-weight: 700;\n}\n.font-medium{\n  font-weight: 500;\n}\n.font-semibold{\n  font-weight: 600;\n}\n.leading-none{\n  line-height: 1;\n}\n.leading-relaxed{\n  line-height: 1.625;\n}\n.tracking-tight{\n  letter-spacing: -0.025em;\n}\n.text-black{\n  --tw-text-opacity: 1;\n  color: rgb(0 0 0 / var(--tw-text-opacity, 1));\n}\n.text-blue-400{\n  --tw-text-opacity: 1;\n  color: rgb(96 165 250 / var(--tw-text-opacity, 1));\n}\n.text-card-foreground{\n  color: hsl(var(--card-foreground));\n}\n.text-destructive-foreground{\n  color: hsl(var(--destructive-foreground));\n}\n.text-foreground{\n  color: hsl(var(--foreground));\n}\n.text-foreground\\/50{\n  color: hsl(var(--foreground) / 0.5);\n}\n.text-green-400{\n  --tw-text-opacity: 1;\n  color: rgb(74 222 128 / var(--tw-text-opacity, 1));\n}\n.text-muted-foreground{\n  color: hsl(var(--muted-foreground));\n}\n.text-orange-200{\n  --tw-text-opacity: 1;\n  color: rgb(254 215 170 / var(--tw-text-opacity, 1));\n}\n.text-orange-400{\n  --tw-text-opacity: 1;\n  color: rgb(251 146 60 / var(--tw-text-opacity, 1));\n}\n.text-primary{\n  color: hsl(var(--primary));\n}\n.text-primary-foreground{\n  color: hsl(var(--primary-foreground));\n}\n.text-purple-300{\n  --tw-text-opacity: 1;\n  color: rgb(216 180 254 / var(--tw-text-opacity, 1));\n}\n.text-purple-400{\n  --tw-text-opacity: 1;\n  color: rgb(192 132 252 / var(--tw-text-opacity, 1));\n}\n.text-purple-600{\n  --tw-text-opacity: 1;\n  color: rgb(147 51 234 / var(--tw-text-opacity, 1));\n}\n.text-red-400{\n  --tw-text-opacity: 1;\n  color: rgb(248 113 113 / var(--tw-text-opacity, 1));\n}\n.text-secondary-foreground{\n  color: hsl(var(--secondary-foreground));\n}\n.text-white{\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\n.text-white\\/40{\n  color: rgb(255 255 255 / 0.4);\n}\n.text-white\\/50{\n  color: rgb(255 255 255 / 0.5);\n}\n.text-white\\/60{\n  color: rgb(255 255 255 / 0.6);\n}\n.text-white\\/70{\n  color: rgb(255 255 255 / 0.7);\n}\n.text-white\\/80{\n  color: rgb(255 255 255 / 0.8);\n}\n.text-yellow-200{\n  --tw-text-opacity: 1;\n  color: rgb(254 240 138 / var(--tw-text-opacity, 1));\n}\n.text-yellow-400{\n  --tw-text-opacity: 1;\n  color: rgb(250 204 21 / var(--tw-text-opacity, 1));\n}\n.underline-offset-4{\n  text-underline-offset: 4px;\n}\n.opacity-0{\n  opacity: 0;\n}\n.opacity-50{\n  opacity: 0.5;\n}\n.opacity-70{\n  opacity: 0.7;\n}\n.opacity-90{\n  opacity: 0.9;\n}\n.shadow-lg{\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-sm{\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.outline{\n  outline-style: solid;\n}\n.ring{\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n.ring-offset-background{\n  --tw-ring-offset-color: hsl(var(--background));\n}\n.blur{\n  --tw-blur: blur(8px);\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.filter{\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.backdrop-blur-md{\n  --tw-backdrop-blur: blur(12px);\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\n.backdrop-blur-sm{\n  --tw-backdrop-blur: blur(4px);\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\n.transition-all{\n  transition-property: all;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-colors{\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-opacity{\n  transition-property: opacity;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-transform{\n  transition-property: transform;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.duration-300{\n  transition-duration: 300ms;\n}\n@keyframes enter{\n  from{\n    opacity: var(--tw-enter-opacity, 1);\n    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));\n  }\n}\n@keyframes exit{\n  to{\n    opacity: var(--tw-exit-opacity, 1);\n    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));\n  }\n}\n.fade-in{\n  --tw-enter-opacity: 0;\n}\n.duration-300{\n  animation-duration: 300ms;\n}\n\n/* StreamIt Pro Original Design Styles */\n* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\nbody {\n  font-family: 'Inter', sans-serif;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  min-height: 100vh;\n  overflow-x: hidden;\n}\n\n/* Animated Background */\n.animated-bg {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  z-index: -2;\n}\n\n.animated-bg::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background:\n    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),\n    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),\n    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);\n  animation: float 20s ease-in-out infinite;\n}\n\n@keyframes float {\n  0%, 100% { transform: translateY(0px) rotate(0deg); }\n  33% { transform: translateY(-30px) rotate(1deg); }\n  66% { transform: translateY(-20px) rotate(-1deg); }\n}\n\n/* Glass Morphism Effects */\n.glass {\n  background: rgba(255, 255, 255, 0.1);\n  -webkit-backdrop-filter: blur(20px);\n          backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 20px;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n}\n\n.glass-dark {\n  background: rgba(0, 0, 0, 0.2);\n  -webkit-backdrop-filter: blur(20px);\n          backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  border-radius: 20px;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\n}\n\n.glass-button {\n  background: rgba(255, 255, 255, 0.15);\n  -webkit-backdrop-filter: blur(10px);\n          backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 15px;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);\n}\n\n.glass-button:hover {\n  background: rgba(255, 255, 255, 0.25);\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\n}\n\n.glass-button:active {\n  transform: translateY(0px);\n}\n\n/* Primary Button Styles */\n.btn-primary {\n  background: linear-gradient(135deg, #a855f7 0%, #9333ea 100%);\n  color: white;\n  border: none;\n  padding: 12px 24px;\n  border-radius: 15px;\n  font-weight: 600;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 16px rgba(168, 85, 247, 0.3);\n}\n\n.btn-primary:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(168, 85, 247, 0.4);\n  background: linear-gradient(135deg, #9333ea 0%, #7c3aed 100%);\n}\n\n.btn-secondary {\n  background: rgba(255, 255, 255, 0.1);\n  color: white;\n  border: 1px solid rgba(255, 255, 255, 0.3);\n  padding: 12px 24px;\n  border-radius: 15px;\n  font-weight: 500;\n  transition: all 0.3s ease;\n  -webkit-backdrop-filter: blur(10px);\n          backdrop-filter: blur(10px);\n}\n\n.btn-secondary:hover {\n  background: rgba(255, 255, 255, 0.2);\n  transform: translateY(-2px);\n}\n\n/* Input Styles */\n.glass-input {\n  background: rgba(255, 255, 255, 0.1);\n  -webkit-backdrop-filter: blur(10px);\n          backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 15px;\n  padding: 12px 16px;\n  color: white;\n  font-size: 16px;\n  transition: all 0.3s ease;\n}\n\n.glass-input::-moz-placeholder {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.glass-input::placeholder {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.glass-input:focus {\n  outline: none;\n  border-color: rgba(168, 85, 247, 0.5);\n  box-shadow: 0 0 20px rgba(168, 85, 247, 0.2);\n  background: rgba(255, 255, 255, 0.15);\n}\n\n/* Video Call Styles */\n.video-container {\n  background: rgba(0, 0, 0, 0.3);\n  -webkit-backdrop-filter: blur(20px);\n          backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  border-radius: 15px;\n  overflow: hidden;\n  position: relative;\n  aspect-ratio: 16/9;\n}\n\n.video-tile {\n  background: #1a1a1a;\n  border-radius: 15px;\n  overflow: hidden;\n  position: relative;\n  aspect-ratio: 16/9;\n}\n\n.video-tile video {\n  width: 100%;\n  height: 100%;\n  -o-object-fit: cover;\n     object-fit: cover;\n}\n\n.video-controls {\n  background: rgba(0, 0, 0, 0.5);\n  -webkit-backdrop-filter: blur(20px);\n          backdrop-filter: blur(20px);\n  border-radius: 20px;\n  padding: 12px;\n  display: flex;\n  justify-content: center;\n  gap: 8px;\n  margin: 0 auto;\n  width: -moz-fit-content;\n  width: fit-content;\n}\n\n.control-btn {\n  width: 48px;\n  height: 48px;\n  border-radius: 50%;\n  border: none;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.3s ease;\n  cursor: pointer;\n  position: relative;\n}\n\n.control-btn.active {\n  background: #ef4444;\n  color: white;\n  box-shadow: 0 4px 16px rgba(239, 68, 68, 0.4);\n}\n\n.control-btn.inactive {\n  background: rgba(255, 255, 255, 0.1);\n  color: white;\n  -webkit-backdrop-filter: blur(10px);\n          backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.control-btn:hover {\n  transform: scale(1.05);\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\n}\n\n.control-btn:active {\n  transform: scale(0.95);\n}\n\n/* Chat Styles */\n.chat-container {\n  background: rgba(0, 0, 0, 0.3);\n  -webkit-backdrop-filter: blur(20px);\n          backdrop-filter: blur(20px);\n  border-radius: 20px;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.chat-message {\n  background: rgba(255, 255, 255, 0.1);\n  -webkit-backdrop-filter: blur(10px);\n          backdrop-filter: blur(10px);\n  border-radius: 15px;\n  padding: 12px;\n  margin: 8px 0;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n/* Animations */\n@keyframes fadeIn {\n  from { opacity: 0; transform: translateY(20px); }\n  to { opacity: 1; transform: translateY(0); }\n}\n\n@keyframes slideUp {\n  from { opacity: 0; transform: translateY(30px); }\n  to { opacity: 1; transform: translateY(0); }\n}\n\n@keyframes bounceIn {\n  0% { opacity: 0; transform: scale(0.3); }\n  50% { opacity: 1; transform: scale(1.05); }\n  70% { transform: scale(0.9); }\n  100% { opacity: 1; transform: scale(1); }\n}\n\n.fade-in { animation: fadeIn 0.6s ease-out; }\n.slide-up { animation: slideUp 0.8s ease-out; }\n.bounce-in { animation: bounceIn 0.6s ease-out; }\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .glass {\n    margin: 10px;\n    border-radius: 15px;\n  }\n\n  .glass-button {\n    padding: 10px 20px;\n    font-size: 14px;\n  }\n\n  .control-btn {\n    width: 45px;\n    height: 45px;\n  }\n}\n\n/* Custom video call styles */\n.video-grid {\n  display: grid;\n  gap: 1rem;\n  padding: 1rem;\n}\n\n.video-grid-1 {\n  grid-template-columns: 1fr;\n}\n\n.video-grid-2 {\n  grid-template-columns: 1fr 1fr;\n}\n\n.video-grid-3 {\n  grid-template-columns: 1fr 1fr;\n  grid-template-rows: 1fr 1fr;\n}\n\n.video-grid-4 {\n  grid-template-columns: 1fr 1fr;\n  grid-template-rows: 1fr 1fr;\n}\n\n.video-grid-5, .video-grid-6, .video-grid-7, .video-grid-8, .video-grid-9 {\n  grid-template-columns: repeat(3, 1fr);\n  grid-auto-rows: 1fr;\n}\n\n.video-container{\n  position: relative;\n  aspect-ratio: 16 / 9;\n  overflow: hidden;\n  border-radius: var(--radius);\n  --tw-bg-opacity: 1;\n  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));\n}\n\n.video-container video{\n  height: 100%;\n  width: 100%;\n  -o-object-fit: cover;\n     object-fit: cover;\n}\n\n.video-controls{\n  position: absolute;\n  bottom: 1rem;\n  left: 50%;\n  display: flex;\n  --tw-translate-x: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n  gap: 0.5rem;\n}\n\n.control-button{\n  border-radius: 9999px;\n  padding: 0.75rem;\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 200ms;\n  animation-duration: 200ms;\n}\n\n.control-button.active{\n  --tw-bg-opacity: 1;\n  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));\n}\n\n.control-button.active:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));\n}\n\n.control-button.inactive{\n  --tw-bg-opacity: 1;\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\n}\n\n.control-button.inactive:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));\n}\n\n/* Smooth scrolling */\nhtml {\n  scroll-behavior: smooth;\n}\n\n/* Section spacing */\nsection {\n  scroll-margin-top: 80px;\n}\n\n/* Enhanced mobile responsiveness */\n@media (max-width: 640px) {\n  .glass {\n    margin: 8px;\n    padding: 1.5rem;\n    border-radius: 15px;\n  }\n\n  .btn-primary, .btn-secondary {\n    padding: 10px 20px;\n    font-size: 14px;\n  }\n\n  h1 {\n    font-size: 2.5rem !important;\n  }\n\n  h2 {\n    font-size: 2rem !important;\n  }\n\n  .text-xl {\n    font-size: 1.125rem !important;\n  }\n}\n\n/* Loading states */\n.loading {\n  opacity: 0.7;\n  pointer-events: none;\n}\n\n/* Hover effects for cards */\n.glass:hover {\n  transform: translateY(-2px);\n  transition: all 0.3s ease;\n  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);\n}\n\n/* Gradient text */\n.gradient-text {\n  background: linear-gradient(135deg, #a855f7 0%, #9333ea 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n\n/* Glass input styles */\n.glass-input{\n  border-radius: var(--radius);\n  border-width: 1px;\n  border-color: rgb(255 255 255 / 0.2);\n  background-color: rgb(255 255 255 / 0.1);\n  padding-left: 1rem;\n  padding-right: 1rem;\n  padding-top: 0.75rem;\n  padding-bottom: 0.75rem;\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\n.glass-input::-moz-placeholder{\n  color: rgb(255 255 255 / 0.6);\n}\n.glass-input::placeholder{\n  color: rgb(255 255 255 / 0.6);\n}\n.glass-input:focus{\n  border-color: transparent;\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(168 85 247 / var(--tw-ring-opacity, 1));\n}\n.glass-input{\n  --tw-backdrop-blur: blur(4px);\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  transition-property: all;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 200ms;\n  animation-duration: 200ms;\n}\n\n.glass-input:focus{\n  border-color: rgb(168 85 247 / 0.5);\n  background-color: rgb(255 255 255 / 0.15);\n}\n\n/* Glass dark variant */\n.glass-dark{\n  border-radius: var(--radius);\n  border-width: 1px;\n  border-color: rgb(255 255 255 / 0.1);\n  background-color: rgb(0 0 0 / 0.2);\n  --tw-backdrop-blur: blur(12px);\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\n.file\\:border-0::file-selector-button{\n  border-width: 0px;\n}\n.file\\:bg-transparent::file-selector-button{\n  background-color: transparent;\n}\n.file\\:text-sm::file-selector-button{\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n}\n.file\\:font-medium::file-selector-button{\n  font-weight: 500;\n}\n.placeholder\\:text-muted-foreground::-moz-placeholder{\n  color: hsl(var(--muted-foreground));\n}\n.placeholder\\:text-muted-foreground::placeholder{\n  color: hsl(var(--muted-foreground));\n}\n.hover\\:scale-105:hover{\n  --tw-scale-x: 1.05;\n  --tw-scale-y: 1.05;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.hover\\:scale-\\[1\\.02\\]:hover{\n  --tw-scale-x: 1.02;\n  --tw-scale-y: 1.02;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.hover\\:transform:hover{\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.hover\\:border-white\\/40:hover{\n  border-color: rgb(255 255 255 / 0.4);\n}\n.hover\\:bg-accent:hover{\n  background-color: hsl(var(--accent));\n}\n.hover\\:bg-destructive\\/90:hover{\n  background-color: hsl(var(--destructive) / 0.9);\n}\n.hover\\:bg-gray-200:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-gray-500:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-primary\\/90:hover{\n  background-color: hsl(var(--primary) / 0.9);\n}\n.hover\\:bg-red-500:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-red-600:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-secondary:hover{\n  background-color: hsl(var(--secondary));\n}\n.hover\\:bg-secondary\\/80:hover{\n  background-color: hsl(var(--secondary) / 0.8);\n}\n.hover\\:bg-white\\/10:hover{\n  background-color: rgb(255 255 255 / 0.1);\n}\n.hover\\:bg-white\\/20:hover{\n  background-color: rgb(255 255 255 / 0.2);\n}\n.hover\\:bg-white\\/5:hover{\n  background-color: rgb(255 255 255 / 0.05);\n}\n.hover\\:from-purple-600:hover{\n  --tw-gradient-from: #9333ea var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(147 51 234 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.hover\\:to-pink-600:hover{\n  --tw-gradient-to: #db2777 var(--tw-gradient-to-position);\n}\n.hover\\:text-accent-foreground:hover{\n  color: hsl(var(--accent-foreground));\n}\n.hover\\:text-foreground:hover{\n  color: hsl(var(--foreground));\n}\n.hover\\:text-purple-300:hover{\n  --tw-text-opacity: 1;\n  color: rgb(216 180 254 / var(--tw-text-opacity, 1));\n}\n.hover\\:text-red-300:hover{\n  --tw-text-opacity: 1;\n  color: rgb(252 165 165 / var(--tw-text-opacity, 1));\n}\n.hover\\:text-white:hover{\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\n.hover\\:underline:hover{\n  text-decoration-line: underline;\n}\n.hover\\:opacity-90:hover{\n  opacity: 0.9;\n}\n.focus\\:opacity-100:focus{\n  opacity: 1;\n}\n.focus\\:outline-none:focus{\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\n.focus\\:ring-2:focus{\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n.focus\\:ring-purple-500:focus{\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(168 85 247 / var(--tw-ring-opacity, 1));\n}\n.focus\\:ring-ring:focus{\n  --tw-ring-color: hsl(var(--ring));\n}\n.focus\\:ring-offset-2:focus{\n  --tw-ring-offset-width: 2px;\n}\n.focus-visible\\:outline-none:focus-visible{\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\n.focus-visible\\:ring-2:focus-visible{\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n.focus-visible\\:ring-ring:focus-visible{\n  --tw-ring-color: hsl(var(--ring));\n}\n.focus-visible\\:ring-offset-2:focus-visible{\n  --tw-ring-offset-width: 2px;\n}\n.disabled\\:pointer-events-none:disabled{\n  pointer-events: none;\n}\n.disabled\\:cursor-not-allowed:disabled{\n  cursor: not-allowed;\n}\n.disabled\\:opacity-50:disabled{\n  opacity: 0.5;\n}\n.group:hover .group-hover\\:opacity-100{\n  opacity: 1;\n}\n.group.destructive .group-\\[\\.destructive\\]\\:border-muted\\/40{\n  border-color: hsl(var(--muted) / 0.4);\n}\n.group.destructive .group-\\[\\.destructive\\]\\:text-red-300{\n  --tw-text-opacity: 1;\n  color: rgb(252 165 165 / var(--tw-text-opacity, 1));\n}\n.group.destructive .group-\\[\\.destructive\\]\\:hover\\:border-destructive\\/30:hover{\n  border-color: hsl(var(--destructive) / 0.3);\n}\n.group.destructive .group-\\[\\.destructive\\]\\:hover\\:bg-destructive:hover{\n  background-color: hsl(var(--destructive));\n}\n.group.destructive .group-\\[\\.destructive\\]\\:hover\\:text-destructive-foreground:hover{\n  color: hsl(var(--destructive-foreground));\n}\n.group.destructive .group-\\[\\.destructive\\]\\:hover\\:text-red-50:hover{\n  --tw-text-opacity: 1;\n  color: rgb(254 242 242 / var(--tw-text-opacity, 1));\n}\n.group.destructive .group-\\[\\.destructive\\]\\:focus\\:ring-destructive:focus{\n  --tw-ring-color: hsl(var(--destructive));\n}\n.group.destructive .group-\\[\\.destructive\\]\\:focus\\:ring-red-400:focus{\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(248 113 113 / var(--tw-ring-opacity, 1));\n}\n.group.destructive .group-\\[\\.destructive\\]\\:focus\\:ring-offset-red-600:focus{\n  --tw-ring-offset-color: #dc2626;\n}\n.data-\\[swipe\\=cancel\\]\\:translate-x-0[data-swipe=\"cancel\"]{\n  --tw-translate-x: 0px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.data-\\[swipe\\=end\\]\\:translate-x-\\[var\\(--radix-toast-swipe-end-x\\)\\][data-swipe=\"end\"]{\n  --tw-translate-x: var(--radix-toast-swipe-end-x);\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.data-\\[swipe\\=move\\]\\:translate-x-\\[var\\(--radix-toast-swipe-move-x\\)\\][data-swipe=\"move\"]{\n  --tw-translate-x: var(--radix-toast-swipe-move-x);\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.data-\\[swipe\\=move\\]\\:transition-none[data-swipe=\"move\"]{\n  transition-property: none;\n}\n.data-\\[state\\=open\\]\\:animate-in[data-state=\"open\"]{\n  animation-name: enter;\n  animation-duration: 150ms;\n  --tw-enter-opacity: initial;\n  --tw-enter-scale: initial;\n  --tw-enter-rotate: initial;\n  --tw-enter-translate-x: initial;\n  --tw-enter-translate-y: initial;\n}\n.data-\\[state\\=closed\\]\\:animate-out[data-state=\"closed\"]{\n  animation-name: exit;\n  animation-duration: 150ms;\n  --tw-exit-opacity: initial;\n  --tw-exit-scale: initial;\n  --tw-exit-rotate: initial;\n  --tw-exit-translate-x: initial;\n  --tw-exit-translate-y: initial;\n}\n.data-\\[swipe\\=end\\]\\:animate-out[data-swipe=\"end\"]{\n  animation-name: exit;\n  animation-duration: 150ms;\n  --tw-exit-opacity: initial;\n  --tw-exit-scale: initial;\n  --tw-exit-rotate: initial;\n  --tw-exit-translate-x: initial;\n  --tw-exit-translate-y: initial;\n}\n.data-\\[state\\=closed\\]\\:fade-out-80[data-state=\"closed\"]{\n  --tw-exit-opacity: 0.8;\n}\n.data-\\[state\\=closed\\]\\:slide-out-to-right-full[data-state=\"closed\"]{\n  --tw-exit-translate-x: 100%;\n}\n.data-\\[state\\=open\\]\\:slide-in-from-top-full[data-state=\"open\"]{\n  --tw-enter-translate-y: -100%;\n}\n@media (min-width: 640px){\n  .sm\\:bottom-0{\n    bottom: 0px;\n  }\n  .sm\\:right-0{\n    right: 0px;\n  }\n  .sm\\:top-auto{\n    top: auto;\n  }\n  .sm\\:flex-row{\n    flex-direction: row;\n  }\n  .sm\\:flex-col{\n    flex-direction: column;\n  }\n  .sm\\:px-6{\n    padding-left: 1.5rem;\n    padding-right: 1.5rem;\n  }\n  .data-\\[state\\=open\\]\\:sm\\:slide-in-from-bottom-full[data-state=\"open\"]{\n    --tw-enter-translate-y: 100%;\n  }\n}\n@media (min-width: 768px){\n  .md\\:mt-0{\n    margin-top: 0px;\n  }\n  .md\\:flex{\n    display: flex;\n  }\n  .md\\:h-80{\n    height: 20rem;\n  }\n  .md\\:max-w-\\[420px\\]{\n    max-width: 420px;\n  }\n  .md\\:grid-cols-2{\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n  .md\\:grid-cols-3{\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n  .md\\:grid-cols-4{\n    grid-template-columns: repeat(4, minmax(0, 1fr));\n  }\n  .md\\:flex-row{\n    flex-direction: row;\n  }\n  .md\\:p-12{\n    padding: 3rem;\n  }\n}\n@media (min-width: 1024px){\n  .lg\\:col-span-2{\n    grid-column: span 2 / span 2;\n  }\n  .lg\\:grid-cols-2{\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n  .lg\\:grid-cols-3{\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n  .lg\\:grid-cols-4{\n    grid-template-columns: repeat(4, minmax(0, 1fr));\n  }\n  .lg\\:flex-row{\n    flex-direction: row;\n  }\n  .lg\\:items-center{\n    align-items: center;\n  }\n  .lg\\:justify-between{\n    justify-content: space-between;\n  }\n  .lg\\:px-8{\n    padding-left: 2rem;\n    padding-right: 2rem;\n  }\n}\n\n/* cyrillic-ext */\n@font-face {\n  font-family: '__Inter_e8ce0c';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(/_next/static/media/55c55f0601d81cf3-s.woff2) format('woff2');\n  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;\n}\n/* cyrillic */\n@font-face {\n  font-family: '__Inter_e8ce0c';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(/_next/static/media/26a46d62cd723877-s.woff2) format('woff2');\n  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;\n}\n/* greek-ext */\n@font-face {\n  font-family: '__Inter_e8ce0c';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(/_next/static/media/97e0cb1ae144a2a9-s.woff2) format('woff2');\n  unicode-range: U+1F00-1FFF;\n}\n/* greek */\n@font-face {\n  font-family: '__Inter_e8ce0c';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(/_next/static/media/581909926a08bbc8-s.woff2) format('woff2');\n  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;\n}\n/* vietnamese */\n@font-face {\n  font-family: '__Inter_e8ce0c';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(/_next/static/media/df0a9ae256c0569c-s.woff2) format('woff2');\n  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;\n}\n/* latin-ext */\n@font-face {\n  font-family: '__Inter_e8ce0c';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(/_next/static/media/8e9860b6e62d6359-s.woff2) format('woff2');\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: '__Inter_e8ce0c';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(/_next/static/media/e4af272ccee01ff0-s.p.woff2) format('woff2');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}@font-face {font-family: '__Inter_Fallback_e8ce0c';src: local(\"Arial\");ascent-override: 90.49%;descent-override: 22.56%;line-gap-override: 0.00%;size-adjust: 107.06%\n}.__className_e8ce0c {font-family: '__Inter_e8ce0c', '__Inter_Fallback_e8ce0c';font-style: normal\n}\n\n", "/* cyrillic-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(/_next/static/media/55c55f0601d81cf3-s.woff2) format('woff2');\n  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;\n}\n/* cyrillic */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(/_next/static/media/26a46d62cd723877-s.woff2) format('woff2');\n  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;\n}\n/* greek-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(/_next/static/media/97e0cb1ae144a2a9-s.woff2) format('woff2');\n  unicode-range: U+1F00-1FFF;\n}\n/* greek */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(/_next/static/media/581909926a08bbc8-s.woff2) format('woff2');\n  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;\n}\n/* vietnamese */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(/_next/static/media/df0a9ae256c0569c-s.woff2) format('woff2');\n  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(/_next/static/media/8e9860b6e62d6359-s.woff2) format('woff2');\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(/_next/static/media/e4af272ccee01ff0-s.p.woff2) format('woff2');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n", null], "sourceRoot": ""}