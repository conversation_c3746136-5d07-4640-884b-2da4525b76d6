"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./providers/StoreProvider.tsx":
/*!*************************************!*\
  !*** ./providers/StoreProvider.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StoreContext: function() { return /* binding */ StoreContext; },\n/* harmony export */   StoreProvider: function() { return /* binding */ StoreProvider; },\n/* harmony export */   useVideoCallStoreContext: function() { return /* binding */ useVideoCallStoreContext; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/store */ \"(app-pages-browser)/./lib/store.ts\");\n/* __next_internal_client_entry_do_not_use__ StoreContext,StoreProvider,useVideoCallStoreContext auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nconst StoreContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\nfunction StoreProvider(param) {\n    let { children } = param;\n    _s();\n    const storeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    if (!storeRef.current) {\n        storeRef.current = _lib_store__WEBPACK_IMPORTED_MODULE_2__.useVideoCallStore;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StoreContext.Provider, {\n        value: storeRef.current,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Volumes/Apps/Websites/streamit-main/providers/StoreProvider.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n_s(StoreProvider, \"EtiU7pDwGhTDZwMnrKEqZbxjqXE=\");\n_c = StoreProvider;\nfunction useVideoCallStoreContext(selector, equalityFn) {\n    _s1();\n    const store = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(StoreContext);\n    if (!store) {\n        // Return a default value during SSR instead of throwing\n        if (false) {}\n        throw new Error(\"useVideoCallStoreContext must be used within a StoreProvider\");\n    }\n    return (0,zustand__WEBPACK_IMPORTED_MODULE_3__.useStore)(store, selector, equalityFn);\n}\n_s1(useVideoCallStoreContext, \"+pPtgsT1BIodRi0vpBak38UaALk=\", false, function() {\n    return [\n        zustand__WEBPACK_IMPORTED_MODULE_3__.useStore\n    ];\n});\nvar _c;\n$RefreshReg$(_c, \"StoreProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./providers/StoreProvider.tsx\n"));

/***/ })

});