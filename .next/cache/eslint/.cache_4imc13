[{"/Volumes/Apps/Websites/streamit-main/app/about/page.tsx": "1", "/Volumes/Apps/Websites/streamit-main/app/blog/page.tsx": "2", "/Volumes/Apps/Websites/streamit-main/app/careers/page.tsx": "3", "/Volumes/Apps/Websites/streamit-main/app/contact/page.tsx": "4", "/Volumes/Apps/Websites/streamit-main/app/cookies/page.tsx": "5", "/Volumes/Apps/Websites/streamit-main/app/enterprise/page.tsx": "6", "/Volumes/Apps/Websites/streamit-main/app/features/page.tsx": "7", "/Volumes/Apps/Websites/streamit-main/app/forgot-password/page.tsx": "8", "/Volumes/Apps/Websites/streamit-main/app/help-center/page.tsx": "9", "/Volumes/Apps/Websites/streamit-main/app/layout.tsx": "10", "/Volumes/Apps/Websites/streamit-main/app/login/page.tsx": "11", "/Volumes/Apps/Websites/streamit-main/app/page.tsx": "12", "/Volumes/Apps/Websites/streamit-main/app/pricing/page.tsx": "13", "/Volumes/Apps/Websites/streamit-main/app/privacy/page.tsx": "14", "/Volumes/Apps/Websites/streamit-main/app/room/[id]/layout.tsx": "15", "/Volumes/Apps/Websites/streamit-main/app/room/[id]/page.tsx": "16", "/Volumes/Apps/Websites/streamit-main/app/security/page.tsx": "17", "/Volumes/Apps/Websites/streamit-main/app/signup/page.tsx": "18", "/Volumes/Apps/Websites/streamit-main/app/terms/page.tsx": "19", "/Volumes/Apps/Websites/streamit-main/app/tutorials/page.tsx": "20", "/Volumes/Apps/Websites/streamit-main/app/webinars/page.tsx": "21", "/Volumes/Apps/Websites/streamit-main/components/Footer.tsx": "22", "/Volumes/Apps/Websites/streamit-main/components/Navbar.tsx": "23", "/Volumes/Apps/Websites/streamit-main/components/VideoCall/Chat.tsx": "24", "/Volumes/Apps/Websites/streamit-main/components/VideoCall/ParticipantsList.tsx": "25", "/Volumes/Apps/Websites/streamit-main/components/VideoCall/SettingsModal.tsx": "26", "/Volumes/Apps/Websites/streamit-main/components/VideoCall/VideoCallRoom.tsx": "27", "/Volumes/Apps/Websites/streamit-main/components/VideoCall/VideoControls.tsx": "28", "/Volumes/Apps/Websites/streamit-main/components/VideoCall/VideoGrid.tsx": "29", "/Volumes/Apps/Websites/streamit-main/components/VideoCall/VideoTile.tsx": "30", "/Volumes/Apps/Websites/streamit-main/components/ui/button.tsx": "31", "/Volumes/Apps/Websites/streamit-main/components/ui/card.tsx": "32", "/Volumes/Apps/Websites/streamit-main/components/ui/input.tsx": "33", "/Volumes/Apps/Websites/streamit-main/components/ui/toast.tsx": "34", "/Volumes/Apps/Websites/streamit-main/components/ui/toaster.tsx": "35", "/Volumes/Apps/Websites/streamit-main/components/ui/use-toast.ts": "36", "/Volumes/Apps/Websites/streamit-main/lib/encryption.ts": "37", "/Volumes/Apps/Websites/streamit-main/lib/rtc.ts": "38", "/Volumes/Apps/Websites/streamit-main/lib/socket.ts": "39", "/Volumes/Apps/Websites/streamit-main/lib/store.ts": "40", "/Volumes/Apps/Websites/streamit-main/lib/utils.ts": "41", "/Volumes/Apps/Websites/streamit-main/app/global-error.tsx": "42", "/Volumes/Apps/Websites/streamit-main/app/not-found.tsx": "43"}, {"size": 9640, "mtime": 1751304787410, "results": "44", "hashOfConfig": "45"}, {"size": 10170, "mtime": 1751304846206, "results": "46", "hashOfConfig": "45"}, {"size": 12267, "mtime": 1751304867841, "results": "47", "hashOfConfig": "45"}, {"size": 11327, "mtime": 1751304897930, "results": "48", "hashOfConfig": "45"}, {"size": 14934, "mtime": 1751305148891, "results": "49", "hashOfConfig": "45"}, {"size": 14474, "mtime": 1751304921411, "results": "50", "hashOfConfig": "45"}, {"size": 11824, "mtime": 1751304809893, "results": "51", "hashOfConfig": "45"}, {"size": 5575, "mtime": 1751304961878, "results": "52", "hashOfConfig": "45"}, {"size": 11498, "mtime": 1751305004708, "results": "53", "hashOfConfig": "45"}, {"size": 1629, "mtime": 1751306204911, "results": "54", "hashOfConfig": "45"}, {"size": 8027, "mtime": 1751304935453, "results": "55", "hashOfConfig": "45"}, {"size": 7704, "mtime": 1751306234797, "results": "56", "hashOfConfig": "45"}, {"size": 11805, "mtime": 1751304824791, "results": "57", "hashOfConfig": "45"}, {"size": 11508, "mtime": 1751304991310, "results": "58", "hashOfConfig": "45"}, {"size": 107, "mtime": 1751145009871, "results": "59", "hashOfConfig": "45"}, {"size": 3812, "mtime": 1751305386697, "results": "60", "hashOfConfig": "45"}, {"size": 15511, "mtime": 1751305042686, "results": "61", "hashOfConfig": "45"}, {"size": 13470, "mtime": 1751304948275, "results": "62", "hashOfConfig": "45"}, {"size": 13972, "mtime": 1751305063094, "results": "63", "hashOfConfig": "45"}, {"size": 12711, "mtime": 1751305126299, "results": "64", "hashOfConfig": "45"}, {"size": 11848, "mtime": 1751305017733, "results": "65", "hashOfConfig": "45"}, {"size": 4416, "mtime": 1751286258635, "results": "66", "hashOfConfig": "45"}, {"size": 2479, "mtime": 1751287565852, "results": "67", "hashOfConfig": "45"}, {"size": 5214, "mtime": 1751298322302, "results": "68", "hashOfConfig": "45"}, {"size": 8778, "mtime": 1751298391903, "results": "69", "hashOfConfig": "45"}, {"size": 17565, "mtime": 1751296608839, "results": "70", "hashOfConfig": "45"}, {"size": 12070, "mtime": 1751300377186, "results": "71", "hashOfConfig": "45"}, {"size": 12206, "mtime": 1751299415195, "results": "72", "hashOfConfig": "45"}, {"size": 1904, "mtime": 1751145023600, "results": "73", "hashOfConfig": "45"}, {"size": 4310, "mtime": 1751145023600, "results": "74", "hashOfConfig": "45"}, {"size": 1835, "mtime": 1751145023598, "results": "75", "hashOfConfig": "45"}, {"size": 1849, "mtime": 1751145023597, "results": "76", "hashOfConfig": "45"}, {"size": 824, "mtime": 1751145023599, "results": "77", "hashOfConfig": "45"}, {"size": 4839, "mtime": 1751145023598, "results": "78", "hashOfConfig": "45"}, {"size": 794, "mtime": 1751145023597, "results": "79", "hashOfConfig": "45"}, {"size": 3767, "mtime": 1751145023598, "results": "80", "hashOfConfig": "45"}, {"size": 8119, "mtime": 1751145023605, "results": "81", "hashOfConfig": "45"}, {"size": 9619, "mtime": 1751145023604, "results": "82", "hashOfConfig": "45"}, {"size": 2064, "mtime": 1751145023604, "results": "83", "hashOfConfig": "45"}, {"size": 8444, "mtime": 1751304612920, "results": "84", "hashOfConfig": "45"}, {"size": 166, "mtime": 1751145023604, "results": "85", "hashOfConfig": "45"}, {"size": 1416, "mtime": 1751306258608, "results": "86", "hashOfConfig": "45"}, {"size": 1371, "mtime": 1751306246880, "results": "87", "hashOfConfig": "45"}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1gtzqu5", {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Volumes/Apps/Websites/streamit-main/app/about/page.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/app/blog/page.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/app/careers/page.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/app/contact/page.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/app/cookies/page.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/app/enterprise/page.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/app/features/page.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/app/forgot-password/page.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/app/help-center/page.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/app/layout.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/app/login/page.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/app/page.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/app/pricing/page.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/app/privacy/page.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/app/room/[id]/layout.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/app/room/[id]/page.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/app/security/page.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/app/signup/page.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/app/terms/page.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/app/tutorials/page.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/app/webinars/page.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/components/Footer.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/components/Navbar.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/components/VideoCall/Chat.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/components/VideoCall/ParticipantsList.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/components/VideoCall/SettingsModal.tsx", ["217"], [], "/Volumes/Apps/Websites/streamit-main/components/VideoCall/VideoCallRoom.tsx", [], ["218", "219"], "/Volumes/Apps/Websites/streamit-main/components/VideoCall/VideoControls.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/components/VideoCall/VideoGrid.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/components/VideoCall/VideoTile.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/components/ui/button.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/components/ui/card.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/components/ui/input.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/components/ui/toast.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/components/ui/toaster.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/components/ui/use-toast.ts", [], [], "/Volumes/Apps/Websites/streamit-main/lib/encryption.ts", [], [], "/Volumes/Apps/Websites/streamit-main/lib/rtc.ts", [], [], "/Volumes/Apps/Websites/streamit-main/lib/socket.ts", [], [], "/Volumes/Apps/Websites/streamit-main/lib/store.ts", [], [], "/Volumes/Apps/Websites/streamit-main/lib/utils.ts", [], [], "/Volumes/Apps/Websites/streamit-main/app/global-error.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/app/not-found.tsx", [], [], {"ruleId": "220", "severity": 1, "message": "221", "line": 341, "column": 90, "nodeType": "222", "endLine": 341, "endColumn": 130}, {"ruleId": "223", "severity": 1, "message": "224", "line": 112, "column": 6, "nodeType": "225", "endLine": 112, "endColumn": 42, "suggestions": "226", "suppressions": "227"}, {"ruleId": "223", "severity": 1, "message": "228", "line": 212, "column": 6, "nodeType": "225", "endLine": 212, "endColumn": 21, "suggestions": "229", "suppressions": "230"}, "jsx-a11y/alt-text", "Image elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "JSXOpeningElement", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'removeParticipant', 'reset', 'setConnected', 'setLocalStream', and 'updateParticipant'. Either include them or remove the dependency array.", "ArrayExpression", ["231"], ["232"], "React Hook useEffect has missing dependencies: 'addMessage', 'addParticipant', and 'removeParticipant'. Either include them or remove the dependency array.", ["233"], ["234"], {"desc": "235", "fix": "236"}, {"kind": "237", "justification": "238"}, {"desc": "239", "fix": "240"}, {"kind": "237", "justification": "238"}, "Update the dependencies array to be: [currentUser, roomId, isInitialized, setLocalStream, setConnected, updateParticipant, removeParticipant, reset]", {"range": "241", "text": "242"}, "directive", "", "Update the dependencies array to be: [addMessage, addParticipant, isInitialized, removeParticipant]", {"range": "243", "text": "244"}, [2975, 3011], "[currentUser, roomId, isInitialized, setLocalStream, setConnected, updateParticipant, removeParticipant, reset]", [5979, 5994], "[addMessage, addParticipant, isInitialized, removeParticipant]"]