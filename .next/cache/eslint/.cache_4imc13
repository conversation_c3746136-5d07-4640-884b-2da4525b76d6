[{"/Volumes/Apps/Websites/streamit-main/app/about/page.tsx": "1", "/Volumes/Apps/Websites/streamit-main/app/blog/page.tsx": "2", "/Volumes/Apps/Websites/streamit-main/app/careers/page.tsx": "3", "/Volumes/Apps/Websites/streamit-main/app/contact/page.tsx": "4", "/Volumes/Apps/Websites/streamit-main/app/cookies/page.tsx": "5", "/Volumes/Apps/Websites/streamit-main/app/enterprise/page.tsx": "6", "/Volumes/Apps/Websites/streamit-main/app/features/page.tsx": "7", "/Volumes/Apps/Websites/streamit-main/app/help-center/page.tsx": "8", "/Volumes/Apps/Websites/streamit-main/app/layout.tsx": "9", "/Volumes/Apps/Websites/streamit-main/app/login/page.tsx": "10", "/Volumes/Apps/Websites/streamit-main/app/page.tsx": "11", "/Volumes/Apps/Websites/streamit-main/app/pricing/page.tsx": "12", "/Volumes/Apps/Websites/streamit-main/app/privacy/page.tsx": "13", "/Volumes/Apps/Websites/streamit-main/app/room/[id]/layout.tsx": "14", "/Volumes/Apps/Websites/streamit-main/app/room/[id]/page.tsx": "15", "/Volumes/Apps/Websites/streamit-main/app/security/page.tsx": "16", "/Volumes/Apps/Websites/streamit-main/app/signup/page.tsx": "17", "/Volumes/Apps/Websites/streamit-main/app/terms/page.tsx": "18", "/Volumes/Apps/Websites/streamit-main/app/tutorials/page.tsx": "19", "/Volumes/Apps/Websites/streamit-main/app/webinars/page.tsx": "20", "/Volumes/Apps/Websites/streamit-main/components/Footer.tsx": "21", "/Volumes/Apps/Websites/streamit-main/components/Navbar.tsx": "22", "/Volumes/Apps/Websites/streamit-main/components/VideoCall/Chat.tsx": "23", "/Volumes/Apps/Websites/streamit-main/components/VideoCall/ParticipantsList.tsx": "24", "/Volumes/Apps/Websites/streamit-main/components/VideoCall/SettingsModal.tsx": "25", "/Volumes/Apps/Websites/streamit-main/components/VideoCall/VideoCallRoom.tsx": "26", "/Volumes/Apps/Websites/streamit-main/components/VideoCall/VideoControls.tsx": "27", "/Volumes/Apps/Websites/streamit-main/components/VideoCall/VideoGrid.tsx": "28", "/Volumes/Apps/Websites/streamit-main/components/VideoCall/VideoTile.tsx": "29", "/Volumes/Apps/Websites/streamit-main/components/ui/button.tsx": "30", "/Volumes/Apps/Websites/streamit-main/components/ui/card.tsx": "31", "/Volumes/Apps/Websites/streamit-main/components/ui/input.tsx": "32", "/Volumes/Apps/Websites/streamit-main/components/ui/toast.tsx": "33", "/Volumes/Apps/Websites/streamit-main/components/ui/toaster.tsx": "34", "/Volumes/Apps/Websites/streamit-main/components/ui/use-toast.ts": "35", "/Volumes/Apps/Websites/streamit-main/lib/encryption.ts": "36", "/Volumes/Apps/Websites/streamit-main/lib/rtc.ts": "37", "/Volumes/Apps/Websites/streamit-main/lib/socket.ts": "38", "/Volumes/Apps/Websites/streamit-main/lib/store.ts": "39", "/Volumes/Apps/Websites/streamit-main/lib/utils.ts": "40", "/Volumes/Apps/Websites/streamit-main/app/forgot-password/page.tsx": "41"}, {"size": 9573, "mtime": 1751287631719, "results": "42", "hashOfConfig": "43"}, {"size": 10103, "mtime": 1751288115890, "results": "44", "hashOfConfig": "43"}, {"size": 12200, "mtime": 1751288298051, "results": "45", "hashOfConfig": "43"}, {"size": 11260, "mtime": 1751287667779, "results": "46", "hashOfConfig": "43"}, {"size": 14867, "mtime": 1751288452806, "results": "47", "hashOfConfig": "43"}, {"size": 14410, "mtime": 1751288023463, "results": "48", "hashOfConfig": "43"}, {"size": 11757, "mtime": 1751288774722, "results": "49", "hashOfConfig": "43"}, {"size": 11431, "mtime": 1751288158887, "results": "50", "hashOfConfig": "43"}, {"size": 1748, "mtime": 1751293474544, "results": "51", "hashOfConfig": "43"}, {"size": 7960, "mtime": 1751287877762, "results": "52", "hashOfConfig": "43"}, {"size": 30595, "mtime": 1751294936741, "results": "53", "hashOfConfig": "43"}, {"size": 11738, "mtime": 1751287711271, "results": "54", "hashOfConfig": "43"}, {"size": 11441, "mtime": 1751288338759, "results": "55", "hashOfConfig": "43"}, {"size": 107, "mtime": 1751145009871, "results": "56", "hashOfConfig": "43"}, {"size": 3824, "mtime": 1751293644229, "results": "57", "hashOfConfig": "43"}, {"size": 15446, "mtime": 1751288076324, "results": "58", "hashOfConfig": "43"}, {"size": 13403, "mtime": 1751287924611, "results": "59", "hashOfConfig": "43"}, {"size": 13905, "mtime": 1751288385661, "results": "60", "hashOfConfig": "43"}, {"size": 12644, "mtime": 1751288207132, "results": "61", "hashOfConfig": "43"}, {"size": 11781, "mtime": 1751288253159, "results": "62", "hashOfConfig": "43"}, {"size": 4416, "mtime": 1751286258635, "results": "63", "hashOfConfig": "43"}, {"size": 2479, "mtime": 1751287565852, "results": "64", "hashOfConfig": "43"}, {"size": 5348, "mtime": 1751145023600, "results": "65", "hashOfConfig": "43"}, {"size": 8752, "mtime": 1751145023599, "results": "66", "hashOfConfig": "43"}, {"size": 17565, "mtime": 1751296608839, "results": "67", "hashOfConfig": "43"}, {"size": 11011, "mtime": 1751295386121, "results": "68", "hashOfConfig": "43"}, {"size": 10527, "mtime": 1751296849765, "results": "69", "hashOfConfig": "43"}, {"size": 1904, "mtime": 1751145023600, "results": "70", "hashOfConfig": "43"}, {"size": 4310, "mtime": 1751145023600, "results": "71", "hashOfConfig": "43"}, {"size": 1835, "mtime": 1751145023598, "results": "72", "hashOfConfig": "43"}, {"size": 1849, "mtime": 1751145023597, "results": "73", "hashOfConfig": "43"}, {"size": 824, "mtime": 1751145023599, "results": "74", "hashOfConfig": "43"}, {"size": 4839, "mtime": 1751145023598, "results": "75", "hashOfConfig": "43"}, {"size": 794, "mtime": 1751145023597, "results": "76", "hashOfConfig": "43"}, {"size": 3767, "mtime": 1751145023598, "results": "77", "hashOfConfig": "43"}, {"size": 8119, "mtime": 1751145023605, "results": "78", "hashOfConfig": "43"}, {"size": 9619, "mtime": 1751145023604, "results": "79", "hashOfConfig": "43"}, {"size": 2064, "mtime": 1751145023604, "results": "80", "hashOfConfig": "43"}, {"size": 10558, "mtime": 1751294668119, "results": "81", "hashOfConfig": "43"}, {"size": 166, "mtime": 1751145023604, "results": "82", "hashOfConfig": "43"}, {"size": 5508, "mtime": 1751288582318, "results": "83", "hashOfConfig": "43"}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1gtzqu5", {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Volumes/Apps/Websites/streamit-main/app/about/page.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/app/blog/page.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/app/careers/page.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/app/contact/page.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/app/cookies/page.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/app/enterprise/page.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/app/features/page.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/app/help-center/page.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/app/layout.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/app/login/page.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/app/page.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/app/pricing/page.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/app/privacy/page.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/app/room/[id]/layout.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/app/room/[id]/page.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/app/security/page.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/app/signup/page.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/app/terms/page.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/app/tutorials/page.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/app/webinars/page.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/components/Footer.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/components/Navbar.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/components/VideoCall/Chat.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/components/VideoCall/ParticipantsList.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/components/VideoCall/SettingsModal.tsx", ["207"], [], "/Volumes/Apps/Websites/streamit-main/components/VideoCall/VideoCallRoom.tsx", [], ["208", "209"], "/Volumes/Apps/Websites/streamit-main/components/VideoCall/VideoControls.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/components/VideoCall/VideoGrid.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/components/VideoCall/VideoTile.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/components/ui/button.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/components/ui/card.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/components/ui/input.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/components/ui/toast.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/components/ui/toaster.tsx", [], [], "/Volumes/Apps/Websites/streamit-main/components/ui/use-toast.ts", [], [], "/Volumes/Apps/Websites/streamit-main/lib/encryption.ts", [], [], "/Volumes/Apps/Websites/streamit-main/lib/rtc.ts", [], [], "/Volumes/Apps/Websites/streamit-main/lib/socket.ts", [], [], "/Volumes/Apps/Websites/streamit-main/lib/store.ts", [], [], "/Volumes/Apps/Websites/streamit-main/lib/utils.ts", [], [], "/Volumes/Apps/Websites/streamit-main/app/forgot-password/page.tsx", [], [], {"ruleId": "210", "severity": 1, "message": "211", "line": 341, "column": 90, "nodeType": "212", "endLine": 341, "endColumn": 130}, {"ruleId": "213", "severity": 1, "message": "214", "line": 120, "column": 6, "nodeType": "215", "endLine": 120, "endColumn": 42, "suggestions": "216", "suppressions": "217"}, {"ruleId": "213", "severity": 1, "message": "218", "line": 220, "column": 6, "nodeType": "215", "endLine": 220, "endColumn": 21, "suggestions": "219", "suppressions": "220"}, "jsx-a11y/alt-text", "Image elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "JSXOpeningElement", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'removeParticipant', 'reset', 'setConnected', 'setLocalStream', and 'updateParticipant'. Either include them or remove the dependency array.", "ArrayExpression", ["221"], ["222"], "React Hook useEffect has missing dependencies: 'addMessage', 'addParticipant', and 'removeParticipant'. Either include them or remove the dependency array.", ["223"], ["224"], {"desc": "225", "fix": "226"}, {"kind": "227", "justification": "228"}, {"desc": "229", "fix": "230"}, {"kind": "227", "justification": "228"}, "Update the dependencies array to be: [currentUser, roomId, isInitialized, setLocalStream, setConnected, updateParticipant, removeParticipant, reset]", {"range": "231", "text": "232"}, "directive", "", "Update the dependencies array to be: [addMessage, addParticipant, isInitialized, removeParticipant]", {"range": "233", "text": "234"}, [3193, 3229], "[currentUser, roomId, isInitialized, setLocalStream, setConnected, updateParticipant, removeParticipant, reset]", [6197, 6212], "[addMessage, addParticipant, isInitialized, removeParticipant]"]