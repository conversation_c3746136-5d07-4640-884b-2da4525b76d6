(()=>{var e={};e.id=781,e.ids=[781],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7161:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.ZP,__next_app__:()=>m,originalPathname:()=>d,pages:()=>c,routeModule:()=>p,tree:()=>o}),t(3786),t(5698),t(6560);var a=t(3191),r=t(8716),i=t(8001),l=t(5231),n={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);t.d(s,n);let o=["",{children:["forgot-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,3786)),"/Volumes/Apps/Websites/streamit-main/app/forgot-password/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,5698)),"/Volumes/Apps/Websites/streamit-main/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,6560)),"/Volumes/Apps/Websites/streamit-main/app/not-found.tsx"]}],c=["/Volumes/Apps/Websites/streamit-main/app/forgot-password/page.tsx"],d="/forgot-password/page",m={require:t,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/forgot-password/page",pathname:"/forgot-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},2573:(e,s,t)=>{Promise.resolve().then(t.bind(t,3718))},3718:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>d,dynamic:()=>c});var a=t(326),r=t(7577),i=t(434),l=t(4659),n=t(6333),o=t(5932);let c="force-dynamic";function d(){let[e,s]=(0,r.useState)(""),[t,c]=(0,r.useState)(!1),[d,m]=(0,r.useState)(!1),p=async e=>{e.preventDefault(),m(!0),setTimeout(()=>{m(!1),c(!0)},2e3)};return t?(0,a.jsxs)(a.Fragment,{children:[a.jsx("div",{className:"animated-bg"}),a.jsx("div",{className:"min-h-screen flex items-center justify-center p-4 relative z-10 pt-20",children:a.jsx("div",{className:"max-w-md w-full",children:(0,a.jsxs)("div",{className:"glass p-8 text-center",children:[a.jsx(l.Z,{className:"h-16 w-16 text-green-400 mx-auto mb-6"}),a.jsx("h1",{className:"text-3xl font-bold text-white mb-4",children:"Check Your Email"}),(0,a.jsxs)("p",{className:"text-white/70 mb-6",children:["We've sent a password reset link to ",a.jsx("strong",{children:e}),". Please check your email and follow the instructions to reset your password."]}),a.jsx("p",{className:"text-white/60 text-sm mb-8",children:"Didn't receive the email? Check your spam folder or try again in a few minutes."}),(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx(i.default,{href:"/login",className:"btn-primary w-full inline-block text-center",children:"Back to Login"}),a.jsx("button",{onClick:()=>c(!1),className:"btn-secondary w-full",children:"Try Different Email"})]})]})})})]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx("div",{className:"animated-bg"}),a.jsx("div",{className:"min-h-screen flex items-center justify-center p-4 relative z-10 pt-20",children:(0,a.jsxs)("div",{className:"max-w-md w-full",children:[(0,a.jsxs)("div",{className:"glass p-8",children:[(0,a.jsxs)(i.default,{href:"/login",className:"flex items-center gap-2 text-white/70 hover:text-white transition-colors mb-6",children:[a.jsx(n.Z,{className:"h-4 w-4"}),"Back to Login"]}),(0,a.jsxs)("div",{className:"text-center mb-8",children:[a.jsx("h1",{className:"text-3xl font-bold text-white mb-2",children:"Forgot Password?"}),a.jsx("p",{className:"text-white/70",children:"Enter your email address and we'll send you a link to reset your password"})]}),(0,a.jsxs)("form",{onSubmit:p,className:"space-y-6",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"email",className:"block text-white/80 text-sm font-medium mb-2",children:"Email Address"}),(0,a.jsxs)("div",{className:"relative",children:[a.jsx(o.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-white/60"}),a.jsx("input",{type:"email",id:"email",value:e,onChange:e=>s(e.target.value),required:!0,className:"glass-input w-full pl-10",placeholder:"Enter your email address"})]})]}),a.jsx("button",{type:"submit",disabled:d,className:`btn-primary w-full flex items-center justify-center gap-2 ${d?"opacity-50 cursor-not-allowed":""}`,children:d?a.jsx("div",{className:"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"}):(0,a.jsxs)(a.Fragment,{children:[a.jsx(o.Z,{className:"h-4 w-4"}),"Send Reset Link"]})})]}),a.jsx("div",{className:"mt-8 text-center",children:(0,a.jsxs)("p",{className:"text-white/60 text-sm",children:["Remember your password?"," ",a.jsx(i.default,{href:"/login",className:"text-purple-400 hover:text-purple-300 font-medium",children:"Sign in"})]})})]}),(0,a.jsxs)("div",{className:"mt-8 glass p-6",children:[a.jsx("h3",{className:"text-white font-semibold mb-3",children:"Need Help?"}),(0,a.jsxs)("div",{className:"space-y-2 text-white/70 text-sm",children:[a.jsx("p",{children:"• Make sure you enter the email address associated with your account"}),a.jsx("p",{children:"• Check your spam or junk folder if you don't see the email"}),a.jsx("p",{children:"• The reset link will expire in 24 hours for security"}),a.jsx("p",{children:"• Contact support if you continue to have issues"})]}),a.jsx("div",{className:"mt-4",children:a.jsx(i.default,{href:"/contact",className:"text-purple-400 hover:text-purple-300 text-sm",children:"Contact Support →"})})]})]})})]})}},4659:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(6557).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},5932:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(6557).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},3786:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>l,__esModule:()=>i,default:()=>o,dynamic:()=>n});var a=t(8570);let r=(0,a.createProxy)(String.raw`/Volumes/Apps/Websites/streamit-main/app/forgot-password/page.tsx`),{__esModule:i,$$typeof:l}=r;r.default;let n=(0,a.createProxy)(String.raw`/Volumes/Apps/Websites/streamit-main/app/forgot-password/page.tsx#dynamic`),o=(0,a.createProxy)(String.raw`/Volumes/Apps/Websites/streamit-main/app/forgot-password/page.tsx#default`)}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[378,628],()=>t(7161));module.exports=a})();