(()=>{var e={};e.id=781,e.ids=[781],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7310:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>p,originalPathname:()=>m,pages:()=>d,routeModule:()=>x,tree:()=>c}),s(3786),s(5903),s(5866);var r=s(3191),a=s(8716),i=s(7922),l=s.n(i),n=s(5231),o={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);s.d(t,o);let c=["",{children:["forgot-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,3786)),"/Volumes/Apps/Websites/streamit-main/app/forgot-password/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,5903)),"/Volumes/Apps/Websites/streamit-main/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,5866,23)),"next/dist/client/components/not-found-error"]}],d=["/Volumes/Apps/Websites/streamit-main/app/forgot-password/page.tsx"],m="/forgot-password/page",p={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/forgot-password/page",pathname:"/forgot-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},2573:(e,t,s)=>{Promise.resolve().then(s.bind(s,4677))},4677:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var r=s(326),a=s(7577),i=s(434),l=s(4659);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,s(6557).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);var o=s(5932);function c(){let[e,t]=(0,a.useState)(""),[s,c]=(0,a.useState)(!1),[d,m]=(0,a.useState)(!1),p=async e=>{e.preventDefault(),m(!0),setTimeout(()=>{m(!1),c(!0)},2e3)};return s?(0,r.jsxs)(r.Fragment,{children:[r.jsx("div",{className:"animated-bg"}),r.jsx("div",{className:"min-h-screen flex items-center justify-center p-4 relative z-10 pt-20",children:r.jsx("div",{className:"max-w-md w-full",children:(0,r.jsxs)("div",{className:"glass p-8 text-center",children:[r.jsx(l.Z,{className:"h-16 w-16 text-green-400 mx-auto mb-6"}),r.jsx("h1",{className:"text-3xl font-bold text-white mb-4",children:"Check Your Email"}),(0,r.jsxs)("p",{className:"text-white/70 mb-6",children:["We've sent a password reset link to ",r.jsx("strong",{children:e}),". Please check your email and follow the instructions to reset your password."]}),r.jsx("p",{className:"text-white/60 text-sm mb-8",children:"Didn't receive the email? Check your spam folder or try again in a few minutes."}),(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx(i.default,{href:"/login",className:"btn-primary w-full inline-block text-center",children:"Back to Login"}),r.jsx("button",{onClick:()=>c(!1),className:"btn-secondary w-full",children:"Try Different Email"})]})]})})})]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx("div",{className:"animated-bg"}),r.jsx("div",{className:"min-h-screen flex items-center justify-center p-4 relative z-10 pt-20",children:(0,r.jsxs)("div",{className:"max-w-md w-full",children:[(0,r.jsxs)("div",{className:"glass p-8",children:[(0,r.jsxs)(i.default,{href:"/login",className:"flex items-center gap-2 text-white/70 hover:text-white transition-colors mb-6",children:[r.jsx(n,{className:"h-4 w-4"}),"Back to Login"]}),(0,r.jsxs)("div",{className:"text-center mb-8",children:[r.jsx("h1",{className:"text-3xl font-bold text-white mb-2",children:"Forgot Password?"}),r.jsx("p",{className:"text-white/70",children:"Enter your email address and we'll send you a link to reset your password"})]}),(0,r.jsxs)("form",{onSubmit:p,className:"space-y-6",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"email",className:"block text-white/80 text-sm font-medium mb-2",children:"Email Address"}),(0,r.jsxs)("div",{className:"relative",children:[r.jsx(o.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-white/60"}),r.jsx("input",{type:"email",id:"email",value:e,onChange:e=>t(e.target.value),required:!0,className:"glass-input w-full pl-10",placeholder:"Enter your email address"})]})]}),r.jsx("button",{type:"submit",disabled:d,className:`btn-primary w-full flex items-center justify-center gap-2 ${d?"opacity-50 cursor-not-allowed":""}`,children:d?r.jsx("div",{className:"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"}):(0,r.jsxs)(r.Fragment,{children:[r.jsx(o.Z,{className:"h-4 w-4"}),"Send Reset Link"]})})]}),r.jsx("div",{className:"mt-8 text-center",children:(0,r.jsxs)("p",{className:"text-white/60 text-sm",children:["Remember your password?"," ",r.jsx(i.default,{href:"/login",className:"text-purple-400 hover:text-purple-300 font-medium",children:"Sign in"})]})})]}),(0,r.jsxs)("div",{className:"mt-8 glass p-6",children:[r.jsx("h3",{className:"text-white font-semibold mb-3",children:"Need Help?"}),(0,r.jsxs)("div",{className:"space-y-2 text-white/70 text-sm",children:[r.jsx("p",{children:"• Make sure you enter the email address associated with your account"}),r.jsx("p",{children:"• Check your spam or junk folder if you don't see the email"}),r.jsx("p",{children:"• The reset link will expire in 24 hours for security"}),r.jsx("p",{children:"• Contact support if you continue to have issues"})]}),r.jsx("div",{className:"mt-4",children:r.jsx(i.default,{href:"/contact",className:"text-purple-400 hover:text-purple-300 text-sm",children:"Contact Support →"})})]})]})})]})}},4659:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(6557).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},5932:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(6557).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},3786:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>l,__esModule:()=>i,default:()=>n});var r=s(8570);let a=(0,r.createProxy)(String.raw`/Volumes/Apps/Websites/streamit-main/app/forgot-password/page.tsx`),{__esModule:i,$$typeof:l}=a;a.default;let n=(0,r.createProxy)(String.raw`/Volumes/Apps/Websites/streamit-main/app/forgot-password/page.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[110,653],()=>s(7310));module.exports=r})();