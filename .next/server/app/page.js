/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FApps%2FWebsites%2Fstreamit-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FApps%2FWebsites%2Fstreamit-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"/Volumes/Apps/Websites/streamit-main/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Volumes/Apps/Websites/streamit-main/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FApps%2FWebsites%2Fstreamit-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fcomponents%2FFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fcomponents%2FNavbar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fcomponents%2Fui%2Ftoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fproviders%2FStoreProvider.tsx%22%2C%22ids%22%3A%5B%22StoreProvider%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fcomponents%2FFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fcomponents%2FNavbar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fcomponents%2Fui%2Ftoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fproviders%2FStoreProvider.tsx%22%2C%22ids%22%3A%5B%22StoreProvider%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Footer.tsx */ \"(ssr)/./components/Footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Navbar.tsx */ \"(ssr)/./components/Navbar.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/toaster.tsx */ \"(ssr)/./components/ui/toaster.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./providers/StoreProvider.tsx */ \"(ssr)/./providers/StoreProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fcomponents%2FFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fcomponents%2FNavbar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fcomponents%2Fui%2Ftoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fproviders%2FStoreProvider.tsx%22%2C%22ids%22%3A%5B%22StoreProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlZvbHVtZXMlMkZBcHBzJTJGV2Vic2l0ZXMlMkZzdHJlYW1pdC1tYWluJTJGYXBwJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdJQUFzRiIsInNvdXJjZXMiOlsid2VicGFjazovL3N0cmVhbWl0LXByby1uZXh0anMvP2NiYzUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVm9sdW1lcy9BcHBzL1dlYnNpdGVzL3N0cmVhbWl0LW1haW4vYXBwL3BhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Play_Shield_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Play,Shield,UserPlus,Users,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/video.js\");\n/* harmony import */ var _barrel_optimize_names_Play_Shield_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Play,Shield,UserPlus,Users,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Play_Shield_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Play,Shield,UserPlus,Users,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Play_Shield_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Play,Shield,UserPlus,Users,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Play_Shield_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Play,Shield,UserPlus,Users,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Play_Shield_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Play,Shield,UserPlus,Users,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction HomePage() {\n    const [showNameModal, setShowNameModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showJoinModal, setShowJoinModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userName, setUserName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [roomId, setRoomId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isStarting, setIsStarting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const handleStartMeeting = ()=>{\n        setShowNameModal(true);\n        setIsStarting(true);\n    };\n    const handleJoinMeeting = ()=>{\n        setShowJoinModal(true);\n        setIsStarting(false);\n    };\n    const startMeeting = ()=>{\n        if (!userName.trim()) {\n            alert(\"Please enter your name\");\n            return;\n        }\n        const newRoomId = Math.random().toString(36).substring(2, 15);\n        localStorage.setItem(\"userName\", userName.trim());\n        setShowNameModal(false);\n        router.push(`/room/${newRoomId}`);\n    };\n    const joinRoom = ()=>{\n        if (!roomId.trim() || !userName.trim()) {\n            alert(\"Please enter both room ID and your name\");\n            return;\n        }\n        localStorage.setItem(\"userName\", userName.trim());\n        setShowJoinModal(false);\n        router.push(`/room/${roomId.trim()}`);\n    };\n    const closeModals = ()=>{\n        setShowNameModal(false);\n        setShowJoinModal(false);\n        setUserName(\"\");\n        setRoomId(\"\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animated-bg\"\n            }, void 0, false, {\n                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex flex-col items-center justify-center p-4 relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-12 fade-in\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mr-4 shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Play_Shield_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"h-8 w-8 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-5xl font-bold text-white\",\n                                        children: \"StreamIt Pro\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-white/80 max-w-2xl mx-auto leading-relaxed\",\n                                children: \"Professional video conferencing platform with crystal-clear HD video, advanced audio processing, and seamless collaboration tools\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-6 mb-16 slide-up\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleStartMeeting,\n                                className: \"btn-primary flex items-center gap-3 text-lg px-8 py-4 min-w-[200px] justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Play_Shield_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Start Meeting\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleJoinMeeting,\n                                className: \"btn-secondary flex items-center gap-3 text-lg px-8 py-4 min-w-[200px] justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Play_Shield_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Join Meeting\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid md:grid-cols-3 gap-8 max-w-4xl w-full bounce-in\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"glass text-center p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Play_Shield_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-8 w-8 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-white mb-3\",\n                                        children: \"Multi-Participant\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white/70\",\n                                        children: \"Connect with multiple participants in crystal-clear HD video calls with advanced audio processing\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"glass text-center p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Play_Shield_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-8 w-8 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-white mb-3\",\n                                        children: \"Secure & Private\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white/70\",\n                                        children: \"End-to-end encrypted communications with WebRTC technology ensuring your privacy and security\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"glass text-center p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Play_Shield_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-8 w-8 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-white mb-3\",\n                                        children: \"Lightning Fast\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white/70\",\n                                        children: \"Optimized for performance with minimal latency, adaptive quality, and seamless user experience\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mt-16 text-white/60\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Built with Next.js, TypeScript, WebRTC & Modern Web Technologies\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this),\n            showNameModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"glass p-8 max-w-md w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-6 text-center\",\n                            children: isStarting ? \"Start Your Meeting\" : \"Join Meeting\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Enter your name\",\n                                    value: userName,\n                                    onChange: (e)=>setUserName(e.target.value),\n                                    className: \"glass-input w-full\",\n                                    autoFocus: true,\n                                    onKeyDown: (e)=>e.key === \"Enter\" && startMeeting()\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: closeModals,\n                                            className: \"btn-secondary flex-1\",\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: startMeeting,\n                                            className: \"btn-primary flex-1\",\n                                            children: \"Start Meeting\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                lineNumber: 137,\n                columnNumber: 9\n            }, this),\n            showJoinModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"glass p-8 max-w-md w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-6 text-center\",\n                            children: \"Join Meeting\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Enter your name\",\n                                    value: userName,\n                                    onChange: (e)=>setUserName(e.target.value),\n                                    className: \"glass-input w-full\",\n                                    autoFocus: true\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Enter meeting ID\",\n                                    value: roomId,\n                                    onChange: (e)=>setRoomId(e.target.value),\n                                    className: \"glass-input w-full\",\n                                    onKeyDown: (e)=>e.key === \"Enter\" && joinRoom()\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: closeModals,\n                                            className: \"btn-secondary flex-1\",\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: joinRoom,\n                                            className: \"btn-primary flex-1\",\n                                            children: \"Join Meeting\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                lineNumber: 173,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Footer.tsx":
/*!*******************************!*\
  !*** ./components/Footer.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Linkedin_Twitter_Video_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Linkedin,Twitter,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/video.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Linkedin_Twitter_Video_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Linkedin,Twitter,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Linkedin_Twitter_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Linkedin,Twitter,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Linkedin_Twitter_Video_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Linkedin,Twitter,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Footer() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-black/50 backdrop-blur-md border-t border-white/10 mt-20\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mr-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Linkedin_Twitter_Video_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                className: \"h-5 w-5 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                                lineNumber: 14,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                            lineNumber: 13,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xl font-bold text-white\",\n                                            children: \"StreamIt Pro\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                            lineNumber: 16,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                    lineNumber: 12,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/60 text-sm mt-2\",\n                                    children: \"Professional video conferencing solution for teams of all sizes.\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                    lineNumber: 18,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4 mt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-white/60 hover:text-white transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Linkedin_Twitter_Video_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                                lineNumber: 23,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                            lineNumber: 22,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-white/60 hover:text-white transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Linkedin_Twitter_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                                lineNumber: 26,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                            lineNumber: 25,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-white/60 hover:text-white transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Linkedin_Twitter_Video_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                                lineNumber: 29,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                            lineNumber: 28,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                    lineNumber: 21,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                            lineNumber: 11,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-white font-semibold mb-4\",\n                                    children: \"Product\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/features\",\n                                                className: \"text-white/60 hover:text-white transition-colors text-sm\",\n                                                children: \"Features\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                                lineNumber: 37,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                            lineNumber: 37,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/pricing\",\n                                                className: \"text-white/60 hover:text-white transition-colors text-sm\",\n                                                children: \"Pricing\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                                lineNumber: 38,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                            lineNumber: 38,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/enterprise\",\n                                                className: \"text-white/60 hover:text-white transition-colors text-sm\",\n                                                children: \"Enterprise\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                                lineNumber: 39,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                            lineNumber: 39,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/security\",\n                                                className: \"text-white/60 hover:text-white transition-colors text-sm\",\n                                                children: \"Security\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                                lineNumber: 40,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                            lineNumber: 40,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                    lineNumber: 36,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-white font-semibold mb-4\",\n                                    children: \"Resources\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/blog\",\n                                                className: \"text-white/60 hover:text-white transition-colors text-sm\",\n                                                children: \"Blog\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                                lineNumber: 47,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/help-center\",\n                                                className: \"text-white/60 hover:text-white transition-colors text-sm\",\n                                                children: \"Help Center\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                                lineNumber: 48,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                            lineNumber: 48,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/tutorials\",\n                                                className: \"text-white/60 hover:text-white transition-colors text-sm\",\n                                                children: \"Tutorials\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                                lineNumber: 49,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/webinars\",\n                                                className: \"text-white/60 hover:text-white transition-colors text-sm\",\n                                                children: \"Webinars\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                                lineNumber: 50,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-white font-semibold mb-4\",\n                                    children: \"Company\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/about\",\n                                                className: \"text-white/60 hover:text-white transition-colors text-sm\",\n                                                children: \"About Us\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                                lineNumber: 57,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/careers\",\n                                                className: \"text-white/60 hover:text-white transition-colors text-sm\",\n                                                children: \"Careers\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                                lineNumber: 58,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/contact\",\n                                                className: \"text-white/60 hover:text-white transition-colors text-sm\",\n                                                children: \"Contact Us\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                                lineNumber: 59,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/privacy\",\n                                                className: \"text-white/60 hover:text-white transition-colors text-sm\",\n                                                children: \"Privacy Policy\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                                lineNumber: 60,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                            lineNumber: 60,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                    lineNumber: 10,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-white/10 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-white/50 text-sm\",\n                            children: [\n                                \"\\xa9 \",\n                                new Date().getFullYear(),\n                                \" StreamIt Pro. All rights reserved.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-6 mt-4 md:mt-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/terms\",\n                                    className: \"text-white/60 hover:text-white transition-colors text-sm\",\n                                    children: \"Terms of Service\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/privacy\",\n                                    className: \"text-white/60 hover:text-white transition-colors text-sm\",\n                                    children: \"Privacy Policy\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/cookies\",\n                                    className: \"text-white/60 hover:text-white transition-colors text-sm\",\n                                    children: \"Cookies\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n            lineNumber: 9,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Navbar.tsx":
/*!*******************************!*\
  !*** ./components/Navbar.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_Video_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/video.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Navbar() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"fixed top-0 left-0 right-0 z-50 bg-black/50 backdrop-blur-md border-b border-white/10\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between h-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            href: \"/\",\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mr-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Video_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        className: \"h-5 w-5 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Apps/Websites/streamit-main/components/Navbar.tsx\",\n                                        lineNumber: 14,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/components/Navbar.tsx\",\n                                    lineNumber: 13,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xl font-bold text-white\",\n                                    children: \"StreamIt Pro\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/components/Navbar.tsx\",\n                                    lineNumber: 16,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Navbar.tsx\",\n                            lineNumber: 12,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Apps/Websites/streamit-main/components/Navbar.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden md:flex items-center space-x-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#features\",\n                                className: \"text-white/80 hover:text-white transition-colors cursor-pointer\",\n                                onClick: (e)=>{\n                                    e.preventDefault();\n                                    document.getElementById(\"features\")?.scrollIntoView({\n                                        behavior: \"smooth\"\n                                    });\n                                },\n                                children: \"Features\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Apps/Websites/streamit-main/components/Navbar.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#pricing\",\n                                className: \"text-white/80 hover:text-white transition-colors cursor-pointer\",\n                                onClick: (e)=>{\n                                    e.preventDefault();\n                                    document.getElementById(\"pricing\")?.scrollIntoView({\n                                        behavior: \"smooth\"\n                                    });\n                                },\n                                children: \"Pricing\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Apps/Websites/streamit-main/components/Navbar.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: \"/about\",\n                                className: \"text-white/80 hover:text-white transition-colors\",\n                                children: \"About\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Apps/Websites/streamit-main/components/Navbar.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: \"/contact\",\n                                className: \"text-white/80 hover:text-white transition-colors\",\n                                children: \"Contact\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Apps/Websites/streamit-main/components/Navbar.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Apps/Websites/streamit-main/components/Navbar.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: \"/login\",\n                                className: \"px-4 py-2 rounded-md text-sm font-medium text-white hover:bg-white/10 transition-colors\",\n                                children: \"Log in\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Apps/Websites/streamit-main/components/Navbar.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: \"/signup\",\n                                className: \"ml-4 px-4 py-2 rounded-md text-sm font-medium text-white bg-gradient-to-r from-purple-600 to-pink-600 hover:opacity-90 transition-opacity\",\n                                children: \"Sign up free\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Apps/Websites/streamit-main/components/Navbar.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Apps/Websites/streamit-main/components/Navbar.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Apps/Websites/streamit-main/components/Navbar.tsx\",\n                lineNumber: 10,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Navbar.tsx\",\n            lineNumber: 9,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Volumes/Apps/Websites/streamit-main/components/Navbar.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Navbar.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/toast.tsx":
/*!*********************************!*\
  !*** ./components/ui/toast.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toast: () => (/* binding */ Toast),\n/* harmony export */   ToastAction: () => (/* binding */ ToastAction),\n/* harmony export */   ToastClose: () => (/* binding */ ToastClose),\n/* harmony export */   ToastDescription: () => (/* binding */ ToastDescription),\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   ToastTitle: () => (/* binding */ ToastTitle),\n/* harmony export */   ToastViewport: () => (/* binding */ ToastViewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-toast */ \"(ssr)/./node_modules/@radix-ui/react-toast/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\n\nconst ToastProvider = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Provider;\nconst ToastViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Viewport, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Volumes/Apps/Websites/streamit-main/components/ui/toast.tsx\",\n        lineNumber: 14,\n        columnNumber: 3\n    }, undefined));\nToastViewport.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Viewport.displayName;\nconst toastVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full\", {\n    variants: {\n        variant: {\n            default: \"border bg-background text-foreground\",\n            destructive: \"destructive border-destructive bg-destructive text-destructive-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nconst Toast = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(toastVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Volumes/Apps/Websites/streamit-main/components/ui/toast.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, undefined);\n});\nToast.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\nconst ToastAction = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Action, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Volumes/Apps/Websites/streamit-main/components/ui/toast.tsx\",\n        lineNumber: 60,\n        columnNumber: 3\n    }, undefined));\nToastAction.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Action.displayName;\nconst ToastClose = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Close, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600\", className),\n        \"toast-close\": \"\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"/Volumes/Apps/Websites/streamit-main/components/ui/toast.tsx\",\n            lineNumber: 84,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Volumes/Apps/Websites/streamit-main/components/ui/toast.tsx\",\n        lineNumber: 75,\n        columnNumber: 3\n    }, undefined));\nToastClose.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Close.displayName;\nconst ToastTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Title, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm font-semibold\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Volumes/Apps/Websites/streamit-main/components/ui/toast.tsx\",\n        lineNumber: 93,\n        columnNumber: 3\n    }, undefined));\nToastTitle.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Title.displayName;\nconst ToastDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Description, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm opacity-90\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Volumes/Apps/Websites/streamit-main/components/ui/toast.tsx\",\n        lineNumber: 105,\n        columnNumber: 3\n    }, undefined));\nToastDescription.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Description.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL3RvYXN0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQThCO0FBQzBCO0FBQ1M7QUFDakM7QUFFQTtBQUVoQyxNQUFNSyxnQkFBZ0JKLDJEQUF3QjtBQUU5QyxNQUFNTSw4QkFBZ0JQLDZDQUFnQixDQUdwQyxDQUFDLEVBQUVTLFNBQVMsRUFBRSxHQUFHQyxPQUFPLEVBQUVDLG9CQUMxQiw4REFBQ1YsMkRBQXdCO1FBQ3ZCVSxLQUFLQTtRQUNMRixXQUFXTCw4Q0FBRUEsQ0FDWCxxSUFDQUs7UUFFRCxHQUFHQyxLQUFLOzs7Ozs7QUFHYkgsY0FBY00sV0FBVyxHQUFHWiwyREFBd0IsQ0FBQ1ksV0FBVztBQUVoRSxNQUFNQyxnQkFBZ0JaLDZEQUFHQSxDQUN2Qiw2bEJBQ0E7SUFDRWEsVUFBVTtRQUNSQyxTQUFTO1lBQ1BDLFNBQVM7WUFDVEMsYUFDRTtRQUNKO0lBQ0Y7SUFDQUMsaUJBQWlCO1FBQ2ZILFNBQVM7SUFDWDtBQUNGO0FBR0YsTUFBTUksc0JBQVFwQiw2Q0FBZ0IsQ0FJNUIsQ0FBQyxFQUFFUyxTQUFTLEVBQUVPLE9BQU8sRUFBRSxHQUFHTixPQUFPLEVBQUVDO0lBQ25DLHFCQUNFLDhEQUFDVix1REFBb0I7UUFDbkJVLEtBQUtBO1FBQ0xGLFdBQVdMLDhDQUFFQSxDQUFDVSxjQUFjO1lBQUVFO1FBQVEsSUFBSVA7UUFDekMsR0FBR0MsS0FBSzs7Ozs7O0FBR2Y7QUFDQVUsTUFBTVAsV0FBVyxHQUFHWix1REFBb0IsQ0FBQ1ksV0FBVztBQUVwRCxNQUFNUyw0QkFBY3RCLDZDQUFnQixDQUdsQyxDQUFDLEVBQUVTLFNBQVMsRUFBRSxHQUFHQyxPQUFPLEVBQUVDLG9CQUMxQiw4REFBQ1YseURBQXNCO1FBQ3JCVSxLQUFLQTtRQUNMRixXQUFXTCw4Q0FBRUEsQ0FDWCxzZ0JBQ0FLO1FBRUQsR0FBR0MsS0FBSzs7Ozs7O0FBR2JZLFlBQVlULFdBQVcsR0FBR1oseURBQXNCLENBQUNZLFdBQVc7QUFFNUQsTUFBTVcsMkJBQWF4Qiw2Q0FBZ0IsQ0FHakMsQ0FBQyxFQUFFUyxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNWLHdEQUFxQjtRQUNwQlUsS0FBS0E7UUFDTEYsV0FBV0wsOENBQUVBLENBQ1gseVZBQ0FLO1FBRUZpQixlQUFZO1FBQ1gsR0FBR2hCLEtBQUs7a0JBRVQsNEVBQUNQLDZFQUFDQTtZQUFDTSxXQUFVOzs7Ozs7Ozs7OztBQUdqQmUsV0FBV1gsV0FBVyxHQUFHWix3REFBcUIsQ0FBQ1ksV0FBVztBQUUxRCxNQUFNYywyQkFBYTNCLDZDQUFnQixDQUdqQyxDQUFDLEVBQUVTLFNBQVMsRUFBRSxHQUFHQyxPQUFPLEVBQUVDLG9CQUMxQiw4REFBQ1Ysd0RBQXFCO1FBQ3BCVSxLQUFLQTtRQUNMRixXQUFXTCw4Q0FBRUEsQ0FBQyx5QkFBeUJLO1FBQ3RDLEdBQUdDLEtBQUs7Ozs7OztBQUdiaUIsV0FBV2QsV0FBVyxHQUFHWix3REFBcUIsQ0FBQ1ksV0FBVztBQUUxRCxNQUFNZ0IsaUNBQW1CN0IsNkNBQWdCLENBR3ZDLENBQUMsRUFBRVMsU0FBUyxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQzFCLDhEQUFDViw4REFBMkI7UUFDMUJVLEtBQUtBO1FBQ0xGLFdBQVdMLDhDQUFFQSxDQUFDLHNCQUFzQks7UUFDbkMsR0FBR0MsS0FBSzs7Ozs7O0FBR2JtQixpQkFBaUJoQixXQUFXLEdBQUdaLDhEQUEyQixDQUFDWSxXQUFXO0FBZ0JyRSIsInNvdXJjZXMiOlsid2VicGFjazovL3N0cmVhbWl0LXByby1uZXh0anMvLi9jb21wb25lbnRzL3VpL3RvYXN0LnRzeD9lYzRmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5pbXBvcnQgKiBhcyBUb2FzdFByaW1pdGl2ZXMgZnJvbSBcIkByYWRpeC11aS9yZWFjdC10b2FzdFwiXG5pbXBvcnQgeyBjdmEsIHR5cGUgVmFyaWFudFByb3BzIH0gZnJvbSBcImNsYXNzLXZhcmlhbmNlLWF1dGhvcml0eVwiXG5pbXBvcnQgeyBYIH0gZnJvbSBcImx1Y2lkZS1yZWFjdFwiXG5cbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCJcblxuY29uc3QgVG9hc3RQcm92aWRlciA9IFRvYXN0UHJpbWl0aXZlcy5Qcm92aWRlclxuXG5jb25zdCBUb2FzdFZpZXdwb3J0ID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgUmVhY3QuRWxlbWVudFJlZjx0eXBlb2YgVG9hc3RQcmltaXRpdmVzLlZpZXdwb3J0PixcbiAgUmVhY3QuQ29tcG9uZW50UHJvcHNXaXRob3V0UmVmPHR5cGVvZiBUb2FzdFByaW1pdGl2ZXMuVmlld3BvcnQ+XG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXG4gIDxUb2FzdFByaW1pdGl2ZXMuVmlld3BvcnRcbiAgICByZWY9e3JlZn1cbiAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgXCJmaXhlZCB0b3AtMCB6LVsxMDBdIGZsZXggbWF4LWgtc2NyZWVuIHctZnVsbCBmbGV4LWNvbC1yZXZlcnNlIHAtNCBzbTpib3R0b20tMCBzbTpyaWdodC0wIHNtOnRvcC1hdXRvIHNtOmZsZXgtY29sIG1kOm1heC13LVs0MjBweF1cIixcbiAgICAgIGNsYXNzTmFtZVxuICAgICl9XG4gICAgey4uLnByb3BzfVxuICAvPlxuKSlcblRvYXN0Vmlld3BvcnQuZGlzcGxheU5hbWUgPSBUb2FzdFByaW1pdGl2ZXMuVmlld3BvcnQuZGlzcGxheU5hbWVcblxuY29uc3QgdG9hc3RWYXJpYW50cyA9IGN2YShcbiAgXCJncm91cCBwb2ludGVyLWV2ZW50cy1hdXRvIHJlbGF0aXZlIGZsZXggdy1mdWxsIGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gc3BhY2UteC00IG92ZXJmbG93LWhpZGRlbiByb3VuZGVkLW1kIGJvcmRlciBwLTYgcHItOCBzaGFkb3ctbGcgdHJhbnNpdGlvbi1hbGwgZGF0YS1bc3dpcGU9Y2FuY2VsXTp0cmFuc2xhdGUteC0wIGRhdGEtW3N3aXBlPWVuZF06dHJhbnNsYXRlLXgtW3ZhcigtLXJhZGl4LXRvYXN0LXN3aXBlLWVuZC14KV0gZGF0YS1bc3dpcGU9bW92ZV06dHJhbnNsYXRlLXgtW3ZhcigtLXJhZGl4LXRvYXN0LXN3aXBlLW1vdmUteCldIGRhdGEtW3N3aXBlPW1vdmVdOnRyYW5zaXRpb24tbm9uZSBkYXRhLVtzdGF0ZT1vcGVuXTphbmltYXRlLWluIGRhdGEtW3N0YXRlPWNsb3NlZF06YW5pbWF0ZS1vdXQgZGF0YS1bc3dpcGU9ZW5kXTphbmltYXRlLW91dCBkYXRhLVtzdGF0ZT1jbG9zZWRdOmZhZGUtb3V0LTgwIGRhdGEtW3N0YXRlPWNsb3NlZF06c2xpZGUtb3V0LXRvLXJpZ2h0LWZ1bGwgZGF0YS1bc3RhdGU9b3Blbl06c2xpZGUtaW4tZnJvbS10b3AtZnVsbCBkYXRhLVtzdGF0ZT1vcGVuXTpzbTpzbGlkZS1pbi1mcm9tLWJvdHRvbS1mdWxsXCIsXG4gIHtcbiAgICB2YXJpYW50czoge1xuICAgICAgdmFyaWFudDoge1xuICAgICAgICBkZWZhdWx0OiBcImJvcmRlciBiZy1iYWNrZ3JvdW5kIHRleHQtZm9yZWdyb3VuZFwiLFxuICAgICAgICBkZXN0cnVjdGl2ZTpcbiAgICAgICAgICBcImRlc3RydWN0aXZlIGJvcmRlci1kZXN0cnVjdGl2ZSBiZy1kZXN0cnVjdGl2ZSB0ZXh0LWRlc3RydWN0aXZlLWZvcmVncm91bmRcIixcbiAgICAgIH0sXG4gICAgfSxcbiAgICBkZWZhdWx0VmFyaWFudHM6IHtcbiAgICAgIHZhcmlhbnQ6IFwiZGVmYXVsdFwiLFxuICAgIH0sXG4gIH1cbilcblxuY29uc3QgVG9hc3QgPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBSZWFjdC5FbGVtZW50UmVmPHR5cGVvZiBUb2FzdFByaW1pdGl2ZXMuUm9vdD4sXG4gIFJlYWN0LkNvbXBvbmVudFByb3BzV2l0aG91dFJlZjx0eXBlb2YgVG9hc3RQcmltaXRpdmVzLlJvb3Q+ICZcbiAgICBWYXJpYW50UHJvcHM8dHlwZW9mIHRvYXN0VmFyaWFudHM+XG4+KCh7IGNsYXNzTmFtZSwgdmFyaWFudCwgLi4ucHJvcHMgfSwgcmVmKSA9PiB7XG4gIHJldHVybiAoXG4gICAgPFRvYXN0UHJpbWl0aXZlcy5Sb290XG4gICAgICByZWY9e3JlZn1cbiAgICAgIGNsYXNzTmFtZT17Y24odG9hc3RWYXJpYW50cyh7IHZhcmlhbnQgfSksIGNsYXNzTmFtZSl9XG4gICAgICB7Li4ucHJvcHN9XG4gICAgLz5cbiAgKVxufSlcblRvYXN0LmRpc3BsYXlOYW1lID0gVG9hc3RQcmltaXRpdmVzLlJvb3QuZGlzcGxheU5hbWVcblxuY29uc3QgVG9hc3RBY3Rpb24gPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBSZWFjdC5FbGVtZW50UmVmPHR5cGVvZiBUb2FzdFByaW1pdGl2ZXMuQWN0aW9uPixcbiAgUmVhY3QuQ29tcG9uZW50UHJvcHNXaXRob3V0UmVmPHR5cGVvZiBUb2FzdFByaW1pdGl2ZXMuQWN0aW9uPlxuPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8VG9hc3RQcmltaXRpdmVzLkFjdGlvblxuICAgIHJlZj17cmVmfVxuICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICBcImlubGluZS1mbGV4IGgtOCBzaHJpbmstMCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcm91bmRlZC1tZCBib3JkZXIgYmctdHJhbnNwYXJlbnQgcHgtMyB0ZXh0LXNtIGZvbnQtbWVkaXVtIHJpbmctb2Zmc2V0LWJhY2tncm91bmQgdHJhbnNpdGlvbi1jb2xvcnMgaG92ZXI6Ymctc2Vjb25kYXJ5IGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1yaW5nIGZvY3VzOnJpbmctb2Zmc2V0LTIgZGlzYWJsZWQ6cG9pbnRlci1ldmVudHMtbm9uZSBkaXNhYmxlZDpvcGFjaXR5LTUwIGdyb3VwLVsuZGVzdHJ1Y3RpdmVdOmJvcmRlci1tdXRlZC80MCBncm91cC1bLmRlc3RydWN0aXZlXTpob3Zlcjpib3JkZXItZGVzdHJ1Y3RpdmUvMzAgZ3JvdXAtWy5kZXN0cnVjdGl2ZV06aG92ZXI6YmctZGVzdHJ1Y3RpdmUgZ3JvdXAtWy5kZXN0cnVjdGl2ZV06aG92ZXI6dGV4dC1kZXN0cnVjdGl2ZS1mb3JlZ3JvdW5kIGdyb3VwLVsuZGVzdHJ1Y3RpdmVdOmZvY3VzOnJpbmctZGVzdHJ1Y3RpdmVcIixcbiAgICAgIGNsYXNzTmFtZVxuICAgICl9XG4gICAgey4uLnByb3BzfVxuICAvPlxuKSlcblRvYXN0QWN0aW9uLmRpc3BsYXlOYW1lID0gVG9hc3RQcmltaXRpdmVzLkFjdGlvbi5kaXNwbGF5TmFtZVxuXG5jb25zdCBUb2FzdENsb3NlID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgUmVhY3QuRWxlbWVudFJlZjx0eXBlb2YgVG9hc3RQcmltaXRpdmVzLkNsb3NlPixcbiAgUmVhY3QuQ29tcG9uZW50UHJvcHNXaXRob3V0UmVmPHR5cGVvZiBUb2FzdFByaW1pdGl2ZXMuQ2xvc2U+XG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXG4gIDxUb2FzdFByaW1pdGl2ZXMuQ2xvc2VcbiAgICByZWY9e3JlZn1cbiAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgXCJhYnNvbHV0ZSByaWdodC0yIHRvcC0yIHJvdW5kZWQtbWQgcC0xIHRleHQtZm9yZWdyb3VuZC81MCBvcGFjaXR5LTAgdHJhbnNpdGlvbi1vcGFjaXR5IGhvdmVyOnRleHQtZm9yZWdyb3VuZCBmb2N1czpvcGFjaXR5LTEwMCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGdyb3VwLWhvdmVyOm9wYWNpdHktMTAwIGdyb3VwLVsuZGVzdHJ1Y3RpdmVdOnRleHQtcmVkLTMwMCBncm91cC1bLmRlc3RydWN0aXZlXTpob3Zlcjp0ZXh0LXJlZC01MCBncm91cC1bLmRlc3RydWN0aXZlXTpmb2N1czpyaW5nLXJlZC00MDAgZ3JvdXAtWy5kZXN0cnVjdGl2ZV06Zm9jdXM6cmluZy1vZmZzZXQtcmVkLTYwMFwiLFxuICAgICAgY2xhc3NOYW1lXG4gICAgKX1cbiAgICB0b2FzdC1jbG9zZT1cIlwiXG4gICAgey4uLnByb3BzfVxuICA+XG4gICAgPFggY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gIDwvVG9hc3RQcmltaXRpdmVzLkNsb3NlPlxuKSlcblRvYXN0Q2xvc2UuZGlzcGxheU5hbWUgPSBUb2FzdFByaW1pdGl2ZXMuQ2xvc2UuZGlzcGxheU5hbWVcblxuY29uc3QgVG9hc3RUaXRsZSA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIFJlYWN0LkVsZW1lbnRSZWY8dHlwZW9mIFRvYXN0UHJpbWl0aXZlcy5UaXRsZT4sXG4gIFJlYWN0LkNvbXBvbmVudFByb3BzV2l0aG91dFJlZjx0eXBlb2YgVG9hc3RQcmltaXRpdmVzLlRpdGxlPlxuPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8VG9hc3RQcmltaXRpdmVzLlRpdGxlXG4gICAgcmVmPXtyZWZ9XG4gICAgY2xhc3NOYW1lPXtjbihcInRleHQtc20gZm9udC1zZW1pYm9sZFwiLCBjbGFzc05hbWUpfVxuICAgIHsuLi5wcm9wc31cbiAgLz5cbikpXG5Ub2FzdFRpdGxlLmRpc3BsYXlOYW1lID0gVG9hc3RQcmltaXRpdmVzLlRpdGxlLmRpc3BsYXlOYW1lXG5cbmNvbnN0IFRvYXN0RGVzY3JpcHRpb24gPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBSZWFjdC5FbGVtZW50UmVmPHR5cGVvZiBUb2FzdFByaW1pdGl2ZXMuRGVzY3JpcHRpb24+LFxuICBSZWFjdC5Db21wb25lbnRQcm9wc1dpdGhvdXRSZWY8dHlwZW9mIFRvYXN0UHJpbWl0aXZlcy5EZXNjcmlwdGlvbj5cbj4oKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgPFRvYXN0UHJpbWl0aXZlcy5EZXNjcmlwdGlvblxuICAgIHJlZj17cmVmfVxuICAgIGNsYXNzTmFtZT17Y24oXCJ0ZXh0LXNtIG9wYWNpdHktOTBcIiwgY2xhc3NOYW1lKX1cbiAgICB7Li4ucHJvcHN9XG4gIC8+XG4pKVxuVG9hc3REZXNjcmlwdGlvbi5kaXNwbGF5TmFtZSA9IFRvYXN0UHJpbWl0aXZlcy5EZXNjcmlwdGlvbi5kaXNwbGF5TmFtZVxuXG50eXBlIFRvYXN0UHJvcHMgPSBSZWFjdC5Db21wb25lbnRQcm9wc1dpdGhvdXRSZWY8dHlwZW9mIFRvYXN0PlxuXG50eXBlIFRvYXN0QWN0aW9uRWxlbWVudCA9IFJlYWN0LlJlYWN0RWxlbWVudDx0eXBlb2YgVG9hc3RBY3Rpb24+XG5cbmV4cG9ydCB7XG4gIHR5cGUgVG9hc3RQcm9wcyxcbiAgdHlwZSBUb2FzdEFjdGlvbkVsZW1lbnQsXG4gIFRvYXN0UHJvdmlkZXIsXG4gIFRvYXN0Vmlld3BvcnQsXG4gIFRvYXN0LFxuICBUb2FzdFRpdGxlLFxuICBUb2FzdERlc2NyaXB0aW9uLFxuICBUb2FzdENsb3NlLFxuICBUb2FzdEFjdGlvbixcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIlRvYXN0UHJpbWl0aXZlcyIsImN2YSIsIlgiLCJjbiIsIlRvYXN0UHJvdmlkZXIiLCJQcm92aWRlciIsIlRvYXN0Vmlld3BvcnQiLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwicHJvcHMiLCJyZWYiLCJWaWV3cG9ydCIsImRpc3BsYXlOYW1lIiwidG9hc3RWYXJpYW50cyIsInZhcmlhbnRzIiwidmFyaWFudCIsImRlZmF1bHQiLCJkZXN0cnVjdGl2ZSIsImRlZmF1bHRWYXJpYW50cyIsIlRvYXN0IiwiUm9vdCIsIlRvYXN0QWN0aW9uIiwiQWN0aW9uIiwiVG9hc3RDbG9zZSIsIkNsb3NlIiwidG9hc3QtY2xvc2UiLCJUb2FzdFRpdGxlIiwiVGl0bGUiLCJUb2FzdERlc2NyaXB0aW9uIiwiRGVzY3JpcHRpb24iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/toast.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/toaster.tsx":
/*!***********************************!*\
  !*** ./components/ui/toaster.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Toaster)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/toast */ \"(ssr)/./components/ui/toast.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(ssr)/./components/ui/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ Toaster auto */ \n\n\nfunction Toaster() {\n    const { toasts } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastProvider, {\n        children: [\n            toasts.map(function({ id, title, description, action, ...props }) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.Toast, {\n                    ...props,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-1\",\n                            children: [\n                                title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastTitle, {\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/components/ui/toaster.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 25\n                                }, this),\n                                description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastDescription, {\n                                    children: description\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/components/ui/toaster.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/ui/toaster.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 13\n                        }, this),\n                        action,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastClose, {}, void 0, false, {\n                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/ui/toaster.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, id, true, {\n                    fileName: \"/Volumes/Apps/Websites/streamit-main/components/ui/toaster.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 11\n                }, this);\n            }),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastViewport, {}, void 0, false, {\n                fileName: \"/Volumes/Apps/Websites/streamit-main/components/ui/toaster.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Volumes/Apps/Websites/streamit-main/components/ui/toaster.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/toaster.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/use-toast.ts":
/*!************************************!*\
  !*** ./components/ui/use-toast.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reducer: () => (/* binding */ reducer),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst TOAST_LIMIT = 1;\nconst TOAST_REMOVE_DELAY = 1000000;\nconst actionTypes = {\n    ADD_TOAST: \"ADD_TOAST\",\n    UPDATE_TOAST: \"UPDATE_TOAST\",\n    DISMISS_TOAST: \"DISMISS_TOAST\",\n    REMOVE_TOAST: \"REMOVE_TOAST\"\n};\nlet count = 0;\nfunction genId() {\n    count = (count + 1) % Number.MAX_SAFE_INTEGER;\n    return count.toString();\n}\nconst toastTimeouts = new Map();\nconst addToRemoveQueue = (toastId)=>{\n    if (toastTimeouts.has(toastId)) {\n        return;\n    }\n    const timeout = setTimeout(()=>{\n        toastTimeouts.delete(toastId);\n        dispatch({\n            type: \"REMOVE_TOAST\",\n            toastId: toastId\n        });\n    }, TOAST_REMOVE_DELAY);\n    toastTimeouts.set(toastId, timeout);\n};\nconst reducer = (state, action)=>{\n    switch(action.type){\n        case \"ADD_TOAST\":\n            return {\n                ...state,\n                toasts: [\n                    action.toast,\n                    ...state.toasts\n                ].slice(0, TOAST_LIMIT)\n            };\n        case \"UPDATE_TOAST\":\n            return {\n                ...state,\n                toasts: state.toasts.map((t)=>t.id === action.toast.id ? {\n                        ...t,\n                        ...action.toast\n                    } : t)\n            };\n        case \"DISMISS_TOAST\":\n            {\n                const { toastId } = action;\n                if (toastId) {\n                    addToRemoveQueue(toastId);\n                } else {\n                    state.toasts.forEach((toast)=>{\n                        addToRemoveQueue(toast.id);\n                    });\n                }\n                return {\n                    ...state,\n                    toasts: state.toasts.map((t)=>t.id === toastId || toastId === undefined ? {\n                            ...t,\n                            open: false\n                        } : t)\n                };\n            }\n        case \"REMOVE_TOAST\":\n            if (action.toastId === undefined) {\n                return {\n                    ...state,\n                    toasts: []\n                };\n            }\n            return {\n                ...state,\n                toasts: state.toasts.filter((t)=>t.id !== action.toastId)\n            };\n    }\n};\nconst listeners = [];\nlet memoryState = {\n    toasts: []\n};\nfunction dispatch(action) {\n    memoryState = reducer(memoryState, action);\n    listeners.forEach((listener)=>{\n        listener(memoryState);\n    });\n}\nfunction toast({ ...props }) {\n    const id = genId();\n    const update = (props)=>dispatch({\n            type: \"UPDATE_TOAST\",\n            toast: {\n                ...props,\n                id\n            }\n        });\n    const dismiss = ()=>dispatch({\n            type: \"DISMISS_TOAST\",\n            toastId: id\n        });\n    dispatch({\n        type: \"ADD_TOAST\",\n        toast: {\n            ...props,\n            id,\n            open: true,\n            onOpenChange: (open)=>{\n                if (!open) dismiss();\n            }\n        }\n    });\n    return {\n        id: id,\n        dismiss,\n        update\n    };\n}\nfunction useToast() {\n    const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(memoryState);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        listeners.push(setState);\n        return ()=>{\n            const index = listeners.indexOf(setState);\n            if (index > -1) {\n                listeners.splice(index, 1);\n            }\n        };\n    }, [\n        state\n    ]);\n    return {\n        ...state,\n        toast,\n        dismiss: (toastId)=>dispatch({\n                type: \"DISMISS_TOAST\",\n                toastId\n            })\n    };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/use-toast.ts\n");

/***/ }),

/***/ "(ssr)/./lib/store.ts":
/*!**********************!*\
  !*** ./lib/store.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useVideoCallStore: () => (/* binding */ useVideoCallStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n\n\nconst initialState = {\n    roomId: null,\n    isConnected: false,\n    roomLocked: false,\n    currentUser: null,\n    participants: new Map(),\n    localStream: null,\n    isAudioMuted: false,\n    isVideoMuted: false,\n    isScreenSharing: false,\n    messages: [],\n    unreadCount: 0,\n    messageHistory: new Map(),\n    isChatOpen: false,\n    isSettingsOpen: false,\n    securitySettings: {\n        encryptionEnabled: true,\n        antiSpamEnabled: true,\n        maxMessagesPerMinute: 10,\n        allowScreenShare: true,\n        allowFileSharing: false,\n        requireApprovalToJoin: false\n    },\n    adminControls: {\n        canMuteAll: true,\n        canMuteParticipant: true,\n        canRemoveParticipant: true,\n        canControlCamera: true,\n        canManageRoles: true\n    },\n    blockedUsers: new Set(),\n    spamDetection: new Map()\n};\nconst useVideoCallStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.devtools)((set, get)=>({\n        ...initialState,\n        // Room actions\n        setRoomId: (roomId)=>set({\n                roomId\n            }),\n        setConnected: (isConnected)=>set({\n                isConnected\n            }),\n        // User actions\n        setCurrentUser: (currentUser)=>set({\n                currentUser\n            }),\n        addParticipant: (participant)=>{\n            const participants = new Map(get().participants);\n            participants.set(participant.id, participant);\n            set({\n                participants\n            });\n        },\n        removeParticipant: (participantId)=>{\n            const participants = new Map(get().participants);\n            participants.delete(participantId);\n            set({\n                participants\n            });\n        },\n        updateParticipant: (participantId, updates)=>{\n            const participants = new Map(get().participants);\n            const participant = participants.get(participantId);\n            if (participant) {\n                participants.set(participantId, {\n                    ...participant,\n                    ...updates\n                });\n                set({\n                    participants\n                });\n            }\n        },\n        // Media actions\n        setLocalStream: (localStream)=>set({\n                localStream\n            }),\n        toggleAudio: ()=>{\n            const { isAudioMuted, localStream } = get();\n            if (localStream) {\n                localStream.getAudioTracks().forEach((track)=>{\n                    track.enabled = isAudioMuted;\n                });\n            }\n            set({\n                isAudioMuted: !isAudioMuted\n            });\n        },\n        toggleVideo: ()=>{\n            const { isVideoMuted, localStream } = get();\n            if (localStream) {\n                localStream.getVideoTracks().forEach((track)=>{\n                    track.enabled = isVideoMuted;\n                });\n            }\n            set({\n                isVideoMuted: !isVideoMuted\n            });\n        },\n        toggleScreenShare: ()=>{\n            set((state)=>({\n                    isScreenSharing: !state.isScreenSharing\n                }));\n        },\n        // Chat actions\n        addMessage: (message)=>{\n            const { messages, securitySettings, spamDetection } = get();\n            // Anti-spam check\n            if (securitySettings.antiSpamEnabled) {\n                const now = Date.now();\n                const userSpam = spamDetection.get(message.userId) || {\n                    count: 0,\n                    lastReset: now\n                };\n                // Reset count if more than a minute has passed\n                if (now - userSpam.lastReset > 60000) {\n                    userSpam.count = 0;\n                    userSpam.lastReset = now;\n                }\n                userSpam.count++;\n                spamDetection.set(message.userId, userSpam);\n                // Mark as spam if exceeding limit\n                if (userSpam.count > securitySettings.maxMessagesPerMinute) {\n                    message.isSpam = true;\n                }\n            }\n            set({\n                messages: [\n                    ...messages,\n                    message\n                ],\n                unreadCount: get().isChatOpen ? 0 : get().unreadCount + 1,\n                spamDetection: new Map(spamDetection)\n            });\n        },\n        clearUnreadCount: ()=>set({\n                unreadCount: 0\n            }),\n        // UI actions\n        toggleChat: ()=>{\n            const isChatOpen = !get().isChatOpen;\n            set({\n                isChatOpen,\n                unreadCount: isChatOpen ? 0 : get().unreadCount\n            });\n        },\n        toggleSettings: ()=>set((state)=>({\n                    isSettingsOpen: !state.isSettingsOpen\n                })),\n        // Admin actions\n        muteParticipant: (participantId)=>{\n            get().updateParticipant(participantId, {\n                isAudioMuted: true\n            });\n        },\n        muteAllParticipants: ()=>{\n            const { participants } = get();\n            participants.forEach((_, id)=>{\n                get().updateParticipant(id, {\n                    isAudioMuted: true\n                });\n            });\n        },\n        unmuteAllParticipants: ()=>{\n            const { participants } = get();\n            participants.forEach((_, id)=>{\n                get().updateParticipant(id, {\n                    isAudioMuted: false\n                });\n            });\n        },\n        promoteToCoHost: (participantId)=>{\n            get().updateParticipant(participantId, {\n                role: \"co-host\"\n            });\n        },\n        demoteFromCoHost: (participantId)=>{\n            get().updateParticipant(participantId, {\n                role: \"participant\"\n            });\n        },\n        blockUser: (participantId)=>{\n            const blockedUsers = new Set(get().blockedUsers);\n            blockedUsers.add(participantId);\n            set({\n                blockedUsers\n            });\n            get().removeParticipant(participantId);\n        },\n        unblockUser: (participantId)=>{\n            const blockedUsers = new Set(get().blockedUsers);\n            blockedUsers.delete(participantId);\n            set({\n                blockedUsers\n            });\n        },\n        lockRoom: ()=>set({\n                roomLocked: true\n            }),\n        unlockRoom: ()=>set({\n                roomLocked: false\n            }),\n        updateSecuritySettings: (settings)=>{\n            set((state)=>({\n                    securitySettings: {\n                        ...state.securitySettings,\n                        ...settings\n                    }\n                }));\n        },\n        // Utility actions\n        reset: ()=>set(initialState)\n    }), {\n    name: \"video-call-store\"\n}));\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useVideoCallStore);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvc3RvcmUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFnQztBQUNhO0FBeUc3QyxNQUFNRSxlQUFlO0lBQ25CQyxRQUFRO0lBQ1JDLGFBQWE7SUFDYkMsWUFBWTtJQUNaQyxhQUFhO0lBQ2JDLGNBQWMsSUFBSUM7SUFDbEJDLGFBQWE7SUFDYkMsY0FBYztJQUNkQyxjQUFjO0lBQ2RDLGlCQUFpQjtJQUNqQkMsVUFBVSxFQUFFO0lBQ1pDLGFBQWE7SUFDYkMsZ0JBQWdCLElBQUlQO0lBQ3BCUSxZQUFZO0lBQ1pDLGdCQUFnQjtJQUNoQkMsa0JBQWtCO1FBQ2hCQyxtQkFBbUI7UUFDbkJDLGlCQUFpQjtRQUNqQkMsc0JBQXNCO1FBQ3RCQyxrQkFBa0I7UUFDbEJDLGtCQUFrQjtRQUNsQkMsdUJBQXVCO0lBQ3pCO0lBQ0FDLGVBQWU7UUFDYkMsWUFBWTtRQUNaQyxvQkFBb0I7UUFDcEJDLHNCQUFzQjtRQUN0QkMsa0JBQWtCO1FBQ2xCQyxnQkFBZ0I7SUFDbEI7SUFDQUMsY0FBYyxJQUFJQztJQUNsQkMsZUFBZSxJQUFJekI7QUFDckI7QUFFTyxNQUFNMEIsb0JBQW9CbEMsK0NBQU1BLEdBQ3JDQyw0REFBUUEsQ0FDTixDQUFDa0MsS0FBS0MsTUFBUztRQUNiLEdBQUdsQyxZQUFZO1FBRWYsZUFBZTtRQUNmbUMsV0FBVyxDQUFDbEMsU0FBbUJnQyxJQUFJO2dCQUFFaEM7WUFBTztRQUM1Q21DLGNBQWMsQ0FBQ2xDLGNBQXlCK0IsSUFBSTtnQkFBRS9CO1lBQVk7UUFFMUQsZUFBZTtRQUNmbUMsZ0JBQWdCLENBQUNqQyxjQUE2QjZCLElBQUk7Z0JBQUU3QjtZQUFZO1FBRWhFa0MsZ0JBQWdCLENBQUNDO1lBQ2YsTUFBTWxDLGVBQWUsSUFBSUMsSUFBSTRCLE1BQU03QixZQUFZO1lBQy9DQSxhQUFhNEIsR0FBRyxDQUFDTSxZQUFZQyxFQUFFLEVBQUVEO1lBQ2pDTixJQUFJO2dCQUFFNUI7WUFBYTtRQUNyQjtRQUVBb0MsbUJBQW1CLENBQUNDO1lBQ2xCLE1BQU1yQyxlQUFlLElBQUlDLElBQUk0QixNQUFNN0IsWUFBWTtZQUMvQ0EsYUFBYXNDLE1BQU0sQ0FBQ0Q7WUFDcEJULElBQUk7Z0JBQUU1QjtZQUFhO1FBQ3JCO1FBRUF1QyxtQkFBbUIsQ0FBQ0YsZUFBdUJHO1lBQ3pDLE1BQU14QyxlQUFlLElBQUlDLElBQUk0QixNQUFNN0IsWUFBWTtZQUMvQyxNQUFNa0MsY0FBY2xDLGFBQWE2QixHQUFHLENBQUNRO1lBQ3JDLElBQUlILGFBQWE7Z0JBQ2ZsQyxhQUFhNEIsR0FBRyxDQUFDUyxlQUFlO29CQUFFLEdBQUdILFdBQVc7b0JBQUUsR0FBR00sT0FBTztnQkFBQztnQkFDN0RaLElBQUk7b0JBQUU1QjtnQkFBYTtZQUNyQjtRQUNGO1FBRUEsZ0JBQWdCO1FBQ2hCeUMsZ0JBQWdCLENBQUN2QyxjQUFvQzBCLElBQUk7Z0JBQUUxQjtZQUFZO1FBRXZFd0MsYUFBYTtZQUNYLE1BQU0sRUFBRXZDLFlBQVksRUFBRUQsV0FBVyxFQUFFLEdBQUcyQjtZQUN0QyxJQUFJM0IsYUFBYTtnQkFDZkEsWUFBWXlDLGNBQWMsR0FBR0MsT0FBTyxDQUFDQyxDQUFBQTtvQkFDbkNBLE1BQU1DLE9BQU8sR0FBRzNDO2dCQUNsQjtZQUNGO1lBQ0F5QixJQUFJO2dCQUFFekIsY0FBYyxDQUFDQTtZQUFhO1FBQ3BDO1FBRUE0QyxhQUFhO1lBQ1gsTUFBTSxFQUFFM0MsWUFBWSxFQUFFRixXQUFXLEVBQUUsR0FBRzJCO1lBQ3RDLElBQUkzQixhQUFhO2dCQUNmQSxZQUFZOEMsY0FBYyxHQUFHSixPQUFPLENBQUNDLENBQUFBO29CQUNuQ0EsTUFBTUMsT0FBTyxHQUFHMUM7Z0JBQ2xCO1lBQ0Y7WUFDQXdCLElBQUk7Z0JBQUV4QixjQUFjLENBQUNBO1lBQWE7UUFDcEM7UUFFQTZDLG1CQUFtQjtZQUNqQnJCLElBQUlzQixDQUFBQSxRQUFVO29CQUFFN0MsaUJBQWlCLENBQUM2QyxNQUFNN0MsZUFBZTtnQkFBQztRQUMxRDtRQUVBLGVBQWU7UUFDZjhDLFlBQVksQ0FBQ0M7WUFDWCxNQUFNLEVBQUU5QyxRQUFRLEVBQUVLLGdCQUFnQixFQUFFZSxhQUFhLEVBQUUsR0FBR0c7WUFFdEQsa0JBQWtCO1lBQ2xCLElBQUlsQixpQkFBaUJFLGVBQWUsRUFBRTtnQkFDcEMsTUFBTXdDLE1BQU1DLEtBQUtELEdBQUc7Z0JBQ3BCLE1BQU1FLFdBQVc3QixjQUFjRyxHQUFHLENBQUN1QixRQUFRSSxNQUFNLEtBQUs7b0JBQUVDLE9BQU87b0JBQUdDLFdBQVdMO2dCQUFJO2dCQUVqRiwrQ0FBK0M7Z0JBQy9DLElBQUlBLE1BQU1FLFNBQVNHLFNBQVMsR0FBRyxPQUFPO29CQUNwQ0gsU0FBU0UsS0FBSyxHQUFHO29CQUNqQkYsU0FBU0csU0FBUyxHQUFHTDtnQkFDdkI7Z0JBRUFFLFNBQVNFLEtBQUs7Z0JBQ2QvQixjQUFjRSxHQUFHLENBQUN3QixRQUFRSSxNQUFNLEVBQUVEO2dCQUVsQyxrQ0FBa0M7Z0JBQ2xDLElBQUlBLFNBQVNFLEtBQUssR0FBRzlDLGlCQUFpQkcsb0JBQW9CLEVBQUU7b0JBQzFEc0MsUUFBUU8sTUFBTSxHQUFHO2dCQUNuQjtZQUNGO1lBRUEvQixJQUFJO2dCQUNGdEIsVUFBVTt1QkFBSUE7b0JBQVU4QztpQkFBUTtnQkFDaEM3QyxhQUFhc0IsTUFBTXBCLFVBQVUsR0FBRyxJQUFJb0IsTUFBTXRCLFdBQVcsR0FBRztnQkFDeERtQixlQUFlLElBQUl6QixJQUFJeUI7WUFDekI7UUFDRjtRQUVBa0Msa0JBQWtCLElBQU1oQyxJQUFJO2dCQUFFckIsYUFBYTtZQUFFO1FBRTdDLGFBQWE7UUFDYnNELFlBQVk7WUFDVixNQUFNcEQsYUFBYSxDQUFDb0IsTUFBTXBCLFVBQVU7WUFDcENtQixJQUFJO2dCQUNGbkI7Z0JBQ0FGLGFBQWFFLGFBQWEsSUFBSW9CLE1BQU10QixXQUFXO1lBQ2pEO1FBQ0Y7UUFFQXVELGdCQUFnQixJQUFNbEMsSUFBSXNCLENBQUFBLFFBQVU7b0JBQUV4QyxnQkFBZ0IsQ0FBQ3dDLE1BQU14QyxjQUFjO2dCQUFDO1FBRTVFLGdCQUFnQjtRQUNoQnFELGlCQUFpQixDQUFDMUI7WUFDaEJSLE1BQU1VLGlCQUFpQixDQUFDRixlQUFlO2dCQUFFbEMsY0FBYztZQUFLO1FBQzlEO1FBRUE2RCxxQkFBcUI7WUFDbkIsTUFBTSxFQUFFaEUsWUFBWSxFQUFFLEdBQUc2QjtZQUN6QjdCLGFBQWE0QyxPQUFPLENBQUMsQ0FBQ3FCLEdBQUc5QjtnQkFDdkJOLE1BQU1VLGlCQUFpQixDQUFDSixJQUFJO29CQUFFaEMsY0FBYztnQkFBSztZQUNuRDtRQUNGO1FBRUErRCx1QkFBdUI7WUFDckIsTUFBTSxFQUFFbEUsWUFBWSxFQUFFLEdBQUc2QjtZQUN6QjdCLGFBQWE0QyxPQUFPLENBQUMsQ0FBQ3FCLEdBQUc5QjtnQkFDdkJOLE1BQU1VLGlCQUFpQixDQUFDSixJQUFJO29CQUFFaEMsY0FBYztnQkFBTTtZQUNwRDtRQUNGO1FBRUFnRSxpQkFBaUIsQ0FBQzlCO1lBQ2hCUixNQUFNVSxpQkFBaUIsQ0FBQ0YsZUFBZTtnQkFBRStCLE1BQU07WUFBVTtRQUMzRDtRQUVBQyxrQkFBa0IsQ0FBQ2hDO1lBQ2pCUixNQUFNVSxpQkFBaUIsQ0FBQ0YsZUFBZTtnQkFBRStCLE1BQU07WUFBYztRQUMvRDtRQUVBRSxXQUFXLENBQUNqQztZQUNWLE1BQU1iLGVBQWUsSUFBSUMsSUFBSUksTUFBTUwsWUFBWTtZQUMvQ0EsYUFBYStDLEdBQUcsQ0FBQ2xDO1lBQ2pCVCxJQUFJO2dCQUFFSjtZQUFhO1lBQ25CSyxNQUFNTyxpQkFBaUIsQ0FBQ0M7UUFDMUI7UUFFQW1DLGFBQWEsQ0FBQ25DO1lBQ1osTUFBTWIsZUFBZSxJQUFJQyxJQUFJSSxNQUFNTCxZQUFZO1lBQy9DQSxhQUFhYyxNQUFNLENBQUNEO1lBQ3BCVCxJQUFJO2dCQUFFSjtZQUFhO1FBQ3JCO1FBRUFpRCxVQUFVLElBQU03QyxJQUFJO2dCQUFFOUIsWUFBWTtZQUFLO1FBQ3ZDNEUsWUFBWSxJQUFNOUMsSUFBSTtnQkFBRTlCLFlBQVk7WUFBTTtRQUUxQzZFLHdCQUF3QixDQUFDQztZQUN2QmhELElBQUlzQixDQUFBQSxRQUFVO29CQUNadkMsa0JBQWtCO3dCQUFFLEdBQUd1QyxNQUFNdkMsZ0JBQWdCO3dCQUFFLEdBQUdpRSxRQUFRO29CQUFDO2dCQUM3RDtRQUNGO1FBRUEsa0JBQWtCO1FBQ2xCQyxPQUFPLElBQU1qRCxJQUFJakM7SUFDbkIsSUFDQTtJQUNFbUYsTUFBTTtBQUNSLElBRUg7QUFFRCxpRUFBZW5ELGlCQUFpQkEsRUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL3N0cmVhbWl0LXByby1uZXh0anMvLi9saWIvc3RvcmUudHM/ZTg0MiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGUgfSBmcm9tICd6dXN0YW5kJ1xuaW1wb3J0IHsgZGV2dG9vbHMgfSBmcm9tICd6dXN0YW5kL21pZGRsZXdhcmUnXG5cbmV4cG9ydCBpbnRlcmZhY2UgUGFydGljaXBhbnQge1xuICBpZDogc3RyaW5nXG4gIG5hbWU6IHN0cmluZ1xuICBzdHJlYW0/OiBNZWRpYVN0cmVhbVxuICBpc0F1ZGlvTXV0ZWQ6IGJvb2xlYW5cbiAgaXNWaWRlb011dGVkOiBib29sZWFuXG4gIGlzU2NyZWVuU2hhcmluZzogYm9vbGVhblxuICByb2xlOiAnaG9zdCcgfCAnY28taG9zdCcgfCAncGFydGljaXBhbnQnXG4gIGlzSGFuZFJhaXNlZDogYm9vbGVhblxuICBqb2luZWRBdDogRGF0ZVxuICBsYXN0QWN0aXZpdHk6IERhdGVcbn1cblxuZXhwb3J0IGludGVyZmFjZSBDaGF0TWVzc2FnZSB7XG4gIGlkOiBzdHJpbmdcbiAgdXNlcklkOiBzdHJpbmdcbiAgdXNlck5hbWU6IHN0cmluZ1xuICBtZXNzYWdlOiBzdHJpbmdcbiAgdGltZXN0YW1wOiBEYXRlXG4gIGlzU3BhbT86IGJvb2xlYW5cbiAgaXNCbG9ja2VkPzogYm9vbGVhblxufVxuXG5leHBvcnQgaW50ZXJmYWNlIFNlY3VyaXR5U2V0dGluZ3Mge1xuICBlbmNyeXB0aW9uRW5hYmxlZDogYm9vbGVhblxuICBhbnRpU3BhbUVuYWJsZWQ6IGJvb2xlYW5cbiAgbWF4TWVzc2FnZXNQZXJNaW51dGU6IG51bWJlclxuICBhbGxvd1NjcmVlblNoYXJlOiBib29sZWFuXG4gIGFsbG93RmlsZVNoYXJpbmc6IGJvb2xlYW5cbiAgcmVxdWlyZUFwcHJvdmFsVG9Kb2luOiBib29sZWFuXG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgQWRtaW5Db250cm9scyB7XG4gIGNhbk11dGVBbGw6IGJvb2xlYW5cbiAgY2FuTXV0ZVBhcnRpY2lwYW50OiBib29sZWFuXG4gIGNhblJlbW92ZVBhcnRpY2lwYW50OiBib29sZWFuXG4gIGNhbkNvbnRyb2xDYW1lcmE6IGJvb2xlYW5cbiAgY2FuTWFuYWdlUm9sZXM6IGJvb2xlYW5cbn1cblxuZXhwb3J0IGludGVyZmFjZSBWaWRlb0NhbGxTdGF0ZSB7XG4gIC8vIFJvb20gc3RhdGVcbiAgcm9vbUlkOiBzdHJpbmcgfCBudWxsXG4gIGlzQ29ubmVjdGVkOiBib29sZWFuXG4gIHJvb21Mb2NrZWQ6IGJvb2xlYW5cblxuICAvLyBVc2VyIHN0YXRlXG4gIGN1cnJlbnRVc2VyOiBQYXJ0aWNpcGFudCB8IG51bGxcbiAgcGFydGljaXBhbnRzOiBNYXA8c3RyaW5nLCBQYXJ0aWNpcGFudD5cblxuICAvLyBNZWRpYSBzdGF0ZVxuICBsb2NhbFN0cmVhbTogTWVkaWFTdHJlYW0gfCBudWxsXG4gIGlzQXVkaW9NdXRlZDogYm9vbGVhblxuICBpc1ZpZGVvTXV0ZWQ6IGJvb2xlYW5cbiAgaXNTY3JlZW5TaGFyaW5nOiBib29sZWFuXG5cbiAgLy8gQ2hhdCBzdGF0ZVxuICBtZXNzYWdlczogQ2hhdE1lc3NhZ2VbXVxuICB1bnJlYWRDb3VudDogbnVtYmVyXG4gIG1lc3NhZ2VIaXN0b3J5OiBNYXA8c3RyaW5nLCBudW1iZXI+XG5cbiAgLy8gVUkgc3RhdGVcbiAgaXNDaGF0T3BlbjogYm9vbGVhblxuICBpc1NldHRpbmdzT3BlbjogYm9vbGVhblxuXG4gIC8vIFNlY3VyaXR5ICYgQWRtaW5cbiAgc2VjdXJpdHlTZXR0aW5nczogU2VjdXJpdHlTZXR0aW5nc1xuICBhZG1pbkNvbnRyb2xzOiBBZG1pbkNvbnRyb2xzXG4gIGJsb2NrZWRVc2VyczogU2V0PHN0cmluZz5cbiAgc3BhbURldGVjdGlvbjogTWFwPHN0cmluZywgeyBjb3VudDogbnVtYmVyLCBsYXN0UmVzZXQ6IG51bWJlciB9PlxuXG4gIC8vIEFjdGlvbnNcbiAgc2V0Um9vbUlkOiAocm9vbUlkOiBzdHJpbmcpID0+IHZvaWRcbiAgc2V0Q29ubmVjdGVkOiAoY29ubmVjdGVkOiBib29sZWFuKSA9PiB2b2lkXG4gIHNldEN1cnJlbnRVc2VyOiAodXNlcjogUGFydGljaXBhbnQpID0+IHZvaWRcbiAgYWRkUGFydGljaXBhbnQ6IChwYXJ0aWNpcGFudDogUGFydGljaXBhbnQpID0+IHZvaWRcbiAgcmVtb3ZlUGFydGljaXBhbnQ6IChwYXJ0aWNpcGFudElkOiBzdHJpbmcpID0+IHZvaWRcbiAgdXBkYXRlUGFydGljaXBhbnQ6IChwYXJ0aWNpcGFudElkOiBzdHJpbmcsIHVwZGF0ZXM6IFBhcnRpYWw8UGFydGljaXBhbnQ+KSA9PiB2b2lkXG4gIHNldExvY2FsU3RyZWFtOiAoc3RyZWFtOiBNZWRpYVN0cmVhbSB8IG51bGwpID0+IHZvaWRcbiAgdG9nZ2xlQXVkaW86ICgpID0+IHZvaWRcbiAgdG9nZ2xlVmlkZW86ICgpID0+IHZvaWRcbiAgdG9nZ2xlU2NyZWVuU2hhcmU6ICgpID0+IHZvaWRcbiAgYWRkTWVzc2FnZTogKG1lc3NhZ2U6IENoYXRNZXNzYWdlKSA9PiB2b2lkXG4gIGNsZWFyVW5yZWFkQ291bnQ6ICgpID0+IHZvaWRcbiAgdG9nZ2xlQ2hhdDogKCkgPT4gdm9pZFxuICB0b2dnbGVTZXR0aW5nczogKCkgPT4gdm9pZFxuXG4gIC8vIEFkbWluIEFjdGlvbnNcbiAgbXV0ZVBhcnRpY2lwYW50OiAocGFydGljaXBhbnRJZDogc3RyaW5nKSA9PiB2b2lkXG4gIG11dGVBbGxQYXJ0aWNpcGFudHM6ICgpID0+IHZvaWRcbiAgdW5tdXRlQWxsUGFydGljaXBhbnRzOiAoKSA9PiB2b2lkXG4gIHByb21vdGVUb0NvSG9zdDogKHBhcnRpY2lwYW50SWQ6IHN0cmluZykgPT4gdm9pZFxuICBkZW1vdGVGcm9tQ29Ib3N0OiAocGFydGljaXBhbnRJZDogc3RyaW5nKSA9PiB2b2lkXG4gIGJsb2NrVXNlcjogKHBhcnRpY2lwYW50SWQ6IHN0cmluZykgPT4gdm9pZFxuICB1bmJsb2NrVXNlcjogKHBhcnRpY2lwYW50SWQ6IHN0cmluZykgPT4gdm9pZFxuICBsb2NrUm9vbTogKCkgPT4gdm9pZFxuICB1bmxvY2tSb29tOiAoKSA9PiB2b2lkXG4gIHVwZGF0ZVNlY3VyaXR5U2V0dGluZ3M6IChzZXR0aW5nczogUGFydGlhbDxTZWN1cml0eVNldHRpbmdzPikgPT4gdm9pZFxuXG4gIC8vIFV0aWxpdHkgQWN0aW9uc1xuICByZXNldDogKCkgPT4gdm9pZFxufVxuXG5jb25zdCBpbml0aWFsU3RhdGUgPSB7XG4gIHJvb21JZDogbnVsbCxcbiAgaXNDb25uZWN0ZWQ6IGZhbHNlLFxuICByb29tTG9ja2VkOiBmYWxzZSxcbiAgY3VycmVudFVzZXI6IG51bGwsXG4gIHBhcnRpY2lwYW50czogbmV3IE1hcDxzdHJpbmcsIFBhcnRpY2lwYW50PigpLFxuICBsb2NhbFN0cmVhbTogbnVsbCxcbiAgaXNBdWRpb011dGVkOiBmYWxzZSxcbiAgaXNWaWRlb011dGVkOiBmYWxzZSxcbiAgaXNTY3JlZW5TaGFyaW5nOiBmYWxzZSxcbiAgbWVzc2FnZXM6IFtdLFxuICB1bnJlYWRDb3VudDogMCxcbiAgbWVzc2FnZUhpc3Rvcnk6IG5ldyBNYXA8c3RyaW5nLCBudW1iZXI+KCksXG4gIGlzQ2hhdE9wZW46IGZhbHNlLFxuICBpc1NldHRpbmdzT3BlbjogZmFsc2UsXG4gIHNlY3VyaXR5U2V0dGluZ3M6IHtcbiAgICBlbmNyeXB0aW9uRW5hYmxlZDogdHJ1ZSxcbiAgICBhbnRpU3BhbUVuYWJsZWQ6IHRydWUsXG4gICAgbWF4TWVzc2FnZXNQZXJNaW51dGU6IDEwLFxuICAgIGFsbG93U2NyZWVuU2hhcmU6IHRydWUsXG4gICAgYWxsb3dGaWxlU2hhcmluZzogZmFsc2UsXG4gICAgcmVxdWlyZUFwcHJvdmFsVG9Kb2luOiBmYWxzZSxcbiAgfSxcbiAgYWRtaW5Db250cm9sczoge1xuICAgIGNhbk11dGVBbGw6IHRydWUsXG4gICAgY2FuTXV0ZVBhcnRpY2lwYW50OiB0cnVlLFxuICAgIGNhblJlbW92ZVBhcnRpY2lwYW50OiB0cnVlLFxuICAgIGNhbkNvbnRyb2xDYW1lcmE6IHRydWUsXG4gICAgY2FuTWFuYWdlUm9sZXM6IHRydWUsXG4gIH0sXG4gIGJsb2NrZWRVc2VyczogbmV3IFNldDxzdHJpbmc+KCksXG4gIHNwYW1EZXRlY3Rpb246IG5ldyBNYXA8c3RyaW5nLCB7IGNvdW50OiBudW1iZXIsIGxhc3RSZXNldDogbnVtYmVyIH0+KCksXG59XG5cbmV4cG9ydCBjb25zdCB1c2VWaWRlb0NhbGxTdG9yZSA9IGNyZWF0ZTxWaWRlb0NhbGxTdGF0ZT4oKShcbiAgZGV2dG9vbHMoXG4gICAgKHNldCwgZ2V0KSA9PiAoe1xuICAgICAgLi4uaW5pdGlhbFN0YXRlLFxuXG4gICAgICAvLyBSb29tIGFjdGlvbnNcbiAgICAgIHNldFJvb21JZDogKHJvb21JZDogc3RyaW5nKSA9PiBzZXQoeyByb29tSWQgfSksXG4gICAgICBzZXRDb25uZWN0ZWQ6IChpc0Nvbm5lY3RlZDogYm9vbGVhbikgPT4gc2V0KHsgaXNDb25uZWN0ZWQgfSksXG5cbiAgICAgIC8vIFVzZXIgYWN0aW9uc1xuICAgICAgc2V0Q3VycmVudFVzZXI6IChjdXJyZW50VXNlcjogUGFydGljaXBhbnQpID0+IHNldCh7IGN1cnJlbnRVc2VyIH0pLFxuXG4gICAgICBhZGRQYXJ0aWNpcGFudDogKHBhcnRpY2lwYW50OiBQYXJ0aWNpcGFudCkgPT4ge1xuICAgICAgICBjb25zdCBwYXJ0aWNpcGFudHMgPSBuZXcgTWFwKGdldCgpLnBhcnRpY2lwYW50cylcbiAgICAgICAgcGFydGljaXBhbnRzLnNldChwYXJ0aWNpcGFudC5pZCwgcGFydGljaXBhbnQpXG4gICAgICAgIHNldCh7IHBhcnRpY2lwYW50cyB9KVxuICAgICAgfSxcblxuICAgICAgcmVtb3ZlUGFydGljaXBhbnQ6IChwYXJ0aWNpcGFudElkOiBzdHJpbmcpID0+IHtcbiAgICAgICAgY29uc3QgcGFydGljaXBhbnRzID0gbmV3IE1hcChnZXQoKS5wYXJ0aWNpcGFudHMpXG4gICAgICAgIHBhcnRpY2lwYW50cy5kZWxldGUocGFydGljaXBhbnRJZClcbiAgICAgICAgc2V0KHsgcGFydGljaXBhbnRzIH0pXG4gICAgICB9LFxuXG4gICAgICB1cGRhdGVQYXJ0aWNpcGFudDogKHBhcnRpY2lwYW50SWQ6IHN0cmluZywgdXBkYXRlczogUGFydGlhbDxQYXJ0aWNpcGFudD4pID0+IHtcbiAgICAgICAgY29uc3QgcGFydGljaXBhbnRzID0gbmV3IE1hcChnZXQoKS5wYXJ0aWNpcGFudHMpXG4gICAgICAgIGNvbnN0IHBhcnRpY2lwYW50ID0gcGFydGljaXBhbnRzLmdldChwYXJ0aWNpcGFudElkKVxuICAgICAgICBpZiAocGFydGljaXBhbnQpIHtcbiAgICAgICAgICBwYXJ0aWNpcGFudHMuc2V0KHBhcnRpY2lwYW50SWQsIHsgLi4ucGFydGljaXBhbnQsIC4uLnVwZGF0ZXMgfSlcbiAgICAgICAgICBzZXQoeyBwYXJ0aWNpcGFudHMgfSlcbiAgICAgICAgfVxuICAgICAgfSxcblxuICAgICAgLy8gTWVkaWEgYWN0aW9uc1xuICAgICAgc2V0TG9jYWxTdHJlYW06IChsb2NhbFN0cmVhbTogTWVkaWFTdHJlYW0gfCBudWxsKSA9PiBzZXQoeyBsb2NhbFN0cmVhbSB9KSxcblxuICAgICAgdG9nZ2xlQXVkaW86ICgpID0+IHtcbiAgICAgICAgY29uc3QgeyBpc0F1ZGlvTXV0ZWQsIGxvY2FsU3RyZWFtIH0gPSBnZXQoKVxuICAgICAgICBpZiAobG9jYWxTdHJlYW0pIHtcbiAgICAgICAgICBsb2NhbFN0cmVhbS5nZXRBdWRpb1RyYWNrcygpLmZvckVhY2godHJhY2sgPT4ge1xuICAgICAgICAgICAgdHJhY2suZW5hYmxlZCA9IGlzQXVkaW9NdXRlZFxuICAgICAgICAgIH0pXG4gICAgICAgIH1cbiAgICAgICAgc2V0KHsgaXNBdWRpb011dGVkOiAhaXNBdWRpb011dGVkIH0pXG4gICAgICB9LFxuXG4gICAgICB0b2dnbGVWaWRlbzogKCkgPT4ge1xuICAgICAgICBjb25zdCB7IGlzVmlkZW9NdXRlZCwgbG9jYWxTdHJlYW0gfSA9IGdldCgpXG4gICAgICAgIGlmIChsb2NhbFN0cmVhbSkge1xuICAgICAgICAgIGxvY2FsU3RyZWFtLmdldFZpZGVvVHJhY2tzKCkuZm9yRWFjaCh0cmFjayA9PiB7XG4gICAgICAgICAgICB0cmFjay5lbmFibGVkID0gaXNWaWRlb011dGVkXG4gICAgICAgICAgfSlcbiAgICAgICAgfVxuICAgICAgICBzZXQoeyBpc1ZpZGVvTXV0ZWQ6ICFpc1ZpZGVvTXV0ZWQgfSlcbiAgICAgIH0sXG5cbiAgICAgIHRvZ2dsZVNjcmVlblNoYXJlOiAoKSA9PiB7XG4gICAgICAgIHNldChzdGF0ZSA9PiAoeyBpc1NjcmVlblNoYXJpbmc6ICFzdGF0ZS5pc1NjcmVlblNoYXJpbmcgfSkpXG4gICAgICB9LFxuXG4gICAgICAvLyBDaGF0IGFjdGlvbnNcbiAgICAgIGFkZE1lc3NhZ2U6IChtZXNzYWdlOiBDaGF0TWVzc2FnZSkgPT4ge1xuICAgICAgICBjb25zdCB7IG1lc3NhZ2VzLCBzZWN1cml0eVNldHRpbmdzLCBzcGFtRGV0ZWN0aW9uIH0gPSBnZXQoKVxuXG4gICAgICAgIC8vIEFudGktc3BhbSBjaGVja1xuICAgICAgICBpZiAoc2VjdXJpdHlTZXR0aW5ncy5hbnRpU3BhbUVuYWJsZWQpIHtcbiAgICAgICAgICBjb25zdCBub3cgPSBEYXRlLm5vdygpXG4gICAgICAgICAgY29uc3QgdXNlclNwYW0gPSBzcGFtRGV0ZWN0aW9uLmdldChtZXNzYWdlLnVzZXJJZCkgfHwgeyBjb3VudDogMCwgbGFzdFJlc2V0OiBub3cgfVxuXG4gICAgICAgICAgLy8gUmVzZXQgY291bnQgaWYgbW9yZSB0aGFuIGEgbWludXRlIGhhcyBwYXNzZWRcbiAgICAgICAgICBpZiAobm93IC0gdXNlclNwYW0ubGFzdFJlc2V0ID4gNjAwMDApIHtcbiAgICAgICAgICAgIHVzZXJTcGFtLmNvdW50ID0gMFxuICAgICAgICAgICAgdXNlclNwYW0ubGFzdFJlc2V0ID0gbm93XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgdXNlclNwYW0uY291bnQrK1xuICAgICAgICAgIHNwYW1EZXRlY3Rpb24uc2V0KG1lc3NhZ2UudXNlcklkLCB1c2VyU3BhbSlcblxuICAgICAgICAgIC8vIE1hcmsgYXMgc3BhbSBpZiBleGNlZWRpbmcgbGltaXRcbiAgICAgICAgICBpZiAodXNlclNwYW0uY291bnQgPiBzZWN1cml0eVNldHRpbmdzLm1heE1lc3NhZ2VzUGVyTWludXRlKSB7XG4gICAgICAgICAgICBtZXNzYWdlLmlzU3BhbSA9IHRydWVcbiAgICAgICAgICB9XG4gICAgICAgIH1cblxuICAgICAgICBzZXQoe1xuICAgICAgICAgIG1lc3NhZ2VzOiBbLi4ubWVzc2FnZXMsIG1lc3NhZ2VdLFxuICAgICAgICAgIHVucmVhZENvdW50OiBnZXQoKS5pc0NoYXRPcGVuID8gMCA6IGdldCgpLnVucmVhZENvdW50ICsgMSxcbiAgICAgICAgICBzcGFtRGV0ZWN0aW9uOiBuZXcgTWFwKHNwYW1EZXRlY3Rpb24pXG4gICAgICAgIH0pXG4gICAgICB9LFxuXG4gICAgICBjbGVhclVucmVhZENvdW50OiAoKSA9PiBzZXQoeyB1bnJlYWRDb3VudDogMCB9KSxcblxuICAgICAgLy8gVUkgYWN0aW9uc1xuICAgICAgdG9nZ2xlQ2hhdDogKCkgPT4ge1xuICAgICAgICBjb25zdCBpc0NoYXRPcGVuID0gIWdldCgpLmlzQ2hhdE9wZW5cbiAgICAgICAgc2V0KHtcbiAgICAgICAgICBpc0NoYXRPcGVuLFxuICAgICAgICAgIHVucmVhZENvdW50OiBpc0NoYXRPcGVuID8gMCA6IGdldCgpLnVucmVhZENvdW50XG4gICAgICAgIH0pXG4gICAgICB9LFxuXG4gICAgICB0b2dnbGVTZXR0aW5nczogKCkgPT4gc2V0KHN0YXRlID0+ICh7IGlzU2V0dGluZ3NPcGVuOiAhc3RhdGUuaXNTZXR0aW5nc09wZW4gfSkpLFxuXG4gICAgICAvLyBBZG1pbiBhY3Rpb25zXG4gICAgICBtdXRlUGFydGljaXBhbnQ6IChwYXJ0aWNpcGFudElkOiBzdHJpbmcpID0+IHtcbiAgICAgICAgZ2V0KCkudXBkYXRlUGFydGljaXBhbnQocGFydGljaXBhbnRJZCwgeyBpc0F1ZGlvTXV0ZWQ6IHRydWUgfSlcbiAgICAgIH0sXG5cbiAgICAgIG11dGVBbGxQYXJ0aWNpcGFudHM6ICgpID0+IHtcbiAgICAgICAgY29uc3QgeyBwYXJ0aWNpcGFudHMgfSA9IGdldCgpXG4gICAgICAgIHBhcnRpY2lwYW50cy5mb3JFYWNoKChfLCBpZCkgPT4ge1xuICAgICAgICAgIGdldCgpLnVwZGF0ZVBhcnRpY2lwYW50KGlkLCB7IGlzQXVkaW9NdXRlZDogdHJ1ZSB9KVxuICAgICAgICB9KVxuICAgICAgfSxcblxuICAgICAgdW5tdXRlQWxsUGFydGljaXBhbnRzOiAoKSA9PiB7XG4gICAgICAgIGNvbnN0IHsgcGFydGljaXBhbnRzIH0gPSBnZXQoKVxuICAgICAgICBwYXJ0aWNpcGFudHMuZm9yRWFjaCgoXywgaWQpID0+IHtcbiAgICAgICAgICBnZXQoKS51cGRhdGVQYXJ0aWNpcGFudChpZCwgeyBpc0F1ZGlvTXV0ZWQ6IGZhbHNlIH0pXG4gICAgICAgIH0pXG4gICAgICB9LFxuXG4gICAgICBwcm9tb3RlVG9Db0hvc3Q6IChwYXJ0aWNpcGFudElkOiBzdHJpbmcpID0+IHtcbiAgICAgICAgZ2V0KCkudXBkYXRlUGFydGljaXBhbnQocGFydGljaXBhbnRJZCwgeyByb2xlOiAnY28taG9zdCcgfSlcbiAgICAgIH0sXG5cbiAgICAgIGRlbW90ZUZyb21Db0hvc3Q6IChwYXJ0aWNpcGFudElkOiBzdHJpbmcpID0+IHtcbiAgICAgICAgZ2V0KCkudXBkYXRlUGFydGljaXBhbnQocGFydGljaXBhbnRJZCwgeyByb2xlOiAncGFydGljaXBhbnQnIH0pXG4gICAgICB9LFxuXG4gICAgICBibG9ja1VzZXI6IChwYXJ0aWNpcGFudElkOiBzdHJpbmcpID0+IHtcbiAgICAgICAgY29uc3QgYmxvY2tlZFVzZXJzID0gbmV3IFNldChnZXQoKS5ibG9ja2VkVXNlcnMpXG4gICAgICAgIGJsb2NrZWRVc2Vycy5hZGQocGFydGljaXBhbnRJZClcbiAgICAgICAgc2V0KHsgYmxvY2tlZFVzZXJzIH0pXG4gICAgICAgIGdldCgpLnJlbW92ZVBhcnRpY2lwYW50KHBhcnRpY2lwYW50SWQpXG4gICAgICB9LFxuXG4gICAgICB1bmJsb2NrVXNlcjogKHBhcnRpY2lwYW50SWQ6IHN0cmluZykgPT4ge1xuICAgICAgICBjb25zdCBibG9ja2VkVXNlcnMgPSBuZXcgU2V0KGdldCgpLmJsb2NrZWRVc2VycylcbiAgICAgICAgYmxvY2tlZFVzZXJzLmRlbGV0ZShwYXJ0aWNpcGFudElkKVxuICAgICAgICBzZXQoeyBibG9ja2VkVXNlcnMgfSlcbiAgICAgIH0sXG5cbiAgICAgIGxvY2tSb29tOiAoKSA9PiBzZXQoeyByb29tTG9ja2VkOiB0cnVlIH0pLFxuICAgICAgdW5sb2NrUm9vbTogKCkgPT4gc2V0KHsgcm9vbUxvY2tlZDogZmFsc2UgfSksXG5cbiAgICAgIHVwZGF0ZVNlY3VyaXR5U2V0dGluZ3M6IChzZXR0aW5nczogUGFydGlhbDxTZWN1cml0eVNldHRpbmdzPikgPT4ge1xuICAgICAgICBzZXQoc3RhdGUgPT4gKHtcbiAgICAgICAgICBzZWN1cml0eVNldHRpbmdzOiB7IC4uLnN0YXRlLnNlY3VyaXR5U2V0dGluZ3MsIC4uLnNldHRpbmdzIH1cbiAgICAgICAgfSkpXG4gICAgICB9LFxuXG4gICAgICAvLyBVdGlsaXR5IGFjdGlvbnNcbiAgICAgIHJlc2V0OiAoKSA9PiBzZXQoaW5pdGlhbFN0YXRlKSxcbiAgICB9KSxcbiAgICB7XG4gICAgICBuYW1lOiAndmlkZW8tY2FsbC1zdG9yZScsXG4gICAgfVxuICApXG4pXG5cbmV4cG9ydCBkZWZhdWx0IHVzZVZpZGVvQ2FsbFN0b3JlIl0sIm5hbWVzIjpbImNyZWF0ZSIsImRldnRvb2xzIiwiaW5pdGlhbFN0YXRlIiwicm9vbUlkIiwiaXNDb25uZWN0ZWQiLCJyb29tTG9ja2VkIiwiY3VycmVudFVzZXIiLCJwYXJ0aWNpcGFudHMiLCJNYXAiLCJsb2NhbFN0cmVhbSIsImlzQXVkaW9NdXRlZCIsImlzVmlkZW9NdXRlZCIsImlzU2NyZWVuU2hhcmluZyIsIm1lc3NhZ2VzIiwidW5yZWFkQ291bnQiLCJtZXNzYWdlSGlzdG9yeSIsImlzQ2hhdE9wZW4iLCJpc1NldHRpbmdzT3BlbiIsInNlY3VyaXR5U2V0dGluZ3MiLCJlbmNyeXB0aW9uRW5hYmxlZCIsImFudGlTcGFtRW5hYmxlZCIsIm1heE1lc3NhZ2VzUGVyTWludXRlIiwiYWxsb3dTY3JlZW5TaGFyZSIsImFsbG93RmlsZVNoYXJpbmciLCJyZXF1aXJlQXBwcm92YWxUb0pvaW4iLCJhZG1pbkNvbnRyb2xzIiwiY2FuTXV0ZUFsbCIsImNhbk11dGVQYXJ0aWNpcGFudCIsImNhblJlbW92ZVBhcnRpY2lwYW50IiwiY2FuQ29udHJvbENhbWVyYSIsImNhbk1hbmFnZVJvbGVzIiwiYmxvY2tlZFVzZXJzIiwiU2V0Iiwic3BhbURldGVjdGlvbiIsInVzZVZpZGVvQ2FsbFN0b3JlIiwic2V0IiwiZ2V0Iiwic2V0Um9vbUlkIiwic2V0Q29ubmVjdGVkIiwic2V0Q3VycmVudFVzZXIiLCJhZGRQYXJ0aWNpcGFudCIsInBhcnRpY2lwYW50IiwiaWQiLCJyZW1vdmVQYXJ0aWNpcGFudCIsInBhcnRpY2lwYW50SWQiLCJkZWxldGUiLCJ1cGRhdGVQYXJ0aWNpcGFudCIsInVwZGF0ZXMiLCJzZXRMb2NhbFN0cmVhbSIsInRvZ2dsZUF1ZGlvIiwiZ2V0QXVkaW9UcmFja3MiLCJmb3JFYWNoIiwidHJhY2siLCJlbmFibGVkIiwidG9nZ2xlVmlkZW8iLCJnZXRWaWRlb1RyYWNrcyIsInRvZ2dsZVNjcmVlblNoYXJlIiwic3RhdGUiLCJhZGRNZXNzYWdlIiwibWVzc2FnZSIsIm5vdyIsIkRhdGUiLCJ1c2VyU3BhbSIsInVzZXJJZCIsImNvdW50IiwibGFzdFJlc2V0IiwiaXNTcGFtIiwiY2xlYXJVbnJlYWRDb3VudCIsInRvZ2dsZUNoYXQiLCJ0b2dnbGVTZXR0aW5ncyIsIm11dGVQYXJ0aWNpcGFudCIsIm11dGVBbGxQYXJ0aWNpcGFudHMiLCJfIiwidW5tdXRlQWxsUGFydGljaXBhbnRzIiwicHJvbW90ZVRvQ29Ib3N0Iiwicm9sZSIsImRlbW90ZUZyb21Db0hvc3QiLCJibG9ja1VzZXIiLCJhZGQiLCJ1bmJsb2NrVXNlciIsImxvY2tSb29tIiwidW5sb2NrUm9vbSIsInVwZGF0ZVNlY3VyaXR5U2V0dGluZ3MiLCJzZXR0aW5ncyIsInJlc2V0IiwibmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./lib/store.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3RyZWFtaXQtcHJvLW5leHRqcy8uL2xpYi91dGlscy50cz9mNzQ1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHR5cGUgQ2xhc3NWYWx1ZSwgY2xzeCB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./providers/StoreProvider.tsx":
/*!*************************************!*\
  !*** ./providers/StoreProvider.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StoreContext: () => (/* binding */ StoreContext),\n/* harmony export */   StoreProvider: () => (/* binding */ StoreProvider),\n/* harmony export */   useVideoCallStoreContext: () => (/* binding */ useVideoCallStoreContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/store */ \"(ssr)/./lib/store.ts\");\n/* __next_internal_client_entry_do_not_use__ StoreContext,StoreProvider,useVideoCallStoreContext auto */ \n\n\n\nconst StoreContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\nfunction StoreProvider({ children }) {\n    const storeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    if (!storeRef.current) {\n        storeRef.current = _lib_store__WEBPACK_IMPORTED_MODULE_2__.useVideoCallStore;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StoreContext.Provider, {\n        value: storeRef.current,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Volumes/Apps/Websites/streamit-main/providers/StoreProvider.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\nfunction useVideoCallStoreContext(selector, equalityFn) {\n    const store = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(StoreContext);\n    if (!store) {\n        // Return a default value during SSR instead of throwing\n        if (true) {\n            return selector({});\n        }\n        throw new Error(\"useVideoCallStoreContext must be used within a StoreProvider\");\n    }\n    return (0,zustand__WEBPACK_IMPORTED_MODULE_3__.useStore)(store, selector, equalityFn);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./providers/StoreProvider.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"854b45a9e914\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHJlYW1pdC1wcm8tbmV4dGpzLy4vYXBwL2dsb2JhbHMuY3NzP2NjOGQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI4NTRiNDVhOWU5MTRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_ui_toaster__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/toaster */ \"(rsc)/./components/ui/toaster.tsx\");\n/* harmony import */ var _providers_StoreProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/providers/StoreProvider */ \"(rsc)/./providers/StoreProvider.tsx\");\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Navbar */ \"(rsc)/./components/Navbar.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Footer */ \"(rsc)/./components/Footer.tsx\");\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"StreamIt Pro - Professional Video Conferencing Platform\",\n    description: \"Experience crystal-clear HD video calls, advanced audio processing, and seamless collaboration with StreamIt Pro. The ultimate video conferencing solution for teams of all sizes.\",\n    keywords: \"video conferencing, video calls, online meetings, webRTC, collaboration, streaming, HD video, professional meetings\",\n    authors: [\n        {\n            name: \"StreamIt Pro Team\"\n        }\n    ],\n    openGraph: {\n        title: \"StreamIt Pro - Professional Video Conferencing Platform\",\n        description: \"Experience crystal-clear HD video calls, advanced audio processing, and seamless collaboration with StreamIt Pro.\",\n        type: \"website\",\n        locale: \"en_US\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"StreamIt Pro - Professional Video Conferencing Platform\",\n        description: \"Experience crystal-clear HD video calls, advanced audio processing, and seamless collaboration with StreamIt Pro.\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().className)} overflow-x-hidden`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_StoreProvider__WEBPACK_IMPORTED_MODULE_3__.StoreProvider, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/layout.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"min-h-screen\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/layout.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/layout.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toaster__WEBPACK_IMPORTED_MODULE_2__.Toaster, {}, void 0, false, {\n                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/layout.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Apps/Websites/streamit-main/app/layout.tsx\",\n                lineNumber: 37,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Volumes/Apps/Websites/streamit-main/app/layout.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Volumes/Apps/Websites/streamit-main/app/layout.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Volumes/Apps/Websites/streamit-main/app/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Volumes/Apps/Websites/streamit-main/app/page.tsx#default`));


/***/ }),

/***/ "(rsc)/./components/Footer.tsx":
/*!*******************************!*\
  !*** ./components/Footer.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Volumes/Apps/Websites/streamit-main/components/Footer.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Volumes/Apps/Websites/streamit-main/components/Footer.tsx#default`));


/***/ }),

/***/ "(rsc)/./components/Navbar.tsx":
/*!*******************************!*\
  !*** ./components/Navbar.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Volumes/Apps/Websites/streamit-main/components/Navbar.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Volumes/Apps/Websites/streamit-main/components/Navbar.tsx#default`));


/***/ }),

/***/ "(rsc)/./components/ui/toaster.tsx":
/*!***********************************!*\
  !*** ./components/ui/toaster.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Toaster: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Volumes/Apps/Websites/streamit-main/components/ui/toaster.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Volumes/Apps/Websites/streamit-main/components/ui/toaster.tsx#Toaster`);


/***/ }),

/***/ "(rsc)/./providers/StoreProvider.tsx":
/*!*************************************!*\
  !*** ./providers/StoreProvider.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   StoreContext: () => (/* binding */ e0),
/* harmony export */   StoreProvider: () => (/* binding */ e1),
/* harmony export */   useVideoCallStoreContext: () => (/* binding */ e2)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Volumes/Apps/Websites/streamit-main/providers/StoreProvider.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Volumes/Apps/Websites/streamit-main/providers/StoreProvider.tsx#StoreContext`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Volumes/Apps/Websites/streamit-main/providers/StoreProvider.tsx#StoreProvider`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Volumes/Apps/Websites/streamit-main/providers/StoreProvider.tsx#useVideoCallStoreContext`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@radix-ui","vendor-chunks/lucide-react","vendor-chunks/use-sync-external-store","vendor-chunks/zustand","vendor-chunks/tailwind-merge","vendor-chunks/clsx","vendor-chunks/class-variance-authority"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FApps%2FWebsites%2Fstreamit-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();