/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FApps%2FWebsites%2Fstreamit-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FApps%2FWebsites%2Fstreamit-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"/Volumes/Apps/Websites/streamit-main/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Volumes/Apps/Websites/streamit-main/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FApps%2FWebsites%2Fstreamit-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fcomponents%2FFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fcomponents%2FNavbar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fcomponents%2Fui%2Ftoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fproviders%2FStoreProvider.tsx%22%2C%22ids%22%3A%5B%22StoreProvider%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fcomponents%2FFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fcomponents%2FNavbar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fcomponents%2Fui%2Ftoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fproviders%2FStoreProvider.tsx%22%2C%22ids%22%3A%5B%22StoreProvider%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Footer.tsx */ \"(ssr)/./components/Footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Navbar.tsx */ \"(ssr)/./components/Navbar.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/toaster.tsx */ \"(ssr)/./components/ui/toaster.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./providers/StoreProvider.tsx */ \"(ssr)/./providers/StoreProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fcomponents%2FFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fcomponents%2FNavbar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fcomponents%2Fui%2Ftoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fproviders%2FStoreProvider.tsx%22%2C%22ids%22%3A%5B%22StoreProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlZvbHVtZXMlMkZBcHBzJTJGV2Vic2l0ZXMlMkZzdHJlYW1pdC1tYWluJTJGYXBwJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdJQUFzRiIsInNvdXJjZXMiOlsid2VicGFjazovL3N0cmVhbWl0LXByby1uZXh0anMvP2NiYzUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVm9sdW1lcy9BcHBzL1dlYnNpdGVzL3N0cmVhbWl0LW1haW4vYXBwL3BhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Check,ChevronDown,ChevronUp,Clock,Globe,Headphones,Heart,Mail,MessageCircle,Monitor,Phone,Play,Shield,Star,TrendingUp,UserPlus,Users,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/video.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Check,ChevronDown,ChevronUp,Clock,Globe,Headphones,Heart,Mail,MessageCircle,Monitor,Phone,Play,Shield,Star,TrendingUp,UserPlus,Users,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Check,ChevronDown,ChevronUp,Clock,Globe,Headphones,Heart,Mail,MessageCircle,Monitor,Phone,Play,Shield,Star,TrendingUp,UserPlus,Users,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Check,ChevronDown,ChevronUp,Clock,Globe,Headphones,Heart,Mail,MessageCircle,Monitor,Phone,Play,Shield,Star,TrendingUp,UserPlus,Users,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Check,ChevronDown,ChevronUp,Clock,Globe,Headphones,Heart,Mail,MessageCircle,Monitor,Phone,Play,Shield,Star,TrendingUp,UserPlus,Users,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Check,ChevronDown,ChevronUp,Clock,Globe,Headphones,Heart,Mail,MessageCircle,Monitor,Phone,Play,Shield,Star,TrendingUp,UserPlus,Users,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Check,ChevronDown,ChevronUp,Clock,Globe,Headphones,Heart,Mail,MessageCircle,Monitor,Phone,Play,Shield,Star,TrendingUp,UserPlus,Users,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Check,ChevronDown,ChevronUp,Clock,Globe,Headphones,Heart,Mail,MessageCircle,Monitor,Phone,Play,Shield,Star,TrendingUp,UserPlus,Users,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Check,ChevronDown,ChevronUp,Clock,Globe,Headphones,Heart,Mail,MessageCircle,Monitor,Phone,Play,Shield,Star,TrendingUp,UserPlus,Users,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Check,ChevronDown,ChevronUp,Clock,Globe,Headphones,Heart,Mail,MessageCircle,Monitor,Phone,Play,Shield,Star,TrendingUp,UserPlus,Users,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/headphones.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Check,ChevronDown,ChevronUp,Clock,Globe,Headphones,Heart,Mail,MessageCircle,Monitor,Phone,Play,Shield,Star,TrendingUp,UserPlus,Users,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Check,ChevronDown,ChevronUp,Clock,Globe,Headphones,Heart,Mail,MessageCircle,Monitor,Phone,Play,Shield,Star,TrendingUp,UserPlus,Users,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Check,ChevronDown,ChevronUp,Clock,Globe,Headphones,Heart,Mail,MessageCircle,Monitor,Phone,Play,Shield,Star,TrendingUp,UserPlus,Users,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Check,ChevronDown,ChevronUp,Clock,Globe,Headphones,Heart,Mail,MessageCircle,Monitor,Phone,Play,Shield,Star,TrendingUp,UserPlus,Users,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Check,ChevronDown,ChevronUp,Clock,Globe,Headphones,Heart,Mail,MessageCircle,Monitor,Phone,Play,Shield,Star,TrendingUp,UserPlus,Users,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Check,ChevronDown,ChevronUp,Clock,Globe,Headphones,Heart,Mail,MessageCircle,Monitor,Phone,Play,Shield,Star,TrendingUp,UserPlus,Users,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Check,ChevronDown,ChevronUp,Clock,Globe,Headphones,Heart,Mail,MessageCircle,Monitor,Phone,Play,Shield,Star,TrendingUp,UserPlus,Users,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Check,ChevronDown,ChevronUp,Clock,Globe,Headphones,Heart,Mail,MessageCircle,Monitor,Phone,Play,Shield,Star,TrendingUp,UserPlus,Users,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Check,ChevronDown,ChevronUp,Clock,Globe,Headphones,Heart,Mail,MessageCircle,Monitor,Phone,Play,Shield,Star,TrendingUp,UserPlus,Users,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Check,ChevronDown,ChevronUp,Clock,Globe,Headphones,Heart,Mail,MessageCircle,Monitor,Phone,Play,Shield,Star,TrendingUp,UserPlus,Users,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Check,ChevronDown,ChevronUp,Clock,Globe,Headphones,Heart,Mail,MessageCircle,Monitor,Phone,Play,Shield,Star,TrendingUp,UserPlus,Users,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction HomePage() {\n    const [showNameModal, setShowNameModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showJoinModal, setShowJoinModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userName, setUserName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [roomId, setRoomId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isStarting, setIsStarting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openFaq, setOpenFaq] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const handleStartMeeting = ()=>{\n        setShowNameModal(true);\n        setIsStarting(true);\n    };\n    const handleJoinMeeting = ()=>{\n        setShowJoinModal(true);\n        setIsStarting(false);\n    };\n    const startMeeting = ()=>{\n        if (!userName.trim()) {\n            alert(\"Please enter your name\");\n            return;\n        }\n        const newRoomId = Math.random().toString(36).substring(2, 15);\n        localStorage.setItem(\"userName\", userName.trim());\n        setShowNameModal(false);\n        router.push(`/room/${newRoomId}`);\n    };\n    const joinRoom = ()=>{\n        if (!roomId.trim() || !userName.trim()) {\n            alert(\"Please enter both room ID and your name\");\n            return;\n        }\n        localStorage.setItem(\"userName\", userName.trim());\n        setShowJoinModal(false);\n        router.push(`/room/${roomId.trim()}`);\n    };\n    const closeModals = ()=>{\n        setShowNameModal(false);\n        setShowJoinModal(false);\n        setUserName(\"\");\n        setRoomId(\"\");\n    };\n    const toggleFaq = (index)=>{\n        setOpenFaq(openFaq === index ? null : index);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animated-bg\"\n            }, void 0, false, {\n                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"min-h-screen flex flex-col items-center justify-center p-4 relative z-10 pt-20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-12 fade-in\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mr-4 shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"h-8 w-8 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-5xl font-bold text-white\",\n                                        children: \"StreamIt Pro\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-white/80 max-w-2xl mx-auto leading-relaxed mb-8\",\n                                children: \"Professional video conferencing platform with crystal-clear HD video, advanced audio processing, and seamless collaboration tools\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center gap-6 text-white/60 text-sm mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"h-4 w-4 text-green-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                lineNumber: 88,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"No Downloads Required\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"h-4 w-4 text-green-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"End-to-End Encrypted\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"h-4 w-4 text-green-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"HD Video Quality\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-6 mb-16 slide-up\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleStartMeeting,\n                                className: \"btn-primary flex items-center gap-3 text-lg px-8 py-4 min-w-[200px] justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Start Meeting\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleJoinMeeting,\n                                className: \"btn-secondary flex items-center gap-3 text-lg px-8 py-4 min-w-[200px] justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Join Meeting\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl w-full bounce-in\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-white mb-2\",\n                                        children: \"10M+\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-white/60 text-sm\",\n                                        children: \"Active Users\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-white mb-2\",\n                                        children: \"99.9%\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-white/60 text-sm\",\n                                        children: \"Uptime\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-white mb-2\",\n                                        children: \"150+\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-white/60 text-sm\",\n                                        children: \"Countries\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-white mb-2\",\n                                        children: \"24/7\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-white/60 text-sm\",\n                                        children: \"Support\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"features\",\n                className: \"py-20 px-4 relative z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl font-bold text-white mb-4\",\n                                    children: \"Powerful Features for Modern Teams\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-white/70 max-w-3xl mx-auto\",\n                                    children: \"Everything you need for professional video conferencing, collaboration, and communication\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-3 gap-8 mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"glass text-center p-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-8 w-8 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-white mb-3\",\n                                            children: \"Multi-Participant Calls\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/70 mb-4\",\n                                            children: \"Connect with up to 100 participants in crystal-clear HD video calls with advanced audio processing\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"text-white/60 text-sm space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"h-4 w-4 text-green-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 166,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Up to 100 participants\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 167,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"h-4 w-4 text-green-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 170,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"HD video quality\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 171,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"h-4 w-4 text-green-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 174,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Noise cancellation\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 175,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"glass text-center p-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-8 w-8 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-white mb-3\",\n                                            children: \"Enterprise Security\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/70 mb-4\",\n                                            children: \"End-to-end encrypted communications with WebRTC technology ensuring your privacy and security\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"text-white/60 text-sm space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"h-4 w-4 text-green-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 190,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"End-to-end encryption\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 191,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"h-4 w-4 text-green-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 194,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"GDPR compliant\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 195,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"h-4 w-4 text-green-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"SOC 2 certified\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 199,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"glass text-center p-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-8 w-8 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-white mb-3\",\n                                            children: \"Lightning Performance\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/70 mb-4\",\n                                            children: \"Optimized for performance with minimal latency, adaptive quality, and seamless user experience\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"text-white/60 text-sm space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"h-4 w-4 text-green-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 214,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Sub-100ms latency\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 215,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"h-4 w-4 text-green-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 218,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Adaptive bitrate\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 219,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"h-4 w-4 text-green-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 222,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Global CDN\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 223,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"glass p-6 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-12 w-12 text-purple-400 mx-auto mb-3\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-white font-semibold mb-2\",\n                                            children: \"Global Reach\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/60 text-sm\",\n                                            children: \"Available in 150+ countries worldwide\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"glass p-6 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-12 w-12 text-blue-400 mx-auto mb-3\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-white font-semibold mb-2\",\n                                            children: \"24/7 Availability\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/60 text-sm\",\n                                            children: \"Round-the-clock service reliability\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"glass p-6 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-12 w-12 text-green-400 mx-auto mb-3\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-white font-semibold mb-2\",\n                                            children: \"Premium Support\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/60 text-sm\",\n                                            children: \"Expert technical support team\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"glass p-6 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-12 w-12 text-orange-400 mx-auto mb-3\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-white font-semibold mb-2\",\n                                            children: \"Cross-Platform\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/60 text-sm\",\n                                            children: \"Works on all devices and browsers\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 px-4 relative z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid lg:grid-cols-2 gap-12 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-4xl font-bold text-white mb-6\",\n                                        children: \"Revolutionizing Remote Communication\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-white/80 mb-6\",\n                                        children: \"StreamIt Pro was built from the ground up to solve the challenges of modern remote work. Our platform combines cutting-edge WebRTC technology with intuitive design to deliver the most reliable video conferencing experience.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4 mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-6 w-6 text-purple-400 mt-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-white font-semibold\",\n                                                                children: \"Industry Leading\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                                lineNumber: 272,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white/60 text-sm\",\n                                                                children: \"Recognized by top tech publications and industry experts\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                                lineNumber: 273,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-6 w-6 text-green-400 mt-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-white font-semibold\",\n                                                                children: \"Rapid Growth\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                                lineNumber: 279,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white/60 text-sm\",\n                                                                children: \"Trusted by millions of users worldwide\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                                lineNumber: 280,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"h-6 w-6 text-red-400 mt-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-white font-semibold\",\n                                                                children: \"User Focused\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                                lineNumber: 286,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white/60 text-sm\",\n                                                                children: \"Built with user experience and feedback at the core\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                                lineNumber: 287,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"btn-primary flex items-center gap-2\",\n                                        children: [\n                                            \"Learn More About Us\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"glass p-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-3xl font-bold text-white mb-2\",\n                                                    children: \"2019\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-white/60 text-sm\",\n                                                    children: \"Founded\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-3xl font-bold text-white mb-2\",\n                                                    children: \"50+\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-white/60 text-sm\",\n                                                    children: \"Team Members\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                    lineNumber: 304,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-3xl font-bold text-white mb-2\",\n                                                    children: \"10M+\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-white/60 text-sm\",\n                                                    children: \"Meetings Hosted\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-3xl font-bold text-white mb-2\",\n                                                    children: \"500+\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-white/60 text-sm\",\n                                                    children: \"Enterprise Clients\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                        lineNumber: 258,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                    lineNumber: 257,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                lineNumber: 256,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"pricing\",\n                className: \"py-20 px-4 relative z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl font-bold text-white mb-4\",\n                                    children: \"Simple, Transparent Pricing\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-white/70 max-w-3xl mx-auto\",\n                                    children: \"Choose the perfect plan for your team. All plans include our core features with no hidden fees.\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                            lineNumber: 323,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"glass p-8 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-white mb-4\",\n                                            children: \"Basic\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-4xl font-bold text-white\",\n                                                children: \"Free\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 336,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-3 mb-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center gap-2 text-white/70\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"h-4 w-4 text-green-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 341,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Up to 5 participants\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 342,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center gap-2 text-white/70\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"h-4 w-4 text-green-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 345,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"40-minute meetings\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 346,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center gap-2 text-white/70\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"h-4 w-4 text-green-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 349,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"HD video quality\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center gap-2 text-white/70\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"h-4 w-4 text-green-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 353,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Basic chat features\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 354,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"btn-secondary w-full\",\n                                            children: \"Get Started\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                    lineNumber: 334,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"glass p-8 text-center border-2 border-purple-500 relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -top-3 left-1/2 transform -translate-x-1/2 bg-gradient-to-r from-purple-500 to-pink-500 text-white px-4 py-1 rounded-full text-sm font-semibold\",\n                                            children: \"Most Popular\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-white mb-4\",\n                                            children: \"Pro\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-4xl font-bold text-white\",\n                                                    children: \"$12\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white/60\",\n                                                    children: \"/month\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                    lineNumber: 368,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-3 mb-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center gap-2 text-white/70\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"h-4 w-4 text-green-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 372,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Up to 25 participants\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 373,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center gap-2 text-white/70\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"h-4 w-4 text-green-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Unlimited meeting time\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 377,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center gap-2 text-white/70\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"h-4 w-4 text-green-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"4K video quality\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 381,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center gap-2 text-white/70\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"h-4 w-4 text-green-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 384,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Advanced chat & file sharing\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 385,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                    lineNumber: 383,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center gap-2 text-white/70\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"h-4 w-4 text-green-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 388,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Meeting recordings\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 389,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"btn-primary w-full\",\n                                            children: \"Start Free Trial\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 392,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"glass p-8 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-white mb-4\",\n                                            children: \"Enterprise\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-4xl font-bold text-white\",\n                                                    children: \"$25\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white/60\",\n                                                    children: \"/month\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                    lineNumber: 400,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-3 mb-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center gap-2 text-white/70\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"h-4 w-4 text-green-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 404,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Up to 100 participants\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 405,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center gap-2 text-white/70\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"h-4 w-4 text-green-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 408,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Unlimited everything\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 409,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                    lineNumber: 407,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center gap-2 text-white/70\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"h-4 w-4 text-green-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 412,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Enterprise security\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 413,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                    lineNumber: 411,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center gap-2 text-white/70\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"h-4 w-4 text-green-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 416,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Priority support\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 417,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                    lineNumber: 415,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center gap-2 text-white/70\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"h-4 w-4 text-green-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 420,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Custom integrations\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 421,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"btn-secondary w-full\",\n                                            children: \"Contact Sales\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 424,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                            lineNumber: 332,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                    lineNumber: 322,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                lineNumber: 321,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 px-4 relative z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl font-bold text-white mb-4\",\n                                    children: \"Trusted by Teams Worldwide\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                    lineNumber: 434,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-white/70 max-w-3xl mx-auto\",\n                                    children: \"See what our customers have to say about their StreamIt Pro experience\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                    lineNumber: 437,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                            lineNumber: 433,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"glass p-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mb-4\",\n                                            children: [\n                                                ...Array(5)\n                                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-5 w-5 text-yellow-400 fill-current\"\n                                                }, i, false, {\n                                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 444,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/80 mb-6\",\n                                            children: '\"StreamIt Pro has revolutionized how our remote team collaborates. The video quality is exceptional and the interface is incredibly intuitive.\"'\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 449,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white font-semibold\",\n                                                        children: \"SJ\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                        lineNumber: 454,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-white font-semibold\",\n                                                            children: \"Sarah Johnson\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 457,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-white/60 text-sm\",\n                                                            children: \"CTO, TechCorp\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 458,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                    lineNumber: 456,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 452,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                    lineNumber: 443,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"glass p-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mb-4\",\n                                            children: [\n                                                ...Array(5)\n                                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-5 w-5 text-yellow-400 fill-current\"\n                                                }, i, false, {\n                                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                    lineNumber: 466,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 464,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/80 mb-6\",\n                                            children: '\"The security features give us peace of mind when discussing sensitive client information. Plus, the performance is consistently reliable.\"'\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white font-semibold\",\n                                                        children: \"MR\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                        lineNumber: 474,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                    lineNumber: 473,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-white font-semibold\",\n                                                            children: \"Michael Rodriguez\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 477,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-white/60 text-sm\",\n                                                            children: \"Director, Legal Firm\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 478,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 472,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                    lineNumber: 463,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"glass p-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mb-4\",\n                                            children: [\n                                                ...Array(5)\n                                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-5 w-5 text-yellow-400 fill-current\"\n                                                }, i, false, {\n                                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                    lineNumber: 486,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 484,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/80 mb-6\",\n                                            children: '\"We\\'ve tried many video conferencing solutions, but StreamIt Pro stands out with its ease of use and crystal-clear audio quality.\"'\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 489,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white font-semibold\",\n                                                        children: \"AL\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                        lineNumber: 494,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                    lineNumber: 493,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-white font-semibold\",\n                                                            children: \"Amanda Lee\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 497,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-white/60 text-sm\",\n                                                            children: \"VP Marketing, StartupXYZ\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                            lineNumber: 498,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                    lineNumber: 496,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 492,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                    lineNumber: 483,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                            lineNumber: 442,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                    lineNumber: 432,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                lineNumber: 431,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 px-4 relative z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl font-bold text-white mb-4\",\n                                    children: \"Frequently Asked Questions\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                    lineNumber: 510,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-white/70\",\n                                    children: \"Get answers to common questions about StreamIt Pro\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                    lineNumber: 513,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                            lineNumber: 509,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                {\n                                    question: \"How many participants can join a meeting?\",\n                                    answer: \"The number of participants depends on your plan. Basic allows up to 5, Pro supports up to 25, and Enterprise can handle up to 100 participants in a single meeting.\"\n                                },\n                                {\n                                    question: \"Is StreamIt Pro secure?\",\n                                    answer: \"Yes, absolutely. We use end-to-end encryption for all communications, are GDPR compliant, and SOC 2 certified. Your privacy and security are our top priorities.\"\n                                },\n                                {\n                                    question: \"Do I need to download any software?\",\n                                    answer: \"No downloads required! StreamIt Pro works directly in your web browser using modern WebRTC technology. It's compatible with Chrome, Firefox, Safari, and Edge.\"\n                                },\n                                {\n                                    question: \"Can I record meetings?\",\n                                    answer: \"Meeting recording is available on Pro and Enterprise plans. Recordings are stored securely in the cloud and can be shared with team members who missed the meeting.\"\n                                },\n                                {\n                                    question: \"What devices are supported?\",\n                                    answer: \"StreamIt Pro works on all modern devices including desktops, laptops, tablets, and smartphones. Our responsive design ensures a great experience across all screen sizes.\"\n                                },\n                                {\n                                    question: \"Is there a free trial?\",\n                                    answer: \"Yes! We offer a 14-day free trial of our Pro plan with no credit card required. You can also use our Basic plan for free with some limitations.\"\n                                }\n                            ].map((faq, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"glass\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>toggleFaq(index),\n                                            className: \"w-full p-6 text-left flex items-center justify-between hover:bg-white/5 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white font-semibold\",\n                                                    children: faq.question\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                    lineNumber: 550,\n                                                    columnNumber: 19\n                                                }, this),\n                                                openFaq === index ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white/60\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                    lineNumber: 552,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white/60\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                    lineNumber: 554,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 546,\n                                            columnNumber: 17\n                                        }, this),\n                                        openFaq === index && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-6 pb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white/70\",\n                                                children: faq.answer\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                                lineNumber: 559,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 558,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                    lineNumber: 545,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                            lineNumber: 518,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                    lineNumber: 508,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                lineNumber: 507,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 px-4 relative z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl font-bold text-white mb-4\",\n                                    children: \"Get in Touch\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                    lineNumber: 572,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-white/70 max-w-3xl mx-auto\",\n                                    children: \"Have questions or need help? Our team is here to support you every step of the way.\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                    lineNumber: 575,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                            lineNumber: 571,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"glass p-8 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            className: \"h-12 w-12 text-purple-400 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 582,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-white mb-3\",\n                                            children: \"Live Chat\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 583,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/70 mb-4\",\n                                            children: \"Get instant help from our support team\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 584,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"btn-primary\",\n                                            children: \"Start Chat\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 587,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                    lineNumber: 581,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"glass p-8 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"h-12 w-12 text-blue-400 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 591,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-white mb-3\",\n                                            children: \"Email Support\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 592,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/70 mb-4\",\n                                            children: \"Send us an email and we'll respond within 24 hours\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 593,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"btn-secondary\",\n                                            children: \"Send Email\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 596,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                    lineNumber: 590,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"glass p-8 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Check_ChevronDown_ChevronUp_Clock_Globe_Headphones_Heart_Mail_MessageCircle_Monitor_Phone_Play_Shield_Star_TrendingUp_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            className: \"h-12 w-12 text-green-400 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 600,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-white mb-3\",\n                                            children: \"Phone Support\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 601,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/70 mb-4\",\n                                            children: \"Call us for immediate assistance\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 602,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"btn-secondary\",\n                                            children: \"Call Now\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 605,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                    lineNumber: 599,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                            lineNumber: 580,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                    lineNumber: 570,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                lineNumber: 569,\n                columnNumber: 7\n            }, this),\n            showNameModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"glass p-8 max-w-md w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-6 text-center\",\n                            children: isStarting ? \"Start Your Meeting\" : \"Join Meeting\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                            lineNumber: 615,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Enter your name\",\n                                    value: userName,\n                                    onChange: (e)=>setUserName(e.target.value),\n                                    className: \"glass-input w-full\",\n                                    autoFocus: true,\n                                    onKeyPress: (e)=>e.key === \"Enter\" && startMeeting()\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                    lineNumber: 619,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: closeModals,\n                                            className: \"btn-secondary flex-1\",\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 629,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: startMeeting,\n                                            className: \"btn-primary flex-1\",\n                                            children: \"Start Meeting\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 635,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                    lineNumber: 628,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                            lineNumber: 618,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                    lineNumber: 614,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                lineNumber: 613,\n                columnNumber: 9\n            }, this),\n            showJoinModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"glass p-8 max-w-md w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-6 text-center\",\n                            children: \"Join Meeting\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                            lineNumber: 651,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Enter your name\",\n                                    value: userName,\n                                    onChange: (e)=>setUserName(e.target.value),\n                                    className: \"glass-input w-full\",\n                                    autoFocus: true\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                    lineNumber: 655,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Enter meeting ID\",\n                                    value: roomId,\n                                    onChange: (e)=>setRoomId(e.target.value),\n                                    className: \"glass-input w-full\",\n                                    onKeyPress: (e)=>e.key === \"Enter\" && joinRoom()\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                    lineNumber: 663,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: closeModals,\n                                            className: \"btn-secondary flex-1\",\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 672,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: joinRoom,\n                                            className: \"btn-primary flex-1\",\n                                            children: \"Join Meeting\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 678,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                    lineNumber: 671,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                            lineNumber: 654,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                    lineNumber: 650,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                lineNumber: 649,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Footer.tsx":
/*!*******************************!*\
  !*** ./components/Footer.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Linkedin_Twitter_Video_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Linkedin,Twitter,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/video.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Linkedin_Twitter_Video_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Linkedin,Twitter,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Linkedin_Twitter_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Linkedin,Twitter,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Linkedin_Twitter_Video_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Linkedin,Twitter,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Footer() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-black/50 backdrop-blur-md border-t border-white/10 mt-20\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mr-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Linkedin_Twitter_Video_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                className: \"h-5 w-5 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                                lineNumber: 14,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                            lineNumber: 13,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xl font-bold text-white\",\n                                            children: \"StreamIt Pro\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                            lineNumber: 16,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                    lineNumber: 12,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/60 text-sm mt-2\",\n                                    children: \"Professional video conferencing solution for teams of all sizes.\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                    lineNumber: 18,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4 mt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-white/60 hover:text-white transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Linkedin_Twitter_Video_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                                lineNumber: 23,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                            lineNumber: 22,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-white/60 hover:text-white transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Linkedin_Twitter_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                                lineNumber: 26,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                            lineNumber: 25,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-white/60 hover:text-white transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Linkedin_Twitter_Video_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                                lineNumber: 29,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                            lineNumber: 28,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                    lineNumber: 21,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                            lineNumber: 11,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-white font-semibold mb-4\",\n                                    children: \"Product\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/features\",\n                                                className: \"text-white/60 hover:text-white transition-colors text-sm\",\n                                                children: \"Features\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                                lineNumber: 37,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                            lineNumber: 37,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/pricing\",\n                                                className: \"text-white/60 hover:text-white transition-colors text-sm\",\n                                                children: \"Pricing\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                                lineNumber: 38,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                            lineNumber: 38,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/enterprise\",\n                                                className: \"text-white/60 hover:text-white transition-colors text-sm\",\n                                                children: \"Enterprise\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                                lineNumber: 39,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                            lineNumber: 39,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/security\",\n                                                className: \"text-white/60 hover:text-white transition-colors text-sm\",\n                                                children: \"Security\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                                lineNumber: 40,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                            lineNumber: 40,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                    lineNumber: 36,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-white font-semibold mb-4\",\n                                    children: \"Resources\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/blog\",\n                                                className: \"text-white/60 hover:text-white transition-colors text-sm\",\n                                                children: \"Blog\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                                lineNumber: 47,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/help-center\",\n                                                className: \"text-white/60 hover:text-white transition-colors text-sm\",\n                                                children: \"Help Center\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                                lineNumber: 48,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                            lineNumber: 48,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/tutorials\",\n                                                className: \"text-white/60 hover:text-white transition-colors text-sm\",\n                                                children: \"Tutorials\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                                lineNumber: 49,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/webinars\",\n                                                className: \"text-white/60 hover:text-white transition-colors text-sm\",\n                                                children: \"Webinars\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                                lineNumber: 50,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-white font-semibold mb-4\",\n                                    children: \"Company\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/about\",\n                                                className: \"text-white/60 hover:text-white transition-colors text-sm\",\n                                                children: \"About Us\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                                lineNumber: 57,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/careers\",\n                                                className: \"text-white/60 hover:text-white transition-colors text-sm\",\n                                                children: \"Careers\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                                lineNumber: 58,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/contact\",\n                                                className: \"text-white/60 hover:text-white transition-colors text-sm\",\n                                                children: \"Contact Us\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                                lineNumber: 59,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/privacy\",\n                                                className: \"text-white/60 hover:text-white transition-colors text-sm\",\n                                                children: \"Privacy Policy\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                                lineNumber: 60,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                            lineNumber: 60,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                    lineNumber: 10,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-white/10 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-white/50 text-sm\",\n                            children: [\n                                \"\\xa9 \",\n                                new Date().getFullYear(),\n                                \" StreamIt Pro. All rights reserved.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-6 mt-4 md:mt-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/terms\",\n                                    className: \"text-white/60 hover:text-white transition-colors text-sm\",\n                                    children: \"Terms of Service\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/privacy\",\n                                    className: \"text-white/60 hover:text-white transition-colors text-sm\",\n                                    children: \"Privacy Policy\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/cookies\",\n                                    className: \"text-white/60 hover:text-white transition-colors text-sm\",\n                                    children: \"Cookies\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n            lineNumber: 9,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Volumes/Apps/Websites/streamit-main/components/Footer.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Navbar.tsx":
/*!*******************************!*\
  !*** ./components/Navbar.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_Video_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/video.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Navbar() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"fixed top-0 left-0 right-0 z-50 bg-black/50 backdrop-blur-md border-b border-white/10\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between h-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            href: \"/\",\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mr-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Video_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        className: \"h-5 w-5 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Apps/Websites/streamit-main/components/Navbar.tsx\",\n                                        lineNumber: 14,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/components/Navbar.tsx\",\n                                    lineNumber: 13,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xl font-bold text-white\",\n                                    children: \"StreamIt Pro\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/components/Navbar.tsx\",\n                                    lineNumber: 16,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Navbar.tsx\",\n                            lineNumber: 12,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Apps/Websites/streamit-main/components/Navbar.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden md:flex items-center space-x-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#features\",\n                                className: \"text-white/80 hover:text-white transition-colors cursor-pointer\",\n                                onClick: (e)=>{\n                                    e.preventDefault();\n                                    document.getElementById(\"features\")?.scrollIntoView({\n                                        behavior: \"smooth\"\n                                    });\n                                },\n                                children: \"Features\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Apps/Websites/streamit-main/components/Navbar.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#pricing\",\n                                className: \"text-white/80 hover:text-white transition-colors cursor-pointer\",\n                                onClick: (e)=>{\n                                    e.preventDefault();\n                                    document.getElementById(\"pricing\")?.scrollIntoView({\n                                        behavior: \"smooth\"\n                                    });\n                                },\n                                children: \"Pricing\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Apps/Websites/streamit-main/components/Navbar.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: \"/about\",\n                                className: \"text-white/80 hover:text-white transition-colors\",\n                                children: \"About\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Apps/Websites/streamit-main/components/Navbar.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: \"/contact\",\n                                className: \"text-white/80 hover:text-white transition-colors\",\n                                children: \"Contact\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Apps/Websites/streamit-main/components/Navbar.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Apps/Websites/streamit-main/components/Navbar.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: \"/login\",\n                                className: \"px-4 py-2 rounded-md text-sm font-medium text-white hover:bg-white/10 transition-colors\",\n                                children: \"Log in\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Apps/Websites/streamit-main/components/Navbar.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: \"/signup\",\n                                className: \"ml-4 px-4 py-2 rounded-md text-sm font-medium text-white bg-gradient-to-r from-purple-600 to-pink-600 hover:opacity-90 transition-opacity\",\n                                children: \"Sign up free\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Apps/Websites/streamit-main/components/Navbar.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Apps/Websites/streamit-main/components/Navbar.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Apps/Websites/streamit-main/components/Navbar.tsx\",\n                lineNumber: 10,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Volumes/Apps/Websites/streamit-main/components/Navbar.tsx\",\n            lineNumber: 9,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Volumes/Apps/Websites/streamit-main/components/Navbar.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Navbar.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/toast.tsx":
/*!*********************************!*\
  !*** ./components/ui/toast.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toast: () => (/* binding */ Toast),\n/* harmony export */   ToastAction: () => (/* binding */ ToastAction),\n/* harmony export */   ToastClose: () => (/* binding */ ToastClose),\n/* harmony export */   ToastDescription: () => (/* binding */ ToastDescription),\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   ToastTitle: () => (/* binding */ ToastTitle),\n/* harmony export */   ToastViewport: () => (/* binding */ ToastViewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-toast */ \"(ssr)/./node_modules/@radix-ui/react-toast/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\n\nconst ToastProvider = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Provider;\nconst ToastViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Viewport, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Volumes/Apps/Websites/streamit-main/components/ui/toast.tsx\",\n        lineNumber: 14,\n        columnNumber: 3\n    }, undefined));\nToastViewport.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Viewport.displayName;\nconst toastVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full\", {\n    variants: {\n        variant: {\n            default: \"border bg-background text-foreground\",\n            destructive: \"destructive border-destructive bg-destructive text-destructive-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nconst Toast = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(toastVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Volumes/Apps/Websites/streamit-main/components/ui/toast.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, undefined);\n});\nToast.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\nconst ToastAction = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Action, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Volumes/Apps/Websites/streamit-main/components/ui/toast.tsx\",\n        lineNumber: 60,\n        columnNumber: 3\n    }, undefined));\nToastAction.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Action.displayName;\nconst ToastClose = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Close, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600\", className),\n        \"toast-close\": \"\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"/Volumes/Apps/Websites/streamit-main/components/ui/toast.tsx\",\n            lineNumber: 84,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Volumes/Apps/Websites/streamit-main/components/ui/toast.tsx\",\n        lineNumber: 75,\n        columnNumber: 3\n    }, undefined));\nToastClose.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Close.displayName;\nconst ToastTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Title, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm font-semibold\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Volumes/Apps/Websites/streamit-main/components/ui/toast.tsx\",\n        lineNumber: 93,\n        columnNumber: 3\n    }, undefined));\nToastTitle.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Title.displayName;\nconst ToastDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Description, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm opacity-90\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Volumes/Apps/Websites/streamit-main/components/ui/toast.tsx\",\n        lineNumber: 105,\n        columnNumber: 3\n    }, undefined));\nToastDescription.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Description.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/toast.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/toaster.tsx":
/*!***********************************!*\
  !*** ./components/ui/toaster.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Toaster)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/toast */ \"(ssr)/./components/ui/toast.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(ssr)/./components/ui/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ Toaster auto */ \n\n\nfunction Toaster() {\n    const { toasts } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastProvider, {\n        children: [\n            toasts.map(function({ id, title, description, action, ...props }) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.Toast, {\n                    ...props,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-1\",\n                            children: [\n                                title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastTitle, {\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/components/ui/toaster.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 25\n                                }, this),\n                                description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastDescription, {\n                                    children: description\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/components/ui/toaster.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/ui/toaster.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 13\n                        }, this),\n                        action,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastClose, {}, void 0, false, {\n                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/ui/toaster.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, id, true, {\n                    fileName: \"/Volumes/Apps/Websites/streamit-main/components/ui/toaster.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 11\n                }, this);\n            }),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastViewport, {}, void 0, false, {\n                fileName: \"/Volumes/Apps/Websites/streamit-main/components/ui/toaster.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Volumes/Apps/Websites/streamit-main/components/ui/toaster.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/toaster.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/use-toast.ts":
/*!************************************!*\
  !*** ./components/ui/use-toast.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reducer: () => (/* binding */ reducer),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst TOAST_LIMIT = 1;\nconst TOAST_REMOVE_DELAY = 1000000;\nconst actionTypes = {\n    ADD_TOAST: \"ADD_TOAST\",\n    UPDATE_TOAST: \"UPDATE_TOAST\",\n    DISMISS_TOAST: \"DISMISS_TOAST\",\n    REMOVE_TOAST: \"REMOVE_TOAST\"\n};\nlet count = 0;\nfunction genId() {\n    count = (count + 1) % Number.MAX_SAFE_INTEGER;\n    return count.toString();\n}\nconst toastTimeouts = new Map();\nconst addToRemoveQueue = (toastId)=>{\n    if (toastTimeouts.has(toastId)) {\n        return;\n    }\n    const timeout = setTimeout(()=>{\n        toastTimeouts.delete(toastId);\n        dispatch({\n            type: \"REMOVE_TOAST\",\n            toastId: toastId\n        });\n    }, TOAST_REMOVE_DELAY);\n    toastTimeouts.set(toastId, timeout);\n};\nconst reducer = (state, action)=>{\n    switch(action.type){\n        case \"ADD_TOAST\":\n            return {\n                ...state,\n                toasts: [\n                    action.toast,\n                    ...state.toasts\n                ].slice(0, TOAST_LIMIT)\n            };\n        case \"UPDATE_TOAST\":\n            return {\n                ...state,\n                toasts: state.toasts.map((t)=>t.id === action.toast.id ? {\n                        ...t,\n                        ...action.toast\n                    } : t)\n            };\n        case \"DISMISS_TOAST\":\n            {\n                const { toastId } = action;\n                if (toastId) {\n                    addToRemoveQueue(toastId);\n                } else {\n                    state.toasts.forEach((toast)=>{\n                        addToRemoveQueue(toast.id);\n                    });\n                }\n                return {\n                    ...state,\n                    toasts: state.toasts.map((t)=>t.id === toastId || toastId === undefined ? {\n                            ...t,\n                            open: false\n                        } : t)\n                };\n            }\n        case \"REMOVE_TOAST\":\n            if (action.toastId === undefined) {\n                return {\n                    ...state,\n                    toasts: []\n                };\n            }\n            return {\n                ...state,\n                toasts: state.toasts.filter((t)=>t.id !== action.toastId)\n            };\n    }\n};\nconst listeners = [];\nlet memoryState = {\n    toasts: []\n};\nfunction dispatch(action) {\n    memoryState = reducer(memoryState, action);\n    listeners.forEach((listener)=>{\n        listener(memoryState);\n    });\n}\nfunction toast({ ...props }) {\n    const id = genId();\n    const update = (props)=>dispatch({\n            type: \"UPDATE_TOAST\",\n            toast: {\n                ...props,\n                id\n            }\n        });\n    const dismiss = ()=>dispatch({\n            type: \"DISMISS_TOAST\",\n            toastId: id\n        });\n    dispatch({\n        type: \"ADD_TOAST\",\n        toast: {\n            ...props,\n            id,\n            open: true,\n            onOpenChange: (open)=>{\n                if (!open) dismiss();\n            }\n        }\n    });\n    return {\n        id: id,\n        dismiss,\n        update\n    };\n}\nfunction useToast() {\n    const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(memoryState);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        listeners.push(setState);\n        return ()=>{\n            const index = listeners.indexOf(setState);\n            if (index > -1) {\n                listeners.splice(index, 1);\n            }\n        };\n    }, [\n        state\n    ]);\n    return {\n        ...state,\n        toast,\n        dismiss: (toastId)=>dispatch({\n                type: \"DISMISS_TOAST\",\n                toastId\n            })\n    };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/use-toast.ts\n");

/***/ }),

/***/ "(ssr)/./lib/store.ts":
/*!**********************!*\
  !*** ./lib/store.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useVideoCallStore: () => (/* binding */ useVideoCallStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n\n\nconst initialState = {\n    roomId: null,\n    isConnected: false,\n    roomLocked: false,\n    currentUser: null,\n    participants: new Map(),\n    localStream: null,\n    isAudioMuted: false,\n    isVideoMuted: false,\n    isScreenSharing: false,\n    messages: [],\n    unreadCount: 0,\n    messageHistory: new Map(),\n    isChatOpen: false,\n    isSettingsOpen: false,\n    securitySettings: {\n        encryptionEnabled: true,\n        antiSpamEnabled: true,\n        maxMessagesPerMinute: 10,\n        allowScreenShare: true,\n        allowFileSharing: true,\n        requireApprovalToJoin: false\n    },\n    adminControls: {\n        canMuteAll: true,\n        canMuteParticipant: true,\n        canRemoveParticipant: true,\n        canControlCamera: true,\n        canManageRoles: true\n    },\n    blockedUsers: new Set(),\n    spamDetection: new Map()\n};\nconst useVideoCallStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.devtools)((set, get)=>({\n        ...initialState,\n        setRoomId: (roomId)=>set({\n                roomId\n            }),\n        setConnected: (isConnected)=>set({\n                isConnected\n            }),\n        setCurrentUser: (currentUser)=>set({\n                currentUser\n            }),\n        addParticipant: (participant)=>set((state)=>{\n                const newParticipants = new Map(state.participants);\n                newParticipants.set(participant.id, participant);\n                return {\n                    participants: newParticipants\n                };\n            }),\n        removeParticipant: (participantId)=>set((state)=>{\n                const newParticipants = new Map(state.participants);\n                newParticipants.delete(participantId);\n                return {\n                    participants: newParticipants\n                };\n            }),\n        updateParticipant: (participantId, updates)=>set((state)=>{\n                const newParticipants = new Map(state.participants);\n                const participant = newParticipants.get(participantId);\n                if (participant) {\n                    newParticipants.set(participantId, {\n                        ...participant,\n                        ...updates\n                    });\n                }\n                return {\n                    participants: newParticipants\n                };\n            }),\n        setLocalStream: (localStream)=>set({\n                localStream\n            }),\n        toggleAudio: ()=>set((state)=>{\n                const newMuted = !state.isAudioMuted;\n                if (state.localStream) {\n                    state.localStream.getAudioTracks().forEach((track)=>{\n                        track.enabled = !newMuted;\n                    });\n                }\n                return {\n                    isAudioMuted: newMuted\n                };\n            }),\n        toggleVideo: ()=>set((state)=>{\n                const newMuted = !state.isVideoMuted;\n                if (state.localStream) {\n                    state.localStream.getVideoTracks().forEach((track)=>{\n                        track.enabled = !newMuted;\n                    });\n                }\n                return {\n                    isVideoMuted: newMuted\n                };\n            }),\n        toggleScreenShare: ()=>set((state)=>({\n                    isScreenSharing: !state.isScreenSharing\n                })),\n        addMessage: (message)=>set((state)=>{\n                // Check for spam if anti-spam is enabled\n                if (state.securitySettings.antiSpamEnabled) {\n                    const now = Date.now();\n                    const userSpam = state.spamDetection.get(message.userId) || {\n                        count: 0,\n                        lastReset: now\n                    };\n                    // Reset count if more than a minute has passed\n                    if (now - userSpam.lastReset > 60000) {\n                        userSpam.count = 0;\n                        userSpam.lastReset = now;\n                    }\n                    userSpam.count++;\n                    state.spamDetection.set(message.userId, userSpam);\n                    // Mark as spam if exceeding limit\n                    if (userSpam.count > state.securitySettings.maxMessagesPerMinute) {\n                        message.isSpam = true;\n                    }\n                }\n                return {\n                    messages: [\n                        ...state.messages,\n                        message\n                    ],\n                    unreadCount: state.isChatOpen ? state.unreadCount : state.unreadCount + 1\n                };\n            }),\n        clearUnreadCount: ()=>set({\n                unreadCount: 0\n            }),\n        toggleChat: ()=>set((state)=>({\n                    isChatOpen: !state.isChatOpen,\n                    unreadCount: !state.isChatOpen ? 0 : state.unreadCount\n                })),\n        toggleSettings: ()=>set((state)=>({\n                    isSettingsOpen: !state.isSettingsOpen\n                })),\n        // Admin Actions\n        muteParticipant: (participantId)=>set((state)=>{\n                const participant = state.participants.get(participantId);\n                if (participant && (state.currentUser?.role === \"host\" || state.currentUser?.role === \"co-host\")) {\n                    const newParticipants = new Map(state.participants);\n                    newParticipants.set(participantId, {\n                        ...participant,\n                        isAudioMuted: true\n                    });\n                    return {\n                        participants: newParticipants\n                    };\n                }\n                return state;\n            }),\n        muteAllParticipants: ()=>set((state)=>{\n                if (state.currentUser?.role === \"host\" || state.currentUser?.role === \"co-host\") {\n                    const newParticipants = new Map();\n                    state.participants.forEach((participant, id)=>{\n                        newParticipants.set(id, {\n                            ...participant,\n                            isAudioMuted: true\n                        });\n                    });\n                    return {\n                        participants: newParticipants\n                    };\n                }\n                return state;\n            }),\n        removeParticipantAsAdmin: (participantId)=>set((state)=>{\n                if (state.currentUser?.role === \"host\" || state.currentUser?.role === \"co-host\") {\n                    const newParticipants = new Map(state.participants);\n                    newParticipants.delete(participantId);\n                    return {\n                        participants: newParticipants\n                    };\n                }\n                return state;\n            }),\n        promoteToCoHost: (participantId)=>set((state)=>{\n                const participant = state.participants.get(participantId);\n                if (participant && state.currentUser?.role === \"host\") {\n                    const newParticipants = new Map(state.participants);\n                    newParticipants.set(participantId, {\n                        ...participant,\n                        role: \"co-host\"\n                    });\n                    return {\n                        participants: newParticipants\n                    };\n                }\n                return state;\n            }),\n        demoteFromCoHost: (participantId)=>set((state)=>{\n                const participant = state.participants.get(participantId);\n                if (participant && state.currentUser?.role === \"host\") {\n                    const newParticipants = new Map(state.participants);\n                    newParticipants.set(participantId, {\n                        ...participant,\n                        role: \"participant\"\n                    });\n                    return {\n                        participants: newParticipants\n                    };\n                }\n                return state;\n            }),\n        toggleRoomLock: ()=>set((state)=>{\n                if (state.currentUser?.role === \"host\") {\n                    return {\n                        roomLocked: !state.roomLocked\n                    };\n                }\n                return state;\n            }),\n        blockUser: (userId)=>set((state)=>{\n                if (state.currentUser?.role === \"host\" || state.currentUser?.role === \"co-host\") {\n                    const newBlockedUsers = new Set(state.blockedUsers);\n                    newBlockedUsers.add(userId);\n                    return {\n                        blockedUsers: newBlockedUsers\n                    };\n                }\n                return state;\n            }),\n        unblockUser: (userId)=>set((state)=>{\n                if (state.currentUser?.role === \"host\" || state.currentUser?.role === \"co-host\") {\n                    const newBlockedUsers = new Set(state.blockedUsers);\n                    newBlockedUsers.delete(userId);\n                    return {\n                        blockedUsers: newBlockedUsers\n                    };\n                }\n                return state;\n            }),\n        // Security Actions\n        updateSecuritySettings: (settings)=>set((state)=>{\n                if (state.currentUser?.role === \"host\") {\n                    return {\n                        securitySettings: {\n                            ...state.securitySettings,\n                            ...settings\n                        }\n                    };\n                }\n                return state;\n            }),\n        detectSpam: (userId)=>{\n            const state = get();\n            if (!state.securitySettings.antiSpamEnabled) return false;\n            const userSpam = state.spamDetection.get(userId);\n            if (!userSpam) return false;\n            const now = Date.now();\n            if (now - userSpam.lastReset > 60000) return false;\n            return userSpam.count > state.securitySettings.maxMessagesPerMinute;\n        },\n        reset: ()=>set(initialState)\n    }), {\n    name: \"video-call-store\"\n}));\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useVideoCallStore);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/store.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3RyZWFtaXQtcHJvLW5leHRqcy8uL2xpYi91dGlscy50cz9mNzQ1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHR5cGUgQ2xhc3NWYWx1ZSwgY2xzeCB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./providers/StoreProvider.tsx":
/*!*************************************!*\
  !*** ./providers/StoreProvider.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StoreContext: () => (/* binding */ StoreContext),\n/* harmony export */   StoreProvider: () => (/* binding */ StoreProvider),\n/* harmony export */   useVideoCallStoreContext: () => (/* binding */ useVideoCallStoreContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/store */ \"(ssr)/./lib/store.ts\");\n/* __next_internal_client_entry_do_not_use__ StoreContext,StoreProvider,useVideoCallStoreContext auto */ \n\n\n\nconst StoreContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\nfunction StoreProvider({ children }) {\n    const storeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    if (!storeRef.current) {\n        storeRef.current = _lib_store__WEBPACK_IMPORTED_MODULE_2__.useVideoCallStore;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StoreContext.Provider, {\n        value: storeRef.current,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Volumes/Apps/Websites/streamit-main/providers/StoreProvider.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\nfunction useVideoCallStoreContext(selector, equalityFn) {\n    const store = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(StoreContext);\n    if (!store) {\n        throw new Error(\"useVideoCallStoreContext must be used within a StoreProvider\");\n    }\n    return (0,zustand__WEBPACK_IMPORTED_MODULE_3__.useStore)(store, selector, equalityFn);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./providers/StoreProvider.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"854b45a9e914\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHJlYW1pdC1wcm8tbmV4dGpzLy4vYXBwL2dsb2JhbHMuY3NzP2NjOGQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI4NTRiNDVhOWU5MTRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_ui_toaster__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/toaster */ \"(rsc)/./components/ui/toaster.tsx\");\n/* harmony import */ var _providers_StoreProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/providers/StoreProvider */ \"(rsc)/./providers/StoreProvider.tsx\");\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Navbar */ \"(rsc)/./components/Navbar.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Footer */ \"(rsc)/./components/Footer.tsx\");\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"StreamIt Pro - Professional Video Conferencing Platform\",\n    description: \"Experience crystal-clear HD video calls, advanced audio processing, and seamless collaboration with StreamIt Pro. The ultimate video conferencing solution for teams of all sizes.\",\n    keywords: \"video conferencing, video calls, online meetings, webRTC, collaboration, streaming, HD video, professional meetings\",\n    authors: [\n        {\n            name: \"StreamIt Pro Team\"\n        }\n    ],\n    openGraph: {\n        title: \"StreamIt Pro - Professional Video Conferencing Platform\",\n        description: \"Experience crystal-clear HD video calls, advanced audio processing, and seamless collaboration with StreamIt Pro.\",\n        type: \"website\",\n        locale: \"en_US\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"StreamIt Pro - Professional Video Conferencing Platform\",\n        description: \"Experience crystal-clear HD video calls, advanced audio processing, and seamless collaboration with StreamIt Pro.\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().className)} overflow-x-hidden`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_StoreProvider__WEBPACK_IMPORTED_MODULE_3__.StoreProvider, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/layout.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"min-h-screen\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/layout.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/layout.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toaster__WEBPACK_IMPORTED_MODULE_2__.Toaster, {}, void 0, false, {\n                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/layout.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Apps/Websites/streamit-main/app/layout.tsx\",\n                lineNumber: 37,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Volumes/Apps/Websites/streamit-main/app/layout.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Volumes/Apps/Websites/streamit-main/app/layout.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Volumes/Apps/Websites/streamit-main/app/page.tsx#default`));


/***/ }),

/***/ "(rsc)/./components/Footer.tsx":
/*!*******************************!*\
  !*** ./components/Footer.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Volumes/Apps/Websites/streamit-main/components/Footer.tsx#default`));


/***/ }),

/***/ "(rsc)/./components/Navbar.tsx":
/*!*******************************!*\
  !*** ./components/Navbar.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Volumes/Apps/Websites/streamit-main/components/Navbar.tsx#default`));


/***/ }),

/***/ "(rsc)/./components/ui/toaster.tsx":
/*!***********************************!*\
  !*** ./components/ui/toaster.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Toaster: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Volumes/Apps/Websites/streamit-main/components/ui/toaster.tsx#Toaster`);


/***/ }),

/***/ "(rsc)/./providers/StoreProvider.tsx":
/*!*************************************!*\
  !*** ./providers/StoreProvider.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   StoreContext: () => (/* binding */ e0),
/* harmony export */   StoreProvider: () => (/* binding */ e1),
/* harmony export */   useVideoCallStoreContext: () => (/* binding */ e2)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Volumes/Apps/Websites/streamit-main/providers/StoreProvider.tsx#StoreContext`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Volumes/Apps/Websites/streamit-main/providers/StoreProvider.tsx#StoreProvider`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Volumes/Apps/Websites/streamit-main/providers/StoreProvider.tsx#useVideoCallStoreContext`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react","vendor-chunks/@radix-ui","vendor-chunks/use-sync-external-store","vendor-chunks/zustand","vendor-chunks/tailwind-merge","vendor-chunks/clsx","vendor-chunks/class-variance-authority"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FApps%2FWebsites%2Fstreamit-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();