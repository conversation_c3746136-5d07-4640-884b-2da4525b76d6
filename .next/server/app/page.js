/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FApps%2FWebsites%2Fstreamit-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FApps%2FWebsites%2Fstreamit-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"/Volumes/Apps/Websites/streamit-main/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Volumes/Apps/Websites/streamit-main/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FApps%2FWebsites%2Fstreamit-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlZvbHVtZXMlMkZBcHBzJTJGV2Vic2l0ZXMlMkZzdHJlYW1pdC1tYWluJTJGYXBwJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdJQUFzRiIsInNvdXJjZXMiOlsid2VicGFjazovL3N0cmVhbWl0LXByby1uZXh0anMvP2NiYzUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVm9sdW1lcy9BcHBzL1dlYnNpdGVzL3N0cmVhbWl0LW1haW4vYXBwL3BhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fcomponents%2Fui%2Ftoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fproviders%2FStoreProvider.tsx%22%2C%22ids%22%3A%5B%22StoreProvider%22%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fcomponents%2Fui%2Ftoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fproviders%2FStoreProvider.tsx%22%2C%22ids%22%3A%5B%22StoreProvider%22%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/toaster.tsx */ \"(ssr)/./components/ui/toaster.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./providers/StoreProvider.tsx */ \"(ssr)/./providers/StoreProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlZvbHVtZXMlMkZBcHBzJTJGV2Vic2l0ZXMlMkZzdHJlYW1pdC1tYWluJTJGY29tcG9uZW50cyUyRnVpJTJGdG9hc3Rlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJUb2FzdGVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlZvbHVtZXMlMkZBcHBzJTJGV2Vic2l0ZXMlMkZzdHJlYW1pdC1tYWluJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmZvbnQlMkZnb29nbGUlMkZ0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMmFwcCUyRmxheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlZvbHVtZXMlMkZBcHBzJTJGV2Vic2l0ZXMlMkZzdHJlYW1pdC1tYWluJTJGYXBwJTJGZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVm9sdW1lcyUyRkFwcHMlMkZXZWJzaXRlcyUyRnN0cmVhbWl0LW1haW4lMkZwcm92aWRlcnMlMkZTdG9yZVByb3ZpZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlN0b3JlUHJvdmlkZXIlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtLQUFnSTtBQUNoSTtBQUNBLHNLQUF3SSIsInNvdXJjZXMiOlsid2VicGFjazovL3N0cmVhbWl0LXByby1uZXh0anMvPzE4ZWQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJUb2FzdGVyXCJdICovIFwiL1ZvbHVtZXMvQXBwcy9XZWJzaXRlcy9zdHJlYW1pdC1tYWluL2NvbXBvbmVudHMvdWkvdG9hc3Rlci50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlN0b3JlUHJvdmlkZXJcIl0gKi8gXCIvVm9sdW1lcy9BcHBzL1dlYnNpdGVzL3N0cmVhbWl0LW1haW4vcHJvdmlkZXJzL1N0b3JlUHJvdmlkZXIudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fcomponents%2Fui%2Ftoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fproviders%2FStoreProvider.tsx%22%2C%22ids%22%3A%5B%22StoreProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Play_Shield_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Play,Shield,UserPlus,Users,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/video.js\");\n/* harmony import */ var _barrel_optimize_names_Play_Shield_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Play,Shield,UserPlus,Users,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Play_Shield_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Play,Shield,UserPlus,Users,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Play_Shield_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Play,Shield,UserPlus,Users,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Play_Shield_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Play,Shield,UserPlus,Users,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Play_Shield_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Play,Shield,UserPlus,Users,Video,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction HomePage() {\n    const [showNameModal, setShowNameModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showJoinModal, setShowJoinModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userName, setUserName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [roomId, setRoomId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isStarting, setIsStarting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const handleStartMeeting = ()=>{\n        setShowNameModal(true);\n        setIsStarting(true);\n    };\n    const handleJoinMeeting = ()=>{\n        setShowJoinModal(true);\n        setIsStarting(false);\n    };\n    const startMeeting = ()=>{\n        if (!userName.trim()) {\n            alert(\"Please enter your name\");\n            return;\n        }\n        const newRoomId = Math.random().toString(36).substring(2, 15);\n        localStorage.setItem(\"userName\", userName.trim());\n        setShowNameModal(false);\n        router.push(`/room/${newRoomId}`);\n    };\n    const joinRoom = ()=>{\n        if (!roomId.trim() || !userName.trim()) {\n            alert(\"Please enter both room ID and your name\");\n            return;\n        }\n        localStorage.setItem(\"userName\", userName.trim());\n        setShowJoinModal(false);\n        router.push(`/room/${roomId.trim()}`);\n    };\n    const closeModals = ()=>{\n        setShowNameModal(false);\n        setShowJoinModal(false);\n        setUserName(\"\");\n        setRoomId(\"\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animated-bg\"\n            }, void 0, false, {\n                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex flex-col items-center justify-center p-4 relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-12 fade-in\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mr-4 shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Play_Shield_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"h-8 w-8 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-5xl font-bold text-white\",\n                                        children: \"StreamIt Pro\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-white/80 max-w-2xl mx-auto leading-relaxed\",\n                                children: \"Professional video conferencing platform with crystal-clear HD video, advanced audio processing, and seamless collaboration tools\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-6 mb-16 slide-up\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleStartMeeting,\n                                className: \"btn-primary flex items-center gap-3 text-lg px-8 py-4 min-w-[200px] justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Play_Shield_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Start Meeting\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleJoinMeeting,\n                                className: \"btn-secondary flex items-center gap-3 text-lg px-8 py-4 min-w-[200px] justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Play_Shield_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Join Meeting\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid md:grid-cols-3 gap-8 max-w-4xl w-full bounce-in\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"glass text-center p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Play_Shield_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-8 w-8 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-white mb-3\",\n                                        children: \"Multi-Participant\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white/70\",\n                                        children: \"Connect with multiple participants in crystal-clear HD video calls with advanced audio processing\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"glass text-center p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Play_Shield_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-8 w-8 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-white mb-3\",\n                                        children: \"Secure & Private\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white/70\",\n                                        children: \"End-to-end encrypted communications with WebRTC technology ensuring your privacy and security\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"glass text-center p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Play_Shield_UserPlus_Users_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-8 w-8 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-white mb-3\",\n                                        children: \"Lightning Fast\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white/70\",\n                                        children: \"Optimized for performance with minimal latency, adaptive quality, and seamless user experience\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mt-16 text-white/60\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Built with Next.js, TypeScript, WebRTC & Modern Web Technologies\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this),\n            showNameModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"glass p-8 max-w-md w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-6 text-center\",\n                            children: isStarting ? \"Start Your Meeting\" : \"Join Meeting\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Enter your name\",\n                                    value: userName,\n                                    onChange: (e)=>setUserName(e.target.value),\n                                    className: \"glass-input w-full\",\n                                    autoFocus: true,\n                                    onKeyPress: (e)=>e.key === \"Enter\" && startMeeting()\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: closeModals,\n                                            className: \"btn-secondary flex-1\",\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: startMeeting,\n                                            className: \"btn-primary flex-1\",\n                                            children: \"Start Meeting\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                lineNumber: 137,\n                columnNumber: 9\n            }, this),\n            showJoinModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"glass p-8 max-w-md w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-6 text-center\",\n                            children: \"Join Meeting\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Enter your name\",\n                                    value: userName,\n                                    onChange: (e)=>setUserName(e.target.value),\n                                    className: \"glass-input w-full\",\n                                    autoFocus: true\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Enter meeting ID\",\n                                    value: roomId,\n                                    onChange: (e)=>setRoomId(e.target.value),\n                                    className: \"glass-input w-full\",\n                                    onKeyPress: (e)=>e.key === \"Enter\" && joinRoom()\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: closeModals,\n                                            className: \"btn-secondary flex-1\",\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: joinRoom,\n                                            className: \"btn-primary flex-1\",\n                                            children: \"Join Meeting\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Apps/Websites/streamit-main/app/page.tsx\",\n                lineNumber: 173,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/toast.tsx":
/*!*********************************!*\
  !*** ./components/ui/toast.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toast: () => (/* binding */ Toast),\n/* harmony export */   ToastAction: () => (/* binding */ ToastAction),\n/* harmony export */   ToastClose: () => (/* binding */ ToastClose),\n/* harmony export */   ToastDescription: () => (/* binding */ ToastDescription),\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   ToastTitle: () => (/* binding */ ToastTitle),\n/* harmony export */   ToastViewport: () => (/* binding */ ToastViewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-toast */ \"(ssr)/./node_modules/@radix-ui/react-toast/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\n\nconst ToastProvider = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Provider;\nconst ToastViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Viewport, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Volumes/Apps/Websites/streamit-main/components/ui/toast.tsx\",\n        lineNumber: 14,\n        columnNumber: 3\n    }, undefined));\nToastViewport.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Viewport.displayName;\nconst toastVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full\", {\n    variants: {\n        variant: {\n            default: \"border bg-background text-foreground\",\n            destructive: \"destructive border-destructive bg-destructive text-destructive-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nconst Toast = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(toastVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Volumes/Apps/Websites/streamit-main/components/ui/toast.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, undefined);\n});\nToast.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\nconst ToastAction = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Action, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Volumes/Apps/Websites/streamit-main/components/ui/toast.tsx\",\n        lineNumber: 60,\n        columnNumber: 3\n    }, undefined));\nToastAction.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Action.displayName;\nconst ToastClose = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Close, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600\", className),\n        \"toast-close\": \"\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"/Volumes/Apps/Websites/streamit-main/components/ui/toast.tsx\",\n            lineNumber: 84,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Volumes/Apps/Websites/streamit-main/components/ui/toast.tsx\",\n        lineNumber: 75,\n        columnNumber: 3\n    }, undefined));\nToastClose.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Close.displayName;\nconst ToastTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Title, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm font-semibold\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Volumes/Apps/Websites/streamit-main/components/ui/toast.tsx\",\n        lineNumber: 93,\n        columnNumber: 3\n    }, undefined));\nToastTitle.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Title.displayName;\nconst ToastDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Description, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm opacity-90\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Volumes/Apps/Websites/streamit-main/components/ui/toast.tsx\",\n        lineNumber: 105,\n        columnNumber: 3\n    }, undefined));\nToastDescription.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Description.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/toast.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/toaster.tsx":
/*!***********************************!*\
  !*** ./components/ui/toaster.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Toaster)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/toast */ \"(ssr)/./components/ui/toast.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(ssr)/./components/ui/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ Toaster auto */ \n\n\nfunction Toaster() {\n    const { toasts } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastProvider, {\n        children: [\n            toasts.map(function({ id, title, description, action, ...props }) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.Toast, {\n                    ...props,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-1\",\n                            children: [\n                                title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastTitle, {\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/components/ui/toaster.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 25\n                                }, this),\n                                description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastDescription, {\n                                    children: description\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Apps/Websites/streamit-main/components/ui/toaster.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/ui/toaster.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 13\n                        }, this),\n                        action,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastClose, {}, void 0, false, {\n                            fileName: \"/Volumes/Apps/Websites/streamit-main/components/ui/toaster.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, id, true, {\n                    fileName: \"/Volumes/Apps/Websites/streamit-main/components/ui/toaster.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 11\n                }, this);\n            }),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastViewport, {}, void 0, false, {\n                fileName: \"/Volumes/Apps/Websites/streamit-main/components/ui/toaster.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Volumes/Apps/Websites/streamit-main/components/ui/toaster.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/toaster.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/use-toast.ts":
/*!************************************!*\
  !*** ./components/ui/use-toast.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reducer: () => (/* binding */ reducer),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst TOAST_LIMIT = 1;\nconst TOAST_REMOVE_DELAY = 1000000;\nconst actionTypes = {\n    ADD_TOAST: \"ADD_TOAST\",\n    UPDATE_TOAST: \"UPDATE_TOAST\",\n    DISMISS_TOAST: \"DISMISS_TOAST\",\n    REMOVE_TOAST: \"REMOVE_TOAST\"\n};\nlet count = 0;\nfunction genId() {\n    count = (count + 1) % Number.MAX_SAFE_INTEGER;\n    return count.toString();\n}\nconst toastTimeouts = new Map();\nconst addToRemoveQueue = (toastId)=>{\n    if (toastTimeouts.has(toastId)) {\n        return;\n    }\n    const timeout = setTimeout(()=>{\n        toastTimeouts.delete(toastId);\n        dispatch({\n            type: \"REMOVE_TOAST\",\n            toastId: toastId\n        });\n    }, TOAST_REMOVE_DELAY);\n    toastTimeouts.set(toastId, timeout);\n};\nconst reducer = (state, action)=>{\n    switch(action.type){\n        case \"ADD_TOAST\":\n            return {\n                ...state,\n                toasts: [\n                    action.toast,\n                    ...state.toasts\n                ].slice(0, TOAST_LIMIT)\n            };\n        case \"UPDATE_TOAST\":\n            return {\n                ...state,\n                toasts: state.toasts.map((t)=>t.id === action.toast.id ? {\n                        ...t,\n                        ...action.toast\n                    } : t)\n            };\n        case \"DISMISS_TOAST\":\n            {\n                const { toastId } = action;\n                if (toastId) {\n                    addToRemoveQueue(toastId);\n                } else {\n                    state.toasts.forEach((toast)=>{\n                        addToRemoveQueue(toast.id);\n                    });\n                }\n                return {\n                    ...state,\n                    toasts: state.toasts.map((t)=>t.id === toastId || toastId === undefined ? {\n                            ...t,\n                            open: false\n                        } : t)\n                };\n            }\n        case \"REMOVE_TOAST\":\n            if (action.toastId === undefined) {\n                return {\n                    ...state,\n                    toasts: []\n                };\n            }\n            return {\n                ...state,\n                toasts: state.toasts.filter((t)=>t.id !== action.toastId)\n            };\n    }\n};\nconst listeners = [];\nlet memoryState = {\n    toasts: []\n};\nfunction dispatch(action) {\n    memoryState = reducer(memoryState, action);\n    listeners.forEach((listener)=>{\n        listener(memoryState);\n    });\n}\nfunction toast({ ...props }) {\n    const id = genId();\n    const update = (props)=>dispatch({\n            type: \"UPDATE_TOAST\",\n            toast: {\n                ...props,\n                id\n            }\n        });\n    const dismiss = ()=>dispatch({\n            type: \"DISMISS_TOAST\",\n            toastId: id\n        });\n    dispatch({\n        type: \"ADD_TOAST\",\n        toast: {\n            ...props,\n            id,\n            open: true,\n            onOpenChange: (open)=>{\n                if (!open) dismiss();\n            }\n        }\n    });\n    return {\n        id: id,\n        dismiss,\n        update\n    };\n}\nfunction useToast() {\n    const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(memoryState);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        listeners.push(setState);\n        return ()=>{\n            const index = listeners.indexOf(setState);\n            if (index > -1) {\n                listeners.splice(index, 1);\n            }\n        };\n    }, [\n        state\n    ]);\n    return {\n        ...state,\n        toast,\n        dismiss: (toastId)=>dispatch({\n                type: \"DISMISS_TOAST\",\n                toastId\n            })\n    };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/use-toast.ts\n");

/***/ }),

/***/ "(ssr)/./lib/store.ts":
/*!**********************!*\
  !*** ./lib/store.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useVideoCallStore: () => (/* binding */ useVideoCallStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n\n\nconst initialState = {\n    roomId: null,\n    isConnected: false,\n    roomLocked: false,\n    currentUser: null,\n    participants: new Map(),\n    localStream: null,\n    isAudioMuted: false,\n    isVideoMuted: false,\n    isScreenSharing: false,\n    messages: [],\n    unreadCount: 0,\n    messageHistory: new Map(),\n    isChatOpen: false,\n    isSettingsOpen: false,\n    securitySettings: {\n        encryptionEnabled: true,\n        antiSpamEnabled: true,\n        maxMessagesPerMinute: 10,\n        allowScreenShare: true,\n        allowFileSharing: true,\n        requireApprovalToJoin: false\n    },\n    adminControls: {\n        canMuteAll: true,\n        canMuteParticipant: true,\n        canRemoveParticipant: true,\n        canControlCamera: true,\n        canManageRoles: true\n    },\n    blockedUsers: new Set(),\n    spamDetection: new Map()\n};\nconst useVideoCallStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.devtools)((set, get)=>({\n        ...initialState,\n        setRoomId: (roomId)=>set({\n                roomId\n            }),\n        setConnected: (isConnected)=>set({\n                isConnected\n            }),\n        setCurrentUser: (currentUser)=>set({\n                currentUser\n            }),\n        addParticipant: (participant)=>set((state)=>{\n                const newParticipants = new Map(state.participants);\n                newParticipants.set(participant.id, participant);\n                return {\n                    participants: newParticipants\n                };\n            }),\n        removeParticipant: (participantId)=>set((state)=>{\n                const newParticipants = new Map(state.participants);\n                newParticipants.delete(participantId);\n                return {\n                    participants: newParticipants\n                };\n            }),\n        updateParticipant: (participantId, updates)=>set((state)=>{\n                const newParticipants = new Map(state.participants);\n                const participant = newParticipants.get(participantId);\n                if (participant) {\n                    newParticipants.set(participantId, {\n                        ...participant,\n                        ...updates\n                    });\n                }\n                return {\n                    participants: newParticipants\n                };\n            }),\n        setLocalStream: (localStream)=>set({\n                localStream\n            }),\n        toggleAudio: ()=>set((state)=>{\n                const newMuted = !state.isAudioMuted;\n                if (state.localStream) {\n                    state.localStream.getAudioTracks().forEach((track)=>{\n                        track.enabled = !newMuted;\n                    });\n                }\n                return {\n                    isAudioMuted: newMuted\n                };\n            }),\n        toggleVideo: ()=>set((state)=>{\n                const newMuted = !state.isVideoMuted;\n                if (state.localStream) {\n                    state.localStream.getVideoTracks().forEach((track)=>{\n                        track.enabled = !newMuted;\n                    });\n                }\n                return {\n                    isVideoMuted: newMuted\n                };\n            }),\n        toggleScreenShare: ()=>set((state)=>({\n                    isScreenSharing: !state.isScreenSharing\n                })),\n        addMessage: (message)=>set((state)=>{\n                // Check for spam if anti-spam is enabled\n                if (state.securitySettings.antiSpamEnabled) {\n                    const now = Date.now();\n                    const userSpam = state.spamDetection.get(message.userId) || {\n                        count: 0,\n                        lastReset: now\n                    };\n                    // Reset count if more than a minute has passed\n                    if (now - userSpam.lastReset > 60000) {\n                        userSpam.count = 0;\n                        userSpam.lastReset = now;\n                    }\n                    userSpam.count++;\n                    state.spamDetection.set(message.userId, userSpam);\n                    // Mark as spam if exceeding limit\n                    if (userSpam.count > state.securitySettings.maxMessagesPerMinute) {\n                        message.isSpam = true;\n                    }\n                }\n                return {\n                    messages: [\n                        ...state.messages,\n                        message\n                    ],\n                    unreadCount: state.isChatOpen ? state.unreadCount : state.unreadCount + 1\n                };\n            }),\n        clearUnreadCount: ()=>set({\n                unreadCount: 0\n            }),\n        toggleChat: ()=>set((state)=>({\n                    isChatOpen: !state.isChatOpen,\n                    unreadCount: !state.isChatOpen ? 0 : state.unreadCount\n                })),\n        toggleSettings: ()=>set((state)=>({\n                    isSettingsOpen: !state.isSettingsOpen\n                })),\n        // Admin Actions\n        muteParticipant: (participantId)=>set((state)=>{\n                const participant = state.participants.get(participantId);\n                if (participant && (state.currentUser?.role === \"host\" || state.currentUser?.role === \"co-host\")) {\n                    const newParticipants = new Map(state.participants);\n                    newParticipants.set(participantId, {\n                        ...participant,\n                        isAudioMuted: true\n                    });\n                    return {\n                        participants: newParticipants\n                    };\n                }\n                return state;\n            }),\n        muteAllParticipants: ()=>set((state)=>{\n                if (state.currentUser?.role === \"host\" || state.currentUser?.role === \"co-host\") {\n                    const newParticipants = new Map();\n                    state.participants.forEach((participant, id)=>{\n                        newParticipants.set(id, {\n                            ...participant,\n                            isAudioMuted: true\n                        });\n                    });\n                    return {\n                        participants: newParticipants\n                    };\n                }\n                return state;\n            }),\n        removeParticipantAsAdmin: (participantId)=>set((state)=>{\n                if (state.currentUser?.role === \"host\" || state.currentUser?.role === \"co-host\") {\n                    const newParticipants = new Map(state.participants);\n                    newParticipants.delete(participantId);\n                    return {\n                        participants: newParticipants\n                    };\n                }\n                return state;\n            }),\n        promoteToCoHost: (participantId)=>set((state)=>{\n                const participant = state.participants.get(participantId);\n                if (participant && state.currentUser?.role === \"host\") {\n                    const newParticipants = new Map(state.participants);\n                    newParticipants.set(participantId, {\n                        ...participant,\n                        role: \"co-host\"\n                    });\n                    return {\n                        participants: newParticipants\n                    };\n                }\n                return state;\n            }),\n        demoteFromCoHost: (participantId)=>set((state)=>{\n                const participant = state.participants.get(participantId);\n                if (participant && state.currentUser?.role === \"host\") {\n                    const newParticipants = new Map(state.participants);\n                    newParticipants.set(participantId, {\n                        ...participant,\n                        role: \"participant\"\n                    });\n                    return {\n                        participants: newParticipants\n                    };\n                }\n                return state;\n            }),\n        toggleRoomLock: ()=>set((state)=>{\n                if (state.currentUser?.role === \"host\") {\n                    return {\n                        roomLocked: !state.roomLocked\n                    };\n                }\n                return state;\n            }),\n        blockUser: (userId)=>set((state)=>{\n                if (state.currentUser?.role === \"host\" || state.currentUser?.role === \"co-host\") {\n                    const newBlockedUsers = new Set(state.blockedUsers);\n                    newBlockedUsers.add(userId);\n                    return {\n                        blockedUsers: newBlockedUsers\n                    };\n                }\n                return state;\n            }),\n        unblockUser: (userId)=>set((state)=>{\n                if (state.currentUser?.role === \"host\" || state.currentUser?.role === \"co-host\") {\n                    const newBlockedUsers = new Set(state.blockedUsers);\n                    newBlockedUsers.delete(userId);\n                    return {\n                        blockedUsers: newBlockedUsers\n                    };\n                }\n                return state;\n            }),\n        // Security Actions\n        updateSecuritySettings: (settings)=>set((state)=>{\n                if (state.currentUser?.role === \"host\") {\n                    return {\n                        securitySettings: {\n                            ...state.securitySettings,\n                            ...settings\n                        }\n                    };\n                }\n                return state;\n            }),\n        detectSpam: (userId)=>{\n            const state = get();\n            if (!state.securitySettings.antiSpamEnabled) return false;\n            const userSpam = state.spamDetection.get(userId);\n            if (!userSpam) return false;\n            const now = Date.now();\n            if (now - userSpam.lastReset > 60000) return false;\n            return userSpam.count > state.securitySettings.maxMessagesPerMinute;\n        },\n        reset: ()=>set(initialState)\n    }), {\n    name: \"video-call-store\"\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/store.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3RyZWFtaXQtcHJvLW5leHRqcy8uL2xpYi91dGlscy50cz9mNzQ1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHR5cGUgQ2xhc3NWYWx1ZSwgY2xzeCB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./providers/StoreProvider.tsx":
/*!*************************************!*\
  !*** ./providers/StoreProvider.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StoreContext: () => (/* binding */ StoreContext),\n/* harmony export */   StoreProvider: () => (/* binding */ StoreProvider),\n/* harmony export */   useVideoCallStoreContext: () => (/* binding */ useVideoCallStoreContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/store */ \"(ssr)/./lib/store.ts\");\n/* __next_internal_client_entry_do_not_use__ StoreContext,StoreProvider,useVideoCallStoreContext auto */ \n\n\n\nconst StoreContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\nfunction StoreProvider({ children }) {\n    const storeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    if (!storeRef.current) {\n        storeRef.current = _lib_store__WEBPACK_IMPORTED_MODULE_2__.useVideoCallStore;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StoreContext.Provider, {\n        value: storeRef.current,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Volumes/Apps/Websites/streamit-main/providers/StoreProvider.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\nfunction useVideoCallStoreContext(selector, equalityFn) {\n    const store = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(StoreContext);\n    if (!store) {\n        throw new Error(\"useVideoCallStoreContext must be used within a StoreProvider\");\n    }\n    return (0,zustand__WEBPACK_IMPORTED_MODULE_3__.useStore)(store, selector, equalityFn);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./providers/StoreProvider.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"68ef03421b24\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHJlYW1pdC1wcm8tbmV4dGpzLy4vYXBwL2dsb2JhbHMuY3NzP2NjOGQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI2OGVmMDM0MjFiMjRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_ui_toaster__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/toaster */ \"(rsc)/./components/ui/toaster.tsx\");\n/* harmony import */ var _providers_StoreProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/providers/StoreProvider */ \"(rsc)/./providers/StoreProvider.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"StreamIt Pro - Video Conferencing\",\n    description: \"Professional video conferencing platform built with Next.js and WebRTC\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className)} overflow-x-hidden`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_StoreProvider__WEBPACK_IMPORTED_MODULE_3__.StoreProvider, {\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toaster__WEBPACK_IMPORTED_MODULE_2__.Toaster, {}, void 0, false, {\n                        fileName: \"/Volumes/Apps/Websites/streamit-main/app/layout.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Apps/Websites/streamit-main/app/layout.tsx\",\n                lineNumber: 22,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Volumes/Apps/Websites/streamit-main/app/layout.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Volumes/Apps/Websites/streamit-main/app/layout.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBTU1BO0FBSmdCO0FBQzJCO0FBQ1E7QUFJbEQsTUFBTUcsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFBS0MsV0FBVyxDQUFDLEVBQUVYLDJKQUFlLENBQUMsa0JBQWtCLENBQUM7c0JBQ3JELDRFQUFDRSxtRUFBYUE7O29CQUNYSztrQ0FDRCw4REFBQ04sMkRBQU9BOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLbEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHJlYW1pdC1wcm8tbmV4dGpzLy4vYXBwL2xheW91dC50c3g/OTk4OCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCdcbmltcG9ydCB7IEludGVyIH0gZnJvbSAnbmV4dC9mb250L2dvb2dsZSdcbmltcG9ydCAnLi9nbG9iYWxzLmNzcydcbmltcG9ydCB7IFRvYXN0ZXIgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvdG9hc3RlcidcbmltcG9ydCB7IFN0b3JlUHJvdmlkZXIgfSBmcm9tICdAL3Byb3ZpZGVycy9TdG9yZVByb3ZpZGVyJ1xuXG5jb25zdCBpbnRlciA9IEludGVyKHsgc3Vic2V0czogWydsYXRpbiddIH0pXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAnU3RyZWFtSXQgUHJvIC0gVmlkZW8gQ29uZmVyZW5jaW5nJyxcbiAgZGVzY3JpcHRpb246ICdQcm9mZXNzaW9uYWwgdmlkZW8gY29uZmVyZW5jaW5nIHBsYXRmb3JtIGJ1aWx0IHdpdGggTmV4dC5qcyBhbmQgV2ViUlRDJyxcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2Ake2ludGVyLmNsYXNzTmFtZX0gb3ZlcmZsb3cteC1oaWRkZW5gfT5cbiAgICAgICAgPFN0b3JlUHJvdmlkZXI+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICAgIDxUb2FzdGVyIC8+XG4gICAgICAgIDwvU3RvcmVQcm92aWRlcj5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJpbnRlciIsIlRvYXN0ZXIiLCJTdG9yZVByb3ZpZGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5IiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Volumes/Apps/Websites/streamit-main/app/page.tsx#default`));


/***/ }),

/***/ "(rsc)/./components/ui/toaster.tsx":
/*!***********************************!*\
  !*** ./components/ui/toaster.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Toaster: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Volumes/Apps/Websites/streamit-main/components/ui/toaster.tsx#Toaster`);


/***/ }),

/***/ "(rsc)/./providers/StoreProvider.tsx":
/*!*************************************!*\
  !*** ./providers/StoreProvider.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   StoreContext: () => (/* binding */ e0),
/* harmony export */   StoreProvider: () => (/* binding */ e1),
/* harmony export */   useVideoCallStoreContext: () => (/* binding */ e2)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Volumes/Apps/Websites/streamit-main/providers/StoreProvider.tsx#StoreContext`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Volumes/Apps/Websites/streamit-main/providers/StoreProvider.tsx#StoreProvider`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Volumes/Apps/Websites/streamit-main/providers/StoreProvider.tsx#useVideoCallStoreContext`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@radix-ui","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/zustand","vendor-chunks/use-sync-external-store","vendor-chunks/class-variance-authority","vendor-chunks/@swc","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FVolumes%2FApps%2FWebsites%2Fstreamit-main%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FApps%2FWebsites%2Fstreamit-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();