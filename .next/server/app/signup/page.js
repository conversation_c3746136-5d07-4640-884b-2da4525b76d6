(()=>{var e={};e.id=966,e.ids=[966],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},9027:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>h,originalPathname:()=>m,pages:()=>d,routeModule:()=>p,tree:()=>o}),s(9791),s(5903),s(5866);var a=s(3191),r=s(8716),l=s(7922),i=s.n(l),n=s(5231),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);s.d(t,c);let o=["",{children:["signup",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,9791)),"/Volumes/Apps/Websites/streamit-main/app/signup/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,5903)),"/Volumes/Apps/Websites/streamit-main/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,5866,23)),"next/dist/client/components/not-found-error"]}],d=["/Volumes/Apps/Websites/streamit-main/app/signup/page.tsx"],m="/signup/page",h={require:s,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/signup/page",pathname:"/signup",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},1155:(e,t,s)=>{Promise.resolve().then(s.bind(s,9114))},9114:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u});var a=s(326),r=s(7577),l=s(5047),i=s(434),n=s(9635),c=s(5932);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=(0,s(6557).Z)("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]]);var d=s(9015),m=s(1216),h=s(2714),p=s(4230),x=s(2933);function u(){let[e,t]=(0,r.useState)({firstName:"",lastName:"",email:"",company:"",password:"",confirmPassword:"",agreeToTerms:!1,subscribeNewsletter:!0}),[s,u]=(0,r.useState)(!1),[f,w]=(0,r.useState)(!1),[j,y]=(0,r.useState)(!1),g=(0,l.useRouter)(),v=async t=>{if(t.preventDefault(),e.password!==e.confirmPassword){alert("Passwords do not match!");return}if(!e.agreeToTerms){alert("Please agree to the Terms of Service and Privacy Policy");return}y(!0),setTimeout(()=>{y(!1),alert("Account created successfully! Welcome to StreamIt Pro!"),g.push("/")},2e3)},N=e=>{let{name:s,value:a,type:r,checked:l}=e.target;t(e=>({...e,[s]:"checkbox"===r?l:a}))};return(0,a.jsxs)(a.Fragment,{children:[a.jsx("div",{className:"animated-bg"}),a.jsx("div",{className:"min-h-screen flex items-center justify-center p-4 relative z-10 pt-20 pb-20",children:(0,a.jsxs)("div",{className:"max-w-md w-full",children:[(0,a.jsxs)("div",{className:"glass p-8",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[a.jsx("h1",{className:"text-3xl font-bold text-white mb-2",children:"Create Account"}),a.jsx("p",{className:"text-white/70",children:"Join thousands of teams using StreamIt Pro"})]}),(0,a.jsxs)("form",{onSubmit:v,className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"firstName",className:"block text-white/80 text-sm font-medium mb-2",children:"First Name"}),(0,a.jsxs)("div",{className:"relative",children:[a.jsx(n.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-white/60"}),a.jsx("input",{type:"text",id:"firstName",name:"firstName",value:e.firstName,onChange:N,required:!0,className:"glass-input w-full pl-10",placeholder:"John"})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"lastName",className:"block text-white/80 text-sm font-medium mb-2",children:"Last Name"}),a.jsx("input",{type:"text",id:"lastName",name:"lastName",value:e.lastName,onChange:N,required:!0,className:"glass-input w-full",placeholder:"Doe"})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"email",className:"block text-white/80 text-sm font-medium mb-2",children:"Email Address"}),(0,a.jsxs)("div",{className:"relative",children:[a.jsx(c.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-white/60"}),a.jsx("input",{type:"email",id:"email",name:"email",value:e.email,onChange:N,required:!0,className:"glass-input w-full pl-10",placeholder:"<EMAIL>"})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"company",className:"block text-white/80 text-sm font-medium mb-2",children:"Company (Optional)"}),(0,a.jsxs)("div",{className:"relative",children:[a.jsx(o,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-white/60"}),a.jsx("input",{type:"text",id:"company",name:"company",value:e.company,onChange:N,className:"glass-input w-full pl-10",placeholder:"Your company name"})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"password",className:"block text-white/80 text-sm font-medium mb-2",children:"Password"}),(0,a.jsxs)("div",{className:"relative",children:[a.jsx(d.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-white/60"}),a.jsx("input",{type:s?"text":"password",id:"password",name:"password",value:e.password,onChange:N,required:!0,className:"glass-input w-full pl-10 pr-10",placeholder:"Create a strong password"}),a.jsx("button",{type:"button",onClick:()=>u(!s),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white",children:s?a.jsx(m.Z,{className:"h-5 w-5"}):a.jsx(h.Z,{className:"h-5 w-5"})})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"confirmPassword",className:"block text-white/80 text-sm font-medium mb-2",children:"Confirm Password"}),(0,a.jsxs)("div",{className:"relative",children:[a.jsx(d.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-white/60"}),a.jsx("input",{type:f?"text":"password",id:"confirmPassword",name:"confirmPassword",value:e.confirmPassword,onChange:N,required:!0,className:"glass-input w-full pl-10 pr-10",placeholder:"Confirm your password"}),a.jsx("button",{type:"button",onClick:()=>w(!f),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white",children:f?a.jsx(m.Z,{className:"h-5 w-5"}):a.jsx(h.Z,{className:"h-5 w-5"})})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("label",{className:"flex items-start gap-3",children:[a.jsx("input",{type:"checkbox",name:"agreeToTerms",checked:e.agreeToTerms,onChange:N,required:!0,className:"w-4 h-4 text-purple-600 bg-transparent border-white/30 rounded focus:ring-purple-500 focus:ring-2 mt-0.5"}),(0,a.jsxs)("span",{className:"text-sm text-white/70",children:["I agree to the"," ",a.jsx(i.default,{href:"/terms",className:"text-purple-400 hover:text-purple-300",children:"Terms of Service"})," ","and"," ",a.jsx(i.default,{href:"/privacy",className:"text-purple-400 hover:text-purple-300",children:"Privacy Policy"})]})]}),(0,a.jsxs)("label",{className:"flex items-center gap-3",children:[a.jsx("input",{type:"checkbox",name:"subscribeNewsletter",checked:e.subscribeNewsletter,onChange:N,className:"w-4 h-4 text-purple-600 bg-transparent border-white/30 rounded focus:ring-purple-500 focus:ring-2"}),a.jsx("span",{className:"text-sm text-white/70",children:"Subscribe to our newsletter for updates and tips"})]})]}),a.jsx("button",{type:"submit",disabled:j,className:`btn-primary w-full flex items-center justify-center gap-2 ${j?"opacity-50 cursor-not-allowed":""}`,children:j?a.jsx("div",{className:"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"}):(0,a.jsxs)(a.Fragment,{children:["Create Account",a.jsx(p.Z,{className:"h-4 w-4"})]})})]}),(0,a.jsxs)("div",{className:"my-6 flex items-center",children:[a.jsx("div",{className:"flex-1 border-t border-white/20"}),a.jsx("span",{className:"px-4 text-white/60 text-sm",children:"or"}),a.jsx("div",{className:"flex-1 border-t border-white/20"})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("button",{className:"btn-secondary w-full flex items-center justify-center gap-2",children:[(0,a.jsxs)("svg",{className:"w-5 h-5",viewBox:"0 0 24 24",children:[a.jsx("path",{fill:"currentColor",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),a.jsx("path",{fill:"currentColor",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),a.jsx("path",{fill:"currentColor",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),a.jsx("path",{fill:"currentColor",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),"Sign up with Google"]}),(0,a.jsxs)("button",{className:"btn-secondary w-full flex items-center justify-center gap-2",children:[a.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:a.jsx("path",{d:"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"})}),"Sign up with Facebook"]})]}),a.jsx("div",{className:"mt-8 text-center",children:(0,a.jsxs)("p",{className:"text-white/70",children:["Already have an account?"," ",a.jsx(i.default,{href:"/login",className:"text-purple-400 hover:text-purple-300 font-medium",children:"Sign in"})]})})]}),(0,a.jsxs)("div",{className:"mt-8 glass p-6",children:[a.jsx("h3",{className:"text-white font-semibold mb-4 text-center",children:"What you get with StreamIt Pro:"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-white/70 text-sm",children:[a.jsx(x.Z,{className:"h-4 w-4 text-green-400"}),a.jsx("span",{children:"14-day free trial, no credit card required"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-white/70 text-sm",children:[a.jsx(x.Z,{className:"h-4 w-4 text-green-400"}),a.jsx("span",{children:"HD video calls with up to 25 participants"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-white/70 text-sm",children:[a.jsx(x.Z,{className:"h-4 w-4 text-green-400"}),a.jsx("span",{children:"Advanced security and encryption"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-white/70 text-sm",children:[a.jsx(x.Z,{className:"h-4 w-4 text-green-400"}),a.jsx("span",{children:"24/7 customer support"})]})]})]})]})})]})}},4230:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(6557).Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},2933:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(6557).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},1216:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(6557).Z)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},2714:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(6557).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},9015:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(6557).Z)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},5932:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(6557).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},9635:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(6557).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},5047:(e,t,s)=>{"use strict";var a=s(7389);s.o(a,"useParams")&&s.d(t,{useParams:function(){return a.useParams}}),s.o(a,"useRouter")&&s.d(t,{useRouter:function(){return a.useRouter}})},9791:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>i,__esModule:()=>l,default:()=>n});var a=s(8570);let r=(0,a.createProxy)(String.raw`/Volumes/Apps/Websites/streamit-main/app/signup/page.tsx`),{__esModule:l,$$typeof:i}=r;r.default;let n=(0,a.createProxy)(String.raw`/Volumes/Apps/Websites/streamit-main/app/signup/page.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[110,653],()=>s(9027));module.exports=a})();