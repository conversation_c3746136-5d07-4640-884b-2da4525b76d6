(()=>{var e={};e.id=327,e.ids=[327],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},8530:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.ZP,__next_app__:()=>x,originalPathname:()=>m,pages:()=>o,routeModule:()=>d,tree:()=>c}),t(3871),t(5698),t(6560);var a=t(3191),l=t(8716),i=t(8001),n=t(5231),r={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(r[e]=()=>n[e]);t.d(s,r);let c=["",{children:["contact",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,3871)),"/Volumes/Apps/Websites/streamit-main/app/contact/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,5698)),"/Volumes/Apps/Websites/streamit-main/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,6560)),"/Volumes/Apps/Websites/streamit-main/app/not-found.tsx"]}],o=["/Volumes/Apps/Websites/streamit-main/app/contact/page.tsx"],m="/contact/page",x={require:t,loadChunk:()=>Promise.resolve()},d=new a.AppPageRouteModule({definition:{kind:l.x.APP_PAGE,page:"/contact/page",pathname:"/contact",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},1469:(e,s,t)=>{Promise.resolve().then(t.bind(t,4062))},4062:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>d,dynamic:()=>x});var a=t(326),l=t(7577),i=t(9730),n=t(5932),r=t(2887),c=t(7636),o=t(9436),m=t(8998);let x="force-dynamic";function d(){let[e,s]=(0,l.useState)({name:"",email:"",company:"",subject:"",message:""}),t=t=>{s({...e,[t.target.name]:t.target.value})};return(0,a.jsxs)(a.Fragment,{children:[a.jsx("div",{className:"animated-bg"}),(0,a.jsxs)("div",{className:"min-h-screen pt-20 relative z-10",children:[a.jsx("section",{className:"py-20 px-4",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto text-center",children:[a.jsx("h1",{className:"text-5xl font-bold text-white mb-6 fade-in",children:"Contact Us"}),a.jsx("p",{className:"text-xl text-white/80 max-w-3xl mx-auto leading-relaxed slide-up",children:"Have questions or need support? We're here to help. Reach out to our team and we'll get back to you as soon as possible."})]})}),a.jsx("section",{className:"py-20 px-4",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,a.jsxs)("div",{className:"grid md:grid-cols-4 gap-8 mb-16",children:[(0,a.jsxs)("div",{className:"glass p-8 text-center",children:[a.jsx(i.Z,{className:"h-12 w-12 text-purple-400 mx-auto mb-4"}),a.jsx("h3",{className:"text-xl font-semibold text-white mb-3",children:"Live Chat"}),a.jsx("p",{className:"text-white/70 mb-4",children:"Get instant help from our support team"}),a.jsx("p",{className:"text-white/60 text-sm",children:"Available 24/7"})]}),(0,a.jsxs)("div",{className:"glass p-8 text-center",children:[a.jsx(n.Z,{className:"h-12 w-12 text-blue-400 mx-auto mb-4"}),a.jsx("h3",{className:"text-xl font-semibold text-white mb-3",children:"Email"}),a.jsx("p",{className:"text-white/70 mb-4",children:"Send us an email and we'll respond within 24 hours"}),a.jsx("p",{className:"text-white/60 text-sm",children:"<EMAIL>"})]}),(0,a.jsxs)("div",{className:"glass p-8 text-center",children:[a.jsx(r.Z,{className:"h-12 w-12 text-green-400 mx-auto mb-4"}),a.jsx("h3",{className:"text-xl font-semibold text-white mb-3",children:"Phone"}),a.jsx("p",{className:"text-white/70 mb-4",children:"Call us for immediate assistance"}),a.jsx("p",{className:"text-white/60 text-sm",children:"+****************"})]}),(0,a.jsxs)("div",{className:"glass p-8 text-center",children:[a.jsx(c.Z,{className:"h-12 w-12 text-orange-400 mx-auto mb-4"}),a.jsx("h3",{className:"text-xl font-semibold text-white mb-3",children:"Office"}),a.jsx("p",{className:"text-white/70 mb-4",children:"Visit our headquarters"}),a.jsx("p",{className:"text-white/60 text-sm",children:"San Francisco, CA"})]})]}),(0,a.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12",children:[(0,a.jsxs)("div",{className:"glass p-8",children:[a.jsx("h2",{className:"text-2xl font-bold text-white mb-6",children:"Send us a Message"}),(0,a.jsxs)("form",{onSubmit:t=>{t.preventDefault(),console.log("Form submitted:",e),alert("Thank you for your message! We'll get back to you soon."),s({name:"",email:"",company:"",subject:"",message:""})},className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"name",className:"block text-white/80 text-sm font-medium mb-2",children:"Full Name *"}),a.jsx("input",{type:"text",id:"name",name:"name",value:e.name,onChange:t,required:!0,className:"glass-input w-full",placeholder:"Your full name"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"email",className:"block text-white/80 text-sm font-medium mb-2",children:"Email Address *"}),a.jsx("input",{type:"email",id:"email",name:"email",value:e.email,onChange:t,required:!0,className:"glass-input w-full",placeholder:"<EMAIL>"})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"company",className:"block text-white/80 text-sm font-medium mb-2",children:"Company"}),a.jsx("input",{type:"text",id:"company",name:"company",value:e.company,onChange:t,className:"glass-input w-full",placeholder:"Your company name"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"subject",className:"block text-white/80 text-sm font-medium mb-2",children:"Subject *"}),(0,a.jsxs)("select",{id:"subject",name:"subject",value:e.subject,onChange:t,required:!0,className:"glass-input w-full",children:[a.jsx("option",{value:"",children:"Select a subject"}),a.jsx("option",{value:"general",children:"General Inquiry"}),a.jsx("option",{value:"support",children:"Technical Support"}),a.jsx("option",{value:"sales",children:"Sales Question"}),a.jsx("option",{value:"partnership",children:"Partnership"}),a.jsx("option",{value:"feedback",children:"Feedback"})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"message",className:"block text-white/80 text-sm font-medium mb-2",children:"Message *"}),a.jsx("textarea",{id:"message",name:"message",value:e.message,onChange:t,required:!0,rows:6,className:"glass-input w-full resize-none",placeholder:"Tell us how we can help you..."})]}),(0,a.jsxs)("button",{type:"submit",className:"btn-primary w-full flex items-center justify-center gap-2",children:[a.jsx(o.Z,{className:"h-4 w-4"}),"Send Message"]})]})]}),(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{className:"glass p-8",children:[a.jsx("h3",{className:"text-xl font-semibold text-white mb-6",children:"Office Hours"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[a.jsx(m.Z,{className:"h-5 w-5 text-purple-400"}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"text-white font-medium",children:"Monday - Friday"}),a.jsx("div",{className:"text-white/60 text-sm",children:"9:00 AM - 6:00 PM PST"})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[a.jsx(m.Z,{className:"h-5 w-5 text-purple-400"}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"text-white font-medium",children:"Saturday"}),a.jsx("div",{className:"text-white/60 text-sm",children:"10:00 AM - 4:00 PM PST"})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[a.jsx(m.Z,{className:"h-5 w-5 text-purple-400"}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"text-white font-medium",children:"Sunday"}),a.jsx("div",{className:"text-white/60 text-sm",children:"Closed"})]})]})]})]}),(0,a.jsxs)("div",{className:"glass p-8",children:[a.jsx("h3",{className:"text-xl font-semibold text-white mb-6",children:"Quick Links"}),(0,a.jsxs)("div",{className:"space-y-3",children:[a.jsx("a",{href:"#",className:"block text-white/70 hover:text-white transition-colors",children:"Help Center & Documentation"}),a.jsx("a",{href:"#",className:"block text-white/70 hover:text-white transition-colors",children:"System Status"}),a.jsx("a",{href:"#",className:"block text-white/70 hover:text-white transition-colors",children:"Feature Requests"}),a.jsx("a",{href:"#",className:"block text-white/70 hover:text-white transition-colors",children:"Bug Reports"}),a.jsx("a",{href:"#",className:"block text-white/70 hover:text-white transition-colors",children:"Community Forum"})]})]}),(0,a.jsxs)("div",{className:"glass p-8",children:[a.jsx("h3",{className:"text-xl font-semibold text-white mb-6",children:"Enterprise Sales"}),a.jsx("p",{className:"text-white/70 mb-4",children:"Looking for a custom solution for your organization? Our enterprise team is ready to help."}),a.jsx("button",{className:"btn-secondary w-full",children:"Contact Enterprise Sales"})]})]})]})]})}),a.jsx("section",{className:"py-20 px-4",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,a.jsxs)("div",{className:"text-center mb-12",children:[a.jsx("h2",{className:"text-3xl font-bold text-white mb-4",children:"Frequently Asked Questions"}),a.jsx("p",{className:"text-white/70",children:"Can't find what you're looking for? Check out our comprehensive FAQ section."})]}),a.jsx("div",{className:"text-center",children:a.jsx("button",{className:"btn-primary",children:"View All FAQs"})})]})})]})]})}},8998:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(6557).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},5932:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(6557).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},7636:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(6557).Z)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},9730:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(6557).Z)("MessageCircle",[["path",{d:"m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z",key:"v2veuj"}]])},2887:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(6557).Z)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},9436:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(6557).Z)("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]])},3871:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>n,__esModule:()=>i,default:()=>c,dynamic:()=>r});var a=t(8570);let l=(0,a.createProxy)(String.raw`/Volumes/Apps/Websites/streamit-main/app/contact/page.tsx`),{__esModule:i,$$typeof:n}=l;l.default;let r=(0,a.createProxy)(String.raw`/Volumes/Apps/Websites/streamit-main/app/contact/page.tsx#dynamic`),c=(0,a.createProxy)(String.raw`/Volumes/Apps/Websites/streamit-main/app/contact/page.tsx#default`)}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[378,628],()=>t(8530));module.exports=a})();