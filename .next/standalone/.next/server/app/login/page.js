(()=>{var e={};e.id=626,e.ids=[626],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5558:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>d,routeModule:()=>p,tree:()=>o}),s(4687),s(5698),s(5866);var r=s(3191),a=s(8716),i=s(7922),l=s.n(i),n=s(5231),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);s.d(t,c);let o=["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,4687)),"/Volumes/Apps/Websites/streamit-main/app/login/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,5698)),"/Volumes/Apps/Websites/streamit-main/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,5866,23)),"next/dist/client/components/not-found-error"]}],d=["/Volumes/Apps/Websites/streamit-main/app/login/page.tsx"],m="/login/page",u={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},4301:(e,t,s)=>{Promise.resolve().then(s.bind(s,1442))},1442:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>p,dynamic:()=>u});var r=s(326),a=s(7577),i=s(5047),l=s(434),n=s(5932),c=s(9015),o=s(1216),d=s(2714),m=s(4230);let u="force-dynamic";function p(){let[e,t]=(0,a.useState)({email:"",password:"",rememberMe:!1}),[s,u]=(0,a.useState)(!1),[p,x]=(0,a.useState)(!1),h=(0,i.useRouter)(),f=async e=>{e.preventDefault(),x(!0),setTimeout(()=>{x(!1),alert("Login successful! Redirecting to dashboard..."),h.push("/")},2e3)},g=e=>{let{name:s,value:r,type:a,checked:i}=e.target;t(e=>({...e,[s]:"checkbox"===a?i:r}))};return(0,r.jsxs)(r.Fragment,{children:[r.jsx("div",{className:"animated-bg"}),r.jsx("div",{className:"min-h-screen flex items-center justify-center p-4 relative z-10 pt-20",children:(0,r.jsxs)("div",{className:"max-w-md w-full",children:[(0,r.jsxs)("div",{className:"glass p-8",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[r.jsx("h1",{className:"text-3xl font-bold text-white mb-2",children:"Welcome Back"}),r.jsx("p",{className:"text-white/70",children:"Sign in to your StreamIt Pro account"})]}),(0,r.jsxs)("form",{onSubmit:f,className:"space-y-6",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"email",className:"block text-white/80 text-sm font-medium mb-2",children:"Email Address"}),(0,r.jsxs)("div",{className:"relative",children:[r.jsx(n.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-white/60"}),r.jsx("input",{type:"email",id:"email",name:"email",value:e.email,onChange:g,required:!0,className:"glass-input w-full pl-10",placeholder:"<EMAIL>"})]})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"password",className:"block text-white/80 text-sm font-medium mb-2",children:"Password"}),(0,r.jsxs)("div",{className:"relative",children:[r.jsx(c.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-white/60"}),r.jsx("input",{type:s?"text":"password",id:"password",name:"password",value:e.password,onChange:g,required:!0,className:"glass-input w-full pl-10 pr-10",placeholder:"Enter your password"}),r.jsx("button",{type:"button",onClick:()=>u(!s),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white",children:s?r.jsx(o.Z,{className:"h-5 w-5"}):r.jsx(d.Z,{className:"h-5 w-5"})})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("label",{className:"flex items-center",children:[r.jsx("input",{type:"checkbox",name:"rememberMe",checked:e.rememberMe,onChange:g,className:"w-4 h-4 text-purple-600 bg-transparent border-white/30 rounded focus:ring-purple-500 focus:ring-2"}),r.jsx("span",{className:"ml-2 text-sm text-white/70",children:"Remember me"})]}),r.jsx(l.default,{href:"/forgot-password",className:"text-sm text-purple-400 hover:text-purple-300",children:"Forgot password?"})]}),r.jsx("button",{type:"submit",disabled:p,className:`btn-primary w-full flex items-center justify-center gap-2 ${p?"opacity-50 cursor-not-allowed":""}`,children:p?r.jsx("div",{className:"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"}):(0,r.jsxs)(r.Fragment,{children:["Sign In",r.jsx(m.Z,{className:"h-4 w-4"})]})})]}),(0,r.jsxs)("div",{className:"my-6 flex items-center",children:[r.jsx("div",{className:"flex-1 border-t border-white/20"}),r.jsx("span",{className:"px-4 text-white/60 text-sm",children:"or"}),r.jsx("div",{className:"flex-1 border-t border-white/20"})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("button",{className:"btn-secondary w-full flex items-center justify-center gap-2",children:[(0,r.jsxs)("svg",{className:"w-5 h-5",viewBox:"0 0 24 24",children:[r.jsx("path",{fill:"currentColor",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),r.jsx("path",{fill:"currentColor",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),r.jsx("path",{fill:"currentColor",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),r.jsx("path",{fill:"currentColor",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),"Continue with Google"]}),(0,r.jsxs)("button",{className:"btn-secondary w-full flex items-center justify-center gap-2",children:[r.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{d:"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"})}),"Continue with Facebook"]})]}),r.jsx("div",{className:"mt-8 text-center",children:(0,r.jsxs)("p",{className:"text-white/70",children:["Don't have an account?"," ",r.jsx(l.default,{href:"/signup",className:"text-purple-400 hover:text-purple-300 font-medium",children:"Sign up for free"})]})})]}),r.jsx("div",{className:"mt-8 text-center",children:(0,r.jsxs)("p",{className:"text-white/60 text-sm",children:["By signing in, you agree to our"," ",r.jsx(l.default,{href:"/terms",className:"text-purple-400 hover:text-purple-300",children:"Terms of Service"})," ","and"," ",r.jsx(l.default,{href:"/privacy",className:"text-purple-400 hover:text-purple-300",children:"Privacy Policy"})]})})]})})]})}},4230:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(6557).Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},1216:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(6557).Z)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},2714:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(6557).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},9015:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(6557).Z)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},5932:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(6557).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},5047:(e,t,s)=>{"use strict";var r=s(7389);s.o(r,"useParams")&&s.d(t,{useParams:function(){return r.useParams}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}})},4687:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>l,__esModule:()=>i,default:()=>c,dynamic:()=>n});var r=s(8570);let a=(0,r.createProxy)(String.raw`/Volumes/Apps/Websites/streamit-main/app/login/page.tsx`),{__esModule:i,$$typeof:l}=a;a.default;let n=(0,r.createProxy)(String.raw`/Volumes/Apps/Websites/streamit-main/app/login/page.tsx#dynamic`),c=(0,r.createProxy)(String.raw`/Volumes/Apps/Websites/streamit-main/app/login/page.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[938,752],()=>s(5558));module.exports=r})();