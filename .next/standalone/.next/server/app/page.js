(()=>{var e={};e.id=931,e.ids=[931],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1485:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.ZP,__next_app__:()=>m,originalPathname:()=>d,pages:()=>o,routeModule:()=>x,tree:()=>c}),s(908),s(5698),s(6560);var a=s(3191),r=s(8716),i=s(8001),n=s(5231),l={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);s.d(t,l);let c=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,908)),"/Volumes/Apps/Websites/streamit-main/app/page.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,5698)),"/Volumes/Apps/Websites/streamit-main/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,6560)),"/Volumes/Apps/Websites/streamit-main/app/not-found.tsx"]}],o=["/Volumes/Apps/Websites/streamit-main/app/page.tsx"],d="/page",m={require:s,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},144:(e,t,s)=>{Promise.resolve().then(s.bind(s,8329))},8329:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>x});var a=s(326),r=s(7577),i=s(5047),n=s(9758),l=s(4893);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let c=(0,s(6557).Z)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]);var o=s(4061),d=s(165),m=s(3634);function x(){let[e,t]=(0,r.useState)(!1),[s,x]=(0,r.useState)(!1),[u,p]=(0,r.useState)(""),[h,g]=(0,r.useState)(""),[y,f]=(0,r.useState)(!1),j=(0,i.useRouter)(),b=()=>{if(!u.trim()){alert("Please enter your name");return}let e=Math.random().toString(36).substring(2,15);localStorage.setItem("userName",u.trim()),t(!1),j.push(`/room/${e}`)},v=()=>{if(!h.trim()||!u.trim()){alert("Please enter both room ID and your name");return}localStorage.setItem("userName",u.trim()),x(!1),j.push(`/room/${h.trim()}`)},w=()=>{t(!1),x(!1),p(""),g("")};return(0,a.jsxs)(a.Fragment,{children:[a.jsx("div",{className:"animated-bg"}),(0,a.jsxs)("div",{className:"min-h-screen flex flex-col items-center justify-center p-4 relative z-10",children:[(0,a.jsxs)("div",{className:"text-center mb-12 fade-in",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center mb-6",children:[a.jsx("div",{className:"w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mr-4 shadow-lg",children:a.jsx(n.Z,{className:"h-8 w-8 text-white"})}),a.jsx("h1",{className:"text-5xl font-bold text-white",children:"StreamIt Pro"})]}),a.jsx("p",{className:"text-xl text-white/80 max-w-2xl mx-auto leading-relaxed",children:"Professional video conferencing platform with crystal-clear HD video, advanced audio processing, and seamless collaboration tools"})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-6 mb-16 slide-up",children:[(0,a.jsxs)("button",{onClick:()=>{t(!0),f(!0)},className:"btn-primary flex items-center gap-3 text-lg px-8 py-4 min-w-[200px] justify-center",children:[a.jsx(l.Z,{className:"h-6 w-6"}),"Start Meeting"]}),(0,a.jsxs)("button",{onClick:()=>{x(!0),f(!1)},className:"btn-secondary flex items-center gap-3 text-lg px-8 py-4 min-w-[200px] justify-center",children:[a.jsx(c,{className:"h-6 w-6"}),"Join Meeting"]})]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-3 gap-8 max-w-4xl w-full bounce-in",children:[(0,a.jsxs)("div",{className:"glass text-center p-8",children:[a.jsx("div",{className:"w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center mx-auto mb-4",children:a.jsx(o.Z,{className:"h-8 w-8 text-white"})}),a.jsx("h3",{className:"text-xl font-semibold text-white mb-3",children:"Multi-Participant"}),a.jsx("p",{className:"text-white/70",children:"Connect with multiple participants in crystal-clear HD video calls with advanced audio processing"})]}),(0,a.jsxs)("div",{className:"glass text-center p-8",children:[a.jsx("div",{className:"w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4",children:a.jsx(d.Z,{className:"h-8 w-8 text-white"})}),a.jsx("h3",{className:"text-xl font-semibold text-white mb-3",children:"Secure & Private"}),a.jsx("p",{className:"text-white/70",children:"End-to-end encrypted communications with WebRTC technology ensuring your privacy and security"})]}),(0,a.jsxs)("div",{className:"glass text-center p-8",children:[a.jsx("div",{className:"w-16 h-16 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4",children:a.jsx(m.Z,{className:"h-8 w-8 text-white"})}),a.jsx("h3",{className:"text-xl font-semibold text-white mb-3",children:"Lightning Fast"}),a.jsx("p",{className:"text-white/70",children:"Optimized for performance with minimal latency, adaptive quality, and seamless user experience"})]})]}),a.jsx("div",{className:"text-center mt-16 text-white/60",children:a.jsx("p",{children:"Built with Next.js, TypeScript, WebRTC & Modern Web Technologies"})})]}),e&&a.jsx("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:(0,a.jsxs)("div",{className:"glass p-8 max-w-md w-full",children:[a.jsx("h2",{className:"text-2xl font-bold text-white mb-6 text-center",children:y?"Start Your Meeting":"Join Meeting"}),(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx("input",{type:"text",placeholder:"Enter your name",value:u,onChange:e=>p(e.target.value),className:"glass-input w-full",autoFocus:!0,onKeyDown:e=>"Enter"===e.key&&b()}),(0,a.jsxs)("div",{className:"flex gap-3",children:[a.jsx("button",{onClick:w,className:"btn-secondary flex-1",children:"Cancel"}),a.jsx("button",{onClick:b,className:"btn-primary flex-1",children:"Start Meeting"})]})]})]})}),s&&a.jsx("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:(0,a.jsxs)("div",{className:"glass p-8 max-w-md w-full",children:[a.jsx("h2",{className:"text-2xl font-bold text-white mb-6 text-center",children:"Join Meeting"}),(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx("input",{type:"text",placeholder:"Enter your name",value:u,onChange:e=>p(e.target.value),className:"glass-input w-full",autoFocus:!0}),a.jsx("input",{type:"text",placeholder:"Enter meeting ID",value:h,onChange:e=>g(e.target.value),className:"glass-input w-full",onKeyDown:e=>"Enter"===e.key&&v()}),(0,a.jsxs)("div",{className:"flex gap-3",children:[a.jsx("button",{onClick:w,className:"btn-secondary flex-1",children:"Cancel"}),a.jsx("button",{onClick:v,className:"btn-primary flex-1",children:"Join Meeting"})]})]})]})})]})}},4893:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(6557).Z)("Play",[["polygon",{points:"5 3 19 12 5 21 5 3",key:"191637"}]])},165:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(6557).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},4061:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(6557).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},3634:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(6557).Z)("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]])},5047:(e,t,s)=>{"use strict";var a=s(7389);s.o(a,"useParams")&&s.d(t,{useParams:function(){return a.useParams}}),s.o(a,"useRouter")&&s.d(t,{useRouter:function(){return a.useRouter}})},908:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>n,__esModule:()=>i,default:()=>l});var a=s(8570);let r=(0,a.createProxy)(String.raw`/Volumes/Apps/Websites/streamit-main/app/page.tsx`),{__esModule:i,$$typeof:n}=r;r.default;let l=(0,a.createProxy)(String.raw`/Volumes/Apps/Websites/streamit-main/app/page.tsx#default`)}};var t=require("../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[378,628],()=>s(1485));module.exports=a})();