(()=>{var e={};e.id=786,e.ids=[786],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},4300:e=>{"use strict";e.exports=require("buffer")},2081:e=>{"use strict";e.exports=require("child_process")},6113:e=>{"use strict";e.exports=require("crypto")},2361:e=>{"use strict";e.exports=require("events")},7147:e=>{"use strict";e.exports=require("fs")},3685:e=>{"use strict";e.exports=require("http")},5687:e=>{"use strict";e.exports=require("https")},1808:e=>{"use strict";e.exports=require("net")},2037:e=>{"use strict";e.exports=require("os")},2781:e=>{"use strict";e.exports=require("stream")},4404:e=>{"use strict";e.exports=require("tls")},6224:e=>{"use strict";e.exports=require("tty")},7310:e=>{"use strict";e.exports=require("url")},3837:e=>{"use strict";e.exports=require("util")},9796:e=>{"use strict";e.exports=require("zlib")},8359:()=>{},3739:()=>{},2939:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,originalPathname:()=>d,pages:()=>h,routeModule:()=>p,tree:()=>c}),s(6298),s(5698),s(5866);var r=s(3191),i=s(8716),n=s(7922),o=s.n(n),a=s(5231),l={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);s.d(t,l);let c=["",{children:["room",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,6298)),"/Volumes/Apps/Websites/streamit-main/app/room/[id]/page.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,6590)),"/Volumes/Apps/Websites/streamit-main/app/room/[id]/layout.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,5698)),"/Volumes/Apps/Websites/streamit-main/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,5866,23)),"next/dist/client/components/not-found-error"]}],h=["/Volumes/Apps/Websites/streamit-main/app/room/[id]/page.tsx"],d="/room/[id]/page",u={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/room/[id]/page",pathname:"/room/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},4695:(e,t,s)=>{Promise.resolve().then(s.bind(s,6245))},5303:()=>{},6245:(e,t,s)=>{"use strict";let r,i,n;s.r(t),s.d(t,{default:()=>tA,dynamic:()=>tR});var o,a={};s.r(a),s.d(a,{Decoder:()=>eD,Encoder:()=>eP,PacketType:()=>o,protocol:()=>eI});var l=s(326),c=s(7577),h=s(5047);let d=e=>{let t;let s=new Set,r=(e,r)=>{let i="function"==typeof e?e(t):e;if(!Object.is(i,t)){let e=t;t=(null!=r?r:"object"!=typeof i||null===i)?i:Object.assign({},t,i),s.forEach(s=>s(t,e))}},i=()=>t,n={setState:r,getState:i,getInitialState:()=>o,subscribe:e=>(s.add(e),()=>s.delete(e)),destroy:()=>{console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),s.clear()}},o=t=e(r,i,n);return n},u=e=>e?d(e):d;var p=s(1508);let{useDebugValue:f}=c,{useSyncExternalStoreWithSelector:m}=p,g=!1,y=e=>e,x=e=>{"function"!=typeof e&&console.warn("[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.");let t="function"==typeof e?u(e):e,s=(e,s)=>(function(e,t=y,s){s&&!g&&(console.warn("[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937"),g=!0);let r=m(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,s);return f(r),r})(t,e,s);return Object.assign(s,t),s},b=new Map,v=e=>{let t=b.get(e);return t?Object.fromEntries(Object.entries(t.stores).map(([e,t])=>[e,t.getState()])):{}},_=(e,t,s)=>{if(void 0===e)return{type:"untracked",connection:t.connect(s)};let r=b.get(s.name);if(r)return{type:"tracked",store:e,...r};let i={connection:t.connect(s),stores:{}};return b.set(s.name,i),{type:"tracked",store:e,...i}},w=(e,t)=>{let s;try{s=JSON.parse(e)}catch(e){console.error("[zustand devtools middleware] Could not parse the received json",e)}void 0!==s&&t(s)},k={roomId:null,isConnected:!1,roomLocked:!1,currentUser:null,participants:new Map,localStream:null,isAudioMuted:!1,isVideoMuted:!1,isScreenSharing:!1,messages:[],unreadCount:0,messageHistory:new Map,isChatOpen:!1,isSettingsOpen:!1,securitySettings:{encryptionEnabled:!0,antiSpamEnabled:!0,maxMessagesPerMinute:10,allowScreenShare:!0,allowFileSharing:!1,requireApprovalToJoin:!1},adminControls:{canMuteAll:!0,canMuteParticipant:!0,canRemoveParticipant:!0,canControlCamera:!0,canManageRoles:!0},blockedUsers:new Set,spamDetection:new Map},C=(n?x(n):x)(((e,t={})=>(s,r,i)=>{let n;let{enabled:o,anonymousActionType:a,store:l,...c}=t;try{n=(null==o||o)&&window.__REDUX_DEVTOOLS_EXTENSION__}catch(e){}if(!n)return o&&console.warn("[zustand devtools middleware] Please install/enable Redux devtools extension"),e(s,r,i);let{connection:h,...d}=_(l,n,c),u=!0;i.setState=(e,t,n)=>{let o=s(e,t);if(!u)return o;let d=void 0===n?{type:a||"anonymous"}:"string"==typeof n?{type:n}:n;return void 0===l?null==h||h.send(d,r()):null==h||h.send({...d,type:`${l}/${d.type}`},{...v(c.name),[l]:i.getState()}),o};let p=(...e)=>{let t=u;u=!1,s(...e),u=t},f=e(i.setState,r,i);if("untracked"===d.type?null==h||h.init(f):(d.stores[d.store]=i,null==h||h.init(Object.fromEntries(Object.entries(d.stores).map(([e,t])=>[e,e===d.store?f:t.getState()])))),i.dispatchFromDevtools&&"function"==typeof i.dispatch){let e=!1,t=i.dispatch;i.dispatch=(...s)=>{"__setState"!==s[0].type||e||(console.warn('[zustand devtools middleware] "__setState" action type is reserved to set state from the devtools. Avoid using it.'),e=!0),t(...s)}}return h.subscribe(e=>{var t;switch(e.type){case"ACTION":if("string"!=typeof e.payload){console.error("[zustand devtools middleware] Unsupported action format");return}return w(e.payload,e=>{if("__setState"===e.type){if(void 0===l){p(e.state);return}1!==Object.keys(e.state).length&&console.error(`
                    [zustand devtools middleware] Unsupported __setState action format. 
                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),
                    and value of this only key should be a state object. Example: { "type": "__setState", "state": { "abc123Store": { "foo": "bar" } } }
                    `);let t=e.state[l];if(null==t)return;JSON.stringify(i.getState())!==JSON.stringify(t)&&p(t);return}i.dispatchFromDevtools&&"function"==typeof i.dispatch&&i.dispatch(e)});case"DISPATCH":switch(e.payload.type){case"RESET":if(p(f),void 0===l)return null==h?void 0:h.init(i.getState());return null==h?void 0:h.init(v(c.name));case"COMMIT":if(void 0===l){null==h||h.init(i.getState());break}return null==h?void 0:h.init(v(c.name));case"ROLLBACK":return w(e.state,e=>{if(void 0===l){p(e),null==h||h.init(i.getState());return}p(e[l]),null==h||h.init(v(c.name))});case"JUMP_TO_STATE":case"JUMP_TO_ACTION":return w(e.state,e=>{if(void 0===l){p(e);return}JSON.stringify(i.getState())!==JSON.stringify(e[l])&&p(e[l])});case"IMPORT_STATE":{let{nextLiftedState:s}=e.payload,r=null==(t=s.computedStates.slice(-1)[0])?void 0:t.state;if(!r)return;void 0===l?p(r):p(r[l]),null==h||h.send(null,s);break}case"PAUSE_RECORDING":return u=!u}return}}),f})((e,t)=>({...k,setRoomId:t=>e({roomId:t}),setConnected:t=>e({isConnected:t}),setCurrentUser:t=>e({currentUser:t}),addParticipant:s=>{let r=new Map(t().participants);r.set(s.id,s),e({participants:r})},removeParticipant:s=>{let r=new Map(t().participants);r.delete(s),e({participants:r})},updateParticipant:(s,r)=>{let i=new Map(t().participants),n=i.get(s);n&&(i.set(s,{...n,...r}),e({participants:i}))},setLocalStream:t=>e({localStream:t}),toggleAudio:()=>{let{isAudioMuted:s,localStream:r}=t();r&&r.getAudioTracks().forEach(e=>{e.enabled=s}),e({isAudioMuted:!s})},toggleVideo:()=>{let{isVideoMuted:s,localStream:r}=t();r&&r.getVideoTracks().forEach(e=>{e.enabled=s}),e({isVideoMuted:!s})},toggleScreenShare:()=>{e(e=>({isScreenSharing:!e.isScreenSharing}))},addMessage:s=>{let{messages:r,securitySettings:i,spamDetection:n}=t();if(i.antiSpamEnabled){let e=Date.now(),t=n.get(s.userId)||{count:0,lastReset:e};e-t.lastReset>6e4&&(t.count=0,t.lastReset=e),t.count++,n.set(s.userId,t),t.count>i.maxMessagesPerMinute&&(s.isSpam=!0)}e({messages:[...r,s],unreadCount:t().isChatOpen?0:t().unreadCount+1,spamDetection:new Map(n)})},clearUnreadCount:()=>e({unreadCount:0}),toggleChat:()=>{let s=!t().isChatOpen;e({isChatOpen:s,unreadCount:s?0:t().unreadCount})},toggleSettings:()=>e(e=>({isSettingsOpen:!e.isSettingsOpen})),muteParticipant:e=>{t().updateParticipant(e,{isAudioMuted:!0})},muteAllParticipants:()=>{let{participants:e}=t();e.forEach((e,s)=>{t().updateParticipant(s,{isAudioMuted:!0})})},unmuteAllParticipants:()=>{let{participants:e}=t();e.forEach((e,s)=>{t().updateParticipant(s,{isAudioMuted:!1})})},promoteToCoHost:e=>{t().updateParticipant(e,{role:"co-host"})},demoteFromCoHost:e=>{t().updateParticipant(e,{role:"participant"})},blockUser:s=>{let r=new Set(t().blockedUsers);r.add(s),e({blockedUsers:r}),t().removeParticipant(s)},unblockUser:s=>{let r=new Set(t().blockedUsers);r.delete(s),e({blockedUsers:r})},lockRoom:()=>e({roomLocked:!0}),unlockRoom:()=>e({roomLocked:!1}),updateSecuritySettings:t=>{e(e=>({securitySettings:{...e.securitySettings,...t}}))},reset:()=>e(k)}),{name:"video-call-store"}));var S=s(3434),E=s.t(S,2);let N=Object.create(null);N.open="0",N.close="1",N.ping="2",N.pong="3",N.message="4",N.upgrade="5",N.noop="6";let j=Object.create(null);Object.keys(N).forEach(e=>{j[N[e]]=e});let O={type:"error",data:"parser error"},T=({type:e,data:t},s,r)=>r(t instanceof ArrayBuffer||ArrayBuffer.isView(t)?s?t:"b"+F(t,!0).toString("base64"):N[e]+(t||"")),F=(e,t)=>Buffer.isBuffer(e)||e instanceof Uint8Array&&!t?e:e instanceof ArrayBuffer?Buffer.from(e):Buffer.from(e.buffer,e.byteOffset,e.byteLength),R=(e,t)=>{if("string"!=typeof e)return{type:"message",data:A(e,t)};let s=e.charAt(0);return"b"===s?{type:"message",data:A(Buffer.from(e.substring(1),"base64"),t)}:j[s]?e.length>1?{type:j[s],data:e.substring(1)}:{type:j[s]}:O},A=(e,t)=>"arraybuffer"===t?e instanceof ArrayBuffer?e:Buffer.isBuffer(e)?e.buffer.slice(e.byteOffset,e.byteOffset+e.byteLength):e.buffer:Buffer.isBuffer(e)?e:Buffer.from(e),L=(e,t)=>{let s=e.length,r=Array(s),i=0;e.forEach((e,n)=>{T(e,!1,e=>{r[n]=e,++i===s&&t(r.join("\x1e"))})})},I=(e,t)=>{let s=e.split("\x1e"),r=[];for(let e=0;e<s.length;e++){let i=R(s[e],t);if(r.push(i),"error"===i.type)break}return r};function P(e){return e.reduce((e,t)=>e+t.length,0)}function M(e,t){if(e[0].length===t)return e.shift();let s=new Uint8Array(t),r=0;for(let i=0;i<t;i++)s[i]=e[0][r++],r===e[0].length&&(e.shift(),r=0);return e.length&&r<e[0].length&&(e[0]=e[0].slice(r)),s}function D(e){if(e)return function(e){for(var t in D.prototype)e[t]=D.prototype[t];return e}(e)}D.prototype.on=D.prototype.addEventListener=function(e,t){return this._callbacks=this._callbacks||{},(this._callbacks["$"+e]=this._callbacks["$"+e]||[]).push(t),this},D.prototype.once=function(e,t){function s(){this.off(e,s),t.apply(this,arguments)}return s.fn=t,this.on(e,s),this},D.prototype.off=D.prototype.removeListener=D.prototype.removeAllListeners=D.prototype.removeEventListener=function(e,t){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var s,r=this._callbacks["$"+e];if(!r)return this;if(1==arguments.length)return delete this._callbacks["$"+e],this;for(var i=0;i<r.length;i++)if((s=r[i])===t||s.fn===t){r.splice(i,1);break}return 0===r.length&&delete this._callbacks["$"+e],this},D.prototype.emit=function(e){this._callbacks=this._callbacks||{};for(var t=Array(arguments.length-1),s=this._callbacks["$"+e],r=1;r<arguments.length;r++)t[r-1]=arguments[r];if(s){s=s.slice(0);for(var r=0,i=s.length;r<i;++r)s[r].apply(this,t)}return this},D.prototype.emitReserved=D.prototype.emit,D.prototype.listeners=function(e){return this._callbacks=this._callbacks||{},this._callbacks["$"+e]||[]},D.prototype.hasListeners=function(e){return!!this.listeners(e).length};let B=process.nextTick,U=global;class q{constructor(){this._cookies=new Map}parseCookies(e){e&&e.forEach(e=>{let t=function(e){let t=e.split("; "),s=t[0].indexOf("=");if(-1===s)return;let r=t[0].substring(0,s).trim();if(!r.length)return;let i=t[0].substring(s+1).trim();34===i.charCodeAt(0)&&(i=i.slice(1,-1));let n={name:r,value:i};for(let e=1;e<t.length;e++){let s=t[e].split("=");if(2!==s.length)continue;let r=s[0].trim(),i=s[1].trim();switch(r){case"Expires":n.expires=new Date(i);break;case"Max-Age":let o=new Date;o.setUTCSeconds(o.getUTCSeconds()+parseInt(i,10)),n.expires=o}}return n}(e);t&&this._cookies.set(t.name,t)})}get cookies(){let e=Date.now();return this._cookies.forEach((t,s)=>{var r;(null===(r=t.expires)||void 0===r?void 0:r.getTime())<e&&this._cookies.delete(s)}),this._cookies.entries()}addCookies(e){let t=[];for(let[e,s]of this.cookies)t.push(`${e}=${s.value}`);t.length&&(e.setDisableHeaderCheck(!0),e.setRequestHeader("cookie",t.join("; ")))}appendCookies(e){for(let[t,s]of this.cookies)e.append("cookie",`${t}=${s.value}`)}}function V(e,...t){return t.reduce((t,s)=>(e.hasOwnProperty(s)&&(t[s]=e[s]),t),{})}let $=U.setTimeout,W=U.clearTimeout;function z(e,t){t.useNativeTimers?(e.setTimeoutFn=$.bind(U),e.clearTimeoutFn=W.bind(U)):(e.setTimeoutFn=U.setTimeout.bind(U),e.clearTimeoutFn=U.clearTimeout.bind(U))}function H(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}var Z=s(614);let G=Z("engine.io-client:transport");class J extends Error{constructor(e,t,s){super(e),this.description=t,this.context=s,this.type="TransportError"}}class Y extends D{constructor(e){super(),this.writable=!1,z(this,e),this.opts=e,this.query=e.query,this.socket=e.socket,this.supportsBinary=!e.forceBase64}onError(e,t,s){return super.emitReserved("error",new J(e,t,s)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return("opening"===this.readyState||"open"===this.readyState)&&(this.doClose(),this.onClose()),this}send(e){"open"===this.readyState?this.write(e):G("transport is not open, discarding packets")}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(e){let t=R(e,this.socket.binaryType);this.onPacket(t)}onPacket(e){super.emitReserved("packet",e)}onClose(e){this.readyState="closed",super.emitReserved("close",e)}pause(e){}createUri(e,t={}){return e+"://"+this._hostname()+this._port()+this.opts.path+this._query(t)}_hostname(){let e=this.opts.hostname;return -1===e.indexOf(":")?e:"["+e+"]"}_port(){return this.opts.port&&(this.opts.secure&&Number(443!==this.opts.port)||!this.opts.secure&&80!==Number(this.opts.port))?":"+this.opts.port:""}_query(e){let t=function(e){let t="";for(let s in e)e.hasOwnProperty(s)&&(t.length&&(t+="&"),t+=encodeURIComponent(s)+"="+encodeURIComponent(e[s]));return t}(e);return t.length?"?"+t:""}}let X=Z("engine.io-client:polling");class K extends Y{constructor(){super(...arguments),this._polling=!1}get name(){return"polling"}doOpen(){this._poll()}pause(e){this.readyState="pausing";let t=()=>{X("paused"),this.readyState="paused",e()};if(this._polling||!this.writable){let e=0;this._polling&&(X("we are currently polling - waiting to pause"),e++,this.once("pollComplete",function(){X("pre-pause polling complete"),--e||t()})),this.writable||(X("we are currently writing - waiting to pause"),e++,this.once("drain",function(){X("pre-pause writing complete"),--e||t()}))}else t()}_poll(){X("polling"),this._polling=!0,this.doPoll(),this.emitReserved("poll")}onData(e){X("polling got data %s",e),I(e,this.socket.binaryType).forEach(e=>{if("opening"===this.readyState&&"open"===e.type&&this.onOpen(),"close"===e.type)return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(e)}),"closed"!==this.readyState&&(this._polling=!1,this.emitReserved("pollComplete"),"open"===this.readyState?this._poll():X('ignoring poll - transport state "%s"',this.readyState))}doClose(){let e=()=>{X("writing close packet"),this.write([{type:"close"}])};"open"===this.readyState?(X("transport open - closing"),e()):(X("transport not open - deferring close"),this.once("open",e))}write(e){this.writable=!1,L(e,e=>{this.doWrite(e,()=>{this.writable=!0,this.emitReserved("drain")})})}uri(){let e=this.opts.secure?"https":"http",t=this.query||{};return!1!==this.opts.timestampRequests&&(t[this.opts.timestampParam]=H()),this.supportsBinary||t.sid||(t.b64=1),this.createUri(e,t)}}let Q=!1;try{Q="undefined"!=typeof XMLHttpRequest&&"withCredentials"in new XMLHttpRequest}catch(e){}let ee=Q,et=Z("engine.io-client:polling");function es(){}class er extends K{constructor(e){if(super(e),"undefined"!=typeof location){let t="https:"===location.protocol,s=location.port;s||(s=t?"443":"80"),this.xd="undefined"!=typeof location&&e.hostname!==location.hostname||s!==e.port}}doWrite(e,t){let s=this.request({method:"POST",data:e});s.on("success",t),s.on("error",(e,t)=>{this.onError("xhr post error",e,t)})}doPoll(){et("xhr poll");let e=this.request();e.on("data",this.onData.bind(this)),e.on("error",(e,t)=>{this.onError("xhr poll error",e,t)}),this.pollXhr=e}}class ei extends D{constructor(e,t,s){super(),this.createRequest=e,z(this,s),this._opts=s,this._method=s.method||"GET",this._uri=t,this._data=void 0!==s.data?s.data:null,this._create()}_create(){var e;let t=V(this._opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");t.xdomain=!!this._opts.xd;let s=this._xhr=this.createRequest(t);try{et("xhr open %s: %s",this._method,this._uri),s.open(this._method,this._uri,!0);try{if(this._opts.extraHeaders)for(let e in s.setDisableHeaderCheck&&s.setDisableHeaderCheck(!0),this._opts.extraHeaders)this._opts.extraHeaders.hasOwnProperty(e)&&s.setRequestHeader(e,this._opts.extraHeaders[e])}catch(e){}if("POST"===this._method)try{s.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch(e){}try{s.setRequestHeader("Accept","*/*")}catch(e){}null===(e=this._opts.cookieJar)||void 0===e||e.addCookies(s),"withCredentials"in s&&(s.withCredentials=this._opts.withCredentials),this._opts.requestTimeout&&(s.timeout=this._opts.requestTimeout),s.onreadystatechange=()=>{var e;3===s.readyState&&(null===(e=this._opts.cookieJar)||void 0===e||e.parseCookies(s.getResponseHeader("set-cookie"))),4===s.readyState&&(200===s.status||1223===s.status?this._onLoad():this.setTimeoutFn(()=>{this._onError("number"==typeof s.status?s.status:0)},0))},et("xhr data %s",this._data),s.send(this._data)}catch(e){this.setTimeoutFn(()=>{this._onError(e)},0);return}"undefined"!=typeof document&&(this._index=ei.requestsCount++,ei.requests[this._index]=this)}_onError(e){this.emitReserved("error",e,this._xhr),this._cleanup(!0)}_cleanup(e){if(void 0!==this._xhr&&null!==this._xhr){if(this._xhr.onreadystatechange=es,e)try{this._xhr.abort()}catch(e){}"undefined"!=typeof document&&delete ei.requests[this._index],this._xhr=null}}_onLoad(){let e=this._xhr.responseText;null!==e&&(this.emitReserved("data",e),this.emitReserved("success"),this._cleanup())}abort(){this._cleanup()}}function en(){for(let e in ei.requests)ei.requests.hasOwnProperty(e)&&ei.requests[e].abort()}ei.requestsCount=0,ei.requests={},"undefined"!=typeof document&&("function"==typeof attachEvent?attachEvent("onunload",en):"function"==typeof addEventListener&&addEventListener("onpagehide"in U?"pagehide":"unload",en,!1)),function(){let e=function(e){let t=e.xdomain;try{if("undefined"!=typeof XMLHttpRequest&&(!t||ee))return new XMLHttpRequest}catch(e){}if(!t)try{return new U[["Active"].concat("Object").join("X")]("Microsoft.XMLHTTP")}catch(e){}}({xdomain:!1});e&&e.responseType}();let eo=S||E;class ea extends er{request(e={}){var t;return Object.assign(e,{xd:this.xd,cookieJar:null===(t=this.socket)||void 0===t?void 0:t._cookieJar},this.opts),new ei(e=>new eo(e),this.uri(),e)}}s(2504),s(3958),s(4036);var el=s(6342);s(359);let ec=Z("engine.io-client:websocket"),eh="undefined"!=typeof navigator&&"string"==typeof navigator.product&&"reactnative"===navigator.product.toLowerCase();class ed extends Y{get name(){return"websocket"}doOpen(){let e=this.uri(),t=this.opts.protocols,s=eh?{}:V(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(s.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(e,t,s)}catch(e){return this.emitReserved("error",e)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=e=>this.onClose({description:"websocket connection closed",context:e}),this.ws.onmessage=e=>this.onData(e.data),this.ws.onerror=e=>this.onError("websocket error",e)}write(e){this.writable=!1;for(let t=0;t<e.length;t++){let s=e[t],r=t===e.length-1;T(s,this.supportsBinary,e=>{try{this.doWrite(s,e)}catch(e){ec("websocket closed before onclose event")}r&&B(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){void 0!==this.ws&&(this.ws.onerror=()=>{},this.ws.close(),this.ws=null)}uri(){let e=this.opts.secure?"wss":"ws",t=this.query||{};return this.opts.timestampRequests&&(t[this.opts.timestampParam]=H()),this.supportsBinary||(t.b64=1),this.createUri(e,t)}}U.WebSocket||U.MozWebSocket;class eu extends ed{createSocket(e,t,s){var r;if(null===(r=this.socket)||void 0===r?void 0:r._cookieJar)for(let[e,t]of(s.headers=s.headers||{},s.headers.cookie="string"==typeof s.headers.cookie?[s.headers.cookie]:s.headers.cookie||[],this.socket._cookieJar.cookies))s.headers.cookie.push(`${e}=${t.value}`);return new el(e,t,s)}doWrite(e,t){let s={};e.options&&(s.compress=e.options.compress),this.opts.perMessageDeflate&&("string"==typeof t?Buffer.byteLength(t):t.length)<this.opts.perMessageDeflate.threshold&&(s.compress=!1),this.ws.send(t,s)}}let ep=Z("engine.io-client:webtransport");class ef extends Y{get name(){return"webtransport"}doOpen(){try{this._transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(e){return this.emitReserved("error",e)}this._transport.closed.then(()=>{ep("transport closed gracefully"),this.onClose()}).catch(e=>{ep("transport closed due to %s",e),this.onError("webtransport error",e)}),this._transport.ready.then(()=>{this._transport.createBidirectionalStream().then(e=>{let t=function(e,t){i||(i=new TextDecoder);let s=[],r=0,n=-1,o=!1;return new TransformStream({transform(a,l){for(s.push(a);;){if(0===r){if(1>P(s))break;let e=M(s,1);o=(128&e[0])==128,r=(n=127&e[0])<126?3:126===n?1:2}else if(1===r){if(2>P(s))break;let e=M(s,2);n=new DataView(e.buffer,e.byteOffset,e.length).getUint16(0),r=3}else if(2===r){if(8>P(s))break;let e=M(s,8),t=new DataView(e.buffer,e.byteOffset,e.length),i=t.getUint32(0);if(i>2097151){l.enqueue(O);break}n=4294967296*i+t.getUint32(4),r=3}else{if(P(s)<n)break;let e=M(s,n);l.enqueue(R(o?e:i.decode(e),t)),r=0}if(0===n||n>e){l.enqueue(O);break}}}})}(Number.MAX_SAFE_INTEGER,this.socket.binaryType),s=e.readable.pipeThrough(t).getReader(),n=new TransformStream({transform(e,t){!function(e,t){if(e.data instanceof ArrayBuffer||ArrayBuffer.isView(e.data))return t(F(e.data,!1));T(e,!0,e=>{r||(r=new TextEncoder),t(r.encode(e))})}(e,s=>{let r;let i=s.length;if(i<126)new DataView((r=new Uint8Array(1)).buffer).setUint8(0,i);else if(i<65536){let e=new DataView((r=new Uint8Array(3)).buffer);e.setUint8(0,126),e.setUint16(1,i)}else{let e=new DataView((r=new Uint8Array(9)).buffer);e.setUint8(0,127),e.setBigUint64(1,BigInt(i))}e.data&&"string"!=typeof e.data&&(r[0]|=128),t.enqueue(r),t.enqueue(s)})}});n.readable.pipeTo(e.writable),this._writer=n.writable.getWriter();let o=()=>{s.read().then(({done:e,value:t})=>{if(e){ep("session is closed");return}ep("received chunk: %o",t),this.onPacket(t),o()}).catch(e=>{ep("an error occurred while reading: %s",e)})};o();let a={type:"open"};this.query.sid&&(a.data=`{"sid":"${this.query.sid}"}`),this._writer.write(a).then(()=>this.onOpen())})})}write(e){this.writable=!1;for(let t=0;t<e.length;t++){let s=e[t],r=t===e.length-1;this._writer.write(s).then(()=>{r&&B(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){var e;null===(e=this._transport)||void 0===e||e.close()}}let em={websocket:eu,webtransport:ef,polling:ea},eg=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,ey=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function ex(e){if(e.length>8e3)throw"URI too long";let t=e,s=e.indexOf("["),r=e.indexOf("]");-1!=s&&-1!=r&&(e=e.substring(0,s)+e.substring(s,r).replace(/:/g,";")+e.substring(r,e.length));let i=eg.exec(e||""),n={},o=14;for(;o--;)n[ey[o]]=i[o]||"";return -1!=s&&-1!=r&&(n.source=t,n.host=n.host.substring(1,n.host.length-1).replace(/;/g,":"),n.authority=n.authority.replace("[","").replace("]","").replace(/;/g,":"),n.ipv6uri=!0),n.pathNames=function(e,t){let s=t.replace(/\/{2,9}/g,"/").split("/");return("/"==t.slice(0,1)||0===t.length)&&s.splice(0,1),"/"==t.slice(-1)&&s.splice(s.length-1,1),s}(0,n.path),n.queryKey=function(e,t){let s={};return t.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(e,t,r){t&&(s[t]=r)}),s}(0,n.query),n}let eb=Z("engine.io-client:socket"),ev="function"==typeof addEventListener&&"function"==typeof removeEventListener,e_=[];ev&&addEventListener("offline",()=>{eb("closing %d connection(s) because the network was lost",e_.length),e_.forEach(e=>e())},!1);class ew extends D{constructor(e,t){if(super(),this.binaryType="nodebuffer",this.writeBuffer=[],this._prevBufferLen=0,this._pingInterval=-1,this._pingTimeout=-1,this._maxPayload=-1,this._pingTimeoutTime=1/0,e&&"object"==typeof e&&(t=e,e=null),e){let s=ex(e);t.hostname=s.host,t.secure="https"===s.protocol||"wss"===s.protocol,t.port=s.port,s.query&&(t.query=s.query)}else t.host&&(t.hostname=ex(t.host).host);z(this,t),this.secure=null!=t.secure?t.secure:"undefined"!=typeof location&&"https:"===location.protocol,t.hostname&&!t.port&&(t.port=this.secure?"443":"80"),this.hostname=t.hostname||("undefined"!=typeof location?location.hostname:"localhost"),this.port=t.port||("undefined"!=typeof location&&location.port?location.port:this.secure?"443":"80"),this.transports=[],this._transportsByName={},t.transports.forEach(e=>{let t=e.prototype.name;this.transports.push(t),this._transportsByName[t]=e}),this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},t),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),"string"==typeof this.opts.query&&(this.opts.query=function(e){let t={},s=e.split("&");for(let e=0,r=s.length;e<r;e++){let r=s[e].split("=");t[decodeURIComponent(r[0])]=decodeURIComponent(r[1])}return t}(this.opts.query)),ev&&(this.opts.closeOnBeforeunload&&(this._beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this._beforeunloadEventListener,!1)),"localhost"!==this.hostname&&(eb("adding listener for the 'offline' event"),this._offlineEventListener=()=>{this._onClose("transport close",{description:"network connection lost"})},e_.push(this._offlineEventListener))),this.opts.withCredentials&&(this._cookieJar=new q),this._open()}createTransport(e){eb('creating transport "%s"',e);let t=Object.assign({},this.opts.query);t.EIO=4,t.transport=e,this.id&&(t.sid=this.id);let s=Object.assign({},this.opts,{query:t,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[e]);return eb("options: %j",s),new this._transportsByName[e](s)}_open(){if(0===this.transports.length){this.setTimeoutFn(()=>{this.emitReserved("error","No transports available")},0);return}let e=this.opts.rememberUpgrade&&ew.priorWebsocketSuccess&&-1!==this.transports.indexOf("websocket")?"websocket":this.transports[0];this.readyState="opening";let t=this.createTransport(e);t.open(),this.setTransport(t)}setTransport(e){eb("setting transport %s",e.name),this.transport&&(eb("clearing existing transport %s",this.transport.name),this.transport.removeAllListeners()),this.transport=e,e.on("drain",this._onDrain.bind(this)).on("packet",this._onPacket.bind(this)).on("error",this._onError.bind(this)).on("close",e=>this._onClose("transport close",e))}onOpen(){eb("socket open"),this.readyState="open",ew.priorWebsocketSuccess="websocket"===this.transport.name,this.emitReserved("open"),this.flush()}_onPacket(e){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState)switch(eb('socket receive: type "%s", data "%s"',e.type,e.data),this.emitReserved("packet",e),this.emitReserved("heartbeat"),e.type){case"open":this.onHandshake(JSON.parse(e.data));break;case"ping":this._sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this._resetPingTimeout();break;case"error":let t=Error("server error");t.code=e.data,this._onError(t);break;case"message":this.emitReserved("data",e.data),this.emitReserved("message",e.data)}else eb('packet received with socket readyState "%s"',this.readyState)}onHandshake(e){this.emitReserved("handshake",e),this.id=e.sid,this.transport.query.sid=e.sid,this._pingInterval=e.pingInterval,this._pingTimeout=e.pingTimeout,this._maxPayload=e.maxPayload,this.onOpen(),"closed"!==this.readyState&&this._resetPingTimeout()}_resetPingTimeout(){this.clearTimeoutFn(this._pingTimeoutTimer);let e=this._pingInterval+this._pingTimeout;this._pingTimeoutTime=Date.now()+e,this._pingTimeoutTimer=this.setTimeoutFn(()=>{this._onClose("ping timeout")},e),this.opts.autoUnref&&this._pingTimeoutTimer.unref()}_onDrain(){this.writeBuffer.splice(0,this._prevBufferLen),this._prevBufferLen=0,0===this.writeBuffer.length?this.emitReserved("drain"):this.flush()}flush(){if("closed"!==this.readyState&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){let e=this._getWritablePackets();eb("flushing %d packets in socket",e.length),this.transport.send(e),this._prevBufferLen=e.length,this.emitReserved("flush")}}_getWritablePackets(){if(!(this._maxPayload&&"polling"===this.transport.name&&this.writeBuffer.length>1))return this.writeBuffer;let e=1;for(let t=0;t<this.writeBuffer.length;t++){let s=this.writeBuffer[t].data;if(s&&(e+="string"==typeof s?function(e){let t=0,s=0;for(let r=0,i=e.length;r<i;r++)(t=e.charCodeAt(r))<128?s+=1:t<2048?s+=2:t<55296||t>=57344?s+=3:(r++,s+=4);return s}(s):Math.ceil(1.33*(s.byteLength||s.size))),t>0&&e>this._maxPayload)return eb("only send %d out of %d packets",t,this.writeBuffer.length),this.writeBuffer.slice(0,t);e+=2}return eb("payload size is %d (max: %d)",e,this._maxPayload),this.writeBuffer}_hasPingExpired(){if(!this._pingTimeoutTime)return!0;let e=Date.now()>this._pingTimeoutTime;return e&&(eb("throttled timer detected, scheduling connection close"),this._pingTimeoutTime=0,B(()=>{this._onClose("ping timeout")},this.setTimeoutFn)),e}write(e,t,s){return this._sendPacket("message",e,t,s),this}send(e,t,s){return this._sendPacket("message",e,t,s),this}_sendPacket(e,t,s,r){if("function"==typeof t&&(r=t,t=void 0),"function"==typeof s&&(r=s,s=null),"closing"===this.readyState||"closed"===this.readyState)return;(s=s||{}).compress=!1!==s.compress;let i={type:e,data:t,options:s};this.emitReserved("packetCreate",i),this.writeBuffer.push(i),r&&this.once("flush",r),this.flush()}close(){let e=()=>{this._onClose("forced close"),eb("socket closing - telling transport to close"),this.transport.close()},t=()=>{this.off("upgrade",t),this.off("upgradeError",t),e()},s=()=>{this.once("upgrade",t),this.once("upgradeError",t)};return("opening"===this.readyState||"open"===this.readyState)&&(this.readyState="closing",this.writeBuffer.length?this.once("drain",()=>{this.upgrading?s():e()}):this.upgrading?s():e()),this}_onError(e){if(eb("socket error %j",e),ew.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&"opening"===this.readyState)return eb("trying next transport"),this.transports.shift(),this._open();this.emitReserved("error",e),this._onClose("transport error",e)}_onClose(e,t){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState){if(eb('socket close with reason: "%s"',e),this.clearTimeoutFn(this._pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),ev&&(this._beforeunloadEventListener&&removeEventListener("beforeunload",this._beforeunloadEventListener,!1),this._offlineEventListener)){let e=e_.indexOf(this._offlineEventListener);-1!==e&&(eb("removing listener for the 'offline' event"),e_.splice(e,1))}this.readyState="closed",this.id=null,this.emitReserved("close",e,t),this.writeBuffer=[],this._prevBufferLen=0}}}ew.protocol=4;class ek extends ew{constructor(){super(...arguments),this._upgrades=[]}onOpen(){if(super.onOpen(),"open"===this.readyState&&this.opts.upgrade){eb("starting upgrade probes");for(let e=0;e<this._upgrades.length;e++)this._probe(this._upgrades[e])}}_probe(e){eb('probing transport "%s"',e);let t=this.createTransport(e),s=!1;ew.priorWebsocketSuccess=!1;let r=()=>{s||(eb('probe transport "%s" opened',e),t.send([{type:"ping",data:"probe"}]),t.once("packet",r=>{if(!s){if("pong"===r.type&&"probe"===r.data)eb('probe transport "%s" pong',e),this.upgrading=!0,this.emitReserved("upgrading",t),t&&(ew.priorWebsocketSuccess="websocket"===t.name,eb('pausing current transport "%s"',this.transport.name),this.transport.pause(()=>{s||"closed"===this.readyState||(eb("changing transport and sending upgrade packet"),c(),this.setTransport(t),t.send([{type:"upgrade"}]),this.emitReserved("upgrade",t),t=null,this.upgrading=!1,this.flush())}));else{eb('probe transport "%s" failed',e);let s=Error("probe error");s.transport=t.name,this.emitReserved("upgradeError",s)}}}))};function i(){s||(s=!0,c(),t.close(),t=null)}let n=s=>{let r=Error("probe error: "+s);r.transport=t.name,i(),eb('probe transport "%s" failed because of error: %s',e,s),this.emitReserved("upgradeError",r)};function o(){n("transport closed")}function a(){n("socket closed")}function l(e){t&&e.name!==t.name&&(eb('"%s" works - aborting "%s"',e.name,t.name),i())}let c=()=>{t.removeListener("open",r),t.removeListener("error",n),t.removeListener("close",o),this.off("close",a),this.off("upgrading",l)};t.once("open",r),t.once("error",n),t.once("close",o),this.once("close",a),this.once("upgrading",l),-1!==this._upgrades.indexOf("webtransport")&&"webtransport"!==e?this.setTimeoutFn(()=>{s||t.open()},200):t.open()}onHandshake(e){this._upgrades=this._filterUpgrades(e.upgrades),super.onHandshake(e)}_filterUpgrades(e){let t=[];for(let s=0;s<e.length;s++)~this.transports.indexOf(e[s])&&t.push(e[s]);return t}}class eC extends ek{constructor(e,t={}){let s="object"==typeof e?e:t;(!s.transports||s.transports&&"string"==typeof s.transports[0])&&(s.transports=(s.transports||["polling","websocket","webtransport"]).map(e=>em[e]).filter(e=>!!e)),super(e,s)}}eC.protocol;var eS=s(6183);let eE=eS("socket.io-client:url"),eN="function"==typeof ArrayBuffer,ej=e=>"function"==typeof ArrayBuffer.isView?ArrayBuffer.isView(e):e.buffer instanceof ArrayBuffer,eO=Object.prototype.toString,eT="function"==typeof Blob||"undefined"!=typeof Blob&&"[object BlobConstructor]"===eO.call(Blob),eF="function"==typeof File||"undefined"!=typeof File&&"[object FileConstructor]"===eO.call(File);function eR(e){return eN&&(e instanceof ArrayBuffer||ej(e))||eT&&e instanceof Blob||eF&&e instanceof File}let eA=s(6610)("socket.io-parser"),eL=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"],eI=5;!function(e){e[e.CONNECT=0]="CONNECT",e[e.DISCONNECT=1]="DISCONNECT",e[e.EVENT=2]="EVENT",e[e.ACK=3]="ACK",e[e.CONNECT_ERROR=4]="CONNECT_ERROR",e[e.BINARY_EVENT=5]="BINARY_EVENT",e[e.BINARY_ACK=6]="BINARY_ACK"}(o||(o={}));class eP{constructor(e){this.replacer=e}encode(e){return(eA("encoding packet %j",e),(e.type===o.EVENT||e.type===o.ACK)&&function e(t,s){if(!t||"object"!=typeof t)return!1;if(Array.isArray(t)){for(let s=0,r=t.length;s<r;s++)if(e(t[s]))return!0;return!1}if(eR(t))return!0;if(t.toJSON&&"function"==typeof t.toJSON&&1==arguments.length)return e(t.toJSON(),!0);for(let s in t)if(Object.prototype.hasOwnProperty.call(t,s)&&e(t[s]))return!0;return!1}(e))?this.encodeAsBinary({type:e.type===o.EVENT?o.BINARY_EVENT:o.BINARY_ACK,nsp:e.nsp,data:e.data,id:e.id}):[this.encodeAsString(e)]}encodeAsString(e){let t=""+e.type;return(e.type===o.BINARY_EVENT||e.type===o.BINARY_ACK)&&(t+=e.attachments+"-"),e.nsp&&"/"!==e.nsp&&(t+=e.nsp+","),null!=e.id&&(t+=e.id),null!=e.data&&(t+=JSON.stringify(e.data,this.replacer)),eA("encoded %j as %s",e,t),t}encodeAsBinary(e){let t=function(e){let t=[],s=e.data;return e.data=function e(t,s){if(!t)return t;if(eR(t)){let e={_placeholder:!0,num:s.length};return s.push(t),e}if(Array.isArray(t)){let r=Array(t.length);for(let i=0;i<t.length;i++)r[i]=e(t[i],s);return r}if("object"==typeof t&&!(t instanceof Date)){let r={};for(let i in t)Object.prototype.hasOwnProperty.call(t,i)&&(r[i]=e(t[i],s));return r}return t}(s,t),e.attachments=t.length,{packet:e,buffers:t}}(e),s=this.encodeAsString(t.packet),r=t.buffers;return r.unshift(s),r}}function eM(e){return"[object Object]"===Object.prototype.toString.call(e)}class eD extends D{constructor(e){super(),this.reviver=e}add(e){let t;if("string"==typeof e){if(this.reconstructor)throw Error("got plaintext data when reconstructing a packet");let s=(t=this.decodeString(e)).type===o.BINARY_EVENT;s||t.type===o.BINARY_ACK?(t.type=s?o.EVENT:o.ACK,this.reconstructor=new eB(t),0===t.attachments&&super.emitReserved("decoded",t)):super.emitReserved("decoded",t)}else if(eR(e)||e.base64){if(this.reconstructor)(t=this.reconstructor.takeBinaryData(e))&&(this.reconstructor=null,super.emitReserved("decoded",t));else throw Error("got binary data when not reconstructing a packet")}else throw Error("Unknown type: "+e)}decodeString(e){let t=0,s={type:Number(e.charAt(0))};if(void 0===o[s.type])throw Error("unknown packet type "+s.type);if(s.type===o.BINARY_EVENT||s.type===o.BINARY_ACK){let r=t+1;for(;"-"!==e.charAt(++t)&&t!=e.length;);let i=e.substring(r,t);if(i!=Number(i)||"-"!==e.charAt(t))throw Error("Illegal attachments");s.attachments=Number(i)}if("/"===e.charAt(t+1)){let r=t+1;for(;++t&&","!==e.charAt(t)&&t!==e.length;);s.nsp=e.substring(r,t)}else s.nsp="/";let r=e.charAt(t+1);if(""!==r&&Number(r)==r){let r=t+1;for(;++t;){let s=e.charAt(t);if(null==s||Number(s)!=s){--t;break}if(t===e.length)break}s.id=Number(e.substring(r,t+1))}if(e.charAt(++t)){let r=this.tryParse(e.substr(t));if(eD.isPayloadValid(s.type,r))s.data=r;else throw Error("invalid payload")}return eA("decoded %s as %j",e,s),s}tryParse(e){try{return JSON.parse(e,this.reviver)}catch(e){return!1}}static isPayloadValid(e,t){switch(e){case o.CONNECT:return eM(t);case o.DISCONNECT:return void 0===t;case o.CONNECT_ERROR:return"string"==typeof t||eM(t);case o.EVENT:case o.BINARY_EVENT:return Array.isArray(t)&&("number"==typeof t[0]||"string"==typeof t[0]&&-1===eL.indexOf(t[0]));case o.ACK:case o.BINARY_ACK:return Array.isArray(t)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}class eB{constructor(e){this.packet=e,this.buffers=[],this.reconPack=e}takeBinaryData(e){if(this.buffers.push(e),this.buffers.length===this.reconPack.attachments){var t,s;let e=(t=this.reconPack,s=this.buffers,t.data=function e(t,s){if(!t)return t;if(t&&!0===t._placeholder){if("number"==typeof t.num&&t.num>=0&&t.num<s.length)return s[t.num];throw Error("illegal attachments")}if(Array.isArray(t))for(let r=0;r<t.length;r++)t[r]=e(t[r],s);else if("object"==typeof t)for(let r in t)Object.prototype.hasOwnProperty.call(t,r)&&(t[r]=e(t[r],s));return t}(t.data,s),delete t.attachments,t);return this.finishedReconstruction(),e}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}function eU(e,t,s){return e.on(t,s),function(){e.off(t,s)}}let eq=eS("socket.io-client:socket"),eV=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class e$ extends D{constructor(e,t,s){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=e,this.nsp=t,s&&s.auth&&(this.auth=s.auth),this._opts=Object.assign({},s),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;let e=this.io;this.subs=[eU(e,"open",this.onopen.bind(this)),eU(e,"packet",this.onpacket.bind(this)),eU(e,"error",this.onerror.bind(this)),eU(e,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected||(this.subEvents(),this.io._reconnecting||this.io.open(),"open"===this.io._readyState&&this.onopen()),this}open(){return this.connect()}send(...e){return e.unshift("message"),this.emit.apply(this,e),this}emit(e,...t){var s,r,i;if(eV.hasOwnProperty(e))throw Error('"'+e.toString()+'" is a reserved event name');if(t.unshift(e),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(t),this;let n={type:o.EVENT,data:t};if(n.options={},n.options.compress=!1!==this.flags.compress,"function"==typeof t[t.length-1]){let e=this.ids++;eq("emitting packet with ack id %d",e);let s=t.pop();this._registerAckCallback(e,s),n.id=e}let a=null===(r=null===(s=this.io.engine)||void 0===s?void 0:s.transport)||void 0===r?void 0:r.writable,l=this.connected&&!(null===(i=this.io.engine)||void 0===i?void 0:i._hasPingExpired());return this.flags.volatile&&!a?eq("discard packet as the transport is not currently writable"):l?(this.notifyOutgoingListeners(n),this.packet(n)):this.sendBuffer.push(n),this.flags={},this}_registerAckCallback(e,t){var s;let r=null!==(s=this.flags.timeout)&&void 0!==s?s:this._opts.ackTimeout;if(void 0===r){this.acks[e]=t;return}let i=this.io.setTimeoutFn(()=>{delete this.acks[e];for(let t=0;t<this.sendBuffer.length;t++)this.sendBuffer[t].id===e&&(eq("removing packet with ack id %d from the buffer",e),this.sendBuffer.splice(t,1));eq("event with ack id %d has timed out after %d ms",e,r),t.call(this,Error("operation has timed out"))},r),n=(...e)=>{this.io.clearTimeoutFn(i),t.apply(this,e)};n.withError=!0,this.acks[e]=n}emitWithAck(e,...t){return new Promise((s,r)=>{let i=(e,t)=>e?r(e):s(t);i.withError=!0,t.push(i),this.emit(e,...t)})}_addToQueue(e){let t;"function"==typeof e[e.length-1]&&(t=e.pop());let s={id:this._queueSeq++,tryCount:0,pending:!1,args:e,flags:Object.assign({fromQueue:!0},this.flags)};e.push((e,...r)=>{if(s===this._queue[0])return null!==e?s.tryCount>this._opts.retries&&(eq("packet [%d] is discarded after %d tries",s.id,s.tryCount),this._queue.shift(),t&&t(e)):(eq("packet [%d] was successfully sent",s.id),this._queue.shift(),t&&t(null,...r)),s.pending=!1,this._drainQueue()}),this._queue.push(s),this._drainQueue()}_drainQueue(e=!1){if(eq("draining queue"),!this.connected||0===this._queue.length)return;let t=this._queue[0];if(t.pending&&!e){eq("packet [%d] has already been sent and is waiting for an ack",t.id);return}t.pending=!0,t.tryCount++,eq("sending packet [%d] (try n\xb0%d)",t.id,t.tryCount),this.flags=t.flags,this.emit.apply(this,t.args)}packet(e){e.nsp=this.nsp,this.io._packet(e)}onopen(){eq("transport is open - connecting"),"function"==typeof this.auth?this.auth(e=>{this._sendConnectPacket(e)}):this._sendConnectPacket(this.auth)}_sendConnectPacket(e){this.packet({type:o.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},e):e})}onerror(e){this.connected||this.emitReserved("connect_error",e)}onclose(e,t){eq("close (%s)",e),this.connected=!1,delete this.id,this.emitReserved("disconnect",e,t),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach(e=>{if(!this.sendBuffer.some(t=>String(t.id)===e)){let t=this.acks[e];delete this.acks[e],t.withError&&t.call(this,Error("socket has been disconnected"))}})}onpacket(e){if(!(e.nsp!==this.nsp))switch(e.type){case o.CONNECT:e.data&&e.data.sid?this.onconnect(e.data.sid,e.data.pid):this.emitReserved("connect_error",Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case o.EVENT:case o.BINARY_EVENT:this.onevent(e);break;case o.ACK:case o.BINARY_ACK:this.onack(e);break;case o.DISCONNECT:this.ondisconnect();break;case o.CONNECT_ERROR:this.destroy();let t=Error(e.data.message);t.data=e.data.data,this.emitReserved("connect_error",t)}}onevent(e){let t=e.data||[];eq("emitting event %j",t),null!=e.id&&(eq("attaching ack callback to event"),t.push(this.ack(e.id))),this.connected?this.emitEvent(t):this.receiveBuffer.push(Object.freeze(t))}emitEvent(e){if(this._anyListeners&&this._anyListeners.length)for(let t of this._anyListeners.slice())t.apply(this,e);super.emit.apply(this,e),this._pid&&e.length&&"string"==typeof e[e.length-1]&&(this._lastOffset=e[e.length-1])}ack(e){let t=this,s=!1;return function(...r){s||(s=!0,eq("sending ack %j",r),t.packet({type:o.ACK,id:e,data:r}))}}onack(e){let t=this.acks[e.id];if("function"!=typeof t){eq("bad ack %s",e.id);return}delete this.acks[e.id],eq("calling ack %s with %j",e.id,e.data),t.withError&&e.data.unshift(null),t.apply(this,e.data)}onconnect(e,t){eq("socket connected with id %s",e),this.id=e,this.recovered=t&&this._pid===t,this._pid=t,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach(e=>this.emitEvent(e)),this.receiveBuffer=[],this.sendBuffer.forEach(e=>{this.notifyOutgoingListeners(e),this.packet(e)}),this.sendBuffer=[]}ondisconnect(){eq("server disconnect (%s)",this.nsp),this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach(e=>e()),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&(eq("performing disconnect (%s)",this.nsp),this.packet({type:o.DISCONNECT})),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(e){return this.flags.compress=e,this}get volatile(){return this.flags.volatile=!0,this}timeout(e){return this.flags.timeout=e,this}onAny(e){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(e),this}prependAny(e){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(e),this}offAny(e){if(!this._anyListeners)return this;if(e){let t=this._anyListeners;for(let s=0;s<t.length;s++)if(e===t[s]){t.splice(s,1);break}}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(e){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(e),this}prependAnyOutgoing(e){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(e),this}offAnyOutgoing(e){if(!this._anyOutgoingListeners)return this;if(e){let t=this._anyOutgoingListeners;for(let s=0;s<t.length;s++)if(e===t[s]){t.splice(s,1);break}}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(e){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length)for(let t of this._anyOutgoingListeners.slice())t.apply(this,e.data)}}function eW(e){e=e||{},this.ms=e.min||100,this.max=e.max||1e4,this.factor=e.factor||2,this.jitter=e.jitter>0&&e.jitter<=1?e.jitter:0,this.attempts=0}eW.prototype.duration=function(){var e=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var t=Math.random(),s=Math.floor(t*this.jitter*e);e=(1&Math.floor(10*t))==0?e-s:e+s}return 0|Math.min(e,this.max)},eW.prototype.reset=function(){this.attempts=0},eW.prototype.setMin=function(e){this.ms=e},eW.prototype.setMax=function(e){this.max=e},eW.prototype.setJitter=function(e){this.jitter=e};let ez=eS("socket.io-client:manager");class eH extends D{constructor(e,t){var s;super(),this.nsps={},this.subs=[],e&&"object"==typeof e&&(t=e,e=void 0),(t=t||{}).path=t.path||"/socket.io",this.opts=t,z(this,t),this.reconnection(!1!==t.reconnection),this.reconnectionAttempts(t.reconnectionAttempts||1/0),this.reconnectionDelay(t.reconnectionDelay||1e3),this.reconnectionDelayMax(t.reconnectionDelayMax||5e3),this.randomizationFactor(null!==(s=t.randomizationFactor)&&void 0!==s?s:.5),this.backoff=new eW({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(null==t.timeout?2e4:t.timeout),this._readyState="closed",this.uri=e;let r=t.parser||a;this.encoder=new r.Encoder,this.decoder=new r.Decoder,this._autoConnect=!1!==t.autoConnect,this._autoConnect&&this.open()}reconnection(e){return arguments.length?(this._reconnection=!!e,e||(this.skipReconnect=!0),this):this._reconnection}reconnectionAttempts(e){return void 0===e?this._reconnectionAttempts:(this._reconnectionAttempts=e,this)}reconnectionDelay(e){var t;return void 0===e?this._reconnectionDelay:(this._reconnectionDelay=e,null===(t=this.backoff)||void 0===t||t.setMin(e),this)}randomizationFactor(e){var t;return void 0===e?this._randomizationFactor:(this._randomizationFactor=e,null===(t=this.backoff)||void 0===t||t.setJitter(e),this)}reconnectionDelayMax(e){var t;return void 0===e?this._reconnectionDelayMax:(this._reconnectionDelayMax=e,null===(t=this.backoff)||void 0===t||t.setMax(e),this)}timeout(e){return arguments.length?(this._timeout=e,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&0===this.backoff.attempts&&this.reconnect()}open(e){if(ez("readyState %s",this._readyState),~this._readyState.indexOf("open"))return this;ez("opening %s",this.uri),this.engine=new eC(this.uri,this.opts);let t=this.engine,s=this;this._readyState="opening",this.skipReconnect=!1;let r=eU(t,"open",function(){s.onopen(),e&&e()}),i=t=>{ez("error"),this.cleanup(),this._readyState="closed",this.emitReserved("error",t),e?e(t):this.maybeReconnectOnOpen()},n=eU(t,"error",i);if(!1!==this._timeout){let e=this._timeout;ez("connect attempt will timeout after %d",e);let s=this.setTimeoutFn(()=>{ez("connect attempt timed out after %d",e),r(),i(Error("timeout")),t.close()},e);this.opts.autoUnref&&s.unref(),this.subs.push(()=>{this.clearTimeoutFn(s)})}return this.subs.push(r),this.subs.push(n),this}connect(e){return this.open(e)}onopen(){ez("open"),this.cleanup(),this._readyState="open",this.emitReserved("open");let e=this.engine;this.subs.push(eU(e,"ping",this.onping.bind(this)),eU(e,"data",this.ondata.bind(this)),eU(e,"error",this.onerror.bind(this)),eU(e,"close",this.onclose.bind(this)),eU(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(e){try{this.decoder.add(e)}catch(e){this.onclose("parse error",e)}}ondecoded(e){B(()=>{this.emitReserved("packet",e)},this.setTimeoutFn)}onerror(e){ez("error",e),this.emitReserved("error",e)}socket(e,t){let s=this.nsps[e];return s?this._autoConnect&&!s.active&&s.connect():(s=new e$(this,e,t),this.nsps[e]=s),s}_destroy(e){for(let e of Object.keys(this.nsps))if(this.nsps[e].active){ez("socket %s is still active, skipping close",e);return}this._close()}_packet(e){ez("writing packet %j",e);let t=this.encoder.encode(e);for(let s=0;s<t.length;s++)this.engine.write(t[s],e.options)}cleanup(){ez("cleanup"),this.subs.forEach(e=>e()),this.subs.length=0,this.decoder.destroy()}_close(){ez("disconnect"),this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close")}disconnect(){return this._close()}onclose(e,t){var s;ez("closed due to %s",e),this.cleanup(),null===(s=this.engine)||void 0===s||s.close(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",e,t),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;let e=this;if(this.backoff.attempts>=this._reconnectionAttempts)ez("reconnect failed"),this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{let t=this.backoff.duration();ez("will wait %dms before reconnect attempt",t),this._reconnecting=!0;let s=this.setTimeoutFn(()=>{!e.skipReconnect&&(ez("attempting reconnect"),this.emitReserved("reconnect_attempt",e.backoff.attempts),e.skipReconnect||e.open(t=>{t?(ez("reconnect attempt error"),e._reconnecting=!1,e.reconnect(),this.emitReserved("reconnect_error",t)):(ez("reconnect success"),e.onreconnect())}))},t);this.opts.autoUnref&&s.unref(),this.subs.push(()=>{this.clearTimeoutFn(s)})}}onreconnect(){let e=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",e)}}let eZ=eS("socket.io-client"),eG={};function eJ(e,t){let s;"object"==typeof e&&(t=e,e=void 0);let r=function(e,t="",s){let r=e;s=s||"undefined"!=typeof location&&location,null==e&&(e=s.protocol+"//"+s.host),"string"==typeof e&&("/"===e.charAt(0)&&(e="/"===e.charAt(1)?s.protocol+e:s.host+e),/^(https?|wss?):\/\//.test(e)||(eE("protocol-less url %s",e),e=void 0!==s?s.protocol+"//"+e:"https://"+e),eE("parse %s",e),r=ex(e)),!r.port&&(/^(http|ws)$/.test(r.protocol)?r.port="80":/^(http|ws)s$/.test(r.protocol)&&(r.port="443")),r.path=r.path||"/";let i=-1!==r.host.indexOf(":")?"["+r.host+"]":r.host;return r.id=r.protocol+"://"+i+":"+r.port+t,r.href=r.protocol+"://"+i+(s&&s.port===r.port?"":":"+r.port),r}(e,(t=t||{}).path||"/socket.io"),i=r.source,n=r.id,o=r.path,a=eG[n]&&o in eG[n].nsps;return t.forceNew||t["force new connection"]||!1===t.multiplex||a?(eZ("ignoring socket cache for %s",i),s=new eH(i,t)):(eG[n]||(eZ("new io instance for %s",i),eG[n]=new eH(i,t)),s=eG[n]),r.query&&!t.query&&(t.query=r.queryKey),s.socket(r.path,t)}Object.assign(eJ,{Manager:eH,Socket:e$,io:eJ,connect:eJ});class eY{connect(){return this.socket?.connected||(this.socket=eJ(window.location.origin,{transports:["websocket","polling"],upgrade:!0}),this.socket.on("connect",()=>{console.log("Connected to server:",this.socket?.id)}),this.socket.on("disconnect",()=>{console.log("Disconnected from server")}),this.socket.on("connect_error",e=>{console.error("Connection error:",e)})),this.socket}disconnect(){this.socket&&(this.socket.disconnect(),this.socket=null)}joinRoom(e,t,s){this.socket&&(this.roomId=e,this.socket.emit("join-room",{roomId:e,userId:t,userName:s}))}leaveRoom(){this.socket&&this.roomId&&(this.socket.emit("leave-room",{roomId:this.roomId}),this.roomId=null)}sendSignal(e,t){this.socket&&this.roomId&&this.socket.emit("signal",{roomId:this.roomId,targetUserId:e,signal:t})}sendChatMessage(e,t){this.socket&&this.roomId&&this.socket.emit("chat-message",{roomId:this.roomId,message:e,userName:t,timestamp:new Date().toISOString()})}on(e,t){this.socket&&this.socket.on(e,t)}off(e,t){this.socket&&this.socket.off(e,t)}getSocket(){return this.socket}isConnected(){return this.socket?.connected||!1}constructor(){this.socket=null,this.roomId=null}}let eX=new eY,eK={iceServers:[{urls:"stun:stun.l.google.com:19302"},{urls:"stun:stun1.l.google.com:19302"}],iceCandidatePoolSize:10,bundlePolicy:"max-bundle",rtcpMuxPolicy:"require"},eQ={video:{width:{ideal:1280,max:1920},height:{ideal:720,max:1080},frameRate:{ideal:30,max:60},facingMode:"user"},audio:{echoCancellation:!0,noiseSuppression:!0,autoGainControl:!0,sampleRate:48e3}},e0={video:{width:{ideal:1920,max:3840},height:{ideal:1080,max:2160},frameRate:{ideal:30,max:60}},audio:!0};class e1{constructor(){this.peerConnections=new Map,this.localStream=null,this.screenStream=null,this.cameraStream=null,this.currentVideoDeviceId=null,this.currentAudioDeviceId=null,this.setupEventHandlers()}setupEventHandlers(){}async getUserMedia(e=eQ){try{this.localStream=await navigator.mediaDevices.getUserMedia(e),this.cameraStream=this.localStream;let t=this.localStream.getVideoTracks();t.length>0&&(this.currentVideoDeviceId=t[0].getSettings().deviceId||null);let s=this.localStream.getAudioTracks();return s.length>0&&(this.currentAudioDeviceId=s[0].getSettings().deviceId||null),this.localStream}catch(e){throw console.error("Error accessing media devices:",e),e}}async getDisplayMedia(e=e0){try{return this.screenStream=await navigator.mediaDevices.getDisplayMedia(e),this.screenStream.getVideoTracks()[0].onended=()=>{this.restoreVideoTrack()},this.screenStream}catch(e){throw console.error("Error accessing screen share:",e),e}}replaceAudioTrack(e){if(!this.localStream)return;this.localStream.getAudioTracks().forEach(e=>e.stop());let t=e.getAudioTracks()[0];t&&this.localStream.addTrack(t),this.updateAudioTracks()}restoreVideoTrack(){if(!this.localStream||!this.cameraStream)return;this.localStream.getVideoTracks().forEach(e=>e.stop());let e=this.cameraStream.getVideoTracks()[0];e&&this.localStream.addTrack(e.clone()),this.updateVideoTracks(),this.screenStream&&(this.screenStream.getTracks().forEach(e=>e.stop()),this.screenStream=null)}updateVideoTracks(){if(!this.localStream)return;let e=this.localStream.getVideoTracks()[0];e&&this.peerConnections.forEach(t=>{let s=t.getSenders().find(e=>e.track?.kind==="video");s&&s.replaceTrack(e)})}updateAudioTracks(){if(!this.localStream)return;let e=this.localStream.getAudioTracks()[0];e&&this.peerConnections.forEach(t=>{let s=t.getSenders().find(e=>e.track?.kind==="audio");s&&s.replaceTrack(e)})}async switchCamera(e){if(this.localStream)try{let t=await navigator.mediaDevices.getUserMedia({video:{deviceId:{exact:e}},audio:!this.currentAudioDeviceId||{deviceId:{exact:this.currentAudioDeviceId}}});return this.localStream.getTracks().forEach(e=>e.stop()),this.localStream=t,this.cameraStream=t,this.currentVideoDeviceId=e,this.updateVideoTracks(),t}catch(e){throw console.error("Error switching camera:",e),Error("Failed to switch camera")}}createPeerConnection(e){let t=new RTCPeerConnection(eK);return this.localStream&&this.localStream.getTracks().forEach(e=>{t.addTrack(e,this.localStream)}),t.ontrack=t=>{let[s]=t.streams;this.onStreamCallback&&this.onStreamCallback(e,s)},t.onicecandidate=t=>{t.candidate&&this.sendSignal(e,{type:"ice-candidate",candidate:t.candidate})},t.onconnectionstatechange=()=>{console.log(`Connection state for ${e}:`,t.connectionState),("disconnected"===t.connectionState||"failed"===t.connectionState)&&(this.removePeerConnection(e),this.onUserDisconnectedCallback&&this.onUserDisconnectedCallback(e))},this.peerConnections.set(e,t),t}async createOffer(e){let t=this.peerConnections.get(e)||this.createPeerConnection(e),s=await t.createOffer({offerToReceiveAudio:!0,offerToReceiveVideo:!0});return await t.setLocalDescription(s),s}async createAnswer(e,t){let s=this.peerConnections.get(e)||this.createPeerConnection(e);await s.setRemoteDescription(t);let r=await s.createAnswer();return await s.setLocalDescription(r),r}async handleAnswer(e,t){let s=this.peerConnections.get(e);s&&await s.setRemoteDescription(t)}async handleIceCandidate(e,t){let s=this.peerConnections.get(e);s&&await s.addIceCandidate(t)}removePeerConnection(e){let t=this.peerConnections.get(e);t&&(t.close(),this.peerConnections.delete(e))}replaceVideoTrack(e){let t=e.getVideoTracks()[0];this.peerConnections.forEach(async e=>{let s=e.getSenders().find(e=>e.track&&"video"===e.track.kind);s&&t&&await s.replaceTrack(t)})}onStream(e){this.onStreamCallback=e}onUserDisconnected(e){this.onUserDisconnectedCallback=e}sendSignal(e,t){}setSendSignalCallback(e){this.sendSignal=e}cleanup(){this.peerConnections.forEach(e=>e.close()),this.peerConnections.clear(),this.localStream&&(this.localStream.getTracks().forEach(e=>e.stop()),this.localStream=null)}getLocalStream(){return this.localStream}getPeerConnection(e){return this.peerConnections.get(e)}}let e2=new e1;var e3=s(6557);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let e6=(0,e3.Z)("Crown",[["path",{d:"m2 4 3 12h14l3-12-6 7-4-7-4 7-6-7zm3 16h14",key:"zkxr6b"}]]),e4=(0,e3.Z)("Mic",[["path",{d:"M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z",key:"131961"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]]),e9=(0,e3.Z)("MicOff",[["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}],["path",{d:"M18.89 13.23A7.12 7.12 0 0 0 19 12v-2",key:"80xlxr"}],["path",{d:"M5 10v2a7 7 0 0 0 12 5",key:"p2k8kg"}],["path",{d:"M15 9.34V5a3 3 0 0 0-5.68-1.33",key:"1gzdoj"}],["path",{d:"M9 9v3a3 3 0 0 0 5.12 2.12",key:"r2i35w"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]]);var e8=s(9758);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let e5=(0,e3.Z)("VideoOff",[["path",{d:"M10.66 6H14a2 2 0 0 1 2 2v2.34l1 1L22 8v8",key:"ubwiq0"}],["path",{d:"M16 16a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h2l10 10Z",key:"1l10zd"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]]);var e7=s(2634);function te({participant:e,stream:t,isLocal:s,isFeatured:r=!1}){let i=(0,c.useRef)(null),n=t&&t.getVideoTracks().length>0&&!e.isVideoMuted,o=t&&t.getAudioTracks().length>0&&!e.isAudioMuted;return(0,l.jsxs)("div",{className:`video-container relative overflow-hidden ${r?"w-full h-full":"w-full h-full max-w-sm max-h-64"}`,children:[n?l.jsx("video",{ref:i,autoPlay:!0,playsInline:!0,muted:s,className:"w-full h-full object-cover rounded-lg"}):l.jsx("div",{className:"w-full h-full bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center rounded-lg",children:(0,l.jsxs)("div",{className:"text-center",children:[l.jsx("div",{className:`${r?"w-24 h-24":"w-16 h-16"} bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-3 shadow-lg`,children:l.jsx("span",{className:`text-white ${r?"text-xl":"text-lg"} font-bold`,children:e.name.charAt(0).toUpperCase()})}),l.jsx("p",{className:`text-white ${r?"text-lg":"text-sm"} font-medium`,children:e.name}),l.jsx("p",{className:"text-white/60 text-xs",children:"Camera is off"})]})}),(0,l.jsxs)("div",{className:"absolute bottom-2 left-2 right-2 flex items-center justify-between",children:[l.jsx("div",{className:"glass-dark px-2 py-1 rounded-lg",children:(0,l.jsxs)("div",{className:"flex items-center gap-1",children:[s&&l.jsx(e6,{className:"h-3 w-3 text-yellow-400"}),l.jsx("span",{className:"text-white text-xs font-medium",children:s?"You":e.name})]})}),(0,l.jsxs)("div",{className:"flex items-center space-x-1",children:[l.jsx("div",{className:`p-1 rounded-full ${o?"bg-green-500/80":"bg-red-500/80"} backdrop-blur-sm`,children:o?l.jsx(e4,{className:"h-3 w-3 text-white"}):l.jsx(e9,{className:"h-3 w-3 text-white"})}),l.jsx("div",{className:`p-1 rounded-full ${n?"bg-green-500/80":"bg-red-500/80"} backdrop-blur-sm`,children:n?l.jsx(e8.Z,{className:"h-3 w-3 text-white"}):l.jsx(e5,{className:"h-3 w-3 text-white"})}),e.isScreenSharing&&l.jsx("div",{className:"p-1 rounded-full bg-blue-500/80 backdrop-blur-sm",children:l.jsx(e7.Z,{className:"h-3 w-3 text-white"})})]})]}),s&&l.jsx("div",{className:"absolute top-2 left-2",children:l.jsx("div",{className:"glass-dark px-2 py-1 rounded-lg",children:(0,l.jsxs)("span",{className:"text-white text-xs font-medium flex items-center gap-1",children:[l.jsx(e6,{className:"h-2 w-2 text-yellow-400"}),"Host"]})})}),l.jsx("div",{className:"absolute top-2 right-2",children:(0,l.jsxs)("div",{className:"flex space-x-0.5",children:[l.jsx("div",{className:"w-0.5 h-2 bg-green-400 rounded-full"}),l.jsx("div",{className:"w-0.5 h-3 bg-green-400 rounded-full"}),l.jsx("div",{className:"w-0.5 h-4 bg-green-400 rounded-full"})]})})]})}function tt(){let{currentUser:e,participants:t,localStream:s}=C(),r=Array.from(t.values()),i=r.length+1;return l.jsx("div",{className:"flex-1 p-4",children:l.jsx("div",{className:"glass h-full p-4 overflow-hidden",children:(0,l.jsxs)("div",{className:`
          grid gap-3 h-full
          ${1===i||2===i?"grid-cols-1":i<=4||i<=6?"grid-cols-2":"grid-cols-3"}
          ${1===i?"grid-rows-1":2===i||i<=4?"grid-rows-2":"grid-rows-3"}
          place-items-center
        `,children:[e&&l.jsx(te,{participant:e,stream:s,isLocal:!0,isFeatured:1===i},e.id),r.map((e,t)=>l.jsx(te,{participant:e,stream:e.stream,isLocal:!1,isFeatured:2===i&&0===t},e.id))]})})})}var ts=s(8378),tr=s(4019);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let ti=(0,e3.Z)("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]),tn=(0,e3.Z)("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]);var to=s(165),ta=s(8393),tl=s(2933);function tc({isOpen:e,onClose:t}){let[s,r]=(0,c.useState)("video"),[i,n]=(0,c.useState)([]),[o,a]=(0,c.useState)(""),[h,d]=(0,c.useState)(""),[u,p]=(0,c.useState)(""),[f,m]=(0,c.useState)(!1),[g,y]=(0,c.useState)("none"),[x,b]=(0,c.useState)("hd"),[v,_]=(0,c.useState)("high"),[w,k]=(0,c.useState)(!0),[S,E]=(0,c.useState)(!0),[N,j]=(0,c.useState)(!0),[O,T]=(0,c.useState)(!0),[F,R]=(0,c.useState)(!0),[A,L]=(0,c.useState)(!1),[I,P]=(0,c.useState)(!1),{currentUser:M}=C(),D=async e=>{try{a(e);let t=await navigator.mediaDevices.getUserMedia({video:{deviceId:{exact:e},width:{ideal:1280},height:{ideal:720}},audio:!1});e2.replaceVideoTrack(t),L(!1)}catch(e){console.error("Error changing camera:",e)}},B=async e=>{try{d(e),await navigator.mediaDevices.getUserMedia({video:!1,audio:{deviceId:{exact:e},echoCancellation:S,noiseSuppression:w,autoGainControl:N}}),P(!1)}catch(e){console.error("Error changing microphone:",e)}},U=e=>{y(e)},q=e=>{b(e)};if(!e)return null;let V=i.filter(e=>"videoinput"===e.kind),$=i.filter(e=>"audioinput"===e.kind);return i.filter(e=>"audiooutput"===e.kind),l.jsx("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:(0,l.jsxs)("div",{className:"glass max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-white/10",children:[(0,l.jsxs)("h2",{className:"text-2xl font-bold text-white flex items-center gap-3",children:[l.jsx(ts.Z,{className:"h-6 w-6"}),"Meeting Settings"]}),l.jsx("button",{onClick:t,className:"glass-button p-2 text-white hover:text-purple-300",children:l.jsx(tr.Z,{className:"h-6 w-6"})})]}),(0,l.jsxs)("div",{className:"flex h-[600px]",children:[l.jsx("div",{className:"w-64 p-4 border-r border-white/10",children:l.jsx("div",{className:"space-y-2",children:[{id:"video",name:"Video",icon:ti},{id:"audio",name:"Audio",icon:e4},{id:"background",name:"Background",icon:tn},{id:"security",name:"Security",icon:to.Z},{id:"general",name:"General",icon:ts.Z}].map(e=>(0,l.jsxs)("button",{onClick:()=>r(e.id),className:`w-full flex items-center gap-3 p-3 rounded-lg transition-all ${s===e.id?"bg-purple-500/30 text-white":"text-white/70 hover:bg-white/10 hover:text-white"}`,children:[l.jsx(e.icon,{className:"h-5 w-5"}),e.name]},e.id))})}),(0,l.jsxs)("div",{className:"flex-1 p-6 overflow-y-auto",children:["video"===s&&(0,l.jsxs)("div",{className:"space-y-6",children:[l.jsx("h3",{className:"text-xl font-semibold text-white mb-4",children:"Video Settings"}),(0,l.jsxs)("div",{children:[l.jsx("label",{className:"block text-white text-sm font-medium mb-2",children:"Camera"}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsxs)("button",{onClick:()=>L(!A),className:"glass-input w-full flex items-center justify-between",children:[l.jsx("span",{children:V.find(e=>e.deviceId===o)?.label||"Select Camera"}),l.jsx(ta.Z,{className:"h-4 w-4"})]}),A&&l.jsx("div",{className:"absolute top-full left-0 right-0 mt-1 glass-dark rounded-lg border border-white/20 z-10",children:V.map(e=>(0,l.jsxs)("button",{onClick:()=>D(e.deviceId),className:"w-full p-3 text-left text-white hover:bg-white/10 flex items-center justify-between",children:[e.label,o===e.deviceId&&l.jsx(tl.Z,{className:"h-4 w-4"})]},e.deviceId))})]})]}),(0,l.jsxs)("div",{children:[l.jsx("label",{className:"block text-white text-sm font-medium mb-2",children:"Video Quality"}),l.jsx("div",{className:"grid grid-cols-2 gap-2",children:[{id:"sd",name:"SD (480p)",resolution:"640x480"},{id:"hd",name:"HD (720p)",resolution:"1280x720"},{id:"fhd",name:"Full HD (1080p)",resolution:"1920x1080"},{id:"4k",name:"4K (2160p)",resolution:"3840x2160"}].map(e=>(0,l.jsxs)("button",{onClick:()=>q(e.id),className:`p-3 rounded-lg border transition-all ${x===e.id?"bg-purple-500/30 border-purple-500 text-white":"bg-white/10 border-white/20 text-white/70 hover:bg-white/20"}`,children:[l.jsx("div",{className:"font-medium",children:e.name}),l.jsx("div",{className:"text-xs opacity-70",children:e.resolution})]},e.id))})]})]}),"audio"===s&&(0,l.jsxs)("div",{className:"space-y-6",children:[l.jsx("h3",{className:"text-xl font-semibold text-white mb-4",children:"Audio Settings"}),(0,l.jsxs)("div",{children:[l.jsx("label",{className:"block text-white text-sm font-medium mb-2",children:"Microphone"}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsxs)("button",{onClick:()=>P(!I),className:"glass-input w-full flex items-center justify-between",children:[l.jsx("span",{children:$.find(e=>e.deviceId===h)?.label||"Select Microphone"}),l.jsx(ta.Z,{className:"h-4 w-4"})]}),I&&l.jsx("div",{className:"absolute top-full left-0 right-0 mt-1 glass-dark rounded-lg border border-white/20 z-10",children:$.map(e=>(0,l.jsxs)("button",{onClick:()=>B(e.deviceId),className:"w-full p-3 text-left text-white hover:bg-white/10 flex items-center justify-between",children:[e.label,h===e.deviceId&&l.jsx(tl.Z,{className:"h-4 w-4"})]},e.deviceId))})]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[l.jsx("h4",{className:"text-white font-medium",children:"Audio Enhancement"}),(0,l.jsxs)("label",{className:"flex items-center justify-between",children:[l.jsx("span",{className:"text-white",children:"Noise Suppression"}),l.jsx("input",{type:"checkbox",checked:w,onChange:e=>k(e.target.checked),className:"w-5 h-5 rounded bg-white/10 border-white/20"})]}),(0,l.jsxs)("label",{className:"flex items-center justify-between",children:[l.jsx("span",{className:"text-white",children:"Echo Cancellation"}),l.jsx("input",{type:"checkbox",checked:S,onChange:e=>E(e.target.checked),className:"w-5 h-5 rounded bg-white/10 border-white/20"})]}),(0,l.jsxs)("label",{className:"flex items-center justify-between",children:[l.jsx("span",{className:"text-white",children:"Auto Gain Control"}),l.jsx("input",{type:"checkbox",checked:N,onChange:e=>j(e.target.checked),className:"w-5 h-5 rounded bg-white/10 border-white/20"})]})]})]}),"background"===s&&(0,l.jsxs)("div",{className:"space-y-6",children:[l.jsx("h3",{className:"text-xl font-semibold text-white mb-4",children:"Background Effects"}),l.jsx("div",{className:"grid grid-cols-3 gap-4",children:[{id:"none",name:"None",preview:null},{id:"blur",name:"Blur Background",preview:null},{id:"office",name:"Modern Office",preview:"/backgrounds/office.jpg"},{id:"nature",name:"Nature Scene",preview:"/backgrounds/nature.jpg"},{id:"abstract",name:"Abstract Blue",preview:"/backgrounds/abstract.jpg"},{id:"gradient",name:"Purple Gradient",preview:"/backgrounds/gradient.jpg"}].map(e=>(0,l.jsxs)("button",{onClick:()=>U(e.id),className:`p-4 rounded-lg border-2 transition-all ${g===e.id?"border-purple-500 bg-purple-500/20":"border-white/20 bg-white/10 hover:border-white/40"}`,children:[l.jsx("div",{className:"w-full h-20 bg-gradient-to-br from-gray-600 to-gray-800 rounded-lg mb-2 flex items-center justify-center",children:"blur"===e.id?l.jsx(e7.Z,{className:"h-8 w-8 text-white"}):l.jsx(tn,{className:"h-8 w-8 text-white"})}),l.jsx("div",{className:"text-white text-sm font-medium",children:e.name})]},e.id))})]}),"security"===s&&(0,l.jsxs)("div",{className:"space-y-6",children:[l.jsx("h3",{className:"text-xl font-semibold text-white mb-4",children:"Security Settings"}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("label",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{children:[l.jsx("span",{className:"text-white font-medium",children:"End-to-End Encryption"}),l.jsx("p",{className:"text-white/60 text-sm",children:"Encrypt all video and audio streams"})]}),l.jsx("input",{type:"checkbox",checked:O,onChange:e=>T(e.target.checked),className:"w-5 h-5 rounded bg-white/10 border-white/20"})]}),(0,l.jsxs)("label",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{children:[l.jsx("span",{className:"text-white font-medium",children:"Anti-Spam Protection"}),l.jsx("p",{className:"text-white/60 text-sm",children:"Prevent message flooding in chat"})]}),l.jsx("input",{type:"checkbox",checked:F,onChange:e=>R(e.target.checked),className:"w-5 h-5 rounded bg-white/10 border-white/20"})]})]})]}),"general"===s&&(0,l.jsxs)("div",{className:"space-y-6",children:[l.jsx("h3",{className:"text-xl font-semibold text-white mb-4",children:"General Settings"}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{children:[l.jsx("label",{className:"block text-white text-sm font-medium mb-2",children:"Display Name"}),l.jsx("input",{type:"text",value:M?.name||"",className:"glass-input w-full",placeholder:"Your display name"})]}),(0,l.jsxs)("div",{children:[l.jsx("label",{className:"block text-white text-sm font-medium mb-2",children:"Meeting Theme"}),(0,l.jsxs)("select",{className:"glass-input w-full",children:[l.jsx("option",{value:"dark",children:"Dark Theme"}),l.jsx("option",{value:"light",children:"Light Theme"}),l.jsx("option",{value:"auto",children:"Auto"})]})]})]})]})]})]}),(0,l.jsxs)("div",{className:"flex items-center justify-end gap-3 p-6 border-t border-white/10",children:[l.jsx("button",{onClick:t,className:"btn-secondary px-6 py-2",children:"Cancel"}),l.jsx("button",{onClick:t,className:"btn-primary px-6 py-2",children:"Save Changes"})]})]})})}var th=s(6633),td=s(2280);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let tu=(0,e3.Z)("MonitorOff",[["path",{d:"M17 17H4a2 2 0 0 1-2-2V5c0-1.5 1-2 1-2",key:"k0q8oc"}],["path",{d:"M22 15V5a2 2 0 0 0-2-2H9",key:"cp1ac0"}],["path",{d:"M8 21h8",key:"1ev6f3"}],["path",{d:"M12 17v4",key:"1riwvh"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]);function tp(){let[e,t]=(0,c.useState)(!1),[s,r]=(0,c.useState)(null),[i,n]=(0,c.useState)(!1),[o,a]=(0,c.useState)(!1),[h,d]=(0,c.useState)(!1),[u,p]=(0,c.useState)([]),[f,m]=(0,c.useState)(""),[g,y]=(0,c.useState)(""),{toggleAudio:x,toggleVideo:b,isAudioMuted:v,isVideoMuted:_,setLocalStream:w}=C();(0,c.useCallback)(async()=>{try{await navigator.mediaDevices.getUserMedia({audio:!0,video:!0});let e=(await navigator.mediaDevices.enumerateDevices()).filter(e=>"audioinput"===e.kind||"videoinput"===e.kind).map(e=>({deviceId:e.deviceId,label:e.label||`${e.kind} ${e.deviceId.slice(0,8)}`,kind:e.kind}));if(p(e),!f){let t=e.find(e=>"videoinput"===e.kind);t&&m(t.deviceId)}if(!g){let t=e.find(e=>"audioinput"===e.kind);t&&y(t.deviceId)}}catch(e){console.error("Error loading devices:",e)}},[f,g]);let k=e=>{r(e),setTimeout(()=>r(null),2e3)},S=async()=>{try{if(e)E();else{let e=await e2.getDisplayMedia();await e2.replaceVideoTrack(e),e.getVideoTracks()[0].onended=()=>{E()},t(!0),k("Screen sharing started")}}catch(e){console.error("Error toggling screen share:",e),k("Failed to toggle screen share")}},E=async()=>{try{let e=await e2.getUserMedia();e2.replaceVideoTrack(e),t(!1),k("Screen sharing stopped")}catch(e){console.error("Error stopping screen share:",e),k("Failed to stop screen share")}},N=(0,c.useCallback)(async e=>{if(e===f){n(!1);return}try{await e2.switchCamera(e),m(e),n(!1),k("Camera changed")}catch(e){console.error("Error switching camera:",e),k("Failed to switch camera")}},[f]),j=(0,c.useCallback)(async e=>{if(e===g){a(!1);return}try{y(e),a(!1),k("Microphone changed")}catch(e){console.error("Error switching microphone:",e),k("Failed to switch microphone")}},[g]),O=u.filter(e=>"videoinput"===e.kind),T=u.filter(e=>"audioinput"===e.kind);return(0,l.jsxs)("div",{className:"p-4",children:[(0,l.jsxs)("div",{className:"video-controls",children:[(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsxs)("div",{className:"flex",children:[l.jsx("button",{onClick:()=>{x(),k(v?"Microphone unmuted":"Microphone muted")},className:`control-btn ${v?"active":"inactive"} rounded-r-none`,title:v?"Unmute microphone":"Mute microphone",children:v?l.jsx(e9,{className:"h-6 w-6"}):l.jsx(e4,{className:"h-6 w-6"})}),T.length>1&&l.jsx("button",{onClick:()=>a(!o),className:"control-btn inactive rounded-l-none border-l border-white/20 w-8",title:"Select microphone",children:l.jsx(th.Z,{className:"h-4 w-4"})})]}),o&&T.length>1&&l.jsx("div",{className:"absolute bottom-full left-0 mb-2 glass-dark rounded-lg border border-white/20 min-w-48 z-10",children:(0,l.jsxs)("div",{className:"p-2",children:[(0,l.jsxs)("div",{className:"text-white text-xs font-medium mb-2 flex items-center gap-2",children:[l.jsx(td.Z,{className:"h-3 w-3"}),"Select Microphone"]}),T.map(e=>l.jsx("div",{className:"px-4 py-2 text-white hover:bg-white/10 cursor-pointer flex items-center",onClick:()=>j(e.deviceId),children:l.jsx("span",{className:g===e.deviceId?"font-semibold":"",children:e.label})},e.deviceId))]})})]}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsxs)("div",{className:"flex",children:[l.jsx("button",{onClick:()=>{b(),k(_?"Camera turned on":"Camera turned off")},className:`control-btn ${_?"active":"inactive"} rounded-r-none`,title:_?"Turn on camera":"Turn off camera",children:_?l.jsx(e5,{className:"h-6 w-6"}):l.jsx(e8.Z,{className:"h-6 w-6"})}),O.length>1&&l.jsx("button",{onClick:()=>n(!i),className:"control-btn inactive rounded-l-none border-l border-white/20 w-8",title:"Select camera",children:l.jsx(th.Z,{className:"h-4 w-4"})})]}),i&&O.length>1&&l.jsx("div",{className:"absolute bottom-full left-0 mb-2 glass-dark rounded-lg border border-white/20 min-w-48 z-10",children:(0,l.jsxs)("div",{className:"p-2",children:[(0,l.jsxs)("div",{className:"text-white text-xs font-medium mb-2 flex items-center gap-2",children:[l.jsx(ti,{className:"h-3 w-3"}),"Select Camera"]}),O.map(e=>l.jsx("div",{className:"px-4 py-2 text-white hover:bg-white/10 cursor-pointer flex items-center",onClick:()=>N(e.deviceId),children:l.jsx("span",{className:f===e.deviceId?"font-semibold":"",children:e.label})},e.deviceId))]})})]}),l.jsx("button",{onClick:S,className:`control-btn ${e?"active":"inactive"}`,title:e?"Stop screen sharing":"Share screen",children:e?l.jsx(tu,{className:"h-6 w-6"}):l.jsx(e7.Z,{className:"h-6 w-6"})}),l.jsx("button",{onClick:()=>d(!0),className:"p-2 rounded-full hover:bg-gray-200 transition-colors","aria-label":"Settings",children:l.jsx(ts.Z,{className:"w-6 h-6"})})]}),l.jsx(tc,{isOpen:h,onClose:()=>d(!1)}),s&&l.jsx("div",{className:"fixed bottom-20 left-1/2 transform -translate-x-1/2 glass-dark px-4 py-2 rounded-lg text-white z-50 fade-in",children:s})]})}var tf=s(9730),tm=s(9436);function tg(){let[e,t]=(0,c.useState)(""),s=(0,c.useRef)(null),{messages:r,currentUser:i,toggleChat:n,clearUnreadCount:o,securitySettings:a,blockUser:h}=C(),d=i?.role==="host"||i?.role==="co-host",u=e=>{d&&h(e)},p=e=>e.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"});return(0,l.jsxs)("div",{className:"chat-container flex flex-col h-full",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between p-3 border-b border-white/10",children:[(0,l.jsxs)("h3",{className:"text-white font-semibold text-sm flex items-center gap-2",children:[l.jsx(tf.Z,{className:"h-4 w-4"}),"Chat"]}),l.jsx("div",{className:"flex items-center gap-2",children:r.length>0&&(0,l.jsxs)("span",{className:"text-white/60 text-xs",children:[r.length," messages"]})})]}),(0,l.jsxs)("div",{className:"flex-1 overflow-y-auto p-3 space-y-3",children:[0===r.length?(0,l.jsxs)("div",{className:"text-center text-white/60 py-8",children:[l.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-3",children:l.jsx(tm.Z,{className:"h-6 w-6 text-white"})}),l.jsx("p",{className:"text-sm font-medium mb-1",children:"No messages yet"}),l.jsx("p",{className:"text-xs",children:"Start the conversation!"})]}):r.filter(e=>!e.isBlocked).map(e=>(0,l.jsxs)("div",{className:`flex flex-col ${e.userId===i?.id?"items-end":"items-start"} ${e.isSpam?"opacity-50":""}`,children:[(0,l.jsxs)("div",{className:`chat-message max-w-[90%] p-2 relative ${e.userId===i?.id?"bg-gradient-to-r from-purple-500 to-pink-500":e.isSpam?"bg-red-500/20 border border-red-500/50":"bg-white/10"}`,children:[l.jsx("p",{className:"text-white text-xs leading-relaxed",children:e.message}),e.isSpam&&l.jsx("div",{className:"absolute top-0 right-0 bg-red-500 text-white text-xs px-1 rounded-bl",children:"SPAM"})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-1 mt-1 text-xs text-white/50",children:[l.jsx("span",{className:"font-medium text-xs",children:e.userName}),l.jsx("span",{children:"•"}),l.jsx("span",{className:"text-xs",children:p(e.timestamp)}),d&&e.userId!==i?.id&&l.jsx("button",{onClick:()=>u(e.userId),className:"text-red-400 hover:text-red-300 text-xs ml-2",children:"Block"})]})]},e.id)),l.jsx("div",{ref:s})]}),l.jsx("div",{className:"p-3 border-t border-white/10",children:(0,l.jsxs)("form",{onSubmit:s=>{s.preventDefault(),e.trim()&&i&&(eX.sendChatMessage(e.trim(),i.name),t(""))},className:"flex space-x-2",children:[l.jsx("input",{type:"text",value:e,onChange:e=>t(e.target.value),placeholder:"Type a message...",className:"glass-input flex-1 text-sm py-2",maxLength:500,autoComplete:"off"}),l.jsx("button",{type:"submit",disabled:!e.trim(),className:`p-2 rounded-lg transition-all ${e.trim()?"bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white":"bg-white/10 text-white/50 cursor-not-allowed"}`,children:l.jsx(tm.Z,{className:"h-4 w-4"})})]})})]})}var ty=s(4061),tx=s(9015);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let tb=(0,e3.Z)("Unlock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 9.9-1",key:"1mm8w8"}]]),tv=(0,e3.Z)("Hand",[["path",{d:"M18 11V6a2 2 0 0 0-2-2v0a2 2 0 0 0-2 2v0",key:"aigmz7"}],["path",{d:"M14 10V4a2 2 0 0 0-2-2v0a2 2 0 0 0-2 2v2",key:"1n6bmn"}],["path",{d:"M10 10.5V6a2 2 0 0 0-2-2v0a2 2 0 0 0-2 2v8",key:"a9iiix"}],["path",{d:"M18 8a2 2 0 1 1 4 0v6a8 8 0 0 1-8 8h-2c-2.8 0-4.5-.86-5.99-2.34l-3.6-3.6a2 2 0 0 1 2.83-2.82L7 15",key:"1s1gnw"}]]),t_=(0,e3.Z)("MoreVertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]]),tw=(0,e3.Z)("ShieldOff",[["path",{d:"M19.7 14a6.9 6.9 0 0 0 .3-2V5l-8-3-3.2 1.2",key:"342pvf"}],["path",{d:"m2 2 20 20",key:"1ooewy"}],["path",{d:"M4.7 4.7 4 5v7c0 6 8 10 8 10a20.3 20.3 0 0 0 5.62-4.38",key:"p0ycf4"}]]),tk=(0,e3.Z)("UserMinus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]);function tC(){let[e,t]=(0,c.useState)(null),{currentUser:s,participants:r,roomLocked:i,muteParticipant:n,muteAllParticipants:o,removeParticipant:a,promoteToCoHost:h,demoteFromCoHost:d,lockRoom:u,unlockRoom:p,blockUser:f}=C(),m=Array.from(r.values()),g=s?[s,...m]:m,y=e=>{let t=[];return e.isAudioMuted?t.push(l.jsx(e9,{className:"h-3 w-3 text-red-400"},"mic")):t.push(l.jsx(e4,{className:"h-3 w-3 text-green-400"},"mic")),e.isVideoMuted?t.push(l.jsx(e5,{className:"h-3 w-3 text-red-400"},"video")):t.push(l.jsx(e8.Z,{className:"h-3 w-3 text-green-400"},"video")),e.isScreenSharing&&t.push(l.jsx(e7.Z,{className:"h-3 w-3 text-blue-400"},"screen")),t},x=s?.role==="host"||s?.role==="co-host",b=s?.role==="host",v=(e,s)=>{switch(e){case"mute":n(s);break;case"remove":a(s);break;case"promote":h(s);break;case"demote":d(s);break;case"block":f(s)}t(null)};return(0,l.jsxs)("div",{className:"chat-container flex flex-col h-full",children:[l.jsx("div",{className:"p-3 border-b border-white/10",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("h3",{className:"text-white font-semibold text-sm flex items-center gap-2",children:[l.jsx(ty.Z,{className:"h-4 w-4"}),"Participants (",g.length,")",i&&l.jsx(tx.Z,{className:"h-3 w-3 text-yellow-400"})]}),x&&(0,l.jsxs)("div",{className:"flex items-center gap-1",children:[l.jsx("button",{onClick:o,className:"glass-button p-1 text-white/60 hover:text-white",title:"Mute all participants",children:l.jsx(e9,{className:"h-3 w-3"})}),b&&l.jsx("button",{onClick:()=>i?p():u(),className:"glass-button p-1 text-white/60 hover:text-white",title:i?"Unlock room":"Lock room",children:i?l.jsx(tb,{className:"h-3 w-3"}):l.jsx(tx.Z,{className:"h-3 w-3"})})]})]})}),l.jsx("div",{className:"flex-1 overflow-y-auto p-2 space-y-2",children:g.map(r=>{let i=r.id===s?.id;return l.jsx("div",{className:"glass-button p-2 hover:bg-white/20 transition-all relative",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsxs)("div",{className:"w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center relative",children:[l.jsx("span",{className:"text-white text-xs font-bold",children:r.name.charAt(0).toUpperCase()}),r.isHandRaised&&l.jsx("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 rounded-full flex items-center justify-center",children:l.jsx(tv,{className:"h-2 w-2 text-black"})})]}),(0,l.jsxs)("div",{className:"flex flex-col flex-1 min-w-0",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,l.jsxs)("span",{className:"text-white text-xs font-medium truncate",children:[r.name,i&&" (You)"]}),"host"===r.role&&l.jsx(e6,{className:"h-3 w-3 text-yellow-400 flex-shrink-0"}),"co-host"===r.role&&l.jsx(to.Z,{className:"h-3 w-3 text-blue-400 flex-shrink-0"})]}),l.jsx("div",{className:"flex items-center space-x-1",children:y(r)})]})]}),!i&&x&&(0,l.jsxs)("div",{className:"relative",children:[l.jsx("button",{onClick:()=>t(e===r.id?null:r.id),className:"glass-button p-1 text-white/60 hover:text-white",children:l.jsx(t_,{className:"h-3 w-3"})}),e===r.id&&l.jsx("div",{className:"absolute right-0 top-full mt-1 glass-dark rounded-lg border border-white/20 min-w-32 z-10",children:(0,l.jsxs)("div",{className:"p-1",children:[(0,l.jsxs)("button",{onClick:()=>v("mute",r.id),className:"w-full p-2 text-left text-white text-xs hover:bg-white/10 rounded flex items-center gap-2",children:[l.jsx(e9,{className:"h-3 w-3"}),"Mute"]}),b&&"participant"===r.role&&(0,l.jsxs)("button",{onClick:()=>v("promote",r.id),className:"w-full p-2 text-left text-white text-xs hover:bg-white/10 rounded flex items-center gap-2",children:[l.jsx(to.Z,{className:"h-3 w-3"}),"Make Co-Host"]}),b&&"co-host"===r.role&&(0,l.jsxs)("button",{onClick:()=>v("demote",r.id),className:"w-full p-2 text-left text-white text-xs hover:bg-white/10 rounded flex items-center gap-2",children:[l.jsx(tw,{className:"h-3 w-3"}),"Remove Co-Host"]}),(0,l.jsxs)("button",{onClick:()=>v("block",r.id),className:"w-full p-2 text-left text-red-400 text-xs hover:bg-white/10 rounded flex items-center gap-2",children:[l.jsx(tk,{className:"h-3 w-3"}),"Block User"]}),(0,l.jsxs)("button",{onClick:()=>v("remove",r.id),className:"w-full p-2 text-left text-red-400 text-xs hover:bg-white/10 rounded flex items-center gap-2",children:[l.jsx(tk,{className:"h-3 w-3"}),"Remove"]})]})})]})]})},r.id)})})]})}/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let tS=(0,e3.Z)("Share",[["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}],["polyline",{points:"16 6 12 2 8 6",key:"m901s6"}],["line",{x1:"12",x2:"12",y1:"2",y2:"15",key:"1p0rca"}]]),tE=(0,e3.Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]),tN=(0,e3.Z)("Minimize2",[["polyline",{points:"4 14 10 14 10 20",key:"11kfnr"}],["polyline",{points:"20 10 14 10 14 4",key:"rlmsce"}],["line",{x1:"14",x2:"21",y1:"10",y2:"3",key:"o5lafz"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]]),tj=(0,e3.Z)("Maximize2",[["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["polyline",{points:"9 21 3 21 3 15",key:"1avn1i"}],["line",{x1:"21",x2:"14",y1:"3",y2:"10",key:"ota7mn"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]]),tO=(0,e3.Z)("PhoneOff",[["path",{d:"M10.68 13.31a16 16 0 0 0 3.41 2.6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7 2 2 0 0 1 1.72 2v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.42 19.42 0 0 1-3.33-2.67m-2.67-3.34a19.79 19.79 0 0 1-3.07-8.63A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91",key:"z86iuo"}],["line",{x1:"22",x2:"2",y1:"2",y2:"22",key:"11kh81"}]]);function tT({roomId:e}){let t=(0,h.useRouter)(),[s,r]=(0,c.useState)(!1),[i,n]=(0,c.useState)(!1),[o,a]=(0,c.useState)(!1),[d,u]=(0,c.useState)(null),{currentUser:p,participants:f,setConnected:m,addParticipant:g,removeParticipant:y,updateParticipant:x,setLocalStream:b,addMessage:v,reset:_}=C(),w=e=>{u(e),setTimeout(()=>u(null),3e3)},[k,S]=(0,c.useState)(!1);return s&&p?(0,l.jsxs)(l.Fragment,{children:[l.jsx("div",{className:"animated-bg"}),(0,l.jsxs)("div",{className:"min-h-screen flex flex-col relative z-10 pt-20",children:[l.jsx("div",{className:"glass-dark p-4 fixed top-0 left-0 right-0 z-50",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-4",children:[l.jsx("h1",{className:"text-white text-xl font-semibold",children:"StreamIt Pro Meeting"}),(0,l.jsxs)("div",{className:"flex items-center space-x-2 text-white/70",children:[l.jsx(ty.Z,{className:"h-4 w-4"}),(0,l.jsxs)("span",{children:[f.size+1," participants"]})]}),(0,l.jsxs)("div",{className:"text-white/50 text-sm",children:["ID: ",e]})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[l.jsx("button",{onClick:()=>{let t=`${window.location.origin}/room/${e}`;navigator.clipboard.writeText(t),w("Meeting link copied to clipboard!")},className:"glass-button p-2 text-white hover:text-purple-300",title:"Copy meeting link",children:l.jsx(tS,{className:"h-5 w-5"})}),l.jsx("button",{onClick:()=>{navigator.clipboard.writeText(e),w("Meeting ID copied to clipboard!")},className:"glass-button p-2 text-white hover:text-purple-300",title:"Copy meeting ID",children:l.jsx(tE,{className:"h-5 w-5"})}),l.jsx("button",{onClick:()=>n(!i),className:`glass-button p-2 text-white hover:text-purple-300 ${i?"bg-purple-500/30":""}`,title:"Settings",children:l.jsx(ts.Z,{className:"h-5 w-5"})}),l.jsx("button",{onClick:()=>{document.fullscreenElement?(document.exitFullscreen(),a(!1)):(document.documentElement.requestFullscreen(),a(!0))},className:"glass-button p-2 text-white hover:text-purple-300",title:"Toggle fullscreen",children:o?l.jsx(tN,{className:"h-5 w-5"}):l.jsx(tj,{className:"h-5 w-5"})}),l.jsx("button",{onClick:()=>{S(!0)},className:"bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors",title:"Leave meeting",children:l.jsx(tO,{className:"h-5 w-5"})})]})]})}),(0,l.jsxs)("div",{className:"flex-1 flex p-4 pt-20 gap-4 mt-4",children:[(0,l.jsxs)("div",{className:"w-2/3 flex flex-col",children:[l.jsx(tt,{}),l.jsx(tp,{})]}),(0,l.jsxs)("div",{className:"w-1/3 flex flex-col gap-4",children:[l.jsx("div",{className:"h-1/2",children:l.jsx(tC,{})}),l.jsx("div",{className:"h-1/2",children:l.jsx(tg,{})})]})]}),l.jsx(tc,{isOpen:i,onClose:()=>n(!1)}),k&&l.jsx("div",{className:"fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4",children:(0,l.jsxs)("div",{className:"glass p-6 rounded-lg max-w-md w-full",children:[l.jsx("h3",{className:"text-xl font-semibold text-white mb-4",children:"Leave Meeting?"}),l.jsx("p",{className:"text-white/80 mb-6",children:"Are you sure you want to leave the meeting?"}),(0,l.jsxs)("div",{className:"flex justify-end space-x-3",children:[l.jsx("button",{onClick:()=>{S(!1)},className:"px-4 py-2 rounded-lg bg-gray-600 text-white hover:bg-gray-500 transition-colors",children:"Cancel"}),l.jsx("button",{onClick:()=>{eX.leaveRoom(),e2.cleanup(),_(),t.push("/")},className:"px-4 py-2 rounded-lg bg-red-600 text-white hover:bg-red-500 transition-colors",children:"Leave"})]})]})}),d&&l.jsx("div",{className:"fixed top-20 right-4 glass p-4 text-white z-50 fade-in rounded-lg shadow-lg",children:d})]})]}):(0,l.jsxs)(l.Fragment,{children:[l.jsx("div",{className:"animated-bg"}),l.jsx("div",{className:"min-h-screen flex items-center justify-center relative z-10",children:(0,l.jsxs)("div",{className:"glass p-8 text-center",children:[l.jsx("div",{className:"animate-spin rounded-full h-16 w-16 border-4 border-white/30 border-t-white mx-auto mb-6"}),l.jsx("h2",{className:"text-2xl font-semibold text-white mb-2",children:"Connecting to Meeting"}),l.jsx("p",{className:"text-white/70",children:"Please wait while we set up your video call..."})]})})]})}function tF({children:e}){return l.jsx(l.Fragment,{children:e})}let tR="force-dynamic";function tA(){let e=(0,h.useParams)(),t=(0,h.useRouter)(),s=e.id,[r,i]=(0,c.useState)(""),[n,o]=(0,c.useState)(!1),[a,d]=(0,c.useState)(!1),{setRoomId:u,setCurrentUser:p}=C(),f=(0,c.useCallback)(async e=>{if(!e.trim()){alert("Please enter your name");return}o(!0);try{u(s);let t={id:Math.random().toString(36).substring(2,15),name:e.trim(),isAudioMuted:!1,isVideoMuted:!1,isScreenSharing:!1,role:"host",isHandRaised:!1,joinedAt:new Date,lastActivity:new Date};p(t),localStorage.setItem("userName",e.trim()),d(!0)}catch(e){console.error("Error joining room:",e),alert("Failed to join room. Please try again.")}finally{o(!1)}},[s,p,u]);return a?l.jsx(tF,{children:l.jsx(tT,{roomId:s})}):(0,l.jsxs)(l.Fragment,{children:[l.jsx("div",{className:"animated-bg"}),l.jsx("div",{className:"min-h-screen flex items-center justify-center p-4 relative z-10",children:(0,l.jsxs)("div",{className:"glass p-8 max-w-md w-full",children:[(0,l.jsxs)("div",{className:"text-center mb-6",children:[l.jsx("h2",{className:"text-2xl font-bold text-white mb-2",children:"Join Meeting"}),(0,l.jsxs)("p",{className:"text-white/70",children:["Meeting ID: ",l.jsx("span",{className:"font-mono font-semibold text-purple-300",children:s})]})]}),(0,l.jsxs)("form",{onSubmit:e=>{e.preventDefault(),f(r)},className:"space-y-4",children:[l.jsx("input",{type:"text",placeholder:"Enter your name",value:r,onChange:e=>i(e.target.value),disabled:n,autoFocus:!0,className:"glass-input w-full"}),(0,l.jsxs)("div",{className:"flex gap-3",children:[l.jsx("button",{type:"button",onClick:()=>t.push("/"),disabled:n,className:"btn-secondary flex-1",children:"Back"}),l.jsx("button",{type:"submit",disabled:n||!r.trim(),className:"btn-primary flex-1",children:n?"Joining...":"Join Meeting"})]})]})]})})]})}},5549:(e,t,s)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;let s="color: "+this.color;t.splice(1,0,s,"color: inherit");let r=0,i=0;t[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(r++,"%c"===e&&(i=r))}),t.splice(i,0,s)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},t.useColors=function(){let e;return"undefined"!=typeof window&&!!window.process&&("renderer"===window.process.type||!!window.process.__nwjs)||!("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=s(8995)(t);let{formatters:r}=e.exports;r.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},8995:(e,t,s)=>{e.exports=function(e){function t(e){let s,i,n;let o=null;function a(...e){if(!a.enabled)return;let r=Number(new Date),i=r-(s||r);a.diff=i,a.prev=s,a.curr=r,s=r,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let n=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(s,r)=>{if("%%"===s)return"%";n++;let i=t.formatters[r];if("function"==typeof i){let t=e[n];s=i.call(a,t),e.splice(n,1),n--}return s}),t.formatArgs.call(a,e),(a.log||t.log).apply(a,e)}return a.namespace=e,a.useColors=t.useColors(),a.color=t.selectColor(e),a.extend=r,a.destroy=t.destroy,Object.defineProperty(a,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==o?o:(i!==t.namespaces&&(i=t.namespaces,n=t.enabled(e)),n),set:e=>{o=e}}),"function"==typeof t.init&&t.init(a),a}function r(e,s){let r=t(this.namespace+(void 0===s?":":s)+e);return r.log=this.log,r}function i(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,"*")}return t.debug=t,t.default=t,t.coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){let e=[...t.names.map(i),...t.skips.map(i).map(e=>"-"+e)].join(",");return t.enable(""),e},t.enable=function(e){let s;t.save(e),t.namespaces=e,t.names=[],t.skips=[];let r=("string"==typeof e?e:"").split(/[\s,]+/),i=r.length;for(s=0;s<i;s++)r[s]&&("-"===(e=r[s].replace(/\*/g,".*?"))[0]?t.skips.push(RegExp("^"+e.slice(1)+"$")):t.names.push(RegExp("^"+e+"$")))},t.enabled=function(e){let s,r;if("*"===e[e.length-1])return!0;for(s=0,r=t.skips.length;s<r;s++)if(t.skips[s].test(e))return!1;for(s=0,r=t.names.length;s<r;s++)if(t.names[s].test(e))return!0;return!1},t.humanize=s(7914),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach(s=>{t[s]=e[s]}),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let s=0;for(let t=0;t<e.length;t++)s=(s<<5)-s+e.charCodeAt(t)|0;return t.colors[Math.abs(s)%t.colors.length]},t.enable(t.load()),t}},614:(e,t,s)=>{"undefined"==typeof process||"renderer"===process.type||process.__nwjs?e.exports=s(5549):e.exports=s(2839)},2839:(e,t,s)=>{let r=s(6224),i=s(3837);t.init=function(e){e.inspectOpts={};let s=Object.keys(t.inspectOpts);for(let r=0;r<s.length;r++)e.inspectOpts[s[r]]=t.inspectOpts[s[r]]},t.log=function(...e){return process.stderr.write(i.formatWithOptions(t.inspectOpts,...e)+"\n")},t.formatArgs=function(s){let{namespace:r,useColors:i}=this;if(i){let t=this.color,i="\x1b[3"+(t<8?t:"8;5;"+t),n=`  ${i};1m${r} \u001B[0m`;s[0]=n+s[0].split("\n").join("\n"+n),s.push(i+"m+"+e.exports.humanize(this.diff)+"\x1b[0m")}else s[0]=(t.inspectOpts.hideDate?"":new Date().toISOString()+" ")+r+" "+s[0]},t.save=function(e){e?process.env.DEBUG=e:delete process.env.DEBUG},t.load=function(){return process.env.DEBUG},t.useColors=function(){return"colors"in t.inspectOpts?!!t.inspectOpts.colors:r.isatty(process.stderr.fd)},t.destroy=i.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."),t.colors=[6,2,3,4,5,1];try{let e=s(7495);e&&(e.stderr||e).level>=2&&(t.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(e){}t.inspectOpts=Object.keys(process.env).filter(e=>/^debug_/i.test(e)).reduce((e,t)=>{let s=t.substring(6).toLowerCase().replace(/_([a-z])/g,(e,t)=>t.toUpperCase()),r=process.env[t];return r=!!/^(yes|on|true|enabled)$/i.test(r)||!/^(no|off|false|disabled)$/i.test(r)&&("null"===r?null:Number(r)),e[s]=r,e},{}),e.exports=s(8995)(t);let{formatters:n}=e.exports;n.o=function(e){return this.inspectOpts.colors=this.useColors,i.inspect(e,this.inspectOpts).split("\n").map(e=>e.trim()).join(" ")},n.O=function(e){return this.inspectOpts.colors=this.useColors,i.inspect(e,this.inspectOpts)}},3285:e=>{"use strict";e.exports=(e,t=process.argv)=>{let s=e.startsWith("-")?"":1===e.length?"-":"--",r=t.indexOf(s+e),i=t.indexOf("--");return -1!==r&&(-1===i||r<i)}},2933:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(6557).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},8393:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(6557).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},6633:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(6557).Z)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},2280:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(6557).Z)("Headphones",[["path",{d:"M3 14h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7a9 9 0 0 1 18 0v7a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3",key:"1xhozi"}]])},9015:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(6557).Z)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},9730:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(6557).Z)("MessageCircle",[["path",{d:"m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z",key:"v2veuj"}]])},2634:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(6557).Z)("Monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]])},9436:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(6557).Z)("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]])},8378:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(6557).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},165:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(6557).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},4061:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(6557).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},7914:e=>{function t(e,t,s,r){return Math.round(e/s)+" "+r+(t>=1.5*s?"s":"")}e.exports=function(e,s){s=s||{};var r,i,n=typeof e;if("string"===n&&e.length>0)return function(e){if(!((e=String(e)).length>100)){var t=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(t){var s=parseFloat(t[1]);switch((t[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*s;case"weeks":case"week":case"w":return 6048e5*s;case"days":case"day":case"d":return 864e5*s;case"hours":case"hour":case"hrs":case"hr":case"h":return 36e5*s;case"minutes":case"minute":case"mins":case"min":case"m":return 6e4*s;case"seconds":case"second":case"secs":case"sec":case"s":return 1e3*s;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return s;default:return}}}}(e);if("number"===n&&isFinite(e))return s.long?(r=Math.abs(e))>=864e5?t(e,r,864e5,"day"):r>=36e5?t(e,r,36e5,"hour"):r>=6e4?t(e,r,6e4,"minute"):r>=1e3?t(e,r,1e3,"second"):e+" ms":(i=Math.abs(e))>=864e5?Math.round(e/864e5)+"d":i>=36e5?Math.round(e/36e5)+"h":i>=6e4?Math.round(e/6e4)+"m":i>=1e3?Math.round(e/1e3)+"s":e+"ms";throw Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},5047:(e,t,s)=>{"use strict";var r=s(7389);s.o(r,"useParams")&&s.d(t,{useParams:function(){return r.useParams}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}})},1503:(e,t,s)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;let s="color: "+this.color;t.splice(1,0,s,"color: inherit");let r=0,i=0;t[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(r++,"%c"===e&&(i=r))}),t.splice(i,0,s)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},t.useColors=function(){let e;return"undefined"!=typeof window&&!!window.process&&("renderer"===window.process.type||!!window.process.__nwjs)||!("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=s(3504)(t);let{formatters:r}=e.exports;r.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},3504:(e,t,s)=>{e.exports=function(e){function t(e){let s,i,n;let o=null;function a(...e){if(!a.enabled)return;let r=Number(new Date),i=r-(s||r);a.diff=i,a.prev=s,a.curr=r,s=r,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let n=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(s,r)=>{if("%%"===s)return"%";n++;let i=t.formatters[r];if("function"==typeof i){let t=e[n];s=i.call(a,t),e.splice(n,1),n--}return s}),t.formatArgs.call(a,e),(a.log||t.log).apply(a,e)}return a.namespace=e,a.useColors=t.useColors(),a.color=t.selectColor(e),a.extend=r,a.destroy=t.destroy,Object.defineProperty(a,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==o?o:(i!==t.namespaces&&(i=t.namespaces,n=t.enabled(e)),n),set:e=>{o=e}}),"function"==typeof t.init&&t.init(a),a}function r(e,s){let r=t(this.namespace+(void 0===s?":":s)+e);return r.log=this.log,r}function i(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,"*")}return t.debug=t,t.default=t,t.coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){let e=[...t.names.map(i),...t.skips.map(i).map(e=>"-"+e)].join(",");return t.enable(""),e},t.enable=function(e){let s;t.save(e),t.namespaces=e,t.names=[],t.skips=[];let r=("string"==typeof e?e:"").split(/[\s,]+/),i=r.length;for(s=0;s<i;s++)r[s]&&("-"===(e=r[s].replace(/\*/g,".*?"))[0]?t.skips.push(RegExp("^"+e.slice(1)+"$")):t.names.push(RegExp("^"+e+"$")))},t.enabled=function(e){let s,r;if("*"===e[e.length-1])return!0;for(s=0,r=t.skips.length;s<r;s++)if(t.skips[s].test(e))return!1;for(s=0,r=t.names.length;s<r;s++)if(t.names[s].test(e))return!0;return!1},t.humanize=s(7914),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach(s=>{t[s]=e[s]}),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let s=0;for(let t=0;t<e.length;t++)s=(s<<5)-s+e.charCodeAt(t)|0;return t.colors[Math.abs(s)%t.colors.length]},t.enable(t.load()),t}},6183:(e,t,s)=>{"undefined"==typeof process||"renderer"===process.type||process.__nwjs?e.exports=s(1503):e.exports=s(2041)},2041:(e,t,s)=>{let r=s(6224),i=s(3837);t.init=function(e){e.inspectOpts={};let s=Object.keys(t.inspectOpts);for(let r=0;r<s.length;r++)e.inspectOpts[s[r]]=t.inspectOpts[s[r]]},t.log=function(...e){return process.stderr.write(i.formatWithOptions(t.inspectOpts,...e)+"\n")},t.formatArgs=function(s){let{namespace:r,useColors:i}=this;if(i){let t=this.color,i="\x1b[3"+(t<8?t:"8;5;"+t),n=`  ${i};1m${r} \u001B[0m`;s[0]=n+s[0].split("\n").join("\n"+n),s.push(i+"m+"+e.exports.humanize(this.diff)+"\x1b[0m")}else s[0]=(t.inspectOpts.hideDate?"":new Date().toISOString()+" ")+r+" "+s[0]},t.save=function(e){e?process.env.DEBUG=e:delete process.env.DEBUG},t.load=function(){return process.env.DEBUG},t.useColors=function(){return"colors"in t.inspectOpts?!!t.inspectOpts.colors:r.isatty(process.stderr.fd)},t.destroy=i.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."),t.colors=[6,2,3,4,5,1];try{let e=s(7495);e&&(e.stderr||e).level>=2&&(t.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(e){}t.inspectOpts=Object.keys(process.env).filter(e=>/^debug_/i.test(e)).reduce((e,t)=>{let s=t.substring(6).toLowerCase().replace(/_([a-z])/g,(e,t)=>t.toUpperCase()),r=process.env[t];return r=!!/^(yes|on|true|enabled)$/i.test(r)||!/^(no|off|false|disabled)$/i.test(r)&&("null"===r?null:Number(r)),e[s]=r,e},{}),e.exports=s(3504)(t);let{formatters:n}=e.exports;n.o=function(e){return this.inspectOpts.colors=this.useColors,i.inspect(e,this.inspectOpts).split("\n").map(e=>e.trim()).join(" ")},n.O=function(e){return this.inspectOpts.colors=this.useColors,i.inspect(e,this.inspectOpts)}},8684:(e,t,s)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;let s="color: "+this.color;t.splice(1,0,s,"color: inherit");let r=0,i=0;t[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(r++,"%c"===e&&(i=r))}),t.splice(i,0,s)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},t.useColors=function(){let e;return"undefined"!=typeof window&&!!window.process&&("renderer"===window.process.type||!!window.process.__nwjs)||!("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=s(281)(t);let{formatters:r}=e.exports;r.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},281:(e,t,s)=>{e.exports=function(e){function t(e){let s,i,n;let o=null;function a(...e){if(!a.enabled)return;let r=Number(new Date),i=r-(s||r);a.diff=i,a.prev=s,a.curr=r,s=r,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let n=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(s,r)=>{if("%%"===s)return"%";n++;let i=t.formatters[r];if("function"==typeof i){let t=e[n];s=i.call(a,t),e.splice(n,1),n--}return s}),t.formatArgs.call(a,e),(a.log||t.log).apply(a,e)}return a.namespace=e,a.useColors=t.useColors(),a.color=t.selectColor(e),a.extend=r,a.destroy=t.destroy,Object.defineProperty(a,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==o?o:(i!==t.namespaces&&(i=t.namespaces,n=t.enabled(e)),n),set:e=>{o=e}}),"function"==typeof t.init&&t.init(a),a}function r(e,s){let r=t(this.namespace+(void 0===s?":":s)+e);return r.log=this.log,r}function i(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,"*")}return t.debug=t,t.default=t,t.coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){let e=[...t.names.map(i),...t.skips.map(i).map(e=>"-"+e)].join(",");return t.enable(""),e},t.enable=function(e){let s;t.save(e),t.namespaces=e,t.names=[],t.skips=[];let r=("string"==typeof e?e:"").split(/[\s,]+/),i=r.length;for(s=0;s<i;s++)r[s]&&("-"===(e=r[s].replace(/\*/g,".*?"))[0]?t.skips.push(RegExp("^"+e.slice(1)+"$")):t.names.push(RegExp("^"+e+"$")))},t.enabled=function(e){let s,r;if("*"===e[e.length-1])return!0;for(s=0,r=t.skips.length;s<r;s++)if(t.skips[s].test(e))return!1;for(s=0,r=t.names.length;s<r;s++)if(t.names[s].test(e))return!0;return!1},t.humanize=s(7914),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach(s=>{t[s]=e[s]}),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let s=0;for(let t=0;t<e.length;t++)s=(s<<5)-s+e.charCodeAt(t)|0;return t.colors[Math.abs(s)%t.colors.length]},t.enable(t.load()),t}},6610:(e,t,s)=>{"undefined"==typeof process||"renderer"===process.type||process.__nwjs?e.exports=s(8684):e.exports=s(1369)},1369:(e,t,s)=>{let r=s(6224),i=s(3837);t.init=function(e){e.inspectOpts={};let s=Object.keys(t.inspectOpts);for(let r=0;r<s.length;r++)e.inspectOpts[s[r]]=t.inspectOpts[s[r]]},t.log=function(...e){return process.stderr.write(i.formatWithOptions(t.inspectOpts,...e)+"\n")},t.formatArgs=function(s){let{namespace:r,useColors:i}=this;if(i){let t=this.color,i="\x1b[3"+(t<8?t:"8;5;"+t),n=`  ${i};1m${r} \u001B[0m`;s[0]=n+s[0].split("\n").join("\n"+n),s.push(i+"m+"+e.exports.humanize(this.diff)+"\x1b[0m")}else s[0]=(t.inspectOpts.hideDate?"":new Date().toISOString()+" ")+r+" "+s[0]},t.save=function(e){e?process.env.DEBUG=e:delete process.env.DEBUG},t.load=function(){return process.env.DEBUG},t.useColors=function(){return"colors"in t.inspectOpts?!!t.inspectOpts.colors:r.isatty(process.stderr.fd)},t.destroy=i.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."),t.colors=[6,2,3,4,5,1];try{let e=s(7495);e&&(e.stderr||e).level>=2&&(t.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(e){}t.inspectOpts=Object.keys(process.env).filter(e=>/^debug_/i.test(e)).reduce((e,t)=>{let s=t.substring(6).toLowerCase().replace(/_([a-z])/g,(e,t)=>t.toUpperCase()),r=process.env[t];return r=!!/^(yes|on|true|enabled)$/i.test(r)||!/^(no|off|false|disabled)$/i.test(r)&&("null"===r?null:Number(r)),e[s]=r,e},{}),e.exports=s(281)(t);let{formatters:n}=e.exports;n.o=function(e){return this.inspectOpts.colors=this.useColors,i.inspect(e,this.inspectOpts).split("\n").map(e=>e.trim()).join(" ")},n.O=function(e){return this.inspectOpts.colors=this.useColors,i.inspect(e,this.inspectOpts)}},7495:(e,t,s)=>{"use strict";let r;let i=s(2037),n=s(6224),o=s(3285),{env:a}=process;function l(e,t={}){var s;return 0!==(s=function(e,{streamIsTTY:t,sniffFlags:s=!0}={}){let n=function(){if("FORCE_COLOR"in a)return"true"===a.FORCE_COLOR?1:"false"===a.FORCE_COLOR?0:0===a.FORCE_COLOR.length?1:Math.min(Number.parseInt(a.FORCE_COLOR,10),3)}();void 0!==n&&(r=n);let l=s?r:n;if(0===l)return 0;if(s){if(o("color=16m")||o("color=full")||o("color=truecolor"))return 3;if(o("color=256"))return 2}if(e&&!t&&void 0===l)return 0;let c=l||0;if("dumb"===a.TERM)return c;if("win32"===process.platform){let e=i.release().split(".");return Number(e[0])>=10&&Number(e[2])>=10586?Number(e[2])>=14931?3:2:1}if("CI"in a)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE","DRONE"].some(e=>e in a)||"codeship"===a.CI_NAME?1:c;if("TEAMCITY_VERSION"in a)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(a.TEAMCITY_VERSION)?1:0;if("truecolor"===a.COLORTERM)return 3;if("TERM_PROGRAM"in a){let e=Number.parseInt((a.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(a.TERM_PROGRAM){case"iTerm.app":return e>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(a.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(a.TERM)||"COLORTERM"in a?1:c}(e,{streamIsTTY:e&&e.isTTY,...t}))&&{level:s,hasBasic:!0,has256:s>=2,has16m:s>=3}}o("no-color")||o("no-colors")||o("color=false")||o("color=never")?r=0:(o("color")||o("colors")||o("color=true")||o("color=always"))&&(r=1),e.exports={supportsColor:l,stdout:l({isTTY:n.isatty(1)}),stderr:l({isTTY:n.isatty(2)})}},5442:(e,t,s)=>{"use strict";/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=s(7577),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},n=r.useState,o=r.useEffect,a=r.useLayoutEffect,l=r.useDebugValue;function c(e){var t=e.getSnapshot;e=e.value;try{var s=t();return!i(e,s)}catch(e){return!0}}var h="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var s=t(),r=n({inst:{value:s,getSnapshot:t}}),i=r[0].inst,h=r[1];return a(function(){i.value=s,i.getSnapshot=t,c(i)&&h({inst:i})},[e,s,t]),o(function(){return c(i)&&h({inst:i}),e(function(){c(i)&&h({inst:i})})},[e]),l(s),s};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:h},9251:(e,t,s)=>{"use strict";/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=s(7577),i=s(4095),n="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=i.useSyncExternalStore,a=r.useRef,l=r.useEffect,c=r.useMemo,h=r.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,s,r,i){var d=a(null);if(null===d.current){var u={hasValue:!1,value:null};d.current=u}else u=d.current;var p=o(e,(d=c(function(){function e(e){if(!l){if(l=!0,o=e,e=r(e),void 0!==i&&u.hasValue){var t=u.value;if(i(t,e))return a=t}return a=e}if(t=a,n(o,e))return t;var s=r(e);return void 0!==i&&i(t,s)?(o=e,t):(o=e,a=s)}var o,a,l=!1,c=void 0===s?null:s;return[function(){return e(t())},null===c?void 0:function(){return e(c())}]},[t,s,r,i]))[0],d[1]);return l(function(){u.hasValue=!0,u.value=p},[p]),h(p),p}},4095:(e,t,s)=>{"use strict";e.exports=s(5442)},1508:(e,t,s)=>{"use strict";e.exports=s(9251)},7878:(e,t,s)=>{"use strict";let{EMPTY_BUFFER:r}=s(9353),i=Buffer[Symbol.species];function n(e,t,s,r,i){for(let n=0;n<i;n++)s[r+n]=e[n]^t[3&n]}function o(e,t){for(let s=0;s<e.length;s++)e[s]^=t[3&s]}if(e.exports={concat:function(e,t){if(0===e.length)return r;if(1===e.length)return e[0];let s=Buffer.allocUnsafe(t),n=0;for(let t=0;t<e.length;t++){let r=e[t];s.set(r,n),n+=r.length}return n<t?new i(s.buffer,s.byteOffset,n):s},mask:n,toArrayBuffer:function(e){return e.length===e.buffer.byteLength?e.buffer:e.buffer.slice(e.byteOffset,e.byteOffset+e.length)},toBuffer:function e(t){let s;return(e.readOnly=!0,Buffer.isBuffer(t))?t:(t instanceof ArrayBuffer?s=new i(t):ArrayBuffer.isView(t)?s=new i(t.buffer,t.byteOffset,t.byteLength):(s=Buffer.from(t),e.readOnly=!1),s)},unmask:o},!process.env.WS_NO_BUFFER_UTIL)try{let t=s(8359);e.exports.mask=function(e,s,r,i,o){o<48?n(e,s,r,i,o):t.mask(e,s,r,i,o)},e.exports.unmask=function(e,s){e.length<32?o(e,s):t.unmask(e,s)}}catch(e){}},9353:e=>{"use strict";e.exports={BINARY_TYPES:["nodebuffer","arraybuffer","fragments"],EMPTY_BUFFER:Buffer.alloc(0),GUID:"258EAFA5-E914-47DA-95CA-C5AB0DC85B11",kForOnEventAttribute:Symbol("kIsForOnEventAttribute"),kListener:Symbol("kListener"),kStatusCode:Symbol("status-code"),kWebSocket:Symbol("websocket"),NOOP:()=>{}}},1368:(e,t,s)=>{"use strict";let{kForOnEventAttribute:r,kListener:i}=s(9353),n=Symbol("kCode"),o=Symbol("kData"),a=Symbol("kError"),l=Symbol("kMessage"),c=Symbol("kReason"),h=Symbol("kTarget"),d=Symbol("kType"),u=Symbol("kWasClean");class p{constructor(e){this[h]=null,this[d]=e}get target(){return this[h]}get type(){return this[d]}}Object.defineProperty(p.prototype,"target",{enumerable:!0}),Object.defineProperty(p.prototype,"type",{enumerable:!0});class f extends p{constructor(e,t={}){super(e),this[n]=void 0===t.code?0:t.code,this[c]=void 0===t.reason?"":t.reason,this[u]=void 0!==t.wasClean&&t.wasClean}get code(){return this[n]}get reason(){return this[c]}get wasClean(){return this[u]}}Object.defineProperty(f.prototype,"code",{enumerable:!0}),Object.defineProperty(f.prototype,"reason",{enumerable:!0}),Object.defineProperty(f.prototype,"wasClean",{enumerable:!0});class m extends p{constructor(e,t={}){super(e),this[a]=void 0===t.error?null:t.error,this[l]=void 0===t.message?"":t.message}get error(){return this[a]}get message(){return this[l]}}Object.defineProperty(m.prototype,"error",{enumerable:!0}),Object.defineProperty(m.prototype,"message",{enumerable:!0});class g extends p{constructor(e,t={}){super(e),this[o]=void 0===t.data?null:t.data}get data(){return this[o]}}function y(e,t,s){"object"==typeof e&&e.handleEvent?e.handleEvent.call(e,s):e.call(t,s)}Object.defineProperty(g.prototype,"data",{enumerable:!0}),e.exports={CloseEvent:f,ErrorEvent:m,Event:p,EventTarget:{addEventListener(e,t,s={}){let n;for(let n of this.listeners(e))if(!s[r]&&n[i]===t&&!n[r])return;if("message"===e)n=function(e,s){let r=new g("message",{data:s?e:e.toString()});r[h]=this,y(t,this,r)};else if("close"===e)n=function(e,s){let r=new f("close",{code:e,reason:s.toString(),wasClean:this._closeFrameReceived&&this._closeFrameSent});r[h]=this,y(t,this,r)};else if("error"===e)n=function(e){let s=new m("error",{error:e,message:e.message});s[h]=this,y(t,this,s)};else{if("open"!==e)return;n=function(){let e=new p("open");e[h]=this,y(t,this,e)}}n[r]=!!s[r],n[i]=t,s.once?this.once(e,n):this.on(e,n)},removeEventListener(e,t){for(let s of this.listeners(e))if(s[i]===t&&!s[r]){this.removeListener(e,s);break}}},MessageEvent:g}},9964:(e,t,s)=>{"use strict";let{tokenChars:r}=s(4568);function i(e,t,s){void 0===e[t]?e[t]=[s]:e[t].push(s)}e.exports={format:function(e){return Object.keys(e).map(t=>{let s=e[t];return Array.isArray(s)||(s=[s]),s.map(e=>[t].concat(Object.keys(e).map(t=>{let s=e[t];return Array.isArray(s)||(s=[s]),s.map(e=>!0===e?t:`${t}=${e}`).join("; ")})).join("; ")).join(", ")}).join(", ")},parse:function(e){let t,s;let n=Object.create(null),o=Object.create(null),a=!1,l=!1,c=!1,h=-1,d=-1,u=-1,p=0;for(;p<e.length;p++)if(d=e.charCodeAt(p),void 0===t){if(-1===u&&1===r[d])-1===h&&(h=p);else if(0!==p&&(32===d||9===d))-1===u&&-1!==h&&(u=p);else if(59===d||44===d){if(-1===h)throw SyntaxError(`Unexpected character at index ${p}`);-1===u&&(u=p);let s=e.slice(h,u);44===d?(i(n,s,o),o=Object.create(null)):t=s,h=u=-1}else throw SyntaxError(`Unexpected character at index ${p}`)}else if(void 0===s){if(-1===u&&1===r[d])-1===h&&(h=p);else if(32===d||9===d)-1===u&&-1!==h&&(u=p);else if(59===d||44===d){if(-1===h)throw SyntaxError(`Unexpected character at index ${p}`);-1===u&&(u=p),i(o,e.slice(h,u),!0),44===d&&(i(n,t,o),o=Object.create(null),t=void 0),h=u=-1}else if(61===d&&-1!==h&&-1===u)s=e.slice(h,p),h=u=-1;else throw SyntaxError(`Unexpected character at index ${p}`)}else if(l){if(1!==r[d])throw SyntaxError(`Unexpected character at index ${p}`);-1===h?h=p:a||(a=!0),l=!1}else if(c){if(1===r[d])-1===h&&(h=p);else if(34===d&&-1!==h)c=!1,u=p;else if(92===d)l=!0;else throw SyntaxError(`Unexpected character at index ${p}`)}else if(34===d&&61===e.charCodeAt(p-1))c=!0;else if(-1===u&&1===r[d])-1===h&&(h=p);else if(-1!==h&&(32===d||9===d))-1===u&&(u=p);else if(59===d||44===d){if(-1===h)throw SyntaxError(`Unexpected character at index ${p}`);-1===u&&(u=p);let r=e.slice(h,u);a&&(r=r.replace(/\\/g,""),a=!1),i(o,s,r),44===d&&(i(n,t,o),o=Object.create(null),t=void 0),s=void 0,h=u=-1}else throw SyntaxError(`Unexpected character at index ${p}`);if(-1===h||c||32===d||9===d)throw SyntaxError("Unexpected end of input");-1===u&&(u=p);let f=e.slice(h,u);return void 0===t?i(n,f,o):(void 0===s?i(o,f,!0):a?i(o,s,f.replace(/\\/g,"")):i(o,s,f),i(n,t,o)),n}}},5936:e=>{"use strict";let t=Symbol("kDone"),s=Symbol("kRun");class r{constructor(e){this[t]=()=>{this.pending--,this[s]()},this.concurrency=e||1/0,this.jobs=[],this.pending=0}add(e){this.jobs.push(e),this[s]()}[s](){if(this.pending!==this.concurrency&&this.jobs.length){let e=this.jobs.shift();this.pending++,e(this[t])}}}e.exports=r},7959:(e,t,s)=>{"use strict";let r;let i=s(9796),n=s(7878),o=s(5936),{kStatusCode:a}=s(9353),l=Buffer[Symbol.species],c=Buffer.from([0,0,255,255]),h=Symbol("permessage-deflate"),d=Symbol("total-length"),u=Symbol("callback"),p=Symbol("buffers"),f=Symbol("error");class m{constructor(e,t,s){this._maxPayload=0|s,this._options=e||{},this._threshold=void 0!==this._options.threshold?this._options.threshold:1024,this._isServer=!!t,this._deflate=null,this._inflate=null,this.params=null,r||(r=new o(void 0!==this._options.concurrencyLimit?this._options.concurrencyLimit:10))}static get extensionName(){return"permessage-deflate"}offer(){let e={};return this._options.serverNoContextTakeover&&(e.server_no_context_takeover=!0),this._options.clientNoContextTakeover&&(e.client_no_context_takeover=!0),this._options.serverMaxWindowBits&&(e.server_max_window_bits=this._options.serverMaxWindowBits),this._options.clientMaxWindowBits?e.client_max_window_bits=this._options.clientMaxWindowBits:null==this._options.clientMaxWindowBits&&(e.client_max_window_bits=!0),e}accept(e){return e=this.normalizeParams(e),this.params=this._isServer?this.acceptAsServer(e):this.acceptAsClient(e),this.params}cleanup(){if(this._inflate&&(this._inflate.close(),this._inflate=null),this._deflate){let e=this._deflate[u];this._deflate.close(),this._deflate=null,e&&e(Error("The deflate stream was closed while data was being processed"))}}acceptAsServer(e){let t=this._options,s=e.find(e=>(!1!==t.serverNoContextTakeover||!e.server_no_context_takeover)&&(!e.server_max_window_bits||!1!==t.serverMaxWindowBits&&("number"!=typeof t.serverMaxWindowBits||!(t.serverMaxWindowBits>e.server_max_window_bits)))&&("number"!=typeof t.clientMaxWindowBits||!!e.client_max_window_bits));if(!s)throw Error("None of the extension offers can be accepted");return t.serverNoContextTakeover&&(s.server_no_context_takeover=!0),t.clientNoContextTakeover&&(s.client_no_context_takeover=!0),"number"==typeof t.serverMaxWindowBits&&(s.server_max_window_bits=t.serverMaxWindowBits),"number"==typeof t.clientMaxWindowBits?s.client_max_window_bits=t.clientMaxWindowBits:(!0===s.client_max_window_bits||!1===t.clientMaxWindowBits)&&delete s.client_max_window_bits,s}acceptAsClient(e){let t=e[0];if(!1===this._options.clientNoContextTakeover&&t.client_no_context_takeover)throw Error('Unexpected parameter "client_no_context_takeover"');if(t.client_max_window_bits){if(!1===this._options.clientMaxWindowBits||"number"==typeof this._options.clientMaxWindowBits&&t.client_max_window_bits>this._options.clientMaxWindowBits)throw Error('Unexpected or invalid parameter "client_max_window_bits"')}else"number"==typeof this._options.clientMaxWindowBits&&(t.client_max_window_bits=this._options.clientMaxWindowBits);return t}normalizeParams(e){return e.forEach(e=>{Object.keys(e).forEach(t=>{let s=e[t];if(s.length>1)throw Error(`Parameter "${t}" must have only a single value`);if(s=s[0],"client_max_window_bits"===t){if(!0!==s){let e=+s;if(!Number.isInteger(e)||e<8||e>15)throw TypeError(`Invalid value for parameter "${t}": ${s}`);s=e}else if(!this._isServer)throw TypeError(`Invalid value for parameter "${t}": ${s}`)}else if("server_max_window_bits"===t){let e=+s;if(!Number.isInteger(e)||e<8||e>15)throw TypeError(`Invalid value for parameter "${t}": ${s}`);s=e}else if("client_no_context_takeover"===t||"server_no_context_takeover"===t){if(!0!==s)throw TypeError(`Invalid value for parameter "${t}": ${s}`)}else throw Error(`Unknown parameter "${t}"`);e[t]=s})}),e}decompress(e,t,s){r.add(r=>{this._decompress(e,t,(e,t)=>{r(),s(e,t)})})}compress(e,t,s){r.add(r=>{this._compress(e,t,(e,t)=>{r(),s(e,t)})})}_decompress(e,t,s){let r=this._isServer?"client":"server";if(!this._inflate){let e=`${r}_max_window_bits`,t="number"!=typeof this.params[e]?i.Z_DEFAULT_WINDOWBITS:this.params[e];this._inflate=i.createInflateRaw({...this._options.zlibInflateOptions,windowBits:t}),this._inflate[h]=this,this._inflate[d]=0,this._inflate[p]=[],this._inflate.on("error",x),this._inflate.on("data",y)}this._inflate[u]=s,this._inflate.write(e),t&&this._inflate.write(c),this._inflate.flush(()=>{let e=this._inflate[f];if(e){this._inflate.close(),this._inflate=null,s(e);return}let i=n.concat(this._inflate[p],this._inflate[d]);this._inflate._readableState.endEmitted?(this._inflate.close(),this._inflate=null):(this._inflate[d]=0,this._inflate[p]=[],t&&this.params[`${r}_no_context_takeover`]&&this._inflate.reset()),s(null,i)})}_compress(e,t,s){let r=this._isServer?"server":"client";if(!this._deflate){let e=`${r}_max_window_bits`,t="number"!=typeof this.params[e]?i.Z_DEFAULT_WINDOWBITS:this.params[e];this._deflate=i.createDeflateRaw({...this._options.zlibDeflateOptions,windowBits:t}),this._deflate[d]=0,this._deflate[p]=[],this._deflate.on("data",g)}this._deflate[u]=s,this._deflate.write(e),this._deflate.flush(i.Z_SYNC_FLUSH,()=>{if(!this._deflate)return;let e=n.concat(this._deflate[p],this._deflate[d]);t&&(e=new l(e.buffer,e.byteOffset,e.length-4)),this._deflate[u]=null,this._deflate[d]=0,this._deflate[p]=[],t&&this.params[`${r}_no_context_takeover`]&&this._deflate.reset(),s(null,e)})}}function g(e){this[p].push(e),this[d]+=e.length}function y(e){if(this[d]+=e.length,this[h]._maxPayload<1||this[d]<=this[h]._maxPayload){this[p].push(e);return}this[f]=RangeError("Max payload size exceeded"),this[f].code="WS_ERR_UNSUPPORTED_MESSAGE_LENGTH",this[f][a]=1009,this.removeListener("data",y),this.reset()}function x(e){this[h]._inflate=null,e[a]=1007,this[u](e)}e.exports=m},3958:(e,t,s)=>{"use strict";let{Writable:r}=s(2781),i=s(7959),{BINARY_TYPES:n,EMPTY_BUFFER:o,kStatusCode:a,kWebSocket:l}=s(9353),{concat:c,toArrayBuffer:h,unmask:d}=s(7878),{isValidStatusCode:u,isValidUTF8:p}=s(4568),f=Buffer[Symbol.species];class m extends r{constructor(e={}){super(),this._allowSynchronousEvents=void 0===e.allowSynchronousEvents||e.allowSynchronousEvents,this._binaryType=e.binaryType||n[0],this._extensions=e.extensions||{},this._isServer=!!e.isServer,this._maxPayload=0|e.maxPayload,this._skipUTF8Validation=!!e.skipUTF8Validation,this[l]=void 0,this._bufferedBytes=0,this._buffers=[],this._compressed=!1,this._payloadLength=0,this._mask=void 0,this._fragmented=0,this._masked=!1,this._fin=!1,this._opcode=0,this._totalPayloadLength=0,this._messageLength=0,this._fragments=[],this._errored=!1,this._loop=!1,this._state=0}_write(e,t,s){if(8===this._opcode&&0==this._state)return s();this._bufferedBytes+=e.length,this._buffers.push(e),this.startLoop(s)}consume(e){if(this._bufferedBytes-=e,e===this._buffers[0].length)return this._buffers.shift();if(e<this._buffers[0].length){let t=this._buffers[0];return this._buffers[0]=new f(t.buffer,t.byteOffset+e,t.length-e),new f(t.buffer,t.byteOffset,e)}let t=Buffer.allocUnsafe(e);do{let s=this._buffers[0],r=t.length-e;e>=s.length?t.set(this._buffers.shift(),r):(t.set(new Uint8Array(s.buffer,s.byteOffset,e),r),this._buffers[0]=new f(s.buffer,s.byteOffset+e,s.length-e)),e-=s.length}while(e>0);return t}startLoop(e){this._loop=!0;do switch(this._state){case 0:this.getInfo(e);break;case 1:this.getPayloadLength16(e);break;case 2:this.getPayloadLength64(e);break;case 3:this.getMask();break;case 4:this.getData(e);break;case 5:case 6:this._loop=!1;return}while(this._loop);this._errored||e()}getInfo(e){if(this._bufferedBytes<2){this._loop=!1;return}let t=this.consume(2);if((48&t[0])!=0){e(this.createError(RangeError,"RSV2 and RSV3 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_2_3"));return}let s=(64&t[0])==64;if(s&&!this._extensions[i.extensionName]){e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));return}if(this._fin=(128&t[0])==128,this._opcode=15&t[0],this._payloadLength=127&t[1],0===this._opcode){if(s){e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));return}if(!this._fragmented){e(this.createError(RangeError,"invalid opcode 0",!0,1002,"WS_ERR_INVALID_OPCODE"));return}this._opcode=this._fragmented}else if(1===this._opcode||2===this._opcode){if(this._fragmented){e(this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE"));return}this._compressed=s}else if(this._opcode>7&&this._opcode<11){if(!this._fin){e(this.createError(RangeError,"FIN must be set",!0,1002,"WS_ERR_EXPECTED_FIN"));return}if(s){e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));return}if(this._payloadLength>125||8===this._opcode&&1===this._payloadLength){e(this.createError(RangeError,`invalid payload length ${this._payloadLength}`,!0,1002,"WS_ERR_INVALID_CONTROL_PAYLOAD_LENGTH"));return}}else{e(this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE"));return}if(this._fin||this._fragmented||(this._fragmented=this._opcode),this._masked=(128&t[1])==128,this._isServer){if(!this._masked){e(this.createError(RangeError,"MASK must be set",!0,1002,"WS_ERR_EXPECTED_MASK"));return}}else if(this._masked){e(this.createError(RangeError,"MASK must be clear",!0,1002,"WS_ERR_UNEXPECTED_MASK"));return}126===this._payloadLength?this._state=1:127===this._payloadLength?this._state=2:this.haveLength(e)}getPayloadLength16(e){if(this._bufferedBytes<2){this._loop=!1;return}this._payloadLength=this.consume(2).readUInt16BE(0),this.haveLength(e)}getPayloadLength64(e){if(this._bufferedBytes<8){this._loop=!1;return}let t=this.consume(8),s=t.readUInt32BE(0);if(s>2097151){e(this.createError(RangeError,"Unsupported WebSocket frame: payload length > 2^53 - 1",!1,1009,"WS_ERR_UNSUPPORTED_DATA_PAYLOAD_LENGTH"));return}this._payloadLength=4294967296*s+t.readUInt32BE(4),this.haveLength(e)}haveLength(e){if(this._payloadLength&&this._opcode<8&&(this._totalPayloadLength+=this._payloadLength,this._totalPayloadLength>this._maxPayload&&this._maxPayload>0)){e(this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH"));return}this._masked?this._state=3:this._state=4}getMask(){if(this._bufferedBytes<4){this._loop=!1;return}this._mask=this.consume(4),this._state=4}getData(e){let t=o;if(this._payloadLength){if(this._bufferedBytes<this._payloadLength){this._loop=!1;return}t=this.consume(this._payloadLength),this._masked&&(this._mask[0]|this._mask[1]|this._mask[2]|this._mask[3])!=0&&d(t,this._mask)}if(this._opcode>7){this.controlMessage(t,e);return}if(this._compressed){this._state=5,this.decompress(t,e);return}t.length&&(this._messageLength=this._totalPayloadLength,this._fragments.push(t)),this.dataMessage(e)}decompress(e,t){this._extensions[i.extensionName].decompress(e,this._fin,(e,s)=>{if(e)return t(e);if(s.length){if(this._messageLength+=s.length,this._messageLength>this._maxPayload&&this._maxPayload>0){t(this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH"));return}this._fragments.push(s)}this.dataMessage(t),0===this._state&&this.startLoop(t)})}dataMessage(e){if(!this._fin){this._state=0;return}let t=this._messageLength,s=this._fragments;if(this._totalPayloadLength=0,this._messageLength=0,this._fragmented=0,this._fragments=[],2===this._opcode){let r;r="nodebuffer"===this._binaryType?c(s,t):"arraybuffer"===this._binaryType?h(c(s,t)):s,this._allowSynchronousEvents?(this.emit("message",r,!0),this._state=0):(this._state=6,setImmediate(()=>{this.emit("message",r,!0),this._state=0,this.startLoop(e)}))}else{let r=c(s,t);if(!this._skipUTF8Validation&&!p(r)){e(this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8"));return}5===this._state||this._allowSynchronousEvents?(this.emit("message",r,!1),this._state=0):(this._state=6,setImmediate(()=>{this.emit("message",r,!1),this._state=0,this.startLoop(e)}))}}controlMessage(e,t){if(8===this._opcode){if(0===e.length)this._loop=!1,this.emit("conclude",1005,o),this.end();else{let s=e.readUInt16BE(0);if(!u(s)){t(this.createError(RangeError,`invalid status code ${s}`,!0,1002,"WS_ERR_INVALID_CLOSE_CODE"));return}let r=new f(e.buffer,e.byteOffset+2,e.length-2);if(!this._skipUTF8Validation&&!p(r)){t(this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8"));return}this._loop=!1,this.emit("conclude",s,r),this.end()}this._state=0;return}this._allowSynchronousEvents?(this.emit(9===this._opcode?"ping":"pong",e),this._state=0):(this._state=6,setImmediate(()=>{this.emit(9===this._opcode?"ping":"pong",e),this._state=0,this.startLoop(t)}))}createError(e,t,s,r,i){this._loop=!1,this._errored=!0;let n=new e(s?`Invalid WebSocket frame: ${t}`:t);return Error.captureStackTrace(n,this.createError),n.code=i,n[a]=r,n}}e.exports=m},4036:(e,t,s)=>{"use strict";let r;let{Duplex:i}=s(2781),{randomFillSync:n}=s(6113),o=s(7959),{EMPTY_BUFFER:a}=s(9353),{isValidStatusCode:l}=s(4568),{mask:c,toBuffer:h}=s(7878),d=Symbol("kByteLength"),u=Buffer.alloc(4),p=8192;class f{constructor(e,t,s){this._extensions=t||{},s&&(this._generateMask=s,this._maskBuffer=Buffer.alloc(4)),this._socket=e,this._firstFragment=!0,this._compress=!1,this._bufferedBytes=0,this._deflating=!1,this._queue=[]}static frame(e,t){let s,i;let o=!1,a=2,l=!1;t.mask&&(s=t.maskBuffer||u,t.generateMask?t.generateMask(s):(8192===p&&(void 0===r&&(r=Buffer.alloc(8192)),n(r,0,8192),p=0),s[0]=r[p++],s[1]=r[p++],s[2]=r[p++],s[3]=r[p++]),l=(s[0]|s[1]|s[2]|s[3])==0,a=6),"string"==typeof e?i=(!t.mask||l)&&void 0!==t[d]?t[d]:(e=Buffer.from(e)).length:(i=e.length,o=t.mask&&t.readOnly&&!l);let h=i;i>=65536?(a+=8,h=127):i>125&&(a+=2,h=126);let f=Buffer.allocUnsafe(o?i+a:a);return(f[0]=t.fin?128|t.opcode:t.opcode,t.rsv1&&(f[0]|=64),f[1]=h,126===h?f.writeUInt16BE(i,2):127===h&&(f[2]=f[3]=0,f.writeUIntBE(i,4,6)),t.mask)?(f[1]|=128,f[a-4]=s[0],f[a-3]=s[1],f[a-2]=s[2],f[a-1]=s[3],l)?[f,e]:o?(c(e,s,f,a,i),[f]):(c(e,s,e,0,i),[f,e]):[f,e]}close(e,t,s,r){let i;if(void 0===e)i=a;else if("number"==typeof e&&l(e)){if(void 0!==t&&t.length){let s=Buffer.byteLength(t);if(s>123)throw RangeError("The message must not be greater than 123 bytes");(i=Buffer.allocUnsafe(2+s)).writeUInt16BE(e,0),"string"==typeof t?i.write(t,2):i.set(t,2)}else(i=Buffer.allocUnsafe(2)).writeUInt16BE(e,0)}else throw TypeError("First argument must be a valid error code number");let n={[d]:i.length,fin:!0,generateMask:this._generateMask,mask:s,maskBuffer:this._maskBuffer,opcode:8,readOnly:!1,rsv1:!1};this._deflating?this.enqueue([this.dispatch,i,!1,n,r]):this.sendFrame(f.frame(i,n),r)}ping(e,t,s){let r,i;if("string"==typeof e?(r=Buffer.byteLength(e),i=!1):(r=(e=h(e)).length,i=h.readOnly),r>125)throw RangeError("The data size must not be greater than 125 bytes");let n={[d]:r,fin:!0,generateMask:this._generateMask,mask:t,maskBuffer:this._maskBuffer,opcode:9,readOnly:i,rsv1:!1};this._deflating?this.enqueue([this.dispatch,e,!1,n,s]):this.sendFrame(f.frame(e,n),s)}pong(e,t,s){let r,i;if("string"==typeof e?(r=Buffer.byteLength(e),i=!1):(r=(e=h(e)).length,i=h.readOnly),r>125)throw RangeError("The data size must not be greater than 125 bytes");let n={[d]:r,fin:!0,generateMask:this._generateMask,mask:t,maskBuffer:this._maskBuffer,opcode:10,readOnly:i,rsv1:!1};this._deflating?this.enqueue([this.dispatch,e,!1,n,s]):this.sendFrame(f.frame(e,n),s)}send(e,t,s){let r,i;let n=this._extensions[o.extensionName],a=t.binary?2:1,l=t.compress;if("string"==typeof e?(r=Buffer.byteLength(e),i=!1):(r=(e=h(e)).length,i=h.readOnly),this._firstFragment?(this._firstFragment=!1,l&&n&&n.params[n._isServer?"server_no_context_takeover":"client_no_context_takeover"]&&(l=r>=n._threshold),this._compress=l):(l=!1,a=0),t.fin&&(this._firstFragment=!0),n){let n={[d]:r,fin:t.fin,generateMask:this._generateMask,mask:t.mask,maskBuffer:this._maskBuffer,opcode:a,readOnly:i,rsv1:l};this._deflating?this.enqueue([this.dispatch,e,this._compress,n,s]):this.dispatch(e,this._compress,n,s)}else this.sendFrame(f.frame(e,{[d]:r,fin:t.fin,generateMask:this._generateMask,mask:t.mask,maskBuffer:this._maskBuffer,opcode:a,readOnly:i,rsv1:!1}),s)}dispatch(e,t,s,r){if(!t){this.sendFrame(f.frame(e,s),r);return}let i=this._extensions[o.extensionName];this._bufferedBytes+=s[d],this._deflating=!0,i.compress(e,s.fin,(e,t)=>{if(this._socket.destroyed){let e=Error("The socket was closed while data was being compressed");"function"==typeof r&&r(e);for(let t=0;t<this._queue.length;t++){let s=this._queue[t],r=s[s.length-1];"function"==typeof r&&r(e)}return}this._bufferedBytes-=s[d],this._deflating=!1,s.readOnly=!1,this.sendFrame(f.frame(t,s),r),this.dequeue()})}dequeue(){for(;!this._deflating&&this._queue.length;){let e=this._queue.shift();this._bufferedBytes-=e[3][d],Reflect.apply(e[0],this,e.slice(1))}}enqueue(e){this._bufferedBytes+=e[3][d],this._queue.push(e)}sendFrame(e,t){2===e.length?(this._socket.cork(),this._socket.write(e[0]),this._socket.write(e[1],t),this._socket.uncork()):this._socket.write(e[0],t)}}e.exports=f},2504:(e,t,s)=>{"use strict";let{Duplex:r}=s(2781);function i(e){e.emit("close")}function n(){!this.destroyed&&this._writableState.finished&&this.destroy()}function o(e){this.removeListener("error",o),this.destroy(),0===this.listenerCount("error")&&this.emit("error",e)}e.exports=function(e,t){let s=!0,a=new r({...t,autoDestroy:!1,emitClose:!1,objectMode:!1,writableObjectMode:!1});return e.on("message",function(t,s){let r=!s&&a._readableState.objectMode?t.toString():t;a.push(r)||e.pause()}),e.once("error",function(e){a.destroyed||(s=!1,a.destroy(e))}),e.once("close",function(){a.destroyed||a.push(null)}),a._destroy=function(t,r){if(e.readyState===e.CLOSED){r(t),process.nextTick(i,a);return}let n=!1;e.once("error",function(e){n=!0,r(e)}),e.once("close",function(){n||r(t),process.nextTick(i,a)}),s&&e.terminate()},a._final=function(t){if(e.readyState===e.CONNECTING){e.once("open",function(){a._final(t)});return}null!==e._socket&&(e._socket._writableState.finished?(t(),a._readableState.endEmitted&&a.destroy()):(e._socket.once("finish",function(){t()}),e.close()))},a._read=function(){e.isPaused&&e.resume()},a._write=function(t,s,r){if(e.readyState===e.CONNECTING){e.once("open",function(){a._write(t,s,r)});return}e.send(t,r)},a.on("end",n),a.on("error",o),a}},2444:(e,t,s)=>{"use strict";let{tokenChars:r}=s(4568);e.exports={parse:function(e){let t=new Set,s=-1,i=-1,n=0;for(;n<e.length;n++){let o=e.charCodeAt(n);if(-1===i&&1===r[o])-1===s&&(s=n);else if(0!==n&&(32===o||9===o))-1===i&&-1!==s&&(i=n);else if(44===o){if(-1===s)throw SyntaxError(`Unexpected character at index ${n}`);-1===i&&(i=n);let r=e.slice(s,i);if(t.has(r))throw SyntaxError(`The "${r}" subprotocol is duplicated`);t.add(r),s=i=-1}else throw SyntaxError(`Unexpected character at index ${n}`)}if(-1===s||-1!==i)throw SyntaxError("Unexpected end of input");let o=e.slice(s,n);if(t.has(o))throw SyntaxError(`The "${o}" subprotocol is duplicated`);return t.add(o),t}}},4568:(e,t,s)=>{"use strict";let{isUtf8:r}=s(4300);function i(e){let t=e.length,s=0;for(;s<t;)if((128&e[s])==0)s++;else if((224&e[s])==192){if(s+1===t||(192&e[s+1])!=128||(254&e[s])==192)return!1;s+=2}else if((240&e[s])==224){if(s+2>=t||(192&e[s+1])!=128||(192&e[s+2])!=128||224===e[s]&&(224&e[s+1])==128||237===e[s]&&(224&e[s+1])==160)return!1;s+=3}else{if((248&e[s])!=240||s+3>=t||(192&e[s+1])!=128||(192&e[s+2])!=128||(192&e[s+3])!=128||240===e[s]&&(240&e[s+1])==128||244===e[s]&&e[s+1]>143||e[s]>244)return!1;s+=4}return!0}if(e.exports={isValidStatusCode:function(e){return e>=1e3&&e<=1014&&1004!==e&&1005!==e&&1006!==e||e>=3e3&&e<=4999},isValidUTF8:i,tokenChars:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,1,1,1,1,1,0,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,0]},r)e.exports.isValidUTF8=function(e){return e.length<24?i(e):r(e)};else if(!process.env.WS_NO_UTF_8_VALIDATE)try{let t=s(3739);e.exports.isValidUTF8=function(e){return e.length<32?i(e):t(e)}}catch(e){}},359:(e,t,s)=>{"use strict";let r=s(2361),i=s(3685),{Duplex:n}=s(2781),{createHash:o}=s(6113),a=s(9964),l=s(7959),c=s(2444),h=s(6342),{GUID:d,kWebSocket:u}=s(9353),p=/^[+/0-9A-Za-z]{22}==$/;class f extends r{constructor(e,t){if(super(),null==(e={allowSynchronousEvents:!0,autoPong:!0,maxPayload:*********,skipUTF8Validation:!1,perMessageDeflate:!1,handleProtocols:null,clientTracking:!0,verifyClient:null,noServer:!1,backlog:null,server:null,host:null,path:null,port:null,WebSocket:h,...e}).port&&!e.server&&!e.noServer||null!=e.port&&(e.server||e.noServer)||e.server&&e.noServer)throw TypeError('One and only one of the "port", "server", or "noServer" options must be specified');if(null!=e.port?(this._server=i.createServer((e,t)=>{let s=i.STATUS_CODES[426];t.writeHead(426,{"Content-Length":s.length,"Content-Type":"text/plain"}),t.end(s)}),this._server.listen(e.port,e.host,e.backlog,t)):e.server&&(this._server=e.server),this._server){let e=this.emit.bind(this,"connection");this._removeListeners=function(e,t){for(let s of Object.keys(t))e.on(s,t[s]);return function(){for(let s of Object.keys(t))e.removeListener(s,t[s])}}(this._server,{listening:this.emit.bind(this,"listening"),error:this.emit.bind(this,"error"),upgrade:(t,s,r)=>{this.handleUpgrade(t,s,r,e)}})}!0===e.perMessageDeflate&&(e.perMessageDeflate={}),e.clientTracking&&(this.clients=new Set,this._shouldEmitClose=!1),this.options=e,this._state=0}address(){if(this.options.noServer)throw Error('The server is operating in "noServer" mode');return this._server?this._server.address():null}close(e){if(2===this._state){e&&this.once("close",()=>{e(Error("The server is not running"))}),process.nextTick(m,this);return}if(e&&this.once("close",e),1!==this._state){if(this._state=1,this.options.noServer||this.options.server)this._server&&(this._removeListeners(),this._removeListeners=this._server=null),this.clients&&this.clients.size?this._shouldEmitClose=!0:process.nextTick(m,this);else{let e=this._server;this._removeListeners(),this._removeListeners=this._server=null,e.close(()=>{m(this)})}}}shouldHandle(e){if(this.options.path){let t=e.url.indexOf("?");if((-1!==t?e.url.slice(0,t):e.url)!==this.options.path)return!1}return!0}handleUpgrade(e,t,s,r){t.on("error",g);let i=e.headers["sec-websocket-key"],n=e.headers.upgrade,o=+e.headers["sec-websocket-version"];if("GET"!==e.method){x(this,e,t,405,"Invalid HTTP method");return}if(void 0===n||"websocket"!==n.toLowerCase()){x(this,e,t,400,"Invalid Upgrade header");return}if(void 0===i||!p.test(i)){x(this,e,t,400,"Missing or invalid Sec-WebSocket-Key header");return}if(8!==o&&13!==o){x(this,e,t,400,"Missing or invalid Sec-WebSocket-Version header");return}if(!this.shouldHandle(e)){y(t,400);return}let h=e.headers["sec-websocket-protocol"],d=new Set;if(void 0!==h)try{d=c.parse(h)}catch(s){x(this,e,t,400,"Invalid Sec-WebSocket-Protocol header");return}let u=e.headers["sec-websocket-extensions"],f={};if(this.options.perMessageDeflate&&void 0!==u){let s=new l(this.options.perMessageDeflate,!0,this.options.maxPayload);try{let e=a.parse(u);e[l.extensionName]&&(s.accept(e[l.extensionName]),f[l.extensionName]=s)}catch(s){x(this,e,t,400,"Invalid or unacceptable Sec-WebSocket-Extensions header");return}}if(this.options.verifyClient){let n={origin:e.headers[`${8===o?"sec-websocket-origin":"origin"}`],secure:!!(e.socket.authorized||e.socket.encrypted),req:e};if(2===this.options.verifyClient.length){this.options.verifyClient(n,(n,o,a,l)=>{if(!n)return y(t,o||401,a,l);this.completeUpgrade(f,i,d,e,t,s,r)});return}if(!this.options.verifyClient(n))return y(t,401)}this.completeUpgrade(f,i,d,e,t,s,r)}completeUpgrade(e,t,s,r,i,n,c){if(!i.readable||!i.writable)return i.destroy();if(i[u])throw Error("server.handleUpgrade() was called more than once with the same socket, possibly due to a misconfiguration");if(this._state>0)return y(i,503);let h=o("sha1").update(t+d).digest("base64"),p=["HTTP/1.1 101 Switching Protocols","Upgrade: websocket","Connection: Upgrade",`Sec-WebSocket-Accept: ${h}`],f=new this.options.WebSocket(null,void 0,this.options);if(s.size){let e=this.options.handleProtocols?this.options.handleProtocols(s,r):s.values().next().value;e&&(p.push(`Sec-WebSocket-Protocol: ${e}`),f._protocol=e)}if(e[l.extensionName]){let t=e[l.extensionName].params,s=a.format({[l.extensionName]:[t]});p.push(`Sec-WebSocket-Extensions: ${s}`),f._extensions=e}this.emit("headers",p,r),i.write(p.concat("\r\n").join("\r\n")),i.removeListener("error",g),f.setSocket(i,n,{allowSynchronousEvents:this.options.allowSynchronousEvents,maxPayload:this.options.maxPayload,skipUTF8Validation:this.options.skipUTF8Validation}),this.clients&&(this.clients.add(f),f.on("close",()=>{this.clients.delete(f),this._shouldEmitClose&&!this.clients.size&&process.nextTick(m,this)})),c(f,r)}}function m(e){e._state=2,e.emit("close")}function g(){this.destroy()}function y(e,t,s,r){s=s||i.STATUS_CODES[t],r={Connection:"close","Content-Type":"text/html","Content-Length":Buffer.byteLength(s),...r},e.once("finish",e.destroy),e.end(`HTTP/1.1 ${t} ${i.STATUS_CODES[t]}\r
`+Object.keys(r).map(e=>`${e}: ${r[e]}`).join("\r\n")+"\r\n\r\n"+s)}function x(e,t,s,r,i){if(e.listenerCount("wsClientError")){let r=Error(i);Error.captureStackTrace(r,x),e.emit("wsClientError",r,s,t)}else y(s,r,i)}e.exports=f},6342:(e,t,s)=>{"use strict";let r=s(2361),i=s(5687),n=s(3685),o=s(1808),a=s(4404),{randomBytes:l,createHash:c}=s(6113),{Duplex:h,Readable:d}=s(2781),{URL:u}=s(7310),p=s(7959),f=s(3958),m=s(4036),{BINARY_TYPES:g,EMPTY_BUFFER:y,GUID:x,kForOnEventAttribute:b,kListener:v,kStatusCode:_,kWebSocket:w,NOOP:k}=s(9353),{EventTarget:{addEventListener:C,removeEventListener:S}}=s(1368),{format:E,parse:N}=s(9964),{toBuffer:j}=s(7878),O=Symbol("kAborted"),T=[8,13],F=["CONNECTING","OPEN","CLOSING","CLOSED"],R=/^[!#$%&'*+\-.0-9A-Z^_`|a-z~]+$/;class A extends r{constructor(e,t,s){super(),this._binaryType=g[0],this._closeCode=1006,this._closeFrameReceived=!1,this._closeFrameSent=!1,this._closeMessage=y,this._closeTimer=null,this._extensions={},this._paused=!1,this._protocol="",this._readyState=A.CONNECTING,this._receiver=null,this._sender=null,this._socket=null,null!==e?(this._bufferedAmount=0,this._isServer=!1,this._redirects=0,void 0===t?t=[]:Array.isArray(t)||("object"==typeof t&&null!==t?(s=t,t=[]):t=[t]),function e(t,s,r,o){let a,h,d,f;let m={allowSynchronousEvents:!0,autoPong:!0,protocolVersion:T[1],maxPayload:*********,skipUTF8Validation:!1,perMessageDeflate:!0,followRedirects:!1,maxRedirects:10,...o,socketPath:void 0,hostname:void 0,protocol:void 0,timeout:void 0,method:"GET",host:void 0,path:void 0,port:void 0};if(t._autoPong=m.autoPong,!T.includes(m.protocolVersion))throw RangeError(`Unsupported protocol version: ${m.protocolVersion} (supported versions: ${T.join(", ")})`);if(s instanceof u)a=s;else try{a=new u(s)}catch(e){throw SyntaxError(`Invalid URL: ${s}`)}"http:"===a.protocol?a.protocol="ws:":"https:"===a.protocol&&(a.protocol="wss:"),t._url=a.href;let g="wss:"===a.protocol,y="ws+unix:"===a.protocol;if("ws:"===a.protocol||g||y?y&&!a.pathname?h="The URL's pathname is empty":a.hash&&(h="The URL contains a fragment identifier"):h='The URL\'s protocol must be one of "ws:", "wss:", "http:", "https", or "ws+unix:"',h){let e=SyntaxError(h);if(0===t._redirects)throw e;L(t,e);return}let b=g?443:80,v=l(16).toString("base64"),_=g?i.request:n.request,w=new Set;if(m.createConnection=m.createConnection||(g?P:I),m.defaultPort=m.defaultPort||b,m.port=a.port||b,m.host=a.hostname.startsWith("[")?a.hostname.slice(1,-1):a.hostname,m.headers={...m.headers,"Sec-WebSocket-Version":m.protocolVersion,"Sec-WebSocket-Key":v,Connection:"Upgrade",Upgrade:"websocket"},m.path=a.pathname+a.search,m.timeout=m.handshakeTimeout,m.perMessageDeflate&&(d=new p(!0!==m.perMessageDeflate?m.perMessageDeflate:{},!1,m.maxPayload),m.headers["Sec-WebSocket-Extensions"]=E({[p.extensionName]:d.offer()})),r.length){for(let e of r){if("string"!=typeof e||!R.test(e)||w.has(e))throw SyntaxError("An invalid or duplicated subprotocol was specified");w.add(e)}m.headers["Sec-WebSocket-Protocol"]=r.join(",")}if(m.origin&&(m.protocolVersion<13?m.headers["Sec-WebSocket-Origin"]=m.origin:m.headers.Origin=m.origin),(a.username||a.password)&&(m.auth=`${a.username}:${a.password}`),y){let e=m.path.split(":");m.socketPath=e[0],m.path=e[1]}if(m.followRedirects){if(0===t._redirects){t._originalIpc=y,t._originalSecure=g,t._originalHostOrSocketPath=y?m.socketPath:a.host;let e=o&&o.headers;if(o={...o,headers:{}},e)for(let[t,s]of Object.entries(e))o.headers[t.toLowerCase()]=s}else if(0===t.listenerCount("redirect")){let e=y?!!t._originalIpc&&m.socketPath===t._originalHostOrSocketPath:!t._originalIpc&&a.host===t._originalHostOrSocketPath;e&&(!t._originalSecure||g)||(delete m.headers.authorization,delete m.headers.cookie,e||delete m.headers.host,m.auth=void 0)}m.auth&&!o.headers.authorization&&(o.headers.authorization="Basic "+Buffer.from(m.auth).toString("base64")),f=t._req=_(m),t._redirects&&t.emit("redirect",t.url,f)}else f=t._req=_(m);m.timeout&&f.on("timeout",()=>{M(t,f,"Opening handshake has timed out")}),f.on("error",e=>{null===f||f[O]||(f=t._req=null,L(t,e))}),f.on("response",i=>{let n=i.headers.location,a=i.statusCode;if(n&&m.followRedirects&&a>=300&&a<400){let i;if(++t._redirects>m.maxRedirects){M(t,f,"Maximum redirects exceeded");return}f.abort();try{i=new u(n,s)}catch(e){L(t,SyntaxError(`Invalid URL: ${n}`));return}e(t,i,r,o)}else t.emit("unexpected-response",f,i)||M(t,f,`Unexpected server response: ${i.statusCode}`)}),f.on("upgrade",(e,s,r)=>{let i;if(t.emit("upgrade",e),t.readyState!==A.CONNECTING)return;f=t._req=null;let n=e.headers.upgrade;if(void 0===n||"websocket"!==n.toLowerCase()){M(t,s,"Invalid Upgrade header");return}let o=c("sha1").update(v+x).digest("base64");if(e.headers["sec-websocket-accept"]!==o){M(t,s,"Invalid Sec-WebSocket-Accept header");return}let a=e.headers["sec-websocket-protocol"];if(void 0!==a?w.size?w.has(a)||(i="Server sent an invalid subprotocol"):i="Server sent a subprotocol but none was requested":w.size&&(i="Server sent no subprotocol"),i){M(t,s,i);return}a&&(t._protocol=a);let l=e.headers["sec-websocket-extensions"];if(void 0!==l){let e;if(!d){M(t,s,"Server sent a Sec-WebSocket-Extensions header but no extension was requested");return}try{e=N(l)}catch(e){M(t,s,"Invalid Sec-WebSocket-Extensions header");return}let r=Object.keys(e);if(1!==r.length||r[0]!==p.extensionName){M(t,s,"Server indicated an extension that was not requested");return}try{d.accept(e[p.extensionName])}catch(e){M(t,s,"Invalid Sec-WebSocket-Extensions header");return}t._extensions[p.extensionName]=d}t.setSocket(s,r,{allowSynchronousEvents:m.allowSynchronousEvents,generateMask:m.generateMask,maxPayload:m.maxPayload,skipUTF8Validation:m.skipUTF8Validation})}),m.finishRequest?m.finishRequest(f,t):f.end()}(this,e,t,s)):(this._autoPong=s.autoPong,this._isServer=!0)}get binaryType(){return this._binaryType}set binaryType(e){g.includes(e)&&(this._binaryType=e,this._receiver&&(this._receiver._binaryType=e))}get bufferedAmount(){return this._socket?this._socket._writableState.length+this._sender._bufferedBytes:this._bufferedAmount}get extensions(){return Object.keys(this._extensions).join()}get isPaused(){return this._paused}get onclose(){return null}get onerror(){return null}get onopen(){return null}get onmessage(){return null}get protocol(){return this._protocol}get readyState(){return this._readyState}get url(){return this._url}setSocket(e,t,s){let r=new f({allowSynchronousEvents:s.allowSynchronousEvents,binaryType:this.binaryType,extensions:this._extensions,isServer:this._isServer,maxPayload:s.maxPayload,skipUTF8Validation:s.skipUTF8Validation});this._sender=new m(e,this._extensions,s.generateMask),this._receiver=r,this._socket=e,r[w]=this,e[w]=this,r.on("conclude",B),r.on("drain",U),r.on("error",q),r.on("message",$),r.on("ping",W),r.on("pong",z),e.setTimeout&&e.setTimeout(0),e.setNoDelay&&e.setNoDelay(),t.length>0&&e.unshift(t),e.on("close",Z),e.on("data",G),e.on("end",J),e.on("error",Y),this._readyState=A.OPEN,this.emit("open")}emitClose(){if(!this._socket){this._readyState=A.CLOSED,this.emit("close",this._closeCode,this._closeMessage);return}this._extensions[p.extensionName]&&this._extensions[p.extensionName].cleanup(),this._receiver.removeAllListeners(),this._readyState=A.CLOSED,this.emit("close",this._closeCode,this._closeMessage)}close(e,t){if(this.readyState!==A.CLOSED){if(this.readyState===A.CONNECTING){M(this,this._req,"WebSocket was closed before the connection was established");return}if(this.readyState===A.CLOSING){this._closeFrameSent&&(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end();return}this._readyState=A.CLOSING,this._sender.close(e,t,!this._isServer,e=>{!e&&(this._closeFrameSent=!0,(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end())}),this._closeTimer=setTimeout(this._socket.destroy.bind(this._socket),3e4)}}pause(){this.readyState!==A.CONNECTING&&this.readyState!==A.CLOSED&&(this._paused=!0,this._socket.pause())}ping(e,t,s){if(this.readyState===A.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof e?(s=e,e=t=void 0):"function"==typeof t&&(s=t,t=void 0),"number"==typeof e&&(e=e.toString()),this.readyState!==A.OPEN){D(this,e,s);return}void 0===t&&(t=!this._isServer),this._sender.ping(e||y,t,s)}pong(e,t,s){if(this.readyState===A.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof e?(s=e,e=t=void 0):"function"==typeof t&&(s=t,t=void 0),"number"==typeof e&&(e=e.toString()),this.readyState!==A.OPEN){D(this,e,s);return}void 0===t&&(t=!this._isServer),this._sender.pong(e||y,t,s)}resume(){this.readyState!==A.CONNECTING&&this.readyState!==A.CLOSED&&(this._paused=!1,this._receiver._writableState.needDrain||this._socket.resume())}send(e,t,s){if(this.readyState===A.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof t&&(s=t,t={}),"number"==typeof e&&(e=e.toString()),this.readyState!==A.OPEN){D(this,e,s);return}let r={binary:"string"!=typeof e,mask:!this._isServer,compress:!0,fin:!0,...t};this._extensions[p.extensionName]||(r.compress=!1),this._sender.send(e||y,r,s)}terminate(){if(this.readyState!==A.CLOSED){if(this.readyState===A.CONNECTING){M(this,this._req,"WebSocket was closed before the connection was established");return}this._socket&&(this._readyState=A.CLOSING,this._socket.destroy())}}}function L(e,t){e._readyState=A.CLOSING,e.emit("error",t),e.emitClose()}function I(e){return e.path=e.socketPath,o.connect(e)}function P(e){return e.path=void 0,e.servername||""===e.servername||(e.servername=o.isIP(e.host)?"":e.host),a.connect(e)}function M(e,t,s){e._readyState=A.CLOSING;let r=Error(s);Error.captureStackTrace(r,M),t.setHeader?(t[O]=!0,t.abort(),t.socket&&!t.socket.destroyed&&t.socket.destroy(),process.nextTick(L,e,r)):(t.destroy(r),t.once("error",e.emit.bind(e,"error")),t.once("close",e.emitClose.bind(e)))}function D(e,t,s){if(t){let s=j(t).length;e._socket?e._sender._bufferedBytes+=s:e._bufferedAmount+=s}if(s){let t=Error(`WebSocket is not open: readyState ${e.readyState} (${F[e.readyState]})`);process.nextTick(s,t)}}function B(e,t){let s=this[w];s._closeFrameReceived=!0,s._closeMessage=t,s._closeCode=e,void 0!==s._socket[w]&&(s._socket.removeListener("data",G),process.nextTick(H,s._socket),1005===e?s.close():s.close(e,t))}function U(){let e=this[w];e.isPaused||e._socket.resume()}function q(e){let t=this[w];void 0!==t._socket[w]&&(t._socket.removeListener("data",G),process.nextTick(H,t._socket),t.close(e[_])),t.emit("error",e)}function V(){this[w].emitClose()}function $(e,t){this[w].emit("message",e,t)}function W(e){let t=this[w];t._autoPong&&t.pong(e,!this._isServer,k),t.emit("ping",e)}function z(e){this[w].emit("pong",e)}function H(e){e.resume()}function Z(){let e;let t=this[w];this.removeListener("close",Z),this.removeListener("data",G),this.removeListener("end",J),t._readyState=A.CLOSING,this._readableState.endEmitted||t._closeFrameReceived||t._receiver._writableState.errorEmitted||null===(e=t._socket.read())||t._receiver.write(e),t._receiver.end(),this[w]=void 0,clearTimeout(t._closeTimer),t._receiver._writableState.finished||t._receiver._writableState.errorEmitted?t.emitClose():(t._receiver.on("error",V),t._receiver.on("finish",V))}function G(e){this[w]._receiver.write(e)||this.pause()}function J(){let e=this[w];e._readyState=A.CLOSING,e._receiver.end(),this.end()}function Y(){let e=this[w];this.removeListener("error",Y),this.on("error",k),e&&(e._readyState=A.CLOSING,this.destroy())}Object.defineProperty(A,"CONNECTING",{enumerable:!0,value:F.indexOf("CONNECTING")}),Object.defineProperty(A.prototype,"CONNECTING",{enumerable:!0,value:F.indexOf("CONNECTING")}),Object.defineProperty(A,"OPEN",{enumerable:!0,value:F.indexOf("OPEN")}),Object.defineProperty(A.prototype,"OPEN",{enumerable:!0,value:F.indexOf("OPEN")}),Object.defineProperty(A,"CLOSING",{enumerable:!0,value:F.indexOf("CLOSING")}),Object.defineProperty(A.prototype,"CLOSING",{enumerable:!0,value:F.indexOf("CLOSING")}),Object.defineProperty(A,"CLOSED",{enumerable:!0,value:F.indexOf("CLOSED")}),Object.defineProperty(A.prototype,"CLOSED",{enumerable:!0,value:F.indexOf("CLOSED")}),["binaryType","bufferedAmount","extensions","isPaused","protocol","readyState","url"].forEach(e=>{Object.defineProperty(A.prototype,e,{enumerable:!0})}),["open","error","close","message"].forEach(e=>{Object.defineProperty(A.prototype,`on${e}`,{enumerable:!0,get(){for(let t of this.listeners(e))if(t[b])return t[v];return null},set(t){for(let t of this.listeners(e))if(t[b]){this.removeListener(e,t);break}"function"==typeof t&&this.addEventListener(e,t,{[b]:!0})}})}),A.prototype.addEventListener=C,A.prototype.removeEventListener=S,e.exports=A},3434:(e,t,s)=>{/**
 * Wrapper for built-in http.js to emulate the browser XMLHttpRequest object.
 *
 * This can be used with JS designed for browsers to improve reuse of code and
 * allow the use of existing libraries.
 *
 * Usage: include("XMLHttpRequest.js") and use XMLHttpRequest per W3C specs.
 *
 * <AUTHOR> DeFelippi <<EMAIL>>
 * @contributor David Ellis <<EMAIL>>
 * @license MIT
 */var r=s(7147),i=s(7310),n=s(2081).spawn;function o(e){"use strict";e=e||{};var t,o,a=this,l=s(3685),c=s(5687),h={},d=!1,u={"User-Agent":"node-XMLHttpRequest",Accept:"*/*"},p=Object.assign({},u),f=["accept-charset","accept-encoding","access-control-request-headers","access-control-request-method","connection","content-length","content-transfer-encoding","cookie","cookie2","date","expect","host","keep-alive","origin","referer","te","trailer","transfer-encoding","upgrade","via"],m=["TRACE","TRACK","CONNECT"],g=!1,y=!1,x=!1,b={};this.UNSENT=0,this.OPENED=1,this.HEADERS_RECEIVED=2,this.LOADING=3,this.DONE=4,this.readyState=this.UNSENT,this.onreadystatechange=null,this.responseText="",this.responseXML="",this.response=Buffer.alloc(0),this.status=null,this.statusText=null,this.open=function(e,t,s,r,i){if(this.abort(),y=!1,x=!1,!(e&&-1===m.indexOf(e)))throw Error("SecurityError: Request method not allowed");h={method:e,url:t.toString(),async:"boolean"!=typeof s||s,user:r||null,password:i||null},v(this.OPENED)},this.setDisableHeaderCheck=function(e){d=e},this.setRequestHeader=function(e,t){if(this.readyState!=this.OPENED)throw Error("INVALID_STATE_ERR: setRequestHeader can only be called when state is OPEN");if(!d&&(!e||-1!==f.indexOf(e.toLowerCase())))return console.warn('Refused to set unsafe header "'+e+'"'),!1;if(g)throw Error("INVALID_STATE_ERR: send flag is true");return p[e]=t,!0},this.getResponseHeader=function(e){return"string"==typeof e&&this.readyState>this.OPENED&&o.headers[e.toLowerCase()]&&!y?o.headers[e.toLowerCase()]:null},this.getAllResponseHeaders=function(){if(this.readyState<this.HEADERS_RECEIVED||y)return"";var e="";for(var t in o.headers)"set-cookie"!==t&&"set-cookie2"!==t&&(e+=t+": "+o.headers[t]+"\r\n");return e.substr(0,e.length-2)},this.getRequestHeader=function(e){return"string"==typeof e&&p[e]?p[e]:""},this.send=function(s){if(this.readyState!=this.OPENED)throw Error("INVALID_STATE_ERR: connection must be opened before send() is called");if(g)throw Error("INVALID_STATE_ERR: send has already been called");var d,u=!1,f=!1,m=i.parse(h.url);switch(m.protocol){case"https:":u=!0;case"http:":d=m.hostname;break;case"file:":f=!0;break;case void 0:case"":d="localhost";break;default:throw Error("Protocol not supported.")}if(f){if("GET"!==h.method)throw Error("XMLHttpRequest: Only GET method is supported");if(h.async)r.readFile(unescape(m.pathname),function(e,t){e?a.handleError(e,e.errno||-1):(a.status=200,a.responseText=t.toString("utf8"),a.response=t,v(a.DONE))});else try{this.response=r.readFileSync(unescape(m.pathname)),this.responseText=this.response.toString("utf8"),this.status=200,v(a.DONE)}catch(e){this.handleError(e,e.errno||-1)}return}var x=m.port||(u?443:80),b=m.pathname+(m.search?m.search:"");if(p.Host=d,u&&443===x||80===x||(p.Host+=":"+m.port),h.user){void 0===h.password&&(h.password="");var _=new Buffer(h.user+":"+h.password);p.Authorization="Basic "+_.toString("base64")}"GET"===h.method||"HEAD"===h.method?s=null:s?(p["Content-Length"]=Buffer.isBuffer(s)?s.length:Buffer.byteLength(s),Object.keys(p).some(function(e){return"content-type"===e.toLowerCase()})||(p["Content-Type"]="text/plain;charset=UTF-8")):"POST"===h.method&&(p["Content-Length"]=0);var w=e.agent||!1,k={host:d,port:x,path:b,method:h.method,headers:p,agent:w};if(u&&(k.pfx=e.pfx,k.key=e.key,k.passphrase=e.passphrase,k.cert=e.cert,k.ca=e.ca,k.ciphers=e.ciphers,k.rejectUnauthorized=!1!==e.rejectUnauthorized),y=!1,h.async){var C=u?c.request:l.request;g=!0,a.dispatchEvent("readystatechange");var S=function(s){if(302===(o=s).statusCode||303===o.statusCode||307===o.statusCode){h.url=o.headers.location;var r=i.parse(h.url);d=r.hostname;var n={hostname:r.hostname,port:r.port,path:r.path,method:303===o.statusCode?"GET":h.method,headers:p};u&&(n.pfx=e.pfx,n.key=e.key,n.passphrase=e.passphrase,n.cert=e.cert,n.ca=e.ca,n.ciphers=e.ciphers,n.rejectUnauthorized=!1!==e.rejectUnauthorized),(t=C(n,S).on("error",E)).end();return}v(a.HEADERS_RECEIVED),a.status=o.statusCode,o.on("data",function(e){if(e){var t=Buffer.from(e);a.response=Buffer.concat([a.response,t])}g&&v(a.LOADING)}),o.on("end",function(){g&&(g=!1,v(a.DONE),a.responseText=a.response.toString("utf8"))}),o.on("error",function(e){a.handleError(e)})},E=function(e){if(t.reusedSocket&&"ECONNRESET"===e.code)return C(k,S).on("error",E);a.handleError(e)};t=C(k,S).on("error",E),e.autoUnref&&t.on("socket",e=>{e.unref()}),s&&t.write(s),t.end(),a.dispatchEvent("loadstart")}else{var N=".node-xmlhttprequest-content-"+process.pid,j=".node-xmlhttprequest-sync-"+process.pid;r.writeFileSync(j,"","utf8");for(var O="var http = require('http'), https = require('https'), fs = require('fs');var doRequest = http"+(u?"s":"")+".request;var options = "+JSON.stringify(k)+";var responseText = '';var responseData = Buffer.alloc(0);var req = doRequest(options, function(response) {response.on('data', function(chunk) {  var data = Buffer.from(chunk);  responseText += data.toString('utf8');  responseData = Buffer.concat([responseData, data]);});response.on('end', function() {fs.writeFileSync('"+N+"', JSON.stringify({err: null, data: {statusCode: response.statusCode, headers: response.headers, text: responseText, data: responseData.toString('base64')}}), 'utf8');fs.unlinkSync('"+j+"');});response.on('error', function(error) {fs.writeFileSync('"+N+"', 'NODE-XMLHTTPREQUEST-ERROR:' + JSON.stringify(error), 'utf8');fs.unlinkSync('"+j+"');});}).on('error', function(error) {fs.writeFileSync('"+N+"', 'NODE-XMLHTTPREQUEST-ERROR:' + JSON.stringify(error), 'utf8');fs.unlinkSync('"+j+"');});"+(s?"req.write('"+JSON.stringify(s).slice(1,-1).replace(/'/g,"\\'")+"');":"")+"req.end();",T=n(process.argv[0],["-e",O]);r.existsSync(j););if(a.responseText=r.readFileSync(N,"utf8"),T.stdin.end(),r.unlinkSync(N),a.responseText.match(/^NODE-XMLHTTPREQUEST-ERROR:/)){var F=JSON.parse(a.responseText.replace(/^NODE-XMLHTTPREQUEST-ERROR:/,""));a.handleError(F,503)}else{a.status=a.responseText.replace(/^NODE-XMLHTTPREQUEST-STATUS:([0-9]*),.*/,"$1");var R=JSON.parse(a.responseText.replace(/^NODE-XMLHTTPREQUEST-STATUS:[0-9]*,(.*)/,"$1"));o={statusCode:a.status,headers:R.data.headers},a.responseText=R.data.text,a.response=Buffer.from(R.data.data,"base64"),v(a.DONE,!0)}}},this.handleError=function(e,t){this.status=t||0,this.statusText=e,this.responseText=e.stack,y=!0,v(this.DONE)},this.abort=function(){t&&(t.abort(),t=null),p=Object.assign({},u),this.responseText="",this.responseXML="",this.response=Buffer.alloc(0),y=x=!0,this.readyState!==this.UNSENT&&(this.readyState!==this.OPENED||g)&&this.readyState!==this.DONE&&(g=!1,v(this.DONE)),this.readyState=this.UNSENT},this.addEventListener=function(e,t){e in b||(b[e]=[]),b[e].push(t)},this.removeEventListener=function(e,t){e in b&&(b[e]=b[e].filter(function(e){return e!==t}))},this.dispatchEvent=function(e){if("function"==typeof a["on"+e]&&(this.readyState===this.DONE&&h.async?setTimeout(function(){a["on"+e]()},0):a["on"+e]()),e in b)for(let t=0,s=b[e].length;t<s;t++)this.readyState===this.DONE?setTimeout(function(){b[e][t].call(a)},0):b[e][t].call(a)};var v=function(e){if(a.readyState!==e&&(a.readyState!==a.UNSENT||!x)&&(a.readyState=e,(h.async||a.readyState<a.OPENED||a.readyState===a.DONE)&&a.dispatchEvent("readystatechange"),a.readyState===a.DONE)){let e;e=x?"abort":y?"error":"load",a.dispatchEvent(e),a.dispatchEvent("loadend")}}}e.exports=o,o.XMLHttpRequest=o},6590:(e,t,s)=>{"use strict";function r({children:e}){return e}s.r(t),s.d(t,{default:()=>r})},6298:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>o,__esModule:()=>n,default:()=>l,dynamic:()=>a});var r=s(8570);let i=(0,r.createProxy)(String.raw`/Volumes/Apps/Websites/streamit-main/app/room/[id]/page.tsx`),{__esModule:n,$$typeof:o}=i;i.default;let a=(0,r.createProxy)(String.raw`/Volumes/Apps/Websites/streamit-main/app/room/[id]/page.tsx#dynamic`),l=(0,r.createProxy)(String.raw`/Volumes/Apps/Websites/streamit-main/app/room/[id]/page.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[938,752],()=>s(2939));module.exports=r})();