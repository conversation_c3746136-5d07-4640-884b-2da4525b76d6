/** @type {import('next').NextConfig} */
const nextConfig = {
  // Use standalone for Heroku deployment
  output: 'standalone',
  typescript: {
    ignoreBuildErrors: false,
  },
  eslint: {
    ignoreDuringBuilds: false,
  },

  async rewrites() {
    return [
      {
        source: '/api/socket.io/:path*',
        destination: process.env.NODE_ENV === 'production' 
          ? 'https://your-heroku-app.herokuapp.com/socket.io/:path*'
          : 'http://localhost:3011/socket.io/:path*',
      },
    ]
  },
  // Fix for WebRTC and other client-side features
  webpack: (config, { isServer }) => {
    if (!isServer) {
      // Fixes npm packages that depend on `fs` module
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
        dns: false,
        child_process: false,
      };
    }
    return config;
  },
  // Enable experimental features if needed
  experimental: {
    serverComponentsExternalPackages: ['@prisma/client', 'bcryptjs'],
  },
  // Fix for styled-components
  compiler: {
    styledComponents: true,
  },
  // Improve source maps for better debugging
  productionBrowserSourceMaps: true,
  // Add security headers
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'X-Frame-Options',
            value: 'SAMEORIGIN',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
        ],
      },
    ]
  },
}

export default nextConfig
