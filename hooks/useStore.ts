'use client'

import { useStore as use<PERSON>ustandStore, type UseBoundStore, type StoreApi } from 'zustand'
import useVideoCallStore, { type VideoCallState } from '@/lib/store'

// Re-export the VideoCallState type for external use
export type { VideoCallState }

// Define the store type
type VideoCallStore = UseBoundStore<StoreApi<VideoCallState>>

// Create a custom hook that provides type safety for the store
export function useStore<T>(
  selector: (state: VideoCallState) => T,
  equalityFn?: (left: T, right: T) => boolean
): T {
  return useZustandStore(useVideoCallStore, selector, equalityFn)
}

// This file now only exports the useStore hook and types
// The StoreProvider is now responsible for store initialization
