@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* StreamIt Pro Original Design Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  overflow-x: hidden;
}

/* Animated Background */
.animated-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  z-index: -2;
}

.animated-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-30px) rotate(1deg); }
  66% { transform: translateY(-20px) rotate(-1deg); }
}

/* Glass Morphism Effects */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.glass-button {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.glass-button:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.glass-button:active {
  transform: translateY(0px);
}

/* Primary Button Styles */
.btn-primary {
  background: linear-gradient(135deg, #a855f7 0%, #9333ea 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 15px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(168, 85, 247, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(168, 85, 247, 0.4);
  background: linear-gradient(135deg, #9333ea 0%, #7c3aed 100%);
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 12px 24px;
  border-radius: 15px;
  font-weight: 500;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

/* Input Styles */
.glass-input {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  padding: 12px 16px;
  color: white;
  font-size: 16px;
  transition: all 0.3s ease;
}

.glass-input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.glass-input:focus {
  outline: none;
  border-color: rgba(168, 85, 247, 0.5);
  box-shadow: 0 0 20px rgba(168, 85, 247, 0.2);
  background: rgba(255, 255, 255, 0.15);
}

/* Video Call Styles */
.video-container {
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  overflow: hidden;
  position: relative;
  aspect-ratio: 16/9;
}

.video-tile {
  background: #1a1a1a;
  border-radius: 15px;
  overflow: hidden;
  position: relative;
  aspect-ratio: 16/9;
}

.video-tile video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-controls {
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 12px;
  display: flex;
  justify-content: center;
  gap: 8px;
  margin: 0 auto;
  width: fit-content;
}

.control-btn {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
}

.control-btn.active {
  background: #ef4444;
  color: white;
  box-shadow: 0 4px 16px rgba(239, 68, 68, 0.4);
}

.control-btn.inactive {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.control-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.control-btn:active {
  transform: scale(0.95);
}

/* Chat Styles */
.chat-container {
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.chat-message {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 12px;
  margin: 8px 0;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideUp {
  from { opacity: 0; transform: translateY(30px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes bounceIn {
  0% { opacity: 0; transform: scale(0.3); }
  50% { opacity: 1; transform: scale(1.05); }
  70% { transform: scale(0.9); }
  100% { opacity: 1; transform: scale(1); }
}

.fade-in { animation: fadeIn 0.6s ease-out; }
.slide-up { animation: slideUp 0.8s ease-out; }
.bounce-in { animation: bounceIn 0.6s ease-out; }

/* Responsive Design */
@media (max-width: 768px) {
  .glass {
    margin: 10px;
    border-radius: 15px;
  }

  .glass-button {
    padding: 10px 20px;
    font-size: 14px;
  }

  .control-btn {
    width: 45px;
    height: 45px;
  }
}

/* Custom video call styles */
.video-grid {
  display: grid;
  gap: 1rem;
  padding: 1rem;
}

.video-grid-1 {
  grid-template-columns: 1fr;
}

.video-grid-2 {
  grid-template-columns: 1fr 1fr;
}

.video-grid-3 {
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
}

.video-grid-4 {
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
}

.video-grid-5,
.video-grid-6 {
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(2, 1fr);
}

.video-grid-7,
.video-grid-8,
.video-grid-9 {
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(3, 1fr);
}

.video-tile {
  @apply relative rounded-lg overflow-hidden bg-gray-900;
  aspect-ratio: 16/9;
}

.video-tile video {
  @apply w-full h-full object-cover;
}

.video-controls {
  @apply absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-2;
}

.control-button {
  @apply p-3 rounded-full transition-colors duration-200;
}

.control-button.active {
  @apply bg-red-500 hover:bg-red-600;
}

.control-button.inactive {
  @apply bg-gray-700 hover:bg-gray-600;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Section spacing */
section {
  scroll-margin-top: 80px;
}

/* Enhanced mobile responsiveness */
@media (max-width: 640px) {
  .glass {
    margin: 8px;
    padding: 1.5rem;
    border-radius: 15px;
  }

  .btn-primary, .btn-secondary {
    padding: 10px 20px;
    font-size: 14px;
  }

  h1 {
    font-size: 2.5rem !important;
  }

  h2 {
    font-size: 2rem !important;
  }

  .text-xl {
    font-size: 1.125rem !important;
  }
}

/* Loading states */
.loading {
  opacity: 0.7;
  pointer-events: none;
}

/* Hover effects for cards */
.glass:hover {
  transform: translateY(-2px);
  transition: all 0.3s ease;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(135deg, #a855f7 0%, #9333ea 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
