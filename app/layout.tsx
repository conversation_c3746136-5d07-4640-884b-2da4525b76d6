import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { Toaster } from '@/components/ui/toaster'
import Navbar from '@/components/Navbar'
import Footer from '@/components/Footer'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'StreamIt Pro - Professional Video Conferencing Platform',
  description: 'Experience crystal-clear HD video calls, advanced audio processing, and seamless collaboration with StreamIt Pro. The ultimate video conferencing solution for teams of all sizes.',
  keywords: 'video conferencing, video calls, online meetings, webRTC, collaboration, streaming, HD video, professional meetings',
  authors: [{ name: 'StreamIt Pro Team' }],
  openGraph: {
    title: 'StreamIt Pro - Professional Video Conferencing Platform',
    description: 'Experience crystal-clear HD video calls, advanced audio processing, and seamless collaboration with StreamIt Pro.',
    type: 'website',
    locale: 'en_US',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'StreamIt Pro - Professional Video Conferencing Platform',
    description: 'Experience crystal-clear HD video calls, advanced audio processing, and seamless collaboration with StreamIt Pro.',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={`${inter.className} overflow-x-hidden`}>
        <Navbar />
        <main className="min-h-screen">
          {children}
        </main>
        <Footer />
        <Toaster />
      </body>
    </html>
  )
}
