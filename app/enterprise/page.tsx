'use client'

import {
  Building, Users, Shield, Settings, Globe, Headphones,
  Award, TrendingUp, Check, ArrowRight, Phone, Mail,
  Calendar, Zap, Lock, Monitor
} from 'lucide-react'

// Force dynamic rendering
export const dynamic = 'force-dynamic'

export default function EnterprisePage() {
  const enterpriseFeatures = [
    {
      icon: Users,
      title: 'Unlimited Participants',
      description: 'Host meetings with unlimited participants across your organization'
    },
    {
      icon: Shield,
      title: 'Advanced Security',
      description: 'Enterprise-grade security with SSO, SAML, and custom security policies'
    },
    {
      icon: Settings,
      title: 'Admin Dashboard',
      description: 'Comprehensive admin controls for user management and analytics'
    },
    {
      icon: Globe,
      title: 'Global Infrastructure',
      description: 'Dedicated servers and CDN for optimal performance worldwide'
    },
    {
      icon: Headphones,
      title: 'Priority Support',
      description: '24/7 dedicated support with guaranteed response times'
    },
    {
      icon: Award,
      title: 'SLA Guarantee',
      description: '99.99% uptime SLA with service credits for any downtime'
    }
  ]

  const benefits = [
    'Custom branding and white-label options',
    'Advanced analytics and reporting',
    'API access and custom integrations',
    'Dedicated customer success manager',
    'On-premise deployment options',
    'Custom training and onboarding',
    'Compliance certifications (HIPAA, SOX)',
    'Volume discounts for large teams'
  ]

  return (
    <>
      {/* Animated Background */}
      <div className="animated-bg"></div>

      <div className="min-h-screen pt-20 relative z-10">
        {/* Hero Section */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div>
                <h1 className="text-5xl font-bold text-white mb-6 fade-in">
                  Enterprise Video Conferencing Solutions
                </h1>
                <p className="text-xl text-white/80 mb-8 leading-relaxed">
                  Scale your communication with enterprise-grade features, security, 
                  and support designed for large organizations and mission-critical operations.
                </p>
                <div className="flex flex-col sm:flex-row gap-4">
                  <button className="btn-primary flex items-center gap-2 px-8 py-3">
                    <Phone className="h-5 w-5" />
                    Contact Sales
                  </button>
                  <button className="btn-secondary flex items-center gap-2 px-8 py-3">
                    <Calendar className="h-5 w-5" />
                    Schedule Demo
                  </button>
                </div>
              </div>
              <div className="glass p-8">
                <h3 className="text-2xl font-bold text-white mb-6">Trusted by Industry Leaders</h3>
                <div className="grid grid-cols-2 gap-6 text-center">
                  <div>
                    <div className="text-3xl font-bold text-white mb-2">500+</div>
                    <div className="text-white/60 text-sm">Enterprise Clients</div>
                  </div>
                  <div>
                    <div className="text-3xl font-bold text-white mb-2">99.99%</div>
                    <div className="text-white/60 text-sm">Uptime SLA</div>
                  </div>
                  <div>
                    <div className="text-3xl font-bold text-white mb-2">1M+</div>
                    <div className="text-white/60 text-sm">Daily Users</div>
                  </div>
                  <div>
                    <div className="text-3xl font-bold text-white mb-2">24/7</div>
                    <div className="text-white/60 text-sm">Support</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Enterprise Features */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-white mb-4">
                Enterprise-Grade Features
              </h2>
              <p className="text-xl text-white/70 max-w-3xl mx-auto">
                Everything you need to deploy video conferencing at scale across your organization
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {enterpriseFeatures.map((feature, index) => (
                <div key={index} className="glass p-8 text-center">
                  <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-6">
                    <feature.icon className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-white mb-4">{feature.title}</h3>
                  <p className="text-white/70">{feature.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Security & Compliance */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div>
                <h2 className="text-4xl font-bold text-white mb-6">
                  Security & Compliance First
                </h2>
                <p className="text-xl text-white/80 mb-8">
                  Meet the strictest security requirements with our comprehensive 
                  compliance certifications and advanced security features.
                </p>
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <Lock className="h-6 w-6 text-green-400" />
                    <span className="text-white">End-to-end encryption with AES-256</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Shield className="h-6 w-6 text-blue-400" />
                    <span className="text-white">SOC 2 Type II certified</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Award className="h-6 w-6 text-purple-400" />
                    <span className="text-white">HIPAA, GDPR, and SOX compliant</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Settings className="h-6 w-6 text-orange-400" />
                    <span className="text-white">Advanced admin controls and policies</span>
                  </div>
                </div>
              </div>
              <div className="glass p-8">
                <h3 className="text-xl font-bold text-white mb-6">Compliance Certifications</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-white/5 p-4 rounded-lg text-center">
                    <div className="text-white font-semibold">SOC 2</div>
                    <div className="text-white/60 text-sm">Type II</div>
                  </div>
                  <div className="bg-white/5 p-4 rounded-lg text-center">
                    <div className="text-white font-semibold">HIPAA</div>
                    <div className="text-white/60 text-sm">Compliant</div>
                  </div>
                  <div className="bg-white/5 p-4 rounded-lg text-center">
                    <div className="text-white font-semibold">GDPR</div>
                    <div className="text-white/60 text-sm">Compliant</div>
                  </div>
                  <div className="bg-white/5 p-4 rounded-lg text-center">
                    <div className="text-white font-semibold">ISO 27001</div>
                    <div className="text-white/60 text-sm">Certified</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Benefits */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-white mb-4">
                Why Choose StreamIt Pro Enterprise
              </h2>
              <p className="text-xl text-white/70">
                Comprehensive benefits designed for enterprise success
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-8">
              <div className="glass p-8">
                <h3 className="text-2xl font-bold text-white mb-6">Enterprise Benefits</h3>
                <div className="space-y-3">
                  {benefits.map((benefit, index) => (
                    <div key={index} className="flex items-center gap-3">
                      <Check className="h-5 w-5 text-green-400 flex-shrink-0" />
                      <span className="text-white/80">{benefit}</span>
                    </div>
                  ))}
                </div>
              </div>

              <div className="glass p-8">
                <h3 className="text-2xl font-bold text-white mb-6">Deployment Options</h3>
                <div className="space-y-6">
                  <div className="flex items-start gap-4">
                    <Globe className="h-6 w-6 text-blue-400 mt-1" />
                    <div>
                      <h4 className="text-white font-semibold mb-2">Cloud Deployment</h4>
                      <p className="text-white/70 text-sm">Fully managed cloud solution with global infrastructure</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-4">
                    <Monitor className="h-6 w-6 text-green-400 mt-1" />
                    <div>
                      <h4 className="text-white font-semibold mb-2">On-Premise</h4>
                      <p className="text-white/70 text-sm">Deploy within your own infrastructure for maximum control</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-4">
                    <Zap className="h-6 w-6 text-purple-400 mt-1" />
                    <div>
                      <h4 className="text-white font-semibold mb-2">Hybrid</h4>
                      <p className="text-white/70 text-sm">Combine cloud and on-premise for optimal flexibility</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Pricing */}
        <section className="py-20 px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-white mb-4">
                Enterprise Pricing
              </h2>
              <p className="text-xl text-white/70">
                Custom pricing based on your organization's needs
              </p>
            </div>

            <div className="glass p-12 text-center">
              <h3 className="text-3xl font-bold text-white mb-6">Custom Enterprise Plan</h3>
              <p className="text-xl text-white/70 mb-8">
                Tailored solutions with volume discounts, custom features, and dedicated support
              </p>
              
              <div className="grid md:grid-cols-3 gap-6 mb-8">
                <div className="text-center">
                  <div className="text-2xl font-bold text-white mb-2">Volume Discounts</div>
                  <div className="text-white/60">Up to 40% off for large teams</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-white mb-2">Flexible Terms</div>
                  <div className="text-white/60">Annual or multi-year contracts</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-white mb-2">Custom Features</div>
                  <div className="text-white/60">Tailored to your requirements</div>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button className="btn-primary flex items-center gap-2 px-8 py-3">
                  <Phone className="h-5 w-5" />
                  Contact Sales Team
                </button>
                <button className="btn-secondary flex items-center gap-2 px-8 py-3">
                  <Mail className="h-5 w-5" />
                  Request Quote
                </button>
              </div>
            </div>
          </div>
        </section>

        {/* Contact CTA */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <div className="grid md:grid-cols-3 gap-8">
              <div className="glass p-8 text-center">
                <Phone className="h-12 w-12 text-purple-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-3">Sales Team</h3>
                <p className="text-white/70 mb-4">
                  Speak with our enterprise sales specialists
                </p>
                <button className="btn-primary w-full">Call Sales</button>
              </div>

              <div className="glass p-8 text-center">
                <Calendar className="h-12 w-12 text-blue-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-3">Schedule Demo</h3>
                <p className="text-white/70 mb-4">
                  See StreamIt Pro Enterprise in action
                </p>
                <button className="btn-secondary w-full">Book Demo</button>
              </div>

              <div className="glass p-8 text-center">
                <Mail className="h-12 w-12 text-green-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-3">Get Quote</h3>
                <p className="text-white/70 mb-4">
                  Receive a custom pricing proposal
                </p>
                <button className="btn-secondary w-full">Request Quote</button>
              </div>
            </div>
          </div>
        </section>
      </div>
    </>
  )
}
