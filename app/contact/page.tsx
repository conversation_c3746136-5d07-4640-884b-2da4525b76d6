'use client'

import { useState } from 'react'
import { MessageCircle, Mail, Phone, MapPin, Clock, Send } from 'lucide-react'

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    company: '',
    subject: '',
    message: ''
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle form submission here
    console.log('Form submitted:', formData)
    alert('Thank you for your message! We\'ll get back to you soon.')
    setFormData({ name: '', email: '', company: '', subject: '', message: '' })
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  return (
    <>
      {/* Animated Background */}
      <div className="animated-bg"></div>

      <div className="min-h-screen pt-20 relative z-10">
        {/* Hero Section */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto text-center">
            <h1 className="text-5xl font-bold text-white mb-6 fade-in">
              Contact Us
            </h1>
            <p className="text-xl text-white/80 max-w-3xl mx-auto leading-relaxed slide-up">
              Have questions or need support? We're here to help. Reach out to our team and we'll get back to you as soon as possible.
            </p>
          </div>
        </section>

        {/* Contact Methods */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <div className="grid md:grid-cols-4 gap-8 mb-16">
              <div className="glass p-8 text-center">
                <MessageCircle className="h-12 w-12 text-purple-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-3">Live Chat</h3>
                <p className="text-white/70 mb-4">
                  Get instant help from our support team
                </p>
                <p className="text-white/60 text-sm">Available 24/7</p>
              </div>

              <div className="glass p-8 text-center">
                <Mail className="h-12 w-12 text-blue-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-3">Email</h3>
                <p className="text-white/70 mb-4">
                  Send us an email and we'll respond within 24 hours
                </p>
                <p className="text-white/60 text-sm"><EMAIL></p>
              </div>

              <div className="glass p-8 text-center">
                <Phone className="h-12 w-12 text-green-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-3">Phone</h3>
                <p className="text-white/70 mb-4">
                  Call us for immediate assistance
                </p>
                <p className="text-white/60 text-sm">+****************</p>
              </div>

              <div className="glass p-8 text-center">
                <MapPin className="h-12 w-12 text-orange-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-3">Office</h3>
                <p className="text-white/70 mb-4">
                  Visit our headquarters
                </p>
                <p className="text-white/60 text-sm">San Francisco, CA</p>
              </div>
            </div>

            {/* Contact Form and Info */}
            <div className="grid lg:grid-cols-2 gap-12">
              {/* Contact Form */}
              <div className="glass p-8">
                <h2 className="text-2xl font-bold text-white mb-6">Send us a Message</h2>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="name" className="block text-white/80 text-sm font-medium mb-2">
                        Full Name *
                      </label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleChange}
                        required
                        className="glass-input w-full"
                        placeholder="Your full name"
                      />
                    </div>
                    <div>
                      <label htmlFor="email" className="block text-white/80 text-sm font-medium mb-2">
                        Email Address *
                      </label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleChange}
                        required
                        className="glass-input w-full"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="company" className="block text-white/80 text-sm font-medium mb-2">
                      Company
                    </label>
                    <input
                      type="text"
                      id="company"
                      name="company"
                      value={formData.company}
                      onChange={handleChange}
                      className="glass-input w-full"
                      placeholder="Your company name"
                    />
                  </div>

                  <div>
                    <label htmlFor="subject" className="block text-white/80 text-sm font-medium mb-2">
                      Subject *
                    </label>
                    <select
                      id="subject"
                      name="subject"
                      value={formData.subject}
                      onChange={handleChange}
                      required
                      className="glass-input w-full"
                    >
                      <option value="">Select a subject</option>
                      <option value="general">General Inquiry</option>
                      <option value="support">Technical Support</option>
                      <option value="sales">Sales Question</option>
                      <option value="partnership">Partnership</option>
                      <option value="feedback">Feedback</option>
                    </select>
                  </div>

                  <div>
                    <label htmlFor="message" className="block text-white/80 text-sm font-medium mb-2">
                      Message *
                    </label>
                    <textarea
                      id="message"
                      name="message"
                      value={formData.message}
                      onChange={handleChange}
                      required
                      rows={6}
                      className="glass-input w-full resize-none"
                      placeholder="Tell us how we can help you..."
                    />
                  </div>

                  <button
                    type="submit"
                    className="btn-primary w-full flex items-center justify-center gap-2"
                  >
                    <Send className="h-4 w-4" />
                    Send Message
                  </button>
                </form>
              </div>

              {/* Contact Information */}
              <div className="space-y-8">
                <div className="glass p-8">
                  <h3 className="text-xl font-semibold text-white mb-6">Office Hours</h3>
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <Clock className="h-5 w-5 text-purple-400" />
                      <div>
                        <div className="text-white font-medium">Monday - Friday</div>
                        <div className="text-white/60 text-sm">9:00 AM - 6:00 PM PST</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <Clock className="h-5 w-5 text-purple-400" />
                      <div>
                        <div className="text-white font-medium">Saturday</div>
                        <div className="text-white/60 text-sm">10:00 AM - 4:00 PM PST</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <Clock className="h-5 w-5 text-purple-400" />
                      <div>
                        <div className="text-white font-medium">Sunday</div>
                        <div className="text-white/60 text-sm">Closed</div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="glass p-8">
                  <h3 className="text-xl font-semibold text-white mb-6">Quick Links</h3>
                  <div className="space-y-3">
                    <a href="#" className="block text-white/70 hover:text-white transition-colors">
                      Help Center & Documentation
                    </a>
                    <a href="#" className="block text-white/70 hover:text-white transition-colors">
                      System Status
                    </a>
                    <a href="#" className="block text-white/70 hover:text-white transition-colors">
                      Feature Requests
                    </a>
                    <a href="#" className="block text-white/70 hover:text-white transition-colors">
                      Bug Reports
                    </a>
                    <a href="#" className="block text-white/70 hover:text-white transition-colors">
                      Community Forum
                    </a>
                  </div>
                </div>

                <div className="glass p-8">
                  <h3 className="text-xl font-semibold text-white mb-6">Enterprise Sales</h3>
                  <p className="text-white/70 mb-4">
                    Looking for a custom solution for your organization? Our enterprise team is ready to help.
                  </p>
                  <button className="btn-secondary w-full">Contact Enterprise Sales</button>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section className="py-20 px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-white mb-4">
                Frequently Asked Questions
              </h2>
              <p className="text-white/70">
                Can't find what you're looking for? Check out our comprehensive FAQ section.
              </p>
            </div>
            <div className="text-center">
              <button className="btn-primary">View All FAQs</button>
            </div>
          </div>
        </section>
      </div>
    </>
  )
}
