'use client'

import { Map<PERSON><PERSON>, <PERSON>, <PERSON>, Heart, Zap, Award, ArrowRight, Briefcase } from 'lucide-react'

// Force dynamic rendering
export const dynamic = 'force-dynamic'

export default function CareersPage() {
  const openPositions = [
    {
      id: 1,
      title: 'Senior Frontend Developer',
      department: 'Engineering',
      location: 'San Francisco, CA / Remote',
      type: 'Full-time',
      experience: '5+ years',
      description: 'Join our frontend team to build the next generation of video conferencing experiences.'
    },
    {
      id: 2,
      title: 'Product Manager',
      department: 'Product',
      location: 'San Francisco, CA',
      type: 'Full-time',
      experience: '3+ years',
      description: 'Lead product strategy and roadmap for our core video conferencing platform.'
    },
    {
      id: 3,
      title: 'DevOps Engineer',
      department: 'Engineering',
      location: 'Remote',
      type: 'Full-time',
      experience: '4+ years',
      description: 'Help scale our infrastructure to support millions of users worldwide.'
    },
    {
      id: 4,
      title: 'UX Designer',
      department: 'Design',
      location: 'San Francisco, CA / Remote',
      type: 'Full-time',
      experience: '3+ years',
      description: 'Design intuitive and beautiful user experiences for our video conferencing platform.'
    },
    {
      id: 5,
      title: 'Customer Success Manager',
      department: 'Customer Success',
      location: 'New York, NY',
      type: 'Full-time',
      experience: '2+ years',
      description: 'Help our enterprise customers succeed with StreamIt Pro and drive adoption.'
    },
    {
      id: 6,
      title: 'Security Engineer',
      department: 'Security',
      location: 'Remote',
      type: 'Full-time',
      experience: '5+ years',
      description: 'Ensure the security and privacy of our platform and user data.'
    }
  ]

  const benefits = [
    {
      icon: Heart,
      title: 'Health & Wellness',
      description: 'Comprehensive health insurance, dental, vision, and wellness programs'
    },
    {
      icon: Clock,
      title: 'Flexible Work',
      description: 'Remote-first culture with flexible hours and unlimited PTO'
    },
    {
      icon: Zap,
      title: 'Growth & Learning',
      description: 'Professional development budget and conference attendance'
    },
    {
      icon: Award,
      title: 'Equity & Compensation',
      description: 'Competitive salary, equity package, and performance bonuses'
    },
    {
      icon: Users,
      title: 'Team Culture',
      description: 'Collaborative environment with regular team events and offsites'
    },
    {
      icon: Briefcase,
      title: 'Equipment & Setup',
      description: 'Top-tier equipment and home office setup allowance'
    }
  ]

  const values = [
    {
      title: 'Innovation First',
      description: 'We constantly push the boundaries of what\'s possible in video conferencing technology.'
    },
    {
      title: 'User-Centric',
      description: 'Every decision we make is guided by what\'s best for our users and their experience.'
    },
    {
      title: 'Transparency',
      description: 'We believe in open communication, honest feedback, and transparent processes.'
    },
    {
      title: 'Diversity & Inclusion',
      description: 'We celebrate diverse perspectives and create an inclusive environment for everyone.'
    }
  ]

  return (
    <>
      {/* Animated Background */}
      <div className="animated-bg"></div>

      <div className="min-h-screen pt-20 relative z-10">
        {/* Hero Section */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto text-center">
            <h1 className="text-5xl font-bold text-white mb-6 fade-in">
              Join Our Team
            </h1>
            <p className="text-xl text-white/80 max-w-3xl mx-auto leading-relaxed slide-up">
              Help us build the future of video conferencing and remote collaboration. 
              Join a team of passionate individuals making communication seamless for millions.
            </p>
          </div>
        </section>

        {/* Company Stats */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              <div className="glass p-6 text-center">
                <div className="text-3xl font-bold text-white mb-2">50+</div>
                <div className="text-white/60 text-sm">Team Members</div>
              </div>
              <div className="glass p-6 text-center">
                <div className="text-3xl font-bold text-white mb-2">15</div>
                <div className="text-white/60 text-sm">Countries</div>
              </div>
              <div className="glass p-6 text-center">
                <div className="text-3xl font-bold text-white mb-2">10M+</div>
                <div className="text-white/60 text-sm">Users Served</div>
              </div>
              <div className="glass p-6 text-center">
                <div className="text-3xl font-bold text-white mb-2">$50M</div>
                <div className="text-white/60 text-sm">Series B Funding</div>
              </div>
            </div>
          </div>
        </section>

        {/* Open Positions */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <h2 className="text-3xl font-bold text-white mb-12 text-center">Open Positions</h2>
            
            <div className="space-y-6">
              {openPositions.map((position) => (
                <div key={position.id} className="glass p-8 hover:transform hover:scale-[1.02] transition-all duration-300">
                  <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
                    <div className="flex-1">
                      <div className="flex flex-wrap items-center gap-3 mb-4">
                        <h3 className="text-xl font-semibold text-white">{position.title}</h3>
                        <span className="bg-purple-500 text-white px-3 py-1 rounded-full text-sm">
                          {position.department}
                        </span>
                      </div>
                      
                      <p className="text-white/70 mb-4">{position.description}</p>
                      
                      <div className="flex flex-wrap gap-4 text-sm">
                        <div className="flex items-center gap-2">
                          <MapPin className="h-4 w-4 text-white/60" />
                          <span className="text-white/60">{position.location}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4 text-white/60" />
                          <span className="text-white/60">{position.type}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Briefcase className="h-4 w-4 text-white/60" />
                          <span className="text-white/60">{position.experience}</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex flex-col sm:flex-row gap-3">
                      <button className="btn-secondary">Learn More</button>
                      <button className="btn-primary flex items-center gap-2">
                        Apply Now
                        <ArrowRight className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Benefits */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <h2 className="text-3xl font-bold text-white mb-12 text-center">Why Work With Us</h2>
            
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {benefits.map((benefit, index) => (
                <div key={index} className="glass p-8 text-center">
                  <benefit.icon className="h-12 w-12 text-purple-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-white mb-3">{benefit.title}</h3>
                  <p className="text-white/70">{benefit.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Company Values */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <h2 className="text-3xl font-bold text-white mb-12 text-center">Our Values</h2>
            
            <div className="grid md:grid-cols-2 gap-8">
              {values.map((value, index) => (
                <div key={index} className="glass p-8">
                  <h3 className="text-xl font-semibold text-white mb-4">{value.title}</h3>
                  <p className="text-white/70">{value.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Application Process */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <h2 className="text-3xl font-bold text-white mb-12 text-center">Our Hiring Process</h2>
            
            <div className="grid md:grid-cols-4 gap-8">
              <div className="glass p-6 text-center">
                <div className="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center text-white font-bold text-lg mx-auto mb-4">
                  1
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">Application</h3>
                <p className="text-white/70 text-sm">Submit your application and resume through our careers page</p>
              </div>
              <div className="glass p-6 text-center">
                <div className="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center text-white font-bold text-lg mx-auto mb-4">
                  2
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">Phone Screen</h3>
                <p className="text-white/70 text-sm">Initial conversation with our recruiting team</p>
              </div>
              <div className="glass p-6 text-center">
                <div className="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center text-white font-bold text-lg mx-auto mb-4">
                  3
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">Interviews</h3>
                <p className="text-white/70 text-sm">Technical and cultural fit interviews with the team</p>
              </div>
              <div className="glass p-6 text-center">
                <div className="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center text-white font-bold text-lg mx-auto mb-4">
                  4
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">Offer</h3>
                <p className="text-white/70 text-sm">Reference checks and offer discussion</p>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="glass p-12">
              <h2 className="text-3xl font-bold text-white mb-4">
                Don't See the Right Role?
              </h2>
              <p className="text-xl text-white/70 mb-8">
                We're always looking for talented individuals. Send us your resume and we'll keep you in mind for future opportunities.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button className="btn-primary px-8 py-3">Send Resume</button>
                <button className="btn-secondary px-8 py-3">Join Talent Pool</button>
              </div>
            </div>
          </div>
        </section>
      </div>
    </>
  )
}
