'use client'

import {
  Video, Users, Shield, Zap, Monitor, Smartphone, Laptop,
  Globe, Clock, Headphones, MessageCircle, Share2,
  Play, Settings, Lock, Award, TrendingUp, Check
} from 'lucide-react'

export default function FeaturesPage() {
  const features = [
    {
      icon: Video,
      title: 'Crystal Clear HD Video',
      description: 'Experience stunning 4K video quality with adaptive streaming that adjusts to your connection.',
      details: ['4K video resolution', 'Adaptive bitrate streaming', 'Low-light enhancement', 'Virtual backgrounds']
    },
    {
      icon: Users,
      title: 'Multi-Participant Calls',
      description: 'Connect with up to 100 participants in a single meeting with seamless performance.',
      details: ['Up to 100 participants', 'Gallery and speaker view', 'Participant management', 'Breakout rooms']
    },
    {
      icon: Shield,
      title: 'Enterprise Security',
      description: 'Bank-level security with end-to-end encryption and compliance certifications.',
      details: ['End-to-end encryption', 'SOC 2 certified', 'GDPR compliant', 'Single sign-on (SSO)']
    },
    {
      icon: Zap,
      title: 'Lightning Performance',
      description: 'Optimized for speed with global CDN and edge computing for minimal latency.',
      details: ['Sub-100ms latency', 'Global CDN network', 'Edge computing', '99.9% uptime SLA']
    },
    {
      icon: MessageCircle,
      title: 'Advanced Chat',
      description: 'Rich messaging with file sharing, reactions, and persistent chat history.',
      details: ['Real-time messaging', 'File sharing', 'Emoji reactions', 'Chat history']
    },
    {
      icon: Share2,
      title: 'Screen Sharing',
      description: 'Share your entire screen, specific applications, or browser tabs with ease.',
      details: ['Full screen sharing', 'Application sharing', 'Browser tab sharing', 'Remote control']
    },
    {
      icon: Play,
      title: 'Meeting Recording',
      description: 'Record meetings in HD with automatic transcription and cloud storage.',
      details: ['HD recording', 'Auto transcription', 'Cloud storage', 'Easy sharing']
    },
    {
      icon: Globe,
      title: 'Global Accessibility',
      description: 'Available worldwide with multi-language support and accessibility features.',
      details: ['150+ countries', 'Multi-language UI', 'Accessibility compliant', 'Mobile apps']
    }
  ]

  const platforms = [
    { icon: Monitor, name: 'Desktop', description: 'Windows, macOS, Linux' },
    { icon: Smartphone, name: 'Mobile', description: 'iOS and Android apps' },
    { icon: Laptop, name: 'Web Browser', description: 'Chrome, Firefox, Safari, Edge' }
  ]

  return (
    <>
      {/* Animated Background */}
      <div className="animated-bg"></div>

      <div className="min-h-screen pt-20 relative z-10">
        {/* Hero Section */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto text-center">
            <h1 className="text-5xl font-bold text-white mb-6 fade-in">
              Powerful Features for Modern Teams
            </h1>
            <p className="text-xl text-white/80 max-w-3xl mx-auto leading-relaxed slide-up">
              Discover all the tools and capabilities that make StreamIt Pro the ultimate 
              video conferencing solution for businesses of all sizes.
            </p>
          </div>
        </section>

        {/* Main Features Grid */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {features.map((feature, index) => (
                <div key={index} className="glass p-8 hover:transform hover:scale-105 transition-all duration-300">
                  <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-6">
                    <feature.icon className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-white mb-4 text-center">{feature.title}</h3>
                  <p className="text-white/70 mb-6 text-center">{feature.description}</p>
                  <ul className="space-y-2">
                    {feature.details.map((detail, detailIndex) => (
                      <li key={detailIndex} className="flex items-center gap-2 text-white/60 text-sm">
                        <Check className="h-4 w-4 text-green-400 flex-shrink-0" />
                        <span>{detail}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Platform Support */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-white mb-4">
                Works Everywhere You Do
              </h2>
              <p className="text-xl text-white/70 max-w-3xl mx-auto">
                Access StreamIt Pro from any device, anywhere in the world
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              {platforms.map((platform, index) => (
                <div key={index} className="glass p-8 text-center">
                  <platform.icon className="h-16 w-16 text-purple-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-white mb-2">{platform.name}</h3>
                  <p className="text-white/70">{platform.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Technical Specifications */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-white mb-4">
                Technical Specifications
              </h2>
              <p className="text-xl text-white/70">
                Built on cutting-edge technology for maximum performance
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-8">
              <div className="glass p-8">
                <h3 className="text-2xl font-bold text-white mb-6">Video & Audio</h3>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-white/70">Video Resolution</span>
                    <span className="text-white">Up to 4K (3840x2160)</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">Frame Rate</span>
                    <span className="text-white">30 FPS</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">Audio Quality</span>
                    <span className="text-white">48kHz, 16-bit stereo</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">Noise Cancellation</span>
                    <span className="text-white">AI-powered</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">Echo Cancellation</span>
                    <span className="text-white">Advanced AEC</span>
                  </div>
                </div>
              </div>

              <div className="glass p-8">
                <h3 className="text-2xl font-bold text-white mb-6">Network & Performance</h3>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-white/70">Minimum Bandwidth</span>
                    <span className="text-white">150 kbps</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">Recommended Bandwidth</span>
                    <span className="text-white">1.5 Mbps</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">Latency</span>
                    <span className="text-white">Sub-100ms</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">Protocol</span>
                    <span className="text-white">WebRTC</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">Encryption</span>
                    <span className="text-white">AES-256</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Security Features */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-white mb-4">
                Enterprise-Grade Security
              </h2>
              <p className="text-xl text-white/70">
                Your privacy and data security are our top priorities
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="glass p-6 text-center">
                <Lock className="h-12 w-12 text-green-400 mx-auto mb-4" />
                <h4 className="text-white font-semibold mb-2">End-to-End Encryption</h4>
                <p className="text-white/60 text-sm">All communications are encrypted with AES-256</p>
              </div>
              <div className="glass p-6 text-center">
                <Shield className="h-12 w-12 text-blue-400 mx-auto mb-4" />
                <h4 className="text-white font-semibold mb-2">SOC 2 Certified</h4>
                <p className="text-white/60 text-sm">Independently audited security controls</p>
              </div>
              <div className="glass p-6 text-center">
                <Award className="h-12 w-12 text-purple-400 mx-auto mb-4" />
                <h4 className="text-white font-semibold mb-2">GDPR Compliant</h4>
                <p className="text-white/60 text-sm">Full compliance with data protection regulations</p>
              </div>
              <div className="glass p-6 text-center">
                <Settings className="h-12 w-12 text-orange-400 mx-auto mb-4" />
                <h4 className="text-white font-semibold mb-2">Admin Controls</h4>
                <p className="text-white/60 text-sm">Comprehensive administrative and security controls</p>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="glass p-12">
              <h2 className="text-3xl font-bold text-white mb-4">
                Ready to Experience These Features?
              </h2>
              <p className="text-xl text-white/70 mb-8">
                Start your free trial today and see why teams choose StreamIt Pro
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button className="btn-primary px-8 py-3">Start Free Trial</button>
                <button className="btn-secondary px-8 py-3">Schedule Demo</button>
              </div>
            </div>
          </div>
        </section>
      </div>
    </>
  )
}
