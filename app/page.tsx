'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Video, Users, Shield, Zap, Play, UserPlus } from 'lucide-react'

export default function HomePage() {
  const [showNameModal, setShowNameModal] = useState(false)
  const [showJoinModal, setShowJoinModal] = useState(false)
  const [userName, setUserName] = useState('')
  const [roomId, setRoomId] = useState('')
  const [isStarting, setIsStarting] = useState(false)
  const router = useRouter()

  const handleStartMeeting = () => {
    setShowNameModal(true)
    setIsStarting(true)
  }

  const handleJoinMeeting = () => {
    setShowJoinModal(true)
    setIsStarting(false)
  }

  const startMeeting = () => {
    if (!userName.trim()) {
      alert('Please enter your name')
      return
    }

    const newRoomId = Math.random().toString(36).substring(2, 15)
    localStorage.setItem('userName', userName.trim())
    setShowNameModal(false)
    router.push(`/room/${newRoomId}`)
  }

  const joinRoom = () => {
    if (!roomId.trim() || !userName.trim()) {
      alert('Please enter both room ID and your name')
      return
    }

    localStorage.setItem('userName', userName.trim())
    setShowJoinModal(false)
    router.push(`/room/${roomId.trim()}`)
  }

  const closeModals = () => {
    setShowNameModal(false)
    setShowJoinModal(false)
    setUserName('')
    setRoomId('')
  }

  return (
    <>
      {/* Animated Background */}
      <div className="animated-bg"></div>

      <div className="min-h-screen flex flex-col items-center justify-center p-4 relative z-10">
        {/* Header */}
        <div className="text-center mb-12 fade-in">
          <div className="flex items-center justify-center mb-6">
            <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mr-4 shadow-lg">
              <Video className="h-8 w-8 text-white" />
            </div>
            <h1 className="text-5xl font-bold text-white">
              StreamIt Pro
            </h1>
          </div>
          <p className="text-xl text-white/80 max-w-2xl mx-auto leading-relaxed">
            Professional video conferencing platform with crystal-clear HD video,
            advanced audio processing, and seamless collaboration tools
          </p>
        </div>

        {/* Main Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-6 mb-16 slide-up">
          <button
            onClick={handleStartMeeting}
            className="btn-primary flex items-center gap-3 text-lg px-8 py-4 min-w-[200px] justify-center"
          >
            <Play className="h-6 w-6" />
            Start Meeting
          </button>

          <button
            onClick={handleJoinMeeting}
            className="btn-secondary flex items-center gap-3 text-lg px-8 py-4 min-w-[200px] justify-center"
          >
            <UserPlus className="h-6 w-6" />
            Join Meeting
          </button>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-3 gap-8 max-w-4xl w-full bounce-in">
          <div className="glass text-center p-8">
            <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center mx-auto mb-4">
              <Users className="h-8 w-8 text-white" />
            </div>
            <h3 className="text-xl font-semibold text-white mb-3">Multi-Participant</h3>
            <p className="text-white/70">
              Connect with multiple participants in crystal-clear HD video calls with advanced audio processing
            </p>
          </div>

          <div className="glass text-center p-8">
            <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4">
              <Shield className="h-8 w-8 text-white" />
            </div>
            <h3 className="text-xl font-semibold text-white mb-3">Secure & Private</h3>
            <p className="text-white/70">
              End-to-end encrypted communications with WebRTC technology ensuring your privacy and security
            </p>
          </div>

          <div className="glass text-center p-8">
            <div className="w-16 h-16 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4">
              <Zap className="h-8 w-8 text-white" />
            </div>
            <h3 className="text-xl font-semibold text-white mb-3">Lightning Fast</h3>
            <p className="text-white/70">
              Optimized for performance with minimal latency, adaptive quality, and seamless user experience
            </p>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center mt-16 text-white/60">
          <p>Built with Next.js, TypeScript, WebRTC & Modern Web Technologies</p>
        </div>
      </div>

      {/* Name Input Modal */}
      {showNameModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="glass p-8 max-w-md w-full">
            <h2 className="text-2xl font-bold text-white mb-6 text-center">
              {isStarting ? 'Start Your Meeting' : 'Join Meeting'}
            </h2>
            <div className="space-y-4">
              <input
                type="text"
                placeholder="Enter your name"
                value={userName}
                onChange={(e) => setUserName(e.target.value)}
                className="glass-input w-full"
                autoFocus
                onKeyDown={(e) => e.key === 'Enter' && startMeeting()}
              />
              <div className="flex gap-3">
                <button
                  onClick={closeModals}
                  className="btn-secondary flex-1"
                >
                  Cancel
                </button>
                <button
                  onClick={startMeeting}
                  className="btn-primary flex-1"
                >
                  Start Meeting
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Join Meeting Modal */}
      {showJoinModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="glass p-8 max-w-md w-full">
            <h2 className="text-2xl font-bold text-white mb-6 text-center">
              Join Meeting
            </h2>
            <div className="space-y-4">
              <input
                type="text"
                placeholder="Enter your name"
                value={userName}
                onChange={(e) => setUserName(e.target.value)}
                className="glass-input w-full"
                autoFocus
              />
              <input
                type="text"
                placeholder="Enter meeting ID"
                value={roomId}
                onChange={(e) => setRoomId(e.target.value)}
                className="glass-input w-full"
                onKeyDown={(e) => e.key === 'Enter' && joinRoom()}
              />
              <div className="flex gap-3">
                <button
                  onClick={closeModals}
                  className="btn-secondary flex-1"
                >
                  Cancel
                </button>
                <button
                  onClick={joinRoom}
                  className="btn-primary flex-1"
                >
                  Join Meeting
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )
}
