'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import {
  Video, Users, Shield, Zap, Play, UserPlus, Star, Check,
  Globe, Clock, Headphones, Monitor, Smartphone, Laptop,
  ArrowRight, MessageCircle, Phone, Mail, MapPin,
  ChevronDown, ChevronUp, Award, TrendingUp, Heart
} from 'lucide-react'

export default function HomePage() {
  const [showNameModal, setShowNameModal] = useState(false)
  const [showJoinModal, setShowJoinModal] = useState(false)
  const [userName, setUserName] = useState('')
  const [roomId, setRoomId] = useState('')
  const [isStarting, setIsStarting] = useState(false)
  const [openFaq, setOpenFaq] = useState<number | null>(null)
  const router = useRouter()

  const handleStartMeeting = () => {
    setShowNameModal(true)
    setIsStarting(true)
  }

  const handleJoinMeeting = () => {
    setShowJoinModal(true)
    setIsStarting(false)
  }

  const startMeeting = () => {
    if (!userName.trim()) {
      alert('Please enter your name')
      return
    }

    const newRoomId = Math.random().toString(36).substring(2, 15)
    localStorage.setItem('userName', userName.trim())
    setShowNameModal(false)
    router.push(`/room/${newRoomId}`)
  }

  const joinRoom = () => {
    if (!roomId.trim() || !userName.trim()) {
      alert('Please enter both room ID and your name')
      return
    }

    localStorage.setItem('userName', userName.trim())
    setShowJoinModal(false)
    router.push(`/room/${roomId.trim()}`)
  }

  const closeModals = () => {
    setShowNameModal(false)
    setShowJoinModal(false)
    setUserName('')
    setRoomId('')
  }

  const toggleFaq = (index: number) => {
    setOpenFaq(openFaq === index ? null : index)
  }

  return (
    <>
      {/* Animated Background */}
      <div className="animated-bg"></div>

      {/* Hero Section */}
      <section className="min-h-screen flex flex-col items-center justify-center p-4 relative z-10 pt-20">
        {/* Header */}
        <div className="text-center mb-12 fade-in">
          <div className="flex items-center justify-center mb-6">
            <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mr-4 shadow-lg">
              <Video className="h-8 w-8 text-white" />
            </div>
            <h1 className="text-5xl font-bold text-white">
              StreamIt Pro
            </h1>
          </div>
          <p className="text-xl text-white/80 max-w-2xl mx-auto leading-relaxed mb-8">
            Professional video conferencing platform with crystal-clear HD video,
            advanced audio processing, and seamless collaboration tools
          </p>
          <div className="flex items-center justify-center gap-6 text-white/60 text-sm mb-8">
            <div className="flex items-center gap-2">
              <Check className="h-4 w-4 text-green-400" />
              <span>No Downloads Required</span>
            </div>
            <div className="flex items-center gap-2">
              <Check className="h-4 w-4 text-green-400" />
              <span>End-to-End Encrypted</span>
            </div>
            <div className="flex items-center gap-2">
              <Check className="h-4 w-4 text-green-400" />
              <span>HD Video Quality</span>
            </div>
          </div>
        </div>

        {/* Main Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-6 mb-16 slide-up">
          <button
            onClick={handleStartMeeting}
            className="btn-primary flex items-center gap-3 text-lg px-8 py-4 min-w-[200px] justify-center"
          >
            <Play className="h-6 w-6" />
            Start Meeting
          </button>

          <button
            onClick={handleJoinMeeting}
            className="btn-secondary flex items-center gap-3 text-lg px-8 py-4 min-w-[200px] justify-center"
          >
            <UserPlus className="h-6 w-6" />
            Join Meeting
          </button>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl w-full bounce-in">
          <div className="text-center">
            <div className="text-3xl font-bold text-white mb-2">10M+</div>
            <div className="text-white/60 text-sm">Active Users</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-white mb-2">99.9%</div>
            <div className="text-white/60 text-sm">Uptime</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-white mb-2">150+</div>
            <div className="text-white/60 text-sm">Countries</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-white mb-2">24/7</div>
            <div className="text-white/60 text-sm">Support</div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 px-4 relative z-10">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">
              Powerful Features for Modern Teams
            </h2>
            <p className="text-xl text-white/70 max-w-3xl mx-auto">
              Everything you need for professional video conferencing, collaboration, and communication
            </p>
          </div>

          {/* Main Features Grid */}
          <div className="grid md:grid-cols-3 gap-8 mb-16">
            <div className="glass text-center p-8">
              <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-3">Multi-Participant Calls</h3>
              <p className="text-white/70 mb-4">
                Connect with up to 100 participants in crystal-clear HD video calls with advanced audio processing
              </p>
              <ul className="text-white/60 text-sm space-y-2">
                <li className="flex items-center gap-2">
                  <Check className="h-4 w-4 text-green-400" />
                  <span>Up to 100 participants</span>
                </li>
                <li className="flex items-center gap-2">
                  <Check className="h-4 w-4 text-green-400" />
                  <span>HD video quality</span>
                </li>
                <li className="flex items-center gap-2">
                  <Check className="h-4 w-4 text-green-400" />
                  <span>Noise cancellation</span>
                </li>
              </ul>
            </div>

            <div className="glass text-center p-8">
              <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <Shield className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-3">Enterprise Security</h3>
              <p className="text-white/70 mb-4">
                End-to-end encrypted communications with WebRTC technology ensuring your privacy and security
              </p>
              <ul className="text-white/60 text-sm space-y-2">
                <li className="flex items-center gap-2">
                  <Check className="h-4 w-4 text-green-400" />
                  <span>End-to-end encryption</span>
                </li>
                <li className="flex items-center gap-2">
                  <Check className="h-4 w-4 text-green-400" />
                  <span>GDPR compliant</span>
                </li>
                <li className="flex items-center gap-2">
                  <Check className="h-4 w-4 text-green-400" />
                  <span>SOC 2 certified</span>
                </li>
              </ul>
            </div>

            <div className="glass text-center p-8">
              <div className="w-16 h-16 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <Zap className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-3">Lightning Performance</h3>
              <p className="text-white/70 mb-4">
                Optimized for performance with minimal latency, adaptive quality, and seamless user experience
              </p>
              <ul className="text-white/60 text-sm space-y-2">
                <li className="flex items-center gap-2">
                  <Check className="h-4 w-4 text-green-400" />
                  <span>Sub-100ms latency</span>
                </li>
                <li className="flex items-center gap-2">
                  <Check className="h-4 w-4 text-green-400" />
                  <span>Adaptive bitrate</span>
                </li>
                <li className="flex items-center gap-2">
                  <Check className="h-4 w-4 text-green-400" />
                  <span>Global CDN</span>
                </li>
              </ul>
            </div>
          </div>

          {/* Additional Features */}
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="glass p-6 text-center">
              <Globe className="h-12 w-12 text-purple-400 mx-auto mb-3" />
              <h4 className="text-white font-semibold mb-2">Global Reach</h4>
              <p className="text-white/60 text-sm">Available in 150+ countries worldwide</p>
            </div>
            <div className="glass p-6 text-center">
              <Clock className="h-12 w-12 text-blue-400 mx-auto mb-3" />
              <h4 className="text-white font-semibold mb-2">24/7 Availability</h4>
              <p className="text-white/60 text-sm">Round-the-clock service reliability</p>
            </div>
            <div className="glass p-6 text-center">
              <Headphones className="h-12 w-12 text-green-400 mx-auto mb-3" />
              <h4 className="text-white font-semibold mb-2">Premium Support</h4>
              <p className="text-white/60 text-sm">Expert technical support team</p>
            </div>
            <div className="glass p-6 text-center">
              <Monitor className="h-12 w-12 text-orange-400 mx-auto mb-3" />
              <h4 className="text-white font-semibold mb-2">Cross-Platform</h4>
              <p className="text-white/60 text-sm">Works on all devices and browsers</p>
            </div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="py-20 px-4 relative z-10">
        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-4xl font-bold text-white mb-6">
                Revolutionizing Remote Communication
              </h2>
              <p className="text-xl text-white/80 mb-6">
                StreamIt Pro was built from the ground up to solve the challenges of modern remote work.
                Our platform combines cutting-edge WebRTC technology with intuitive design to deliver
                the most reliable video conferencing experience.
              </p>
              <div className="space-y-4 mb-8">
                <div className="flex items-start gap-3">
                  <Award className="h-6 w-6 text-purple-400 mt-1" />
                  <div>
                    <h4 className="text-white font-semibold">Industry Leading</h4>
                    <p className="text-white/60 text-sm">Recognized by top tech publications and industry experts</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <TrendingUp className="h-6 w-6 text-green-400 mt-1" />
                  <div>
                    <h4 className="text-white font-semibold">Rapid Growth</h4>
                    <p className="text-white/60 text-sm">Trusted by millions of users worldwide</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <Heart className="h-6 w-6 text-red-400 mt-1" />
                  <div>
                    <h4 className="text-white font-semibold">User Focused</h4>
                    <p className="text-white/60 text-sm">Built with user experience and feedback at the core</p>
                  </div>
                </div>
              </div>
              <button className="btn-primary flex items-center gap-2">
                Learn More About Us
                <ArrowRight className="h-4 w-4" />
              </button>
            </div>
            <div className="glass p-8">
              <div className="grid grid-cols-2 gap-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-white mb-2">2019</div>
                  <div className="text-white/60 text-sm">Founded</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-white mb-2">50+</div>
                  <div className="text-white/60 text-sm">Team Members</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-white mb-2">10M+</div>
                  <div className="text-white/60 text-sm">Meetings Hosted</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-white mb-2">500+</div>
                  <div className="text-white/60 text-sm">Enterprise Clients</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-20 px-4 relative z-10">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">
              Simple, Transparent Pricing
            </h2>
            <p className="text-xl text-white/70 max-w-3xl mx-auto">
              Choose the perfect plan for your team. All plans include our core features with no hidden fees.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {/* Basic Plan */}
            <div className="glass p-8 text-center">
              <h3 className="text-2xl font-bold text-white mb-4">Basic</h3>
              <div className="mb-6">
                <span className="text-4xl font-bold text-white">Free</span>
              </div>
              <ul className="space-y-3 mb-8">
                <li className="flex items-center gap-2 text-white/70">
                  <Check className="h-4 w-4 text-green-400" />
                  <span>Up to 5 participants</span>
                </li>
                <li className="flex items-center gap-2 text-white/70">
                  <Check className="h-4 w-4 text-green-400" />
                  <span>40-minute meetings</span>
                </li>
                <li className="flex items-center gap-2 text-white/70">
                  <Check className="h-4 w-4 text-green-400" />
                  <span>HD video quality</span>
                </li>
                <li className="flex items-center gap-2 text-white/70">
                  <Check className="h-4 w-4 text-green-400" />
                  <span>Basic chat features</span>
                </li>
              </ul>
              <button className="btn-secondary w-full">Get Started</button>
            </div>

            {/* Pro Plan */}
            <div className="glass p-8 text-center border-2 border-purple-500 relative">
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-gradient-to-r from-purple-500 to-pink-500 text-white px-4 py-1 rounded-full text-sm font-semibold">
                Most Popular
              </div>
              <h3 className="text-2xl font-bold text-white mb-4">Pro</h3>
              <div className="mb-6">
                <span className="text-4xl font-bold text-white">$12</span>
                <span className="text-white/60">/month</span>
              </div>
              <ul className="space-y-3 mb-8">
                <li className="flex items-center gap-2 text-white/70">
                  <Check className="h-4 w-4 text-green-400" />
                  <span>Up to 25 participants</span>
                </li>
                <li className="flex items-center gap-2 text-white/70">
                  <Check className="h-4 w-4 text-green-400" />
                  <span>Unlimited meeting time</span>
                </li>
                <li className="flex items-center gap-2 text-white/70">
                  <Check className="h-4 w-4 text-green-400" />
                  <span>4K video quality</span>
                </li>
                <li className="flex items-center gap-2 text-white/70">
                  <Check className="h-4 w-4 text-green-400" />
                  <span>Advanced chat & file sharing</span>
                </li>
                <li className="flex items-center gap-2 text-white/70">
                  <Check className="h-4 w-4 text-green-400" />
                  <span>Meeting recordings</span>
                </li>
              </ul>
              <button className="btn-primary w-full">Start Free Trial</button>
            </div>

            {/* Enterprise Plan */}
            <div className="glass p-8 text-center">
              <h3 className="text-2xl font-bold text-white mb-4">Enterprise</h3>
              <div className="mb-6">
                <span className="text-4xl font-bold text-white">$25</span>
                <span className="text-white/60">/month</span>
              </div>
              <ul className="space-y-3 mb-8">
                <li className="flex items-center gap-2 text-white/70">
                  <Check className="h-4 w-4 text-green-400" />
                  <span>Up to 100 participants</span>
                </li>
                <li className="flex items-center gap-2 text-white/70">
                  <Check className="h-4 w-4 text-green-400" />
                  <span>Unlimited everything</span>
                </li>
                <li className="flex items-center gap-2 text-white/70">
                  <Check className="h-4 w-4 text-green-400" />
                  <span>Enterprise security</span>
                </li>
                <li className="flex items-center gap-2 text-white/70">
                  <Check className="h-4 w-4 text-green-400" />
                  <span>Priority support</span>
                </li>
                <li className="flex items-center gap-2 text-white/70">
                  <Check className="h-4 w-4 text-green-400" />
                  <span>Custom integrations</span>
                </li>
              </ul>
              <button className="btn-secondary w-full">Contact Sales</button>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 px-4 relative z-10">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">
              Trusted by Teams Worldwide
            </h2>
            <p className="text-xl text-white/70 max-w-3xl mx-auto">
              See what our customers have to say about their StreamIt Pro experience
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="glass p-8">
              <div className="flex items-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-white/80 mb-6">
                "StreamIt Pro has revolutionized how our remote team collaborates. The video quality is exceptional and the interface is incredibly intuitive."
              </p>
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                  <span className="text-white font-semibold">SJ</span>
                </div>
                <div>
                  <div className="text-white font-semibold">Sarah Johnson</div>
                  <div className="text-white/60 text-sm">CTO, TechCorp</div>
                </div>
              </div>
            </div>

            <div className="glass p-8">
              <div className="flex items-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-white/80 mb-6">
                "The security features give us peace of mind when discussing sensitive client information. Plus, the performance is consistently reliable."
              </p>
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center">
                  <span className="text-white font-semibold">MR</span>
                </div>
                <div>
                  <div className="text-white font-semibold">Michael Rodriguez</div>
                  <div className="text-white/60 text-sm">Director, Legal Firm</div>
                </div>
              </div>
            </div>

            <div className="glass p-8">
              <div className="flex items-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-white/80 mb-6">
                "We've tried many video conferencing solutions, but StreamIt Pro stands out with its ease of use and crystal-clear audio quality."
              </p>
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center">
                  <span className="text-white font-semibold">AL</span>
                </div>
                <div>
                  <div className="text-white font-semibold">Amanda Lee</div>
                  <div className="text-white/60 text-sm">VP Marketing, StartupXYZ</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 px-4 relative z-10">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">
              Frequently Asked Questions
            </h2>
            <p className="text-xl text-white/70">
              Get answers to common questions about StreamIt Pro
            </p>
          </div>

          <div className="space-y-4">
            {[
              {
                question: "How many participants can join a meeting?",
                answer: "The number of participants depends on your plan. Basic allows up to 5, Pro supports up to 25, and Enterprise can handle up to 100 participants in a single meeting."
              },
              {
                question: "Is StreamIt Pro secure?",
                answer: "Yes, absolutely. We use end-to-end encryption for all communications, are GDPR compliant, and SOC 2 certified. Your privacy and security are our top priorities."
              },
              {
                question: "Do I need to download any software?",
                answer: "No downloads required! StreamIt Pro works directly in your web browser using modern WebRTC technology. It's compatible with Chrome, Firefox, Safari, and Edge."
              },
              {
                question: "Can I record meetings?",
                answer: "Meeting recording is available on Pro and Enterprise plans. Recordings are stored securely in the cloud and can be shared with team members who missed the meeting."
              },
              {
                question: "What devices are supported?",
                answer: "StreamIt Pro works on all modern devices including desktops, laptops, tablets, and smartphones. Our responsive design ensures a great experience across all screen sizes."
              },
              {
                question: "Is there a free trial?",
                answer: "Yes! We offer a 14-day free trial of our Pro plan with no credit card required. You can also use our Basic plan for free with some limitations."
              }
            ].map((faq, index) => (
              <div key={index} className="glass">
                <button
                  onClick={() => toggleFaq(index)}
                  className="w-full p-6 text-left flex items-center justify-between hover:bg-white/5 transition-colors"
                >
                  <span className="text-white font-semibold">{faq.question}</span>
                  {openFaq === index ? (
                    <ChevronUp className="h-5 w-5 text-white/60" />
                  ) : (
                    <ChevronDown className="h-5 w-5 text-white/60" />
                  )}
                </button>
                {openFaq === index && (
                  <div className="px-6 pb-6">
                    <p className="text-white/70">{faq.answer}</p>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-20 px-4 relative z-10">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">
              Get in Touch
            </h2>
            <p className="text-xl text-white/70 max-w-3xl mx-auto">
              Have questions or need help? Our team is here to support you every step of the way.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="glass p-8 text-center">
              <MessageCircle className="h-12 w-12 text-purple-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-white mb-3">Live Chat</h3>
              <p className="text-white/70 mb-4">
                Get instant help from our support team
              </p>
              <button className="btn-primary">Start Chat</button>
            </div>

            <div className="glass p-8 text-center">
              <Mail className="h-12 w-12 text-blue-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-white mb-3">Email Support</h3>
              <p className="text-white/70 mb-4">
                Send us an email and we'll respond within 24 hours
              </p>
              <button className="btn-secondary">Send Email</button>
            </div>

            <div className="glass p-8 text-center">
              <Phone className="h-12 w-12 text-green-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-white mb-3">Phone Support</h3>
              <p className="text-white/70 mb-4">
                Call us for immediate assistance
              </p>
              <button className="btn-secondary">Call Now</button>
            </div>
          </div>
        </div>
      </section>

      {/* Name Input Modal */}
      {showNameModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="glass p-8 max-w-md w-full">
            <h2 className="text-2xl font-bold text-white mb-6 text-center">
              {isStarting ? 'Start Your Meeting' : 'Join Meeting'}
            </h2>
            <div className="space-y-4">
              <input
                type="text"
                placeholder="Enter your name"
                value={userName}
                onChange={(e) => setUserName(e.target.value)}
                className="glass-input w-full"
                autoFocus
                onKeyPress={(e) => e.key === 'Enter' && startMeeting()}
              />
              <div className="flex gap-3">
                <button
                  onClick={closeModals}
                  className="btn-secondary flex-1"
                >
                  Cancel
                </button>
                <button
                  onClick={startMeeting}
                  className="btn-primary flex-1"
                >
                  Start Meeting
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Join Meeting Modal */}
      {showJoinModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="glass p-8 max-w-md w-full">
            <h2 className="text-2xl font-bold text-white mb-6 text-center">
              Join Meeting
            </h2>
            <div className="space-y-4">
              <input
                type="text"
                placeholder="Enter your name"
                value={userName}
                onChange={(e) => setUserName(e.target.value)}
                className="glass-input w-full"
                autoFocus
              />
              <input
                type="text"
                placeholder="Enter meeting ID"
                value={roomId}
                onChange={(e) => setRoomId(e.target.value)}
                className="glass-input w-full"
                onKeyPress={(e) => e.key === 'Enter' && joinRoom()}
              />
              <div className="flex gap-3">
                <button
                  onClick={closeModals}
                  className="btn-secondary flex-1"
                >
                  Cancel
                </button>
                <button
                  onClick={joinRoom}
                  className="btn-primary flex-1"
                >
                  Join Meeting
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )
}
