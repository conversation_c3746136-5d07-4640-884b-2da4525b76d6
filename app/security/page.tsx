'use client'

import {
  Shield, Lock, Eye, Server, Globe, Award,
  CheckCircle, AlertTriangle, Users, Settings,
  FileText, Zap, Monitor, Key
} from 'lucide-react'

// Force dynamic rendering
export const dynamic = 'force-dynamic'

export default function SecurityPage() {
  const securityFeatures = [
    {
      icon: Lock,
      title: 'End-to-End Encryption',
      description: 'All communications are protected with AES-256 encryption, ensuring your conversations remain private.',
      details: ['AES-256 encryption', 'Perfect Forward Secrecy', 'Encrypted at rest and in transit', 'Zero-knowledge architecture']
    },
    {
      icon: Shield,
      title: 'Enterprise Authentication',
      description: 'Secure access controls with multi-factor authentication and single sign-on integration.',
      details: ['Multi-factor authentication', 'Single Sign-On (SSO)', 'SAML 2.0 support', 'Active Directory integration']
    },
    {
      icon: Eye,
      title: 'Privacy Controls',
      description: 'Comprehensive privacy settings and data protection measures to keep your information secure.',
      details: ['Granular privacy settings', 'Data anonymization', 'Right to be forgotten', 'Consent management']
    },
    {
      icon: Server,
      title: 'Secure Infrastructure',
      description: 'Built on secure, compliant infrastructure with regular security audits and monitoring.',
      details: ['SOC 2 Type II certified', 'ISO 27001 compliant', '24/7 security monitoring', 'Regular penetration testing']
    }
  ]

  const compliance = [
    { name: 'SOC 2 Type II', description: 'Security, availability, and confidentiality controls' },
    { name: 'ISO 27001', description: 'Information security management systems' },
    { name: 'GDPR', description: 'European data protection regulation compliance' },
    { name: 'HIPAA', description: 'Healthcare information privacy and security' },
    { name: 'CCPA', description: 'California consumer privacy act compliance' },
    { name: 'SOX', description: 'Sarbanes-Oxley financial reporting compliance' }
  ]

  const securityPractices = [
    'Regular security audits and penetration testing',
    'Vulnerability management and patch deployment',
    'Incident response and disaster recovery plans',
    'Employee security training and background checks',
    'Secure development lifecycle (SDLC)',
    'Third-party security assessments',
    'Data loss prevention (DLP) measures',
    'Network segmentation and access controls'
  ]

  return (
    <>
      {/* Animated Background */}
      <div className="animated-bg"></div>

      <div className="min-h-screen pt-20 relative z-10">
        {/* Hero Section */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto text-center">
            <h1 className="text-5xl font-bold text-white mb-6 fade-in">
              Enterprise-Grade Security
            </h1>
            <p className="text-xl text-white/80 max-w-3xl mx-auto leading-relaxed slide-up">
              Your privacy and data security are our top priorities. StreamIt Pro is built 
              with multiple layers of security to protect your communications and data.
            </p>
          </div>
        </section>

        {/* Security Features */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-white mb-4">
                Comprehensive Security Features
              </h2>
              <p className="text-xl text-white/70">
                Multi-layered security approach to protect your communications
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-8">
              {securityFeatures.map((feature, index) => (
                <div key={index} className="glass p-8">
                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center flex-shrink-0">
                      <feature.icon className="h-6 w-6 text-white" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-xl font-semibold text-white mb-3">{feature.title}</h3>
                      <p className="text-white/70 mb-4">{feature.description}</p>
                      <ul className="space-y-2">
                        {feature.details.map((detail, detailIndex) => (
                          <li key={detailIndex} className="flex items-center gap-2 text-white/60 text-sm">
                            <CheckCircle className="h-4 w-4 text-green-400 flex-shrink-0" />
                            <span>{detail}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Compliance Certifications */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-white mb-4">
                Compliance & Certifications
              </h2>
              <p className="text-xl text-white/70">
                Independently verified security and compliance standards
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {compliance.map((cert, index) => (
                <div key={index} className="glass p-6 text-center">
                  <Award className="h-12 w-12 text-purple-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-white mb-2">{cert.name}</h3>
                  <p className="text-white/60 text-sm">{cert.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Security Architecture */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div>
                <h2 className="text-4xl font-bold text-white mb-6">
                  Secure by Design
                </h2>
                <p className="text-xl text-white/80 mb-8">
                  Our security architecture is built from the ground up with security 
                  as a fundamental principle, not an afterthought.
                </p>
                <div className="space-y-6">
                  <div className="flex items-start gap-4">
                    <Key className="h-6 w-6 text-yellow-400 mt-1" />
                    <div>
                      <h4 className="text-white font-semibold mb-2">Zero-Knowledge Architecture</h4>
                      <p className="text-white/70 text-sm">We cannot access your encrypted communications, even if we wanted to.</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-4">
                    <Globe className="h-6 w-6 text-blue-400 mt-1" />
                    <div>
                      <h4 className="text-white font-semibold mb-2">Global Security Standards</h4>
                      <p className="text-white/70 text-sm">Compliant with international security frameworks and regulations.</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-4">
                    <Monitor className="h-6 w-6 text-green-400 mt-1" />
                    <div>
                      <h4 className="text-white font-semibold mb-2">Continuous Monitoring</h4>
                      <p className="text-white/70 text-sm">24/7 security monitoring and threat detection systems.</p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="glass p-8">
                <h3 className="text-2xl font-bold text-white mb-6">Security Layers</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                    <span className="text-white">Application Security</span>
                    <CheckCircle className="h-5 w-5 text-green-400" />
                  </div>
                  <div className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                    <span className="text-white">Network Security</span>
                    <CheckCircle className="h-5 w-5 text-green-400" />
                  </div>
                  <div className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                    <span className="text-white">Infrastructure Security</span>
                    <CheckCircle className="h-5 w-5 text-green-400" />
                  </div>
                  <div className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                    <span className="text-white">Physical Security</span>
                    <CheckCircle className="h-5 w-5 text-green-400" />
                  </div>
                  <div className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                    <span className="text-white">Operational Security</span>
                    <CheckCircle className="h-5 w-5 text-green-400" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Security Practices */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-white mb-4">
                Security Best Practices
              </h2>
              <p className="text-xl text-white/70">
                Our comprehensive approach to maintaining the highest security standards
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-8">
              <div className="glass p-8">
                <h3 className="text-2xl font-bold text-white mb-6">Security Practices</h3>
                <div className="space-y-3">
                  {securityPractices.map((practice, index) => (
                    <div key={index} className="flex items-start gap-3">
                      <CheckCircle className="h-5 w-5 text-green-400 flex-shrink-0 mt-0.5" />
                      <span className="text-white/80 text-sm">{practice}</span>
                    </div>
                  ))}
                </div>
              </div>

              <div className="glass p-8">
                <h3 className="text-2xl font-bold text-white mb-6">Incident Response</h3>
                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <AlertTriangle className="h-5 w-5 text-orange-400 mt-1" />
                    <div>
                      <h4 className="text-white font-semibold mb-1">24/7 Monitoring</h4>
                      <p className="text-white/70 text-sm">Continuous monitoring for security threats and anomalies</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <Zap className="h-5 w-5 text-yellow-400 mt-1" />
                    <div>
                      <h4 className="text-white font-semibold mb-1">Rapid Response</h4>
                      <p className="text-white/70 text-sm">Immediate response to security incidents and threats</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <FileText className="h-5 w-5 text-blue-400 mt-1" />
                    <div>
                      <h4 className="text-white font-semibold mb-1">Transparent Reporting</h4>
                      <p className="text-white/70 text-sm">Clear communication about security incidents and resolutions</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <Settings className="h-5 w-5 text-purple-400 mt-1" />
                    <div>
                      <h4 className="text-white font-semibold mb-1">Continuous Improvement</h4>
                      <p className="text-white/70 text-sm">Regular updates to security measures and protocols</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Security Resources */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-white mb-4">
                Security Resources
              </h2>
              <p className="text-xl text-white/70">
                Learn more about our security practices and get help with security questions
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              <div className="glass p-8 text-center">
                <FileText className="h-12 w-12 text-blue-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-3">Security Whitepaper</h3>
                <p className="text-white/70 mb-4">
                  Detailed technical documentation of our security architecture
                </p>
                <button className="btn-secondary w-full">Download PDF</button>
              </div>

              <div className="glass p-8 text-center">
                <Shield className="h-12 w-12 text-green-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-3">Security Portal</h3>
                <p className="text-white/70 mb-4">
                  Access security documentation and compliance certificates
                </p>
                <button className="btn-secondary w-full">Visit Portal</button>
              </div>

              <div className="glass p-8 text-center">
                <Users className="h-12 w-12 text-purple-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-3">Security Team</h3>
                <p className="text-white/70 mb-4">
                  Contact our security team for questions or concerns
                </p>
                <button className="btn-secondary w-full">Contact Security</button>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="glass p-12">
              <h2 className="text-3xl font-bold text-white mb-4">
                Questions About Security?
              </h2>
              <p className="text-xl text-white/70 mb-8">
                Our security team is here to help answer any questions about our security practices
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button className="btn-primary px-8 py-3">Contact Security Team</button>
                <button className="btn-secondary px-8 py-3">View Documentation</button>
              </div>
            </div>
          </div>
        </section>
      </div>
    </>
  )
}
