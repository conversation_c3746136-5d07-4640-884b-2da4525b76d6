'use client'

import { useState } from 'react'

// Force dynamic rendering
export const dynamic = 'force-dynamic'
import { 
  Search, Book, Video, Settings, Shield, Users, 
  MessageCircle, Phone, Mail, ChevronRight, 
  ChevronDown, ChevronUp, HelpCircle, FileText
} from 'lucide-react'

export default function HelpCenterPage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [openFaq, setOpenFaq] = useState<number | null>(null)

  const categories = [
    {
      icon: Video,
      title: 'Getting Started',
      description: 'Learn the basics of using StreamIt Pro',
      articles: 12
    },
    {
      icon: Settings,
      title: 'Account & Settings',
      description: 'Manage your account and preferences',
      articles: 8
    },
    {
      icon: Users,
      title: 'Meetings & Participants',
      description: 'Host and join meetings effectively',
      articles: 15
    },
    {
      icon: Shield,
      title: 'Security & Privacy',
      description: 'Keep your meetings secure',
      articles: 6
    },
    {
      icon: Book,
      title: 'Advanced Features',
      description: 'Make the most of StreamIt Pro',
      articles: 10
    },
    {
      icon: MessageCircle,
      title: 'Troubleshooting',
      description: 'Solve common issues',
      articles: 9
    }
  ]

  const popularArticles = [
    'How to start your first meeting',
    'Setting up your audio and video',
    'Inviting participants to meetings',
    'Using screen sharing effectively',
    'Recording meetings and accessing recordings',
    'Managing meeting security settings',
    'Troubleshooting connection issues',
    'Using chat during meetings'
  ]

  const faqs = [
    {
      question: 'How do I start a meeting?',
      answer: 'To start a meeting, click the "Start Meeting" button on the homepage, enter your name, and you\'ll be taken to your meeting room. You can then invite others by sharing the meeting link.'
    },
    {
      question: 'Can I record meetings?',
      answer: 'Yes, meeting recording is available on Pro and Enterprise plans. Click the record button during your meeting to start recording. Recordings are automatically saved to your account.'
    },
    {
      question: 'How many participants can join a meeting?',
      answer: 'The number of participants depends on your plan: Basic (5 participants), Pro (25 participants), and Enterprise (100+ participants).'
    },
    {
      question: 'Is StreamIt Pro secure?',
      answer: 'Yes, StreamIt Pro uses end-to-end encryption, is SOC 2 certified, and complies with GDPR and other security standards to keep your meetings secure.'
    },
    {
      question: 'Do I need to download software?',
      answer: 'No, StreamIt Pro works directly in your web browser. No downloads or installations required. We also offer mobile apps for iOS and Android.'
    },
    {
      question: 'How do I share my screen?',
      answer: 'During a meeting, click the screen share button in the toolbar. You can choose to share your entire screen, a specific application, or a browser tab.'
    }
  ]

  const toggleFaq = (index: number) => {
    setOpenFaq(openFaq === index ? null : index)
  }

  return (
    <>
      {/* Animated Background */}
      <div className="animated-bg"></div>

      <div className="min-h-screen pt-20 relative z-10">
        {/* Hero Section */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto text-center">
            <h1 className="text-5xl font-bold text-white mb-6 fade-in">
              Help Center
            </h1>
            <p className="text-xl text-white/80 max-w-3xl mx-auto leading-relaxed slide-up mb-8">
              Find answers to your questions and learn how to get the most out of StreamIt Pro
            </p>
            
            {/* Search Bar */}
            <div className="max-w-2xl mx-auto">
              <div className="relative">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-6 w-6 text-white/60" />
                <input
                  type="text"
                  placeholder="Search for help articles..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="glass-input w-full pl-12 pr-4 py-4 text-lg"
                />
              </div>
            </div>
          </div>
        </section>

        {/* Categories */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <h2 className="text-3xl font-bold text-white mb-12 text-center">Browse by Category</h2>
            
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {categories.map((category, index) => (
                <div key={index} className="glass p-6 hover:transform hover:scale-105 transition-all duration-300 cursor-pointer">
                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center flex-shrink-0">
                      <category.icon className="h-6 w-6 text-white" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-xl font-semibold text-white mb-2">{category.title}</h3>
                      <p className="text-white/70 text-sm mb-3">{category.description}</p>
                      <div className="flex items-center justify-between">
                        <span className="text-white/60 text-sm">{category.articles} articles</span>
                        <ChevronRight className="h-4 w-4 text-white/60" />
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Popular Articles */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <h2 className="text-3xl font-bold text-white mb-12 text-center">Popular Articles</h2>
            
            <div className="grid md:grid-cols-2 gap-4">
              {popularArticles.map((article, index) => (
                <div key={index} className="glass p-4 hover:bg-white/10 transition-colors cursor-pointer">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <FileText className="h-5 w-5 text-purple-400" />
                      <span className="text-white">{article}</span>
                    </div>
                    <ChevronRight className="h-4 w-4 text-white/60" />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section className="py-20 px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-white mb-12 text-center">
              Frequently Asked Questions
            </h2>
            
            <div className="space-y-4">
              {faqs.map((faq, index) => (
                <div key={index} className="glass">
                  <button
                    onClick={() => toggleFaq(index)}
                    className="w-full p-6 text-left flex items-center justify-between hover:bg-white/5 transition-colors"
                  >
                    <span className="text-white font-semibold">{faq.question}</span>
                    {openFaq === index ? (
                      <ChevronUp className="h-5 w-5 text-white/60" />
                    ) : (
                      <ChevronDown className="h-5 w-5 text-white/60" />
                    )}
                  </button>
                  {openFaq === index && (
                    <div className="px-6 pb-6">
                      <p className="text-white/70">{faq.answer}</p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Contact Support */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-white mb-4">
                Still Need Help?
              </h2>
              <p className="text-xl text-white/70">
                Our support team is here to help you with any questions
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              <div className="glass p-8 text-center">
                <MessageCircle className="h-12 w-12 text-purple-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-3">Live Chat</h3>
                <p className="text-white/70 mb-4">
                  Get instant help from our support team
                </p>
                <button className="btn-primary w-full">Start Chat</button>
              </div>

              <div className="glass p-8 text-center">
                <Mail className="h-12 w-12 text-blue-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-3">Email Support</h3>
                <p className="text-white/70 mb-4">
                  Send us an email and we'll respond within 24 hours
                </p>
                <button className="btn-secondary w-full">Send Email</button>
              </div>

              <div className="glass p-8 text-center">
                <Phone className="h-12 w-12 text-green-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-3">Phone Support</h3>
                <p className="text-white/70 mb-4">
                  Call us for immediate assistance
                </p>
                <button className="btn-secondary w-full">Call Now</button>
              </div>
            </div>
          </div>
        </section>

        {/* Quick Links */}
        <section className="py-20 px-4">
          <div className="max-w-4xl mx-auto">
            <div className="glass p-8">
              <h3 className="text-2xl font-bold text-white mb-6 text-center">Quick Links</h3>
              <div className="grid md:grid-cols-2 gap-4">
                <a href="#" className="flex items-center gap-3 p-3 hover:bg-white/5 rounded-lg transition-colors">
                  <HelpCircle className="h-5 w-5 text-purple-400" />
                  <span className="text-white">System Status</span>
                </a>
                <a href="#" className="flex items-center gap-3 p-3 hover:bg-white/5 rounded-lg transition-colors">
                  <FileText className="h-5 w-5 text-blue-400" />
                  <span className="text-white">API Documentation</span>
                </a>
                <a href="#" className="flex items-center gap-3 p-3 hover:bg-white/5 rounded-lg transition-colors">
                  <Video className="h-5 w-5 text-green-400" />
                  <span className="text-white">Video Tutorials</span>
                </a>
                <a href="#" className="flex items-center gap-3 p-3 hover:bg-white/5 rounded-lg transition-colors">
                  <MessageCircle className="h-5 w-5 text-orange-400" />
                  <span className="text-white">Community Forum</span>
                </a>
              </div>
            </div>
          </div>
        </section>
      </div>
    </>
  )
}
