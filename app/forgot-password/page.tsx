'use client'

import { useState } from 'react'

// Force dynamic rendering
export const dynamic = 'force-dynamic'
import Link from 'next/link'
import { Mail, ArrowLeft, CheckCircle } from 'lucide-react'

export default function ForgotPasswordPage() {
  const [email, setEmail] = useState('')
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false)
      setIsSubmitted(true)
    }, 2000)
  }

  if (isSubmitted) {
    return (
      <>
        {/* Animated Background */}
        <div className="animated-bg"></div>

        <div className="min-h-screen flex items-center justify-center p-4 relative z-10 pt-20">
          <div className="max-w-md w-full">
            <div className="glass p-8 text-center">
              <CheckCircle className="h-16 w-16 text-green-400 mx-auto mb-6" />
              <h1 className="text-3xl font-bold text-white mb-4">Check Your Email</h1>
              <p className="text-white/70 mb-6">
                We've sent a password reset link to <strong>{email}</strong>. 
                Please check your email and follow the instructions to reset your password.
              </p>
              <p className="text-white/60 text-sm mb-8">
                Didn't receive the email? Check your spam folder or try again in a few minutes.
              </p>
              <div className="space-y-4">
                <Link href="/login" className="btn-primary w-full inline-block text-center">
                  Back to Login
                </Link>
                <button 
                  onClick={() => setIsSubmitted(false)}
                  className="btn-secondary w-full"
                >
                  Try Different Email
                </button>
              </div>
            </div>
          </div>
        </div>
      </>
    )
  }

  return (
    <>
      {/* Animated Background */}
      <div className="animated-bg"></div>

      <div className="min-h-screen flex items-center justify-center p-4 relative z-10 pt-20">
        <div className="max-w-md w-full">
          <div className="glass p-8">
            {/* Back Link */}
            <Link 
              href="/login" 
              className="flex items-center gap-2 text-white/70 hover:text-white transition-colors mb-6"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Login
            </Link>

            {/* Header */}
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold text-white mb-2">Forgot Password?</h1>
              <p className="text-white/70">
                Enter your email address and we'll send you a link to reset your password
              </p>
            </div>

            {/* Reset Form */}
            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label htmlFor="email" className="block text-white/80 text-sm font-medium mb-2">
                  Email Address
                </label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-white/60" />
                  <input
                    type="email"
                    id="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                    className="glass-input w-full pl-10"
                    placeholder="Enter your email address"
                  />
                </div>
              </div>

              <button
                type="submit"
                disabled={isLoading}
                className={`btn-primary w-full flex items-center justify-center gap-2 ${
                  isLoading ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                {isLoading ? (
                  <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                ) : (
                  <>
                    <Mail className="h-4 w-4" />
                    Send Reset Link
                  </>
                )}
              </button>
            </form>

            {/* Additional Info */}
            <div className="mt-8 text-center">
              <p className="text-white/60 text-sm">
                Remember your password?{' '}
                <Link href="/login" className="text-purple-400 hover:text-purple-300 font-medium">
                  Sign in
                </Link>
              </p>
            </div>
          </div>

          {/* Help Text */}
          <div className="mt-8 glass p-6">
            <h3 className="text-white font-semibold mb-3">Need Help?</h3>
            <div className="space-y-2 text-white/70 text-sm">
              <p>• Make sure you enter the email address associated with your account</p>
              <p>• Check your spam or junk folder if you don't see the email</p>
              <p>• The reset link will expire in 24 hours for security</p>
              <p>• Contact support if you continue to have issues</p>
            </div>
            <div className="mt-4">
              <Link href="/contact" className="text-purple-400 hover:text-purple-300 text-sm">
                Contact Support →
              </Link>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
