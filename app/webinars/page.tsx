'use client'

import { Calendar, Clock, Users, Play, ArrowR<PERSON>, Bell } from 'lucide-react'

// Force dynamic rendering
export const dynamic = 'force-dynamic'

export default function WebinarsPage() {
  const upcomingWebinars = [
    {
      id: 1,
      title: 'The Future of Remote Work: Trends for 2024',
      description: 'Join industry experts as they discuss the latest trends shaping remote work and how video conferencing is evolving.',
      date: '2024-02-15',
      time: '2:00 PM PST',
      duration: '60 minutes',
      presenter: '<PERSON>, VP of Product',
      attendees: 245,
      image: '/api/placeholder/400/250'
    },
    {
      id: 2,
      title: 'Mastering Virtual Team Management',
      description: 'Learn proven strategies for managing distributed teams and maintaining productivity in remote environments.',
      date: '2024-02-22',
      time: '11:00 AM PST',
      duration: '45 minutes',
      presenter: '<PERSON>, Team Lead',
      attendees: 189,
      image: '/api/placeholder/400/250'
    },
    {
      id: 3,
      title: 'Security Best Practices for Video Conferencing',
      description: 'Discover how to keep your video meetings secure and protect sensitive information.',
      date: '2024-03-01',
      time: '1:00 PM PST',
      duration: '50 minutes',
      presenter: '<PERSON>, Security Expert',
      attendees: 156,
      image: '/api/placeholder/400/250'
    }
  ]

  const pastWebinars = [
    {
      id: 4,
      title: 'StreamIt Pro Advanced Features Deep Dive',
      description: 'Explore advanced features like breakout rooms, polling, and custom integrations.',
      date: '2024-01-25',
      duration: '55 minutes',
      presenter: 'David Kim, Product Manager',
      views: 1250,
      image: '/api/placeholder/400/250'
    },
    {
      id: 5,
      title: 'Building Better Meeting Culture',
      description: 'Tips for creating more engaging and productive virtual meetings.',
      date: '2024-01-18',
      duration: '40 minutes',
      presenter: 'Lisa Thompson, HR Director',
      views: 980,
      image: '/api/placeholder/400/250'
    },
    {
      id: 6,
      title: 'Optimizing Your Home Office Setup',
      description: 'Create the perfect home office environment for professional video calls.',
      date: '2024-01-11',
      duration: '35 minutes',
      presenter: 'Alex Park, UX Designer',
      views: 1450,
      image: '/api/placeholder/400/250'
    }
  ]

  return (
    <>
      {/* Animated Background */}
      <div className="animated-bg"></div>

      <div className="min-h-screen pt-20 relative z-10">
        {/* Hero Section */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto text-center">
            <h1 className="text-5xl font-bold text-white mb-6 fade-in">
              StreamIt Pro Webinars
            </h1>
            <p className="text-xl text-white/80 max-w-3xl mx-auto leading-relaxed slide-up">
              Join our expert-led webinars to learn best practices, discover new features, 
              and connect with other StreamIt Pro users.
            </p>
          </div>
        </section>

        {/* Upcoming Webinars */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <h2 className="text-3xl font-bold text-white mb-12 text-center">Upcoming Webinars</h2>
            
            <div className="space-y-8">
              {upcomingWebinars.map((webinar, index) => (
                <div key={webinar.id} className={`glass p-8 ${index === 0 ? 'border-2 border-purple-500' : ''}`}>
                  {index === 0 && (
                    <div className="bg-purple-500 text-white px-4 py-2 rounded-full text-sm font-semibold inline-block mb-4">
                      Next Webinar
                    </div>
                  )}
                  <div className="grid lg:grid-cols-3 gap-8 items-center">
                    <div className="lg:col-span-2">
                      <h3 className="text-2xl font-bold text-white mb-4">{webinar.title}</h3>
                      <p className="text-white/70 mb-6">{webinar.description}</p>
                      
                      <div className="grid md:grid-cols-2 gap-4 mb-6">
                        <div className="flex items-center gap-3">
                          <Calendar className="h-5 w-5 text-purple-400" />
                          <span className="text-white">{webinar.date}</span>
                        </div>
                        <div className="flex items-center gap-3">
                          <Clock className="h-5 w-5 text-blue-400" />
                          <span className="text-white">{webinar.time}</span>
                        </div>
                        <div className="flex items-center gap-3">
                          <Users className="h-5 w-5 text-green-400" />
                          <span className="text-white">{webinar.attendees} registered</span>
                        </div>
                        <div className="flex items-center gap-3">
                          <Clock className="h-5 w-5 text-orange-400" />
                          <span className="text-white">{webinar.duration}</span>
                        </div>
                      </div>
                      
                      <p className="text-white/60 mb-6">Presented by {webinar.presenter}</p>
                      
                      <div className="flex flex-col sm:flex-row gap-4">
                        <button className="btn-primary flex items-center gap-2">
                          <Calendar className="h-4 w-4" />
                          Register Now
                        </button>
                        <button className="btn-secondary flex items-center gap-2">
                          <Bell className="h-4 w-4" />
                          Set Reminder
                        </button>
                      </div>
                    </div>
                    
                    <div className="glass-dark p-4 rounded-lg">
                      <div className="w-full h-48 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-lg flex items-center justify-center">
                        <span className="text-white/60">Webinar Preview</span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Past Webinars */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <h2 className="text-3xl font-bold text-white mb-12 text-center">On-Demand Webinars</h2>
            
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {pastWebinars.map((webinar) => (
                <div key={webinar.id} className="glass p-6 hover:transform hover:scale-105 transition-all duration-300">
                  <div className="relative mb-4">
                    <div className="w-full h-48 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-lg flex items-center justify-center relative overflow-hidden">
                      <Play className="h-12 w-12 text-white/80 hover:text-white cursor-pointer transition-colors" />
                      <div className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-xs font-semibold">
                        RECORDED
                      </div>
                      <div className="absolute bottom-2 right-2 bg-black/50 text-white px-2 py-1 rounded text-xs">
                        {webinar.duration}
                      </div>
                    </div>
                  </div>
                  
                  <h3 className="text-lg font-semibold text-white mb-3">{webinar.title}</h3>
                  <p className="text-white/70 mb-4 text-sm">{webinar.description}</p>
                  
                  <div className="space-y-2 mb-4">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-white/60" />
                      <span className="text-white/60 text-sm">{webinar.date}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-white/60" />
                      <span className="text-white/60 text-sm">{webinar.views} views</span>
                    </div>
                  </div>
                  
                  <p className="text-white/60 text-sm mb-4">By {webinar.presenter}</p>
                  
                  <button className="btn-secondary w-full flex items-center justify-center gap-2">
                    <Play className="h-4 w-4" />
                    Watch Now
                  </button>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Newsletter Signup */}
        <section className="py-20 px-4">
          <div className="max-w-4xl mx-auto">
            <div className="glass p-12 text-center">
              <h2 className="text-3xl font-bold text-white mb-4">
                Never Miss a Webinar
              </h2>
              <p className="text-xl text-white/70 mb-8">
                Subscribe to get notified about upcoming webinars and exclusive content
              </p>
              <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                <input
                  type="email"
                  placeholder="Enter your email"
                  className="glass-input flex-1"
                />
                <button className="btn-primary px-6 py-3 flex items-center gap-2">
                  <Bell className="h-4 w-4" />
                  Subscribe
                </button>
              </div>
              <p className="text-white/60 text-sm mt-4">
                Get weekly updates and early access to webinar recordings
              </p>
            </div>
          </div>
        </section>

        {/* Webinar Benefits */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <h2 className="text-3xl font-bold text-white mb-12 text-center">Why Attend Our Webinars?</h2>
            
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="glass p-6 text-center">
                <Users className="h-12 w-12 text-purple-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-white mb-2">Expert Insights</h3>
                <p className="text-white/70 text-sm">Learn from industry experts and StreamIt Pro team members</p>
              </div>
              <div className="glass p-6 text-center">
                <Play className="h-12 w-12 text-blue-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-white mb-2">Interactive Sessions</h3>
                <p className="text-white/70 text-sm">Participate in Q&A sessions and live demonstrations</p>
              </div>
              <div className="glass p-6 text-center">
                <Calendar className="h-12 w-12 text-green-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-white mb-2">Regular Schedule</h3>
                <p className="text-white/70 text-sm">New webinars every week covering different topics</p>
              </div>
              <div className="glass p-6 text-center">
                <ArrowRight className="h-12 w-12 text-orange-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-white mb-2">Actionable Tips</h3>
                <p className="text-white/70 text-sm">Get practical advice you can implement immediately</p>
              </div>
            </div>
          </div>
        </section>
      </div>
    </>
  )
}
