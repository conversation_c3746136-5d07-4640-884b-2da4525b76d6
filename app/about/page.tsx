'use client'

import { Video, Users, Shield, Zap, Award, TrendingUp, Heart, Globe, Clock, Headphones } from 'lucide-react'

export default function AboutPage() {
  return (
    <>
      {/* Animated Background */}
      <div className="animated-bg"></div>

      <div className="min-h-screen pt-20 relative z-10">
        {/* Hero Section */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto text-center">
            <h1 className="text-5xl font-bold text-white mb-6 fade-in">
              About StreamIt Pro
            </h1>
            <p className="text-xl text-white/80 max-w-3xl mx-auto leading-relaxed slide-up">
              We're on a mission to revolutionize remote communication and make video conferencing 
              accessible, secure, and delightful for teams around the world.
            </p>
          </div>
        </section>

        {/* Mission Section */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div>
                <h2 className="text-4xl font-bold text-white mb-6">Our Mission</h2>
                <p className="text-xl text-white/80 mb-6">
                  At StreamIt Pro, we believe that distance should never be a barrier to meaningful 
                  collaboration. Our platform is designed to bring teams together with crystal-clear 
                  video, advanced audio processing, and intuitive features that make remote work feel natural.
                </p>
                <p className="text-white/70 mb-8">
                  Founded in 2019 by a team of passionate engineers and designers, we've grown from a 
                  small startup to a trusted platform serving millions of users worldwide. Our commitment 
                  to innovation, security, and user experience drives everything we do.
                </p>
                <div className="grid grid-cols-2 gap-6">
                  <div className="glass p-6 text-center">
                    <div className="text-3xl font-bold text-white mb-2">10M+</div>
                    <div className="text-white/60 text-sm">Active Users</div>
                  </div>
                  <div className="glass p-6 text-center">
                    <div className="text-3xl font-bold text-white mb-2">150+</div>
                    <div className="text-white/60 text-sm">Countries</div>
                  </div>
                </div>
              </div>
              <div className="glass p-8">
                <h3 className="text-2xl font-bold text-white mb-6">Our Values</h3>
                <div className="space-y-6">
                  <div className="flex items-start gap-4">
                    <Award className="h-6 w-6 text-purple-400 mt-1" />
                    <div>
                      <h4 className="text-white font-semibold mb-2">Excellence</h4>
                      <p className="text-white/70 text-sm">
                        We strive for excellence in every aspect of our platform, from video quality to user experience.
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-4">
                    <Shield className="h-6 w-6 text-green-400 mt-1" />
                    <div>
                      <h4 className="text-white font-semibold mb-2">Security</h4>
                      <p className="text-white/70 text-sm">
                        Your privacy and data security are paramount. We implement industry-leading security measures.
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-4">
                    <Heart className="h-6 w-6 text-red-400 mt-1" />
                    <div>
                      <h4 className="text-white font-semibold mb-2">User-Centric</h4>
                      <p className="text-white/70 text-sm">
                        Every feature we build is designed with our users' needs and feedback at the center.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Team Section */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-white mb-4">Meet Our Team</h2>
              <p className="text-xl text-white/70 max-w-3xl mx-auto">
                Our diverse team of engineers, designers, and innovators work together to create the best video conferencing experience.
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              <div className="glass p-8 text-center">
                <div className="w-24 h-24 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-white text-2xl font-bold">JS</span>
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">John Smith</h3>
                <p className="text-purple-400 mb-3">CEO & Co-Founder</p>
                <p className="text-white/70 text-sm">
                  Former VP of Engineering at major tech companies. Passionate about building products that connect people.
                </p>
              </div>

              <div className="glass p-8 text-center">
                <div className="w-24 h-24 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-white text-2xl font-bold">EJ</span>
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">Emily Johnson</h3>
                <p className="text-blue-400 mb-3">CTO & Co-Founder</p>
                <p className="text-white/70 text-sm">
                  WebRTC expert with 15+ years in real-time communications. Leads our technical innovation.
                </p>
              </div>

              <div className="glass p-8 text-center">
                <div className="w-24 h-24 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-white text-2xl font-bold">MR</span>
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">Michael Rodriguez</h3>
                <p className="text-green-400 mb-3">Head of Design</p>
                <p className="text-white/70 text-sm">
                  Award-winning designer focused on creating intuitive and beautiful user experiences.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Technology Section */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-white mb-4">Our Technology</h2>
              <p className="text-xl text-white/70 max-w-3xl mx-auto">
                Built on cutting-edge technologies to deliver the best video conferencing experience
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="glass p-6 text-center">
                <Globe className="h-12 w-12 text-purple-400 mx-auto mb-3" />
                <h4 className="text-white font-semibold mb-2">WebRTC</h4>
                <p className="text-white/60 text-sm">Real-time peer-to-peer communication</p>
              </div>
              <div className="glass p-6 text-center">
                <Shield className="h-12 w-12 text-green-400 mx-auto mb-3" />
                <h4 className="text-white font-semibold mb-2">End-to-End Encryption</h4>
                <p className="text-white/60 text-sm">Military-grade security protocols</p>
              </div>
              <div className="glass p-6 text-center">
                <Zap className="h-12 w-12 text-yellow-400 mx-auto mb-3" />
                <h4 className="text-white font-semibold mb-2">Edge Computing</h4>
                <p className="text-white/60 text-sm">Global CDN for minimal latency</p>
              </div>
              <div className="glass p-6 text-center">
                <Headphones className="h-12 w-12 text-blue-400 mx-auto mb-3" />
                <h4 className="text-white font-semibold mb-2">AI Audio Processing</h4>
                <p className="text-white/60 text-sm">Advanced noise cancellation</p>
              </div>
            </div>
          </div>
        </section>

        {/* Contact CTA */}
        <section className="py-20 px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="glass p-12">
              <h2 className="text-3xl font-bold text-white mb-4">Ready to Get Started?</h2>
              <p className="text-xl text-white/70 mb-8">
                Join millions of users who trust StreamIt Pro for their video conferencing needs
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button className="btn-primary px-8 py-3">Start Free Trial</button>
                <button className="btn-secondary px-8 py-3">Contact Sales</button>
              </div>
            </div>
          </div>
        </section>
      </div>
    </>
  )
}
