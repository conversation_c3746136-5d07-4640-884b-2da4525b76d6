'use client'

import { Shield, Eye, Lock, FileText, Users, Globe } from 'lucide-react'

export default function PrivacyPage() {
  const sections = [
    {
      id: 'information-collection',
      title: 'Information We Collect',
      icon: FileText
    },
    {
      id: 'information-use',
      title: 'How We Use Information',
      icon: Users
    },
    {
      id: 'information-sharing',
      title: 'Information Sharing',
      icon: Globe
    },
    {
      id: 'data-security',
      title: 'Data Security',
      icon: Shield
    },
    {
      id: 'your-rights',
      title: 'Your Rights',
      icon: Eye
    },
    {
      id: 'contact',
      title: 'Contact Us',
      icon: Lock
    }
  ]

  return (
    <>
      {/* Animated Background */}
      <div className="animated-bg"></div>

      <div className="min-h-screen pt-20 relative z-10">
        {/* Hero Section */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto text-center">
            <h1 className="text-5xl font-bold text-white mb-6 fade-in">
              Privacy Policy
            </h1>
            <p className="text-xl text-white/80 max-w-3xl mx-auto leading-relaxed slide-up">
              Your privacy is important to us. This policy explains how we collect, 
              use, and protect your information when you use StreamIt Pro.
            </p>
            <p className="text-white/60 mt-4">Last updated: January 1, 2024</p>
          </div>
        </section>

        {/* Table of Contents */}
        <section className="py-10 px-4">
          <div className="max-w-4xl mx-auto">
            <div className="glass p-8">
              <h2 className="text-2xl font-bold text-white mb-6">Table of Contents</h2>
              <div className="grid md:grid-cols-2 gap-4">
                {sections.map((section, index) => (
                  <a
                    key={section.id}
                    href={`#${section.id}`}
                    className="flex items-center gap-3 p-3 hover:bg-white/5 rounded-lg transition-colors"
                  >
                    <section.icon className="h-5 w-5 text-purple-400" />
                    <span className="text-white hover:text-purple-300">{section.title}</span>
                  </a>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Privacy Content */}
        <section className="py-20 px-4">
          <div className="max-w-4xl mx-auto space-y-12">
            
            {/* Information Collection */}
            <div id="information-collection" className="glass p-8">
              <div className="flex items-center gap-3 mb-6">
                <FileText className="h-6 w-6 text-purple-400" />
                <h2 className="text-2xl font-bold text-white">Information We Collect</h2>
              </div>
              <div className="space-y-4 text-white/80">
                <h3 className="text-lg font-semibold text-white">Account Information</h3>
                <p>When you create an account, we collect your name, email address, and other information you provide during registration.</p>
                
                <h3 className="text-lg font-semibold text-white">Meeting Data</h3>
                <p>We collect information about your meetings, including participant lists, meeting duration, and usage statistics. We do not record or store the content of your meetings unless you explicitly choose to record them.</p>
                
                <h3 className="text-lg font-semibold text-white">Technical Information</h3>
                <p>We automatically collect technical information such as your IP address, browser type, device information, and usage patterns to improve our service.</p>
                
                <h3 className="text-lg font-semibold text-white">Communications</h3>
                <p>When you contact us for support or feedback, we collect the information you provide in those communications.</p>
              </div>
            </div>

            {/* Information Use */}
            <div id="information-use" className="glass p-8">
              <div className="flex items-center gap-3 mb-6">
                <Users className="h-6 w-6 text-blue-400" />
                <h2 className="text-2xl font-bold text-white">How We Use Information</h2>
              </div>
              <div className="space-y-4 text-white/80">
                <p>We use the information we collect to:</p>
                <ul className="list-disc list-inside space-y-2 ml-4">
                  <li>Provide and maintain our video conferencing service</li>
                  <li>Process your account registration and manage your subscription</li>
                  <li>Facilitate meetings and communication between participants</li>
                  <li>Improve our service through analytics and usage data</li>
                  <li>Provide customer support and respond to your inquiries</li>
                  <li>Send you important updates about our service</li>
                  <li>Ensure the security and integrity of our platform</li>
                  <li>Comply with legal obligations and enforce our terms</li>
                </ul>
              </div>
            </div>

            {/* Information Sharing */}
            <div id="information-sharing" className="glass p-8">
              <div className="flex items-center gap-3 mb-6">
                <Globe className="h-6 w-6 text-green-400" />
                <h2 className="text-2xl font-bold text-white">Information Sharing</h2>
              </div>
              <div className="space-y-4 text-white/80">
                <p>We do not sell, trade, or rent your personal information to third parties. We may share your information only in the following circumstances:</p>
                
                <h3 className="text-lg font-semibold text-white">Service Providers</h3>
                <p>We may share information with trusted third-party service providers who help us operate our service, such as cloud hosting providers and analytics services.</p>
                
                <h3 className="text-lg font-semibold text-white">Legal Requirements</h3>
                <p>We may disclose information if required by law, court order, or government request, or to protect our rights and the safety of our users.</p>
                
                <h3 className="text-lg font-semibold text-white">Business Transfers</h3>
                <p>In the event of a merger, acquisition, or sale of assets, your information may be transferred as part of that transaction.</p>
              </div>
            </div>

            {/* Data Security */}
            <div id="data-security" className="glass p-8">
              <div className="flex items-center gap-3 mb-6">
                <Shield className="h-6 w-6 text-orange-400" />
                <h2 className="text-2xl font-bold text-white">Data Security</h2>
              </div>
              <div className="space-y-4 text-white/80">
                <p>We implement industry-standard security measures to protect your information:</p>
                <ul className="list-disc list-inside space-y-2 ml-4">
                  <li>End-to-end encryption for all meeting communications</li>
                  <li>AES-256 encryption for data at rest</li>
                  <li>Regular security audits and penetration testing</li>
                  <li>SOC 2 Type II compliance</li>
                  <li>Secure data centers with physical access controls</li>
                  <li>Employee background checks and security training</li>
                </ul>
                <p>While we strive to protect your information, no method of transmission over the internet is 100% secure. We cannot guarantee absolute security but are committed to protecting your data.</p>
              </div>
            </div>

            {/* Your Rights */}
            <div id="your-rights" className="glass p-8">
              <div className="flex items-center gap-3 mb-6">
                <Eye className="h-6 w-6 text-purple-400" />
                <h2 className="text-2xl font-bold text-white">Your Rights</h2>
              </div>
              <div className="space-y-4 text-white/80">
                <p>You have the following rights regarding your personal information:</p>
                
                <h3 className="text-lg font-semibold text-white">Access and Portability</h3>
                <p>You can access and download your personal information through your account settings.</p>
                
                <h3 className="text-lg font-semibold text-white">Correction</h3>
                <p>You can update or correct your personal information at any time through your account.</p>
                
                <h3 className="text-lg font-semibold text-white">Deletion</h3>
                <p>You can request deletion of your account and personal information. Some information may be retained for legal or business purposes.</p>
                
                <h3 className="text-lg font-semibold text-white">Opt-out</h3>
                <p>You can opt out of marketing communications at any time by following the unsubscribe instructions in our emails.</p>
                
                <h3 className="text-lg font-semibold text-white">GDPR Rights</h3>
                <p>If you are in the European Union, you have additional rights under GDPR, including the right to object to processing and the right to lodge a complaint with a supervisory authority.</p>
              </div>
            </div>

            {/* Contact */}
            <div id="contact" className="glass p-8">
              <div className="flex items-center gap-3 mb-6">
                <Lock className="h-6 w-6 text-red-400" />
                <h2 className="text-2xl font-bold text-white">Contact Us</h2>
              </div>
              <div className="space-y-4 text-white/80">
                <p>If you have any questions about this Privacy Policy or our data practices, please contact us:</p>
                
                <div className="bg-white/5 p-4 rounded-lg">
                  <p><strong>Email:</strong> <EMAIL></p>
                  <p><strong>Address:</strong> StreamIt Pro, Inc.<br />
                  123 Privacy Street<br />
                  San Francisco, CA 94105<br />
                  United States</p>
                </div>
                
                <p>We will respond to your inquiry within 30 days.</p>
              </div>
            </div>

            {/* Updates */}
            <div className="glass p-8">
              <h2 className="text-2xl font-bold text-white mb-6">Policy Updates</h2>
              <div className="space-y-4 text-white/80">
                <p>We may update this Privacy Policy from time to time. When we make changes, we will:</p>
                <ul className="list-disc list-inside space-y-2 ml-4">
                  <li>Update the "Last updated" date at the top of this policy</li>
                  <li>Notify you via email if the changes are significant</li>
                  <li>Post a notice on our website</li>
                </ul>
                <p>Your continued use of our service after any changes constitutes acceptance of the updated policy.</p>
              </div>
            </div>

          </div>
        </section>
      </div>
    </>
  )
}
