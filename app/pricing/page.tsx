'use client'

import { useState } from 'react'
import { Check, X, Star, Users, Clock, Shield, Zap, Headphones, Globe } from 'lucide-react'

export default function PricingPage() {
  const [isAnnual, setIsAnnual] = useState(false)

  const plans = [
    {
      name: 'Basic',
      description: 'Perfect for small teams and personal use',
      monthlyPrice: 0,
      annualPrice: 0,
      features: [
        { name: 'Up to 5 participants', included: true },
        { name: '40-minute meetings', included: true },
        { name: 'HD video quality', included: true },
        { name: 'Basic chat features', included: true },
        { name: 'Screen sharing', included: true },
        { name: 'Mobile apps', included: true },
        { name: 'Meeting recordings', included: false },
        { name: 'Advanced security', included: false },
        { name: 'Priority support', included: false },
        { name: 'Custom branding', included: false }
      ],
      cta: 'Get Started',
      popular: false
    },
    {
      name: 'Pro',
      description: 'Ideal for growing teams and businesses',
      monthlyPrice: 12,
      annualPrice: 120,
      features: [
        { name: 'Up to 25 participants', included: true },
        { name: 'Unlimited meeting time', included: true },
        { name: '4K video quality', included: true },
        { name: 'Advanced chat & file sharing', included: true },
        { name: 'Screen sharing', included: true },
        { name: 'Mobile apps', included: true },
        { name: 'Meeting recordings', included: true },
        { name: 'Advanced security', included: true },
        { name: 'Email support', included: true },
        { name: 'Custom branding', included: false }
      ],
      cta: 'Start Free Trial',
      popular: true
    },
    {
      name: 'Enterprise',
      description: 'For large organizations with advanced needs',
      monthlyPrice: 25,
      annualPrice: 250,
      features: [
        { name: 'Up to 100 participants', included: true },
        { name: 'Unlimited everything', included: true },
        { name: '4K video quality', included: true },
        { name: 'Advanced collaboration tools', included: true },
        { name: 'Screen sharing', included: true },
        { name: 'Mobile apps', included: true },
        { name: 'Meeting recordings', included: true },
        { name: 'Enterprise security', included: true },
        { name: 'Priority support', included: true },
        { name: 'Custom branding', included: true }
      ],
      cta: 'Contact Sales',
      popular: false
    }
  ]

  return (
    <>
      {/* Animated Background */}
      <div className="animated-bg"></div>

      <div className="min-h-screen pt-20 relative z-10">
        {/* Hero Section */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto text-center">
            <h1 className="text-5xl font-bold text-white mb-6 fade-in">
              Simple, Transparent Pricing
            </h1>
            <p className="text-xl text-white/80 max-w-3xl mx-auto leading-relaxed slide-up mb-8">
              Choose the perfect plan for your team. All plans include our core features with no hidden fees.
            </p>
            
            {/* Billing Toggle */}
            <div className="flex items-center justify-center gap-4 mb-12">
              <span className={`text-white/70 ${!isAnnual ? 'text-white font-semibold' : ''}`}>
                Monthly
              </span>
              <button
                onClick={() => setIsAnnual(!isAnnual)}
                className={`relative w-14 h-7 rounded-full transition-colors ${
                  isAnnual ? 'bg-purple-500' : 'bg-white/20'
                }`}
              >
                <div
                  className={`absolute top-1 w-5 h-5 bg-white rounded-full transition-transform ${
                    isAnnual ? 'translate-x-7' : 'translate-x-1'
                  }`}
                />
              </button>
              <span className={`text-white/70 ${isAnnual ? 'text-white font-semibold' : ''}`}>
                Annual
              </span>
              {isAnnual && (
                <span className="bg-green-500 text-white text-xs px-2 py-1 rounded-full">
                  Save 20%
                </span>
              )}
            </div>
          </div>
        </section>

        {/* Pricing Cards */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <div className="grid md:grid-cols-3 gap-8">
              {plans.map((plan, index) => (
                <div
                  key={plan.name}
                  className={`glass p-8 text-center relative ${
                    plan.popular ? 'border-2 border-purple-500' : ''
                  }`}
                >
                  {plan.popular && (
                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-gradient-to-r from-purple-500 to-pink-500 text-white px-4 py-1 rounded-full text-sm font-semibold">
                      Most Popular
                    </div>
                  )}
                  
                  <h3 className="text-2xl font-bold text-white mb-2">{plan.name}</h3>
                  <p className="text-white/70 text-sm mb-6">{plan.description}</p>
                  
                  <div className="mb-8">
                    <span className="text-4xl font-bold text-white">
                      ${isAnnual ? plan.annualPrice : plan.monthlyPrice}
                    </span>
                    <span className="text-white/60">
                      /{isAnnual ? 'year' : 'month'}
                    </span>
                  </div>

                  <ul className="space-y-3 mb-8 text-left">
                    {plan.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center gap-3">
                        {feature.included ? (
                          <Check className="h-4 w-4 text-green-400 flex-shrink-0" />
                        ) : (
                          <X className="h-4 w-4 text-red-400 flex-shrink-0" />
                        )}
                        <span className={`text-sm ${
                          feature.included ? 'text-white/80' : 'text-white/40'
                        }`}>
                          {feature.name}
                        </span>
                      </li>
                    ))}
                  </ul>

                  <button
                    className={`w-full ${
                      plan.popular ? 'btn-primary' : 'btn-secondary'
                    }`}
                  >
                    {plan.cta}
                  </button>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Features Comparison */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-white mb-4">
                Compare All Features
              </h2>
              <p className="text-xl text-white/70">
                See exactly what's included in each plan
              </p>
            </div>

            <div className="glass p-8 overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-white/10">
                    <th className="text-left text-white font-semibold py-4">Features</th>
                    <th className="text-center text-white font-semibold py-4">Basic</th>
                    <th className="text-center text-white font-semibold py-4">Pro</th>
                    <th className="text-center text-white font-semibold py-4">Enterprise</th>
                  </tr>
                </thead>
                <tbody>
                  {[
                    { feature: 'Participants', basic: '5', pro: '25', enterprise: '100' },
                    { feature: 'Meeting Duration', basic: '40 min', pro: 'Unlimited', enterprise: 'Unlimited' },
                    { feature: 'Video Quality', basic: 'HD', pro: '4K', enterprise: '4K' },
                    { feature: 'Recording', basic: '✗', pro: '✓', enterprise: '✓' },
                    { feature: 'Advanced Security', basic: '✗', pro: '✓', enterprise: '✓' },
                    { feature: 'Priority Support', basic: '✗', pro: '✗', enterprise: '✓' },
                    { feature: 'Custom Branding', basic: '✗', pro: '✗', enterprise: '✓' }
                  ].map((row, index) => (
                    <tr key={index} className="border-b border-white/5">
                      <td className="text-white/80 py-4">{row.feature}</td>
                      <td className="text-center text-white/70 py-4">{row.basic}</td>
                      <td className="text-center text-white/70 py-4">{row.pro}</td>
                      <td className="text-center text-white/70 py-4">{row.enterprise}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section className="py-20 px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-white mb-4">
                Pricing FAQ
              </h2>
            </div>

            <div className="space-y-6">
              {[
                {
                  question: "Can I change my plan anytime?",
                  answer: "Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately and we'll prorate any billing differences."
                },
                {
                  question: "Is there a free trial?",
                  answer: "Yes! We offer a 14-day free trial of our Pro plan with no credit card required. You can also use our Basic plan for free indefinitely."
                },
                {
                  question: "What payment methods do you accept?",
                  answer: "We accept all major credit cards, PayPal, and bank transfers for Enterprise customers. All payments are processed securely."
                },
                {
                  question: "Do you offer discounts for nonprofits?",
                  answer: "Yes, we offer special pricing for qualified nonprofit organizations and educational institutions. Contact our sales team for more information."
                }
              ].map((faq, index) => (
                <div key={index} className="glass p-6">
                  <h3 className="text-white font-semibold mb-3">{faq.question}</h3>
                  <p className="text-white/70">{faq.answer}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="glass p-12">
              <h2 className="text-3xl font-bold text-white mb-4">
                Ready to Get Started?
              </h2>
              <p className="text-xl text-white/70 mb-8">
                Join millions of users who trust StreamIt Pro for their video conferencing needs
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button className="btn-primary px-8 py-3">Start Free Trial</button>
                <button className="btn-secondary px-8 py-3">Contact Sales</button>
              </div>
            </div>
          </div>
        </section>
      </div>
    </>
  )
}
