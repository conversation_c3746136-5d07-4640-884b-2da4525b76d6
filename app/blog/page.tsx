'use client'

import { Calendar, User, ArrowRight, Tag, Search } from 'lucide-react'

// Force dynamic rendering
export const dynamic = 'force-dynamic'
import Link from 'next/link'

export default function BlogPage() {
  const blogPosts = [
    {
      id: 1,
      title: 'The Future of Remote Work: Video Conferencing Trends for 2024',
      excerpt: 'Explore the latest trends shaping the future of remote work and how video conferencing technology is evolving to meet new challenges.',
      author: '<PERSON>',
      date: '2024-01-15',
      category: 'Industry Insights',
      readTime: '5 min read',
      image: '/api/placeholder/400/250'
    },
    {
      id: 2,
      title: 'Best Practices for Secure Video Conferencing',
      excerpt: 'Learn essential security practices to protect your video conferences and ensure your communications remain private and secure.',
      author: '<PERSON>',
      date: '2024-01-10',
      category: 'Security',
      readTime: '7 min read',
      image: '/api/placeholder/400/250'
    },
    {
      id: 3,
      title: 'How to Optimize Your Home Office for Video Calls',
      excerpt: 'Tips and tricks for creating the perfect home office setup that will make you look and sound professional in every video call.',
      author: '<PERSON>',
      date: '2024-01-05',
      category: 'Tips & Tricks',
      readTime: '4 min read',
      image: '/api/placeholder/400/250'
    },
    {
      id: 4,
      title: 'StreamIt Pro vs Competitors: A Comprehensive Comparison',
      excerpt: 'An in-depth comparison of StreamIt Pro with other leading video conferencing platforms to help you make an informed decision.',
      author: 'David Kim',
      date: '2023-12-28',
      category: 'Product Updates',
      readTime: '8 min read',
      image: '/api/placeholder/400/250'
    },
    {
      id: 5,
      title: 'Building Better Team Culture in Remote Teams',
      excerpt: 'Strategies for maintaining strong team culture and collaboration when your team is distributed across different locations.',
      author: 'Lisa Thompson',
      date: '2023-12-20',
      category: 'Team Management',
      readTime: '6 min read',
      image: '/api/placeholder/400/250'
    },
    {
      id: 6,
      title: 'New Features: AI-Powered Noise Cancellation',
      excerpt: 'Discover our latest AI-powered noise cancellation feature that eliminates background noise for crystal-clear audio.',
      author: 'Alex Park',
      date: '2023-12-15',
      category: 'Product Updates',
      readTime: '3 min read',
      image: '/api/placeholder/400/250'
    }
  ]

  const categories = ['All', 'Industry Insights', 'Security', 'Tips & Tricks', 'Product Updates', 'Team Management']

  return (
    <>
      {/* Animated Background */}
      <div className="animated-bg"></div>

      <div className="min-h-screen pt-20 relative z-10">
        {/* Hero Section */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto text-center">
            <h1 className="text-5xl font-bold text-white mb-6 fade-in">
              StreamIt Pro Blog
            </h1>
            <p className="text-xl text-white/80 max-w-3xl mx-auto leading-relaxed slide-up">
              Stay updated with the latest insights, tips, and news about video conferencing, 
              remote work, and team collaboration.
            </p>
          </div>
        </section>

        {/* Search and Filter */}
        <section className="py-10 px-4">
          <div className="max-w-7xl mx-auto">
            <div className="flex flex-col md:flex-row gap-6 items-center justify-between">
              {/* Search */}
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-white/60" />
                <input
                  type="text"
                  placeholder="Search articles..."
                  className="glass-input w-full pl-10"
                />
              </div>

              {/* Categories */}
              <div className="flex flex-wrap gap-2">
                {categories.map((category, index) => (
                  <button
                    key={index}
                    className={`px-4 py-2 rounded-full text-sm transition-colors ${
                      index === 0 
                        ? 'bg-purple-500 text-white' 
                        : 'bg-white/10 text-white/70 hover:bg-white/20'
                    }`}
                  >
                    {category}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Featured Post */}
        <section className="py-10 px-4">
          <div className="max-w-7xl mx-auto">
            <div className="glass p-8 md:p-12">
              <div className="grid lg:grid-cols-2 gap-8 items-center">
                <div>
                  <div className="flex items-center gap-4 mb-4">
                    <span className="bg-purple-500 text-white px-3 py-1 rounded-full text-sm">Featured</span>
                    <span className="text-white/60 text-sm">{blogPosts[0].category}</span>
                  </div>
                  <h2 className="text-3xl font-bold text-white mb-4">{blogPosts[0].title}</h2>
                  <p className="text-white/70 mb-6">{blogPosts[0].excerpt}</p>
                  <div className="flex items-center gap-6 mb-6">
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-white/60" />
                      <span className="text-white/60 text-sm">{blogPosts[0].author}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-white/60" />
                      <span className="text-white/60 text-sm">{blogPosts[0].date}</span>
                    </div>
                    <span className="text-white/60 text-sm">{blogPosts[0].readTime}</span>
                  </div>
                  <Link href={`/blog/${blogPosts[0].id}`} className="btn-primary inline-flex items-center gap-2">
                    Read Article
                    <ArrowRight className="h-4 w-4" />
                  </Link>
                </div>
                <div className="glass-dark p-4 rounded-lg">
                  <div className="w-full h-64 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-lg flex items-center justify-center">
                    <span className="text-white/60">Featured Article Image</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Blog Posts Grid */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <h2 className="text-3xl font-bold text-white mb-12 text-center">Latest Articles</h2>
            
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {blogPosts.slice(1).map((post) => (
                <article key={post.id} className="glass p-6 hover:transform hover:scale-105 transition-all duration-300">
                  <div className="w-full h-48 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-lg mb-6 flex items-center justify-center">
                    <span className="text-white/60">Article Image</span>
                  </div>
                  
                  <div className="flex items-center gap-2 mb-3">
                    <Tag className="h-4 w-4 text-purple-400" />
                    <span className="text-purple-400 text-sm">{post.category}</span>
                  </div>
                  
                  <h3 className="text-xl font-semibold text-white mb-3 line-clamp-2">{post.title}</h3>
                  <p className="text-white/70 mb-4 text-sm line-clamp-3">{post.excerpt}</p>
                  
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-white/60" />
                      <span className="text-white/60 text-sm">{post.author}</span>
                    </div>
                    <span className="text-white/60 text-sm">{post.readTime}</span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-white/60" />
                      <span className="text-white/60 text-sm">{post.date}</span>
                    </div>
                    <Link 
                      href={`/blog/${post.id}`} 
                      className="text-purple-400 hover:text-purple-300 text-sm font-medium flex items-center gap-1"
                    >
                      Read More
                      <ArrowRight className="h-3 w-3" />
                    </Link>
                  </div>
                </article>
              ))}
            </div>
          </div>
        </section>

        {/* Newsletter Signup */}
        <section className="py-20 px-4">
          <div className="max-w-4xl mx-auto">
            <div className="glass p-12 text-center">
              <h2 className="text-3xl font-bold text-white mb-4">
                Stay Updated
              </h2>
              <p className="text-xl text-white/70 mb-8">
                Subscribe to our newsletter and never miss the latest insights and updates
              </p>
              <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                <input
                  type="email"
                  placeholder="Enter your email"
                  className="glass-input flex-1"
                />
                <button className="btn-primary px-6 py-3">Subscribe</button>
              </div>
              <p className="text-white/60 text-sm mt-4">
                No spam, unsubscribe at any time
              </p>
            </div>
          </div>
        </section>
      </div>
    </>
  )
}
